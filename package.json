{"name": "pgl-crm-customer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ag-grid-community/locale": "^33.3.2", "@bprogress/next": "^3.2.12", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-direction": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-visually-hidden": "^1.2.3", "@tanstack/react-table": "^8.21.3", "@types/jsonwebtoken": "^9.0.10", "ag-grid-community": "^33.3.2", "ag-grid-enterprise": "^33.3.2", "ag-grid-react": "^33.3.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "cookie": "^1.0.2", "cropperjs": "^1.6.2", "date-fns": "^4.1.0", "embla-carousel": "^8.6.0", "embla-carousel-react": "^8.6.0", "express": "^5.1.0", "file-saver": "^2.0.5", "firebase": "^11.10.0", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.471.2", "moment": "^2.30.1", "next": "^15.4.2", "next-auth": "5.0.0-beta.25", "next-intl": "^3.26.5", "next-themes": "^0.4.6", "react": "^19.1.0", "react-cropper": "^2.3.3", "react-day-picker": "^8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-intersection-observer": "^9.16.0", "react-zoom-pan-pinch": "^3.7.0", "recharts": "^2.15.4", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tanstack/eslint-plugin-query": "^5.81.2", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.6", "@types/node": "^20.19.9", "@types/nprogress": "^0.2.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-config-next": "15.1.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}, "pnpm": {"onlyBuiltDependencies": ["@firebase/util", "core-js", "protobufjs", "sharp", "unrs-resolver"]}, "packageManager": "pnpm@10.13.1+sha512.37ebf1a5c7a30d5fabe0c5df44ee8da4c965ca0c5af3dbab28c3a1681b70a256218d05c81c9c0dcf767ef6b8551eb5b960042b9ed4300c59242336377e01cfad"}