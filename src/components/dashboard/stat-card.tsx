import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "../ui/card";
import { cn } from "@/lib/utils";
import { cva } from "class-variance-authority";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

interface SubValues {
  open?: number;
  paid?: number;
  label1?: string;
  label2?: string;
}

interface StatCardProps {
  title: string;
  icon?: React.ComponentType | any;
  value?: string | number;
  subValues?: SubValues;
  className?: string;
  variant?: "default" | "primary" | "success" | "warning" | "danger" | "info";
  valuePrefix?: string;
  valueSuffix?: string;
  loading?: boolean;
}

const cardVariants = cva(
  "group transition-all duration-300 ease-in-out hover:shadow-lg relative overflow-hidden h-[140px] border backdrop-blur-[2px]",
  {
    variants: {
      variant: {
        default: "bg-gradient-to-br from-white via-white to-green-50/40 dark:from-slate-900 dark:via-slate-900 dark:to-green-950/20 border-slate-200/80 dark:border-slate-800/80 hover:border-green-300/60 dark:hover:border-green-800/40",
        primary: "bg-gradient-to-br from-white via-white to-blue-50/40 dark:from-green-700/10 dark:via-green-500/5 dark:to-green-100/0 dark:hover:border-green-500/20 dark:border-green-500/10 hover:border-blue-300/60 border-l-4 border-l-primary border-slate-200/80  ",
        success: "bg-gradient-to-br from-white via-white to-emerald-50/40 dark:from-green-700/10 dark:via-green-500/5 dark:to-green-100/0 dark:hover:border-green-500/20 dark:border-green-500/10 border-l-4 border-l-emerald-500 border-slate-200/80 hover:border-emerald-300/60 ",
        warning: "bg-gradient-to-br from-white via-white to-amber-50/40 dark:from-green-700/10 dark:via-green-500/5 dark:to-green-100/0 dark:hover:border-green-500/20 dark:border-green-500/10 border-l-4 border-l-amber-500 border-slate-200/80 hover:border-amber-300/60 ",
        danger: "bg-gradient-to-br from-white via-white to-red-50/40 dark:from-green-700/10 dark:via-green-500/5 dark:to-green-100/0 dark:hover:border-green-500/20 dark:border-green-500/10 border-l-4 border-l-red-500 border-slate-200/80 hover:border-red-300/60 ",
        info: "bg-gradient-to-br from-white via-white to-cyan-50/40 dark:from-green-700/10 dark:via-green-500/5 dark:to-green-100/0 dark:hover:border-green-500/20 dark:border-green-500/10 border-l-4 border-l-blue-500 border-slate-200/80 hover:border-cyan-300/60 ",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

export const StatCard = ({
  title,
  icon: Icon,
  value,
  subValues,
  className,
  variant = "default",
  valuePrefix,
  valueSuffix,
  loading = false,
}: StatCardProps) => {
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Get accent color based on variant
  const getAccentColor = () => {
    switch (variant) {
      case "primary": return "bg-primary dark:bg-primary/50";
      case "success": return "bg-emerald-500 dark:bg-emerald-500/50";
      case "warning": return "bg-amber-500 dark:bg-amber-500/50";
      case "danger": return "bg-red-500";
      case "info": return "bg-blue-500 dark:bg-blue-500/50";
      default: return "bg-green-500 dark:bg-green-500/50";
    }
  };
  
  // Get text color based on variant
  const getTextColor = () => {
    switch (variant) {
      case "primary": return "text-primary dark:text-primary/80";
      case "success": return "text-emerald-600 dark:text-emerald-400 dark:text-emerald-600/95";
      case "warning": return "text-amber-600 dark:text-amber-500/50";
      case "danger": return "text-red-600 dark:text-red-400";
      case "info": return "text-blue-600 dark:text-blue-500/70";
      default: return "text-green-600 dark:text-green-500/90";
    }
  };
  
  // Get icon background color
  const getIconBgColor = () => {
    switch (variant) {
      case "primary": return "bg-primary/10 dark:bg-primary/20";
      case "success": return "bg-emerald-500/10 dark:bg-emerald-500/20";
      case "warning": return "bg-amber-500/10 dark:bg-amber-500/20";
      case "danger": return "bg-red-500/10 dark:bg-red-500/20";
      case "info": return "bg-blue-500/10 dark:bg-blue-500/20";
      default: return "bg-green-500/10 dark:bg-green-500/20";
    }
  };

  // Get shimmer gradient for loading state
  const getShimmerGradient = () => {
    return mounted && theme === "dark" 
      ? "bg-gradient-to-r from-green-500/5 via-green-500/10 to-green-100/0"
      : "bg-gradient-to-r from-gray-100 via-gray-200 to-gray-100";
  };

  return (
    <Card className={cn(cardVariants({ variant }), className, "shadow-sm hover:shadow-md")}>
      {/* Top gradient accent */}
      <div className={cn(
        "absolute top-0 left-0 right-0 h-[3px] opacity-0 group-hover:opacity-100 transition-all duration-500",
        getAccentColor()
      )} />
      
      {/* Background pattern */}
      <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#1e293b_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.1]" />
      
      {/* Subtle gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/40 via-transparent to-green-100/20 dark:from-slate-900/40 dark:via-transparent dark:to-green-900/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      
      {/* Decorative corner accent */}
      <div className={cn(
        "absolute -right-12 -top-12 w-24 h-24 rotate-45 transform transition-transform duration-500 ease-out group-hover:scale-125 group-hover:rotate-[60deg] opacity-20 group-hover:opacity-30",
        getAccentColor()
      )} />
      
      <CardHeader className="flex flex-row items-center justify-between space-y-0 p-3 pb-0 relative z-10">
        <CardTitle className="text-xs font-medium transition-colors duration-300">
          {loading ? (
            <div className={cn("h-3 w-24 rounded animate-pulse", getShimmerGradient())} />
          ) : (
            title
          )}
        </CardTitle>
        
        {loading ? (
          <div className={cn("h-7 w-7 rounded-full animate-pulse", getShimmerGradient())} />
        ) : (
          <div className={cn(
            "rounded-full p-1.5 flex items-center justify-center transition-all duration-300 ease-in-out hover:scale-110 shadow-sm",
            getIconBgColor()
          )}>
            {Icon ? (
              <Icon className={cn("h-3.5 w-3.5 transition-colors duration-300", getTextColor())} />
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                className={cn("h-3.5 w-3.5 transition-colors duration-300", getTextColor())}
              >
                <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
              </svg>
            )}
          </div>
        )}
      </CardHeader>
      
      <CardContent className="relative z-10 p-3 pt-2 flex flex-col h-[100px]">
        {loading ? (
          <>
            <div className={cn("h-8 w-20 rounded animate-pulse mb-auto", getShimmerGradient())} />
            <div className="text-xs flex justify-between mt-auto pt-2 border-t border-green-100/50 dark:border-green-900/20">
              <div className={cn("h-10 w-16 rounded animate-pulse", getShimmerGradient())} />
              <div className={cn("h-10 w-16 rounded animate-pulse", getShimmerGradient())} />
            </div>
          </>
        ) : (
          <>
            <div className={cn("text-2xl font-bold tracking-tight flex items-baseline", getTextColor())}>
              {valuePrefix && <span className="text-sm mr-0.5 opacity-70">{valuePrefix}</span>}
              {value || ""}
              {valueSuffix && <span className="text-sm ml-0.5 opacity-70">{valueSuffix}</span>}
            </div>
            
            {subValues ? (
              <div className="text-xs flex justify-between mt-auto pt-2 border-t border-green-100/50 dark:border-green-900/20">
                <div className={cn(
                  "flex flex-col items-center rounded px-2 py-1 transition-all duration-300 ease-in-out shadow-sm",
                  "bg-green-50/80 dark:bg-green-900/20 hover:bg-green-50 dark:hover:bg-green-900/30"
                )}>
                  <span className={cn("font-bold transition-colors duration-300", getTextColor())}>{subValues.open || 0}</span>
                  <span className="text-[10px] text-slate-600 dark:text-slate-400 transition-colors duration-300">
                    {subValues.label1 || "Open"}
                  </span>
                </div>
                <div className={cn(
                  "flex flex-col items-center rounded px-2 py-1 transition-all duration-300 ease-in-out shadow-sm",
                  "bg-green-50/80 dark:bg-green-900/20 hover:bg-green-50 dark:hover:bg-green-900/30"
                )}>
                  <span className={cn("font-bold transition-colors duration-300", getTextColor())}>{subValues.paid || 0}</span>
                  <span className="text-[10px] text-slate-600 dark:text-slate-400 transition-colors duration-300">
                    {subValues.label2 || "Paid"}
                  </span>
                </div>
              </div>
            ) : (
              <div className="mt-auto"></div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};