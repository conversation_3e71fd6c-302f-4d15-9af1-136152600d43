"use client";
import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardDescription,
} from "../../ui/card";
import { VehicleSummery } from "../summeries/vehicle-summery";
import { ShipmentSummery } from "../summeries/shipment-summery";
import {
  BusFront,
  Ship,
  DollarSign,
  Package,
  Calculator,
  BarChart3,
} from "lucide-react";
import Link from "next/link";
import { StatCard } from "../stat-card";
import { useSession } from "next-auth/react";

import { useTranslations } from "next-intl";
import { DonutChart } from "../charts/donut-chart";
import { useDashboard } from "@/components/dashboard/dashboard-services";
import {
  DonutChartSkeleton,
  SummarySkeleton,
} from "@/components/dashboard/skeletons/dashboard-skeletons";
import { ShippingCalculator } from "@/components/calculator/shipping-calculator";

interface CardData {
  title: string;
  icon?: React.ComponentType<any>;
  value: number | string | undefined;
  url: string;
  subValues?: { open?: number; paid?: number };
  variant?: "default" | "primary" | "success" | "warning" | "danger" | "info";
}

const OverviewTab = React.memo(() => {
  const t = useTranslations("dashboard");
  const { data: session } = useSession();
  const { data, isLoading } = useDashboard();

  const showMixTabs = session?.profile?.mix_shipping_status;

  const showFullTabs = session?.profile?.has_invoices;

  const cardData: CardData[] = [
    {
      title: t("overview.cards.vehicles"),
      icon: BusFront,
      value: data?.totalCount?.vehicle,
      url: "/en/vehicles/all",
      variant: "primary",
    },
    {
      title: t("overview.cards.shipments"),
      icon: Ship,
      value: data?.totalCount?.shipment,
      url: "/en/shipments/all",
      variant: "info",
    },
    {
      title: t("overview.cards.invoices"),
      icon: DollarSign,
      value:
        data?.totalCount?.invoice?.invoice_open ||
        data?.totalCount?.invoice?.invoice_paid
          ? Number(data?.totalCount?.invoice?.invoice_open) +
            Number(data?.totalCount?.invoice?.invoice_paid)
          : "",
      subValues: {
        open: data?.totalCount?.invoice?.invoice_open,
        paid: data?.totalCount?.invoice?.invoice_paid,
      },
      url: "/en/invoices/all",
      variant: "success",
    },
    {
      title: t("overview.cards.mix-shipping"),
      icon: Package,
      value:
        data?.totalCount?.mix_shipping?.mix_shipping_open ||
        data?.totalCount?.mix_shipping?.mix_shipping_paid
          ? Number(data?.totalCount?.mix_shipping.mix_shipping_open) +
            Number(data?.totalCount?.mix_shipping.mix_shipping_paid)
          : "",
      subValues: {
        open: data?.totalCount?.mix_shipping?.mix_shipping_open,
        paid: data?.totalCount?.mix_shipping?.mix_shipping_paid,
      },
      url: "/en/mix-shipping/all",
      variant: "warning",
    },
  ];
  const filteredCardData = showMixTabs
    ? cardData
    : cardData.filter(
        (card) => card.title !== t("overview.cards.mix-shipping")
      );

  const allInvoicesChartData = data?.data?.payment
    ? [
        {
          name: t("overview.invoice-chart.open"),
          value: data?.data?.payment?.all || 0,
          color: "hsl(var(--chart-1))",
        },
        {
          name: t("overview.invoice-chart.paid"),
          value: data?.data?.payment?.paid || 0,
          color: "hsl(var(--chart-2))",
        },
        {
          name: t("overview.invoice-chart.due"),
          value: data?.data?.payment?.due || 0,
          color: "hsl(var(--chart-3))",
        },
      ]
    : [
        {
          name: t("overview.invoice-chart.all"),
          value: 0,
          color: "hsl(var(--chart-1))",
        },
        {
          name: t("overview.invoice-chart.paid"),
          value: 0,
          color: "hsl(var(--chart-2))",
        },
        {
          name: t("overview.invoice-chart.due"),
          value: 0,
          color: "hsl(var(--chart-3))",
        },
      ];

  const mixInvoiceChartData = data?.data?.payment
    ? [
        {
          name: t("overview.mix-invoice-chart.open"),
          value: data?.data?.payment?.mixInvoiceTotal || 0,
          color: "hsl(var(--chart-1))",
        },
        {
          name: t("overview.mix-invoice-chart.paid"),
          value: data?.data?.payment?.mixInvoicePaid || 0,
          color: "hsl(var(--chart-2))",
        },
        {
          name: t("overview.mix-invoice-chart.due"),
          value: data?.data?.payment?.mixInvoiceDue || 0,
          color: "hsl(var(--chart-3))",
        },
      ]
    : [];
  const fullContainerInvoice = data?.data?.payment
    ? [
        {
          name: t("overview.full-open-container-invoice-chart.open"),
          value: data?.data?.payment?.fullInvoiceAmount || 0,
          color: "hsl(var(--chart-1))",
        },
        {
          name: t("overview.full-open-container-invoice-chart.paid"),
          value: data?.data?.payment?.fullInvoicePaid || 0,
          color: "hsl(var(--chart-2))",
        },
        {
          name: t("overview.full-open-container-invoice-chart.due"),
          value:
            (data?.data?.payment?.fullInvoiceAmount || 0) -
            (data?.data?.payment?.fullInvoicePaid || 0),
          color: "hsl(var(--chart-3))",
        },
      ]
    : [
        {
          name: t("overview.full-open-container-invoice-chart.all"),
          value: 0,
          color: "hsl(var(--chart-1))",
        },
        {
          name: t("overview.full-open-container-invoice-chart.paid"),
          value: 0,
          color: "hsl(var(--chart-2))",
        },
        {
          name: t("overview.full-open-container-invoice-chart.due"),
          value: 0,
          color: "hsl(var(--chart-3))",
        },
      ];

  return (
    <div className="container mx-auto">
      <div className="flex flex-col gap-4">
        {session?.profile?.companies?.mix ||
        session?.profile?.companies?.mix_halfcut ? (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-4">
            {/* Shipping Calculator Section */}
            <Card className="relative shadow-[0_10px_20px_-5px_rgba(0,0,0,0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] dark:shadow-md dark:hover:shadow-lg group transition-all duration-500 border-t-3 border-green-500/80 dark:border-green-700/20 overflow-hidden dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border hover:dark:border-green-700/40">
              {/* Softened gradient overlay on hover - light mode only */}
              <div className="absolute inset-0 bg-gradient-to-br from-green-50/30 via-green-50/10 to-transparent opacity-0 group-hover:opacity-50 dark:group-hover:opacity-0 transition-opacity duration-500"></div>

              {/* Dark mode subtle gradient - always visible */}
              <div className="absolute inset-0 bg-gradient-to-br from-green-950/3 via-transparent to-transparent opacity-0 dark:from-green-800/30 dark:opacity-10 transition-opacity duration-500"></div>

              {/* Softened card glow effect on hover */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-green-300/0 via-green-300/10 to-green-300/0 rounded-lg blur opacity-0 group-hover:opacity-100 dark:from-green-700/0 dark:via-green-700/20 dark:to-green-700/0 dark:group-hover:opacity-50 transition duration-500 group-hover:duration-200"></div>

              {/* Background pattern */}
              <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.05] dark:group-hover:opacity-[0.08]"></div>

              {/* Card content */}
              <div className="relative">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="text-green-700 group-hover:text-green-800 dark:text-white dark:group-hover:text-white transition-colors duration-300 font-bold">
                      {t("overview.shipping-calculator.title") ||
                        "Shipping Calculator"}
                    </span>
                    <div className="p-2 rounded-full bg-green-100 dark:bg-green-800/30 group-hover:bg-green-200 dark:group-hover:bg-green-800/50 transition-colors duration-300 shadow-sm">
                      <Calculator className="h-5 w-5 text-green-600 dark:text-white" />
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <ShippingCalculator className="w-full !min-h-0 !p-0 overflow-visible" />
                </CardContent>
              </div>
            </Card>

            {/* Business Overview Section */}
            <Card className="relative shadow-[0_10px_20px_-5px_rgba(0,0,0,0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] dark:shadow-md dark:hover:shadow-lg group transition-all duration-500 border-t-3 border-blue-500/80 dark:border-green-700/20 overflow-hidden dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border hover:dark:border-green-500/20">
              {/* Softened gradient overlay on hover - light mode only */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-blue-50/10 to-transparent opacity-0 group-hover:opacity-50 dark:group-hover:opacity-0 transition-opacity duration-500"></div>

              {/* Dark mode subtle gradient - always visible */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-950/3 via-transparent to-transparent opacity-0 dark:from-green-800/30 dark:opacity-10 transition-opacity duration-500"></div>

              {/* Softened card glow effect on hover */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-300/0 via-blue-300/10 to-blue-300/0 dark:from-green-300/0 dark:via-green-300/5 dark:to-green-300/0 rounded-lg blur opacity-0 group-hover:opacity-50 dark:group-hover:opacity-30 transition duration-500 group-hover:duration-200"></div>

              {/* Background pattern */}
              <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.05]"></div>

              <div className="relative">
                <CardHeader className="">
                  <CardTitle className="flex items-center justify-between">
                    <span className="text-blue-700 group-hover:text-blue-700 dark:text-white dark:group-hover:text-white transition-colors duration-300 font-bold">
                      {t("overview.business-overview.title") ||
                        "Business Overview"}
                    </span>
                    <div className="p-2 rounded-full bg-blue-100 dark:bg-green-800/30 group-hover:bg-blue-200 dark:group-hover:bg-green-800/50 transition-colors duration-300 shadow-sm">
                      <BarChart3 className="h-5 w-5 text-blue-600 dark:text-white" />
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div
                    className={`grid gap-3 md:grid-cols-2 ${
                      showMixTabs ? "lg:grid-cols-2" : "lg:grid-cols-2"
                    }`}
                  >
                    {filteredCardData.map((card, index) => (
                      <Link key={index} href={card.url || "#"} passHref>
                        <StatCard
                          title={card.title}
                          icon={card.icon}
                          value={card.value}
                          subValues={card.subValues}
                          variant={card.variant}
                          loading={isLoading}
                        />
                      </Link>
                    ))}
                  </div>
                </CardContent>
              </div>
            </Card>
          </div>
        ) : (
          <div
            className={`grid gap-4 md:grid-cols-2 ${
              showMixTabs ? "lg:grid-cols-4" : "lg:grid-cols-3"
            }`}
          >
            {filteredCardData.map((card, idx) => (
              <Link key={idx} href={card.url || "#"} passHref>
                <StatCard
                  title={card.title}
                  icon={card.icon}
                  value={card.value}
                  subValues={card.subValues}
                  variant={card.variant}
                  loading={isLoading}
                />
              </Link>
            ))}
          </div>
        )}

        {/* Vehicle Summary Section */}
        <div className="grid grid-cols-1 gap-6">
          {isLoading ? (
            <SummarySkeleton variant="blue" />
          ) : (
            <Card className="relative shadow-[0_10px_20px_-5px_rgba(0,0,0,0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] dark:shadow-md dark:hover:shadow-lg group transition-all duration-500 border-t-3 border-blue-500/80 dark:border-green-700/20 overflow-hidden dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border hover:dark:border-green-500/20">
              {/* Softened gradient overlay on hover - light mode only */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-blue-50/10 to-transparent opacity-0 group-hover:opacity-50 dark:group-hover:opacity-0 transition-opacity duration-500"></div>

              {/* Dark mode subtle gradient - always visible */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-950/3 via-transparent to-transparent opacity-0 dark:from-green-800/30 dark:opacity-10 transition-opacity duration-500"></div>

              {/* Softened card glow effect on hover */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-300/0 via-blue-300/10 to-blue-300/0 dark:from-green-300/0 dark:via-green-300/5 dark:to-green-300/0 rounded-lg blur opacity-0 group-hover:opacity-50 dark:group-hover:opacity-30 transition duration-500 group-hover:duration-200"></div>

              {/* Background pattern */}
              <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.05]"></div>

              <div className="relative">
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center justify-between">
                    <span className="text-blue-700 group-hover:text-blue-700 dark:text-white dark:group-hover:text-white transition-colors duration-300 font-bold">
                      {t("overview.summaries.vehicle.title")}
                    </span>
                    <div className="p-2 rounded-full bg-blue-100 dark:bg-green-800/30 group-hover:bg-blue-200 dark:group-hover:bg-green-800/50 transition-colors duration-300 shadow-sm">
                      <BusFront className="h-5 w-5 text-blue-600 dark:text-white" />
                    </div>
                  </CardTitle>
                  <CardDescription className="text-slate-600 group-hover:text-slate-800 dark:text-white/85 dark:group-hover:text-white transition-colors duration-300">
                    {t("overview.summaries.vehicle.description")}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <VehicleSummery />
                </CardContent>
              </div>
            </Card>
          )}
          {/* Shipment Summary Section */}
          {isLoading ? (
            <SummarySkeleton variant="indigo" />
          ) : (
            <Card className="relative shadow-[0_10px_20px_-5px_rgba(0,0,0,0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] dark:shadow-md dark:hover:shadow-lg group transition-all duration-500 border-t-3 border-indigo-500/80 dark:border-green-700/20 overflow-hidden dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border hover:dark:border-green-500/20">
              {/* Softened gradient overlay on hover - light mode only */}
              <div className="absolute inset-0 bg-gradient-to-br from-indigo-50/30 via-indigo-50/10 to-transparent opacity-0 group-hover:opacity-100 dark:group-hover:opacity-0 transition-opacity duration-500"></div>

              {/* Dark mode subtle gradient - always visible */}
              <div className="absolute inset-0 bg-gradient-to-br from-indigo-950/3 via-transparent to-transparent opacity-0 dark:from-green-800/30 dark:opacity-10 transition-opacity duration-500"></div>

              {/* Softened card glow effect on hover */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-indigo-300/0 via-indigo-300/10 to-indigo-300/0 dark:from-green-300/0 dark:via-green-300/5 dark:to-green-300/0 rounded-lg blur opacity-0 group-hover:opacity-100 dark:group-hover:opacity-30 transition duration-500 group-hover:duration-200"></div>

              {/* Background pattern */}
              <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.05]"></div>

              <div className="relative">
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center justify-between">
                    <span className="text-indigo-700 group-hover:text-indigo-700 dark:text-white dark:group-hover:text-white transition-colors duration-300 font-bold">
                      {t("overview.summaries.shipment.title")}
                    </span>
                    <div className="p-2 rounded-full bg-indigo-100 dark:bg-green-800/30 group-hover:bg-indigo-200 dark:group-hover:bg-green-800/70 transition-colors duration-300 shadow-sm">
                      <Ship className="h-5 w-5 text-indigo-600 dark:text-white" />
                    </div>
                  </CardTitle>
                  <CardDescription className="text-slate-600 group-hover:text-slate-800 dark:text-white/85 dark:group-hover:text-white transition-colors duration-300">
                    {t("overview.summaries.shipment.description")}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ShipmentSummery />
                </CardContent>
              </div>
            </Card>
          )}
        </div>

        {/* Invoice Status Section */}
        <div
          className={`grid gap-5 grid-cols-1 ${
            showMixTabs ? "lg:grid-cols-3" : "lg:grid-cols-2"
          }`}
        >
          {/* First Invoice Card */}
          {showFullTabs ? (
            isLoading ? (
              <DonutChartSkeleton variant="green" />
            ) : (
              <Card className="relative grid shadow-[0_10px_20px_-5px_rgba(0,0,0,0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] dark:shadow-md dark:hover:shadow-lg group transition-all duration-500 border-t-3 border-green-500/80 overflow-hidden dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border dark:border-green-700/20 hover:dark:border-green-700/50">
                {/* Softened gradient overlay on hover - both light and dark mode */}
                <div className="absolute inset-0 bg-gradient-to-br from-green-50/30 via-green-50/10 to-transparent opacity-0 group-hover:opacity-100 dark:from-green-800/40 dark:via-green-900/30 dark:to-transparent dark:group-hover:opacity-30 transition-opacity duration-500"></div>

                {/* Dark mode subtle gradient - always visible */}
                <div className="absolute inset-0 bg-gradient-to-br from-green-950/3 via-transparent to-transparent opacity-0 dark:from-green-800/30 dark:opacity-30 transition-opacity duration-500"></div>

                {/* Softened card glow effect on hover */}
                <div className="absolute -inset-0.5 bg-gradient-to-r from-green-300/0 via-green-300/10 to-green-300/0 rounded-lg blur opacity-0 group-hover:opacity-100 dark:from-green-700/0 dark:via-green-700/20 dark:to-green-700/0 dark:group-hover:opacity-50 transition duration-500 group-hover:duration-200"></div>

                {/* Background pattern */}
                <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.05] dark:group-hover:opacity-[0.08] transition-opacity duration-500"></div>

                <div className="relative">
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center gap-2">
                      <span className="text-green-700 group-hover:text-green-800 dark:text-white dark:group-hover:text-white transition-colors duration-300 font-bold">
                        {t("overview.invoice-status.title")}
                      </span>
                    </CardTitle>
                    <CardDescription className="text-slate-600 group-hover:text-slate-800 dark:text-white/85 dark:group-hover:text-white transition-colors duration-300">
                      {t("overview.invoice-status.description")}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <DonutChart
                      data={allInvoicesChartData}
                      title=""
                      description=""
                      totalLabel="Invoices"
                      open={`${Math.round(data?.data?.payment?.all || 0)}`}
                    />
                    <div className="mt-4 grid grid-cols-1 gap-2">
                      <div className="flex items-center justify-center flex-col bg-red-50/90 dark:bg-red-500/5 p-3 rounded-lg border border-red-100 dark:border-red-500/10 group-hover:shadow-sm dark:group-hover:bg-red-500/10 dark:group-hover:border-red-500/20 transition-all duration-300">
                        <div className="text-xs text-slate-700 dark:text-white dark:group-hover:text-white">
                          {t("overview.invoice-status.due")}
                        </div>
                        <div className="text-xl font-bold text-red-700 dark:text-red-400 dark:group-hover:text-red-300 flex items-center gap-1 transition-colors duration-300">
                          <span className="text-sm">$</span>
                          {Math.round(
                            data?.data?.payment?.due || 0
                          ).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>
            )
          ) : null}

          {/* Second Invoice Card */}
          {showMixTabs ? (
            isLoading ? (
              <DonutChartSkeleton variant="amber" />
            ) : (
              <Card className="relative shadow-[0_10px_20px_-5px_rgba(0,0,0,0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] dark:shadow-md dark:hover:shadow-lg group transition-all duration-500 border-t-3 border-amber-500/80 dark:border-green-700/20 overflow-hidden dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border hover:dark:border-green-700/50">
                {/* Softened gradient overlay on hover - both light and dark mode */}
                <div className="absolute inset-0 bg-gradient-to-br from-amber-50/30 via-amber-50/10 to-transparent opacity-0 group-hover:opacity-100 dark:from-green-800/40 dark:via-green-900/30 dark:to-transparent dark:group-hover:opacity-20 transition-opacity duration-500"></div>

                {/* Dark mode subtle gradient - always visible */}
                <div className="absolute inset-0 bg-gradient-to-br from-amber-950/3 via-transparent to-transparent opacity-0 dark:from-green-800/30 dark:opacity-30 transition-opacity duration-500"></div>

                {/* Softened card glow effect on hover */}
                <div className="absolute -inset-0.5 bg-gradient-to-r from-amber-300/0 via-amber-300/10 to-amber-300/0 rounded-lg blur opacity-0 group-hover:opacity-100 dark:from-green-700/0 dark:via-green-700/20 dark:to-green-700/0 dark:group-hover:opacity-50 transition duration-500 group-hover:duration-200"></div>

                {/* Background pattern */}
                <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.05] dark:group-hover:opacity-[0.08] transition-opacity duration-500"></div>

                <div className="relative">
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center justify-between">
                      <span className="text-amber-700 group-hover:text-amber-800 dark:text-white dark:group-hover:text-white transition-colors duration-300 font-bold">
                        {t("overview.mix-invoice-status.title")}
                      </span>
                    </CardTitle>
                    <CardDescription className="text-slate-600 group-hover:text-slate-800 dark:text-white/85 dark:group-hover:text-white transition-colors duration-300">
                      {t("overview.mix-invoice-status.description")}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <DonutChart
                      data={mixInvoiceChartData}
                      title=""
                      description=""
                      totalLabel="Mix Invoices"
                      open={`${Math.round(
                        data?.data?.payment?.mixInvoiceTotal
                      )}`}
                      key={"mix-invoice"}
                    />
                    <div className="mt-4 grid grid-cols-1 gap-2">
                      <div className="flex justify-center items-center flex-col bg-red-50/90 dark:bg-red-500/5 p-3 rounded-lg border border-red-100 dark:border-red-500/10 group-hover:shadow-sm dark:group-hover:bg-red-500/10 dark:group-hover:border-red-500/20 transition-all duration-300">
                        <div className="text-xs text-slate-700 dark:text-white dark:group-hover:text-white">
                          {t("overview.invoice-status.due")}
                        </div>
                        <div className="text-xl font-bold text-red-700 dark:text-red-400 dark:group-hover:text-red-300 flex items-center gap-1 transition-colors duration-300">
                          <span className="text-sm">$</span>
                          {Math.round(
                            data?.data?.payment?.mixInvoiceDue || 0
                          ).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>
            )
          ) : null}

          {/* Third Invoice Card */}
          {showFullTabs ? (
            isLoading ? (
              <DonutChartSkeleton variant="purple" />
            ) : (
              <Card className="relative shadow-[0_10px_20px_-5px_rgba(90, 81, 81, 0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] dark:shadow-md dark:hover:shadow-lg group transition-all duration-500 border-t-3 border-purple-500/80 dark:border-green-700/20 overflow-hidden dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border hover:dark:border-green-700/50">
                {/* Softened gradient overlay on hover - both light and dark mode */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-50/30 via-purple-50/10 to-transparent opacity-0 group-hover:opacity-100 dark:from-green-800/40 dark:via-green-900/30 dark:to-transparent dark:group-hover:opacity-50 transition-opacity duration-500"></div>

                {/* Dark mode subtle gradient - always visible */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-950/3 via-transparent to-transparent opacity-0 dark:from-green-800/30 dark:opacity-30 transition-opacity duration-500"></div>

                {/* Softened card glow effect on hover */}
                <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-300/0 via-purple-300/10 to-purple-300/0 rounded-lg blur opacity-0 group-hover:opacity-100 dark:from-green-700/0 dark:via-green-700/20 dark:to-green-700/0 dark:group-hover:opacity-50 transition duration-500 group-hover:duration-200"></div>

                {/* Background pattern */}
                <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.05] dark:group-hover:opacity-[0.08] transition-opacity duration-500"></div>

                <div className="relative">
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center gap-2">
                      <span className="text-purple-700 group-hover:text-purple-800 dark:text-white dark:group-hover:text-white transition-colors duration-300 font-bold">
                        {t("overview.container-invoice-status.title")}
                      </span>
                    </CardTitle>
                    <CardDescription className="text-slate-600 group-hover:text-slate-800 dark:text-white/85 dark:group-hover:text-white transition-colors duration-300">
                      {t("overview.container-invoice-status.description")}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <DonutChart
                      data={fullContainerInvoice}
                      title=""
                      description=""
                      totalLabel="Container Invoices"
                      open={`${Math.round(
                        data?.data?.payment?.fullInvoiceAmount
                      )}`}
                      key={"full-invoice"}
                    />
                    <div className="mt-4 grid grid-cols-1 gap-2">
                      <div className="flex items-center justify-center flex-col bg-red-50/90 dark:bg-red-500/5 p-3 rounded-lg border border-red-100 dark:border-red-500/10 group-hover:shadow-sm dark:group-hover:bg-red-500/10 dark:group-hover:border-red-500/20 transition-all duration-300">
                        <div className="text-xs text-slate-700 dark:text-white dark:group-hover:text-white">
                          {t("overview.invoice-status.due")}
                        </div>
                        <div className="text-xl font-bold text-red-700 dark:text-red-400 dark:group-hover:text-red-300 flex items-center gap-1 transition-colors duration-300">
                          <span className="text-sm">$</span>
                          {Math.round(
                            data?.data?.payment?.fullInvoiceAmount -
                              data?.data?.payment?.fullInvoicePaid || 0
                          ).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>
            )
          ) : null}
        </div>
      </div>
    </div>
  );
});

OverviewTab.displayName = "OverviewTab";

export default OverviewTab;
