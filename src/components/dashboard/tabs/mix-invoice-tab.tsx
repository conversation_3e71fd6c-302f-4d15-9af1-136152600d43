"use client";

import React from "react";
import { TrendingDown, TrendingUp } from "lucide-react";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { FullStatCard } from "../stat-card-full";
import { DonutChart } from "../charts/donut-chart";
import { ProgressCard } from "../charts/progress-card";
import { InvoiceBarChart } from "../charts/invoice-bar-chart";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@/components/ui/card";
import { useDashboard } from "@/components/dashboard/dashboard-services";
import {
  StatCardSkeleton,
  ProgressCardSkeleton,
  DonutChartSkeleton,
  BarChartSkeleton,
} from "@/components/dashboard/skeletons/dashboard-skeletons";

// Mock data for the bar chart - replace with real data when available
const mockMonthlyData: any = [
  { name: "Jan", paid: 3200, due: 1800 },
  { name: "Feb", paid: 2800, due: 1200 },
  { name: "<PERSON>", paid: 1900, due: 2700 },
  { name: "Apr", paid: 2200, due: 1500 },
  { name: "May", paid: 2600, due: 1900 },
  { name: "<PERSON>", paid: 3100, due: 1400 },
];

interface CardData {
  title: string;
  icon: React.ElementType;
  value: number | string | undefined;
  url: string;
  subValues?: any;
}

// Custom Dirham icon component
const DirhamIcon = (props: any) => (
  <div
    className="flex items-center justify-center text-current p-0 m-0"
    {...props}
  >
    <span className="font-bold p-0 m-0 text-sm">د.إ</span>
  </div>
);

const MixInvoiceTab = () => {
  const t = useTranslations("dashboard");
  const { data, isLoading } = useDashboard();

  const mixCardData: CardData[] = [
    {
      title: t("overview.cards.mix-shipping"),
      icon: DirhamIcon,
      value:
        data?.totalCount?.mix_shipping?.mix_shipping_open ||
        data?.totalCount?.mix_shipping?.mix_shipping_paid
          ? data?.totalCount?.mix_shipping.mix_shipping_open +
            data?.totalCount?.mix_shipping.mix_shipping_paid
          : "",
      subValues: {
        open: data?.totalCount?.mix_shipping?.mix_shipping_open,
        paid: data?.totalCount?.mix_shipping?.mix_shipping_paid,
      },
      url: "/en/mix-shipping/all",
    },
  ];

  const mixInvoiceChartData = data?.data?.payment
    ? [
        {
          name: t("overview.mix-invoice-chart.open"),
          value: data?.data?.payment?.mixInvoiceTotal || 0,
          color: "hsl(var(--chart-1))",
        },
        {
          name: t("overview.mix-invoice-chart.paid"),
          value: data?.data?.payment?.mixInvoicePaid || 0,
          color: "hsl(var(--chart-2))",
        },
        {
          name: t("overview.mix-invoice-chart.due"),
          value:
            data?.data?.payment?.mixInvoiceTotal -
              data?.data?.payment?.mixInvoicePaid || 0,
          color: "hsl(var(--chart-3))",
        },
      ]
    : [
        {
          name: t("overview.mix-invoice-chart.all"),
          value: 0,
          color: "hsl(var(--chart-1))",
        },
        {
          name: t("overview.mix-invoice-chart.paid"),
          value: 0,
          color: "hsl(var(--chart-2))",
        },
        {
          name: t("overview.mix-invoice-chart.due"),
          value: 0,
          color: "hsl(var(--chart-3))",
        },
      ];

  // Calculate payment completion percentage
  const totalMixInvoiceAmount = data?.data?.payment
    ? data?.data?.payment?.mixInvoiceTotal
    : 0;
  const paidMixPayments = data?.data?.payment
    ? data?.data?.payment?.mixInvoicePaid
    : 0;
  const mixPaymentPercentage =
    totalMixInvoiceAmount > 0
      ? (paidMixPayments / totalMixInvoiceAmount) * 100
      : 0;
  const isMixPositiveTrend = mixPaymentPercentage > 50;

  return (
    <div className="flex flex-col gap-4">
      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-1">
        {isLoading ? (
          <StatCardSkeleton variant="warning" />
        ) : (
          mixCardData.map((card, index) => (
            <Link key={index} href={card.url || "#"} passHref>
              <div className="relative group">
                {/* Hover glow effect for dark mode */}
                <div className="absolute -inset-0.5 bg-gradient-to-r from-amber-700/0 via-amber-700/20 to-amber-700/0 rounded-lg blur opacity-0 group-hover:opacity-30 dark:group-hover:opacity-30 transition duration-500 group-hover:duration-200"></div>

                <FullStatCard
                  title={card.title}
                  icon={card.icon}
                  value={card.value}
                  subValues={card.subValues}
                  className="transition-all duration-300 dark:hover:border-green-500/20 group-hover:shadow-md dark:group-hover:shadow-lg relative z-10"
                />
              </div>
            </Link>
          ))
        )}
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Progress Card */}
        {isLoading ? (
          <ProgressCardSkeleton variant="warning" />
        ) : (
          <Card className="relative shadow-[0_10px_20px_-5px_rgba(0,0,0,0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] dark:shadow-md dark:hover:shadow-lg group transition-all duration-500 border-t-3 border-amber-400 overflow-hidden  dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border dark:border-green-800/30 hover:dark:border-green-700/50">
            {/* Gradient overlays */}
            <div className="absolute inset-0 bg-gradient-to-br from-amber-50/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 dark:from-green-800/40 dark:via-green-900/30 dark:to-transparent dark:group-hover:opacity-50 transition-opacity duration-500"></div>
            <div className="absolute inset-0 bg-gradient-to-br from-amber-950/10 via-transparent to-transparent opacity-0 dark:from-green-800/30 dark:opacity-30 transition-opacity duration-500"></div>
            <div className="absolute -inset-0.5 bg-gradient-to-r from-amber-300/0 via-amber-300/10 to-amber-300/0 rounded-lg blur opacity-0 group-hover:opacity-100 dark:from-green-700/0 dark:via-green-700/20 dark:to-green-700/0 dark:group-hover:opacity-50 transition duration-500 group-hover:duration-200"></div>
            <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.08]"></div>

            <div className="relative p-5 w-full h-full">
              <ProgressCard
                title={t("mix-invoice-tab.progress-card.title")}
                description={t("mix-invoice-tab.progress-card.description")}
                value={paidMixPayments}
                max={totalMixInvoiceAmount}
                variant="warning"
                formatValue={(v) => v.toLocaleString()}
                formatMax={(m) => m.toLocaleString()}
                icon={
                  isMixPositiveTrend ? (
                    <TrendingUp className="h-4 w-4" />
                  ) : (
                    <TrendingDown className="h-4 w-4" />
                  )
                }
                className="h-full w-full"
              />
            </div>
          </Card>
        )}

        {/* Donut Chart */}
        {isLoading ? (
          <DonutChartSkeleton variant="amber" />
        ) : (
          <Card className="relative shadow-[0_10px_20px_-5px_rgba(0,0,0,0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] dark:shadow-md dark:hover:shadow-lg group transition-all duration-500 border-t-3 border-amber-500/80 dark:border-green-700/20 overflow-hidden dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border hover:dark:border-green-700/50">
            {/* Softened gradient overlay on hover - both light and dark mode */}
            <div className="absolute inset-0 bg-gradient-to-br from-amber-50/30 via-amber-50/10 to-transparent opacity-0 group-hover:opacity-100 dark:from-green-800/40 dark:via-green-900/30 dark:to-transparent dark:group-hover:opacity-20 transition-opacity duration-500"></div>

            {/* Dark mode subtle gradient - always visible */}
            <div className="absolute inset-0 bg-gradient-to-br from-amber-950/3 via-transparent to-transparent opacity-0 dark:from-green-800/30 dark:opacity-30 transition-opacity duration-500"></div>

            {/* Softened card glow effect on hover */}
            <div className="absolute -inset-0.5 bg-gradient-to-r from-amber-300/0 via-amber-300/10 to-amber-300/0 rounded-lg blur opacity-0 group-hover:opacity-100 dark:from-green-700/0 dark:via-green-700/20 dark:to-green-700/0 dark:group-hover:opacity-50 transition duration-500 group-hover:duration-200"></div>

            {/* Background pattern */}
            <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.05] dark:group-hover:opacity-[0.08] transition-opacity duration-500"></div>

            <div className="relative">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2">
                  <span className="text-amber-700 group-hover:text-amber-800 dark:text-white dark:group-hover:text-white transition-colors duration-300 font-bold">
                    {t("overview.mix-invoice-status.title")}
                  </span>
                  <span className="text-slate-700 text-sm font-normal dark:text-white/90 dark:group-hover:text-white transition-colors duration-300">
                    د.إ
                  </span>
                </CardTitle>
                <CardDescription className="text-slate-600 group-hover:text-slate-800 dark:text-white/85 dark:group-hover:text-white transition-colors duration-300">
                  {t("overview.mix-invoice-status.description")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <DonutChart
                  data={mixInvoiceChartData}
                  title=""
                  description=""
                  totalLabel="Mix Invoices"
                  open={`${Math.round(data?.data?.payment?.mixInvoiceTotal)}`}
                  key={"mix-invoice"}
                />
                <div className="mt-4 grid grid-cols-1 gap-2">
                  <div className="flex justify-center items-center flex-col bg-red-50/90 dark:bg-red-500/5 p-3 rounded-lg border border-red-100 dark:border-red-500/10 group-hover:shadow-sm dark:group-hover:bg-red-500/10 dark:group-hover:border-red-500/20 transition-all duration-300">
                    <div className="text-xs text-slate-700 dark:text-white dark:group-hover:text-white">
                      {t("overview.invoice-status.due")}
                    </div>
                    <div className="text-xl font-bold text-red-700 dark:text-white dark:group-hover:text-white">
                      {Math.round(
                        data?.data?.payment?.mixInvoiceTotal -
                          data?.data?.payment?.mixInvoicePaid || 0
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </div>
          </Card>
        )}

        {/* Bar Chart */}
        {isLoading ? (
          <BarChartSkeleton variant="purple" />
        ) : (
          <Card className="relative shadow-[0_10px_20px_-5px_rgba(0,0,0,0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] dark:shadow-md dark:hover:shadow-lg group transition-all duration-500 border-t-3 border-purple-400 overflow-hidden  dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border dark:border-green-800/30 hover:dark:border-green-700/50">
            {/* Gradient overlays */}
            <div className="absolute inset-0 bg-gradient-to-br from-purple-50/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 dark:from-green-800/40 dark:via-green-900/30 dark:to-transparent dark:group-hover:opacity-50 transition-opacity duration-500"></div>
            <div className="absolute inset-0 bg-gradient-to-br from-purple-950/10 via-transparent to-transparent opacity-0 dark:from-green-800/30 dark:opacity-30 transition-opacity duration-500"></div>
            <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-300/0 via-purple-300/10 to-purple-300/0 rounded-lg blur opacity-0 group-hover:opacity-100 dark:from-green-700/0 dark:via-green-700/20 dark:to-green-700/0 dark:group-hover:opacity-50 transition duration-500 group-hover:duration-200"></div>
            <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.08]"></div>

            <div className="relative">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center justify-between">
                  <span className="group-hover:text-purple-600 dark:text-purple-300 dark:group-hover:text-purple-200 transition-colors duration-300">
                    {t("mix-invoice-tab.bar-chart.title")}
                  </span>
                  <div className="p-2 rounded-full bg-purple-50 dark:bg-purple-950/50 group-hover:bg-purple-100 dark:group-hover:bg-purple-800/30 transition-colors duration-300 shadow-sm">
                    <TrendingUp className="h-5 w-5 text-purple-500 dark:text-purple-300 dark:group-hover:text-purple-200" />
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <InvoiceBarChart
                  data={mockMonthlyData}
                  title=""
                  description={t("mix-invoice-tab.bar-chart.description")}
                />
              </CardContent>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default MixInvoiceTab;
