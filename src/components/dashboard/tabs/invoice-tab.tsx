"use client";
import React from "react";
import { DollarSign, TrendingUp } from "lucide-react";
import Link from "next/link";
import { LucideProps } from "lucide-react";
import { ForwardRefExoticComponent, RefAttributes } from "react";
import { useTranslations } from "next-intl";
import { FullStatCard } from "../stat-card-full";
import { DonutChart } from "../charts/donut-chart";
import { InvoiceBarChart } from "../charts/invoice-bar-chart";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@/components/ui/card";
import { useDashboard } from "@/components/dashboard/dashboard-services";
import {
  DonutChartSkeleton,
  BarChartSkeleton,
  StatCardSkeleton,
} from "@/components/dashboard/skeletons/dashboard-skeletons";
import { useSession } from "next-auth/react";
import { format } from "date-fns";

interface CardData {
  title: string;
  icon: ForwardRefExoticComponent<
    Omit<LucideProps, "ref"> & RefAttributes<SVGSVGElement>
  >;
  value: number | string | undefined;
  url: string;
  subValues?: any;
}

const InvoiceTab = () => {
  const t = useTranslations("dashboard");
  const { data, isLoading } = useDashboard();
  const { data: session } = useSession();
  const showFullTabs = session?.profile?.has_invoices;
  const showMixTabs = session?.profile?.mix_shipping_status;
  const fullInvoiceData: any[] =
    data?.data?.fullInvoiceData?.map((item: any) => {
      return {
        name: format(new Date(item.month), "MMM"),
        total: Number(item.total_invoice_amount),
        payment:
          Number(item.total_invoice_amount) - Number(item.total_due_balance),
      };
    }) || [];

  const mixInvoiceData: any[] =
    data?.data?.mixInvoiceData?.map((item: any) => {
      return {
        name: format(new Date(item.month), "MMM"),
        total: Number(item.total_amount),
        payment: Number(item.total_amount) - Number(item.usd_due_balance),
      };
    }) || [];

  const cardData: CardData[] = [
    {
      title: t("overview.cards.invoices"),
      icon: DollarSign,
      value:
        data?.totalCount?.invoice?.invoice_open ||
          data?.totalCount?.invoice?.invoice_paid
          ? Number(data?.totalCount?.invoice?.invoice_open) +
          Number(data?.totalCount?.invoice?.invoice_paid)
          : "",
      subValues: {
        open: data?.totalCount?.invoice?.invoice_open,
        paid: data?.totalCount?.invoice?.invoice_paid,
      },
      url: "/en/invoices/all",
    },
  ];

  const fullContainerInvoice = data?.data?.payment
    ? [
      {
        name: t("overview.full-open-container-invoice-chart.open"),
        value: data?.data?.payment?.fullInvoiceAmount || 0,
        color: "hsl(var(--chart-1))",
      },
      {
        name: t("overview.full-open-container-invoice-chart.paid"),
        value: data?.data?.payment?.fullInvoicePaid || 0,
        color: "hsl(var(--chart-2))",
      },
      {
        name: t("overview.full-open-container-invoice-chart.due"),
        value:
          data?.data?.payment?.fullInvoiceAmount -
          data?.data?.payment?.fullInvoicePaid || 0,
        color: "hsl(var(--chart-3))",
      },
    ]
    : [
      {
        name: t("overview.full-open-container-invoice-chart.all"),
        value: 0,
        color: "hsl(var(--chart-1))",
      },
      {
        name: t("overview.full-open-container-invoice-chart.paid"),
        value: 0,
        color: "hsl(var(--chart-2))",
      },
      {
        name: t("overview.full-open-container-invoice-chart.due"),
        value: 0,
        color: "hsl(var(--chart-3))",
      },
    ];

  const mixInvoiceChartData = data?.data?.payment
    ? [
      {
        name: t("overview.mix-invoice-chart.open"),
        value: data?.data?.payment?.mixInvoiceTotal || 0,
        color: "hsl(var(--chart-1))",
      },
      {
        name: t("overview.mix-invoice-chart.paid"),
        value: data?.data?.payment?.mixInvoicePaid || 0,
        color: "hsl(var(--chart-2))",
      },
      {
        name: t("overview.mix-invoice-chart.due"),
        value:
          data?.data?.payment?.mixInvoiceDue || 0,
        color: "hsl(var(--chart-3))",
      },
    ]
    : [];

  return (
    <div className="flex flex-col gap-4">
      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-1">
        {isLoading ? (
          <StatCardSkeleton variant="success" />
        ) : (
          cardData.map((card, index) => (
            <Link key={index} href={card.url || "#"} passHref>
              <div className="relative group">
                {/* Hover glow effect for dark mode */}
                <div className="absolute -inset-0.5 bg-gradient-to-r from-green-700/0 via-green-700/20 to-green-700/0 rounded-lg blur opacity-0 group-hover:opacity-30 dark:group-hover:opacity-30 transition duration-500 group-hover:duration-200"></div>

                <FullStatCard
                  title={card.title}
                  icon={card.icon}
                  value={card.value}
                  subValues={card.subValues}
                  className="transition-all duration-300 dark:hover:border-green-700/20 group-hover:shadow-md dark:group-hover:shadow-lg relative z-10"
                />
              </div>
            </Link>
          ))
        )}
      </div>

      {/* Full Invoice Section */}
      {showFullTabs && (
        <div className="flex flex-col gap-4 mt-6 bg-gradient-to-r from-green-50/30 to-transparent p-4 rounded-lg dark:from-green-900/10 dark:to-transparent">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-1.5 h-8 bg-green-500 rounded-r mr-2 shadow-sm"></div>
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white flex items-center gap-2">
                {t("invoice-tab.full-invoice-section.title")}
              </h3>
            </div>
          </div>
          <div
            className={`grid ${fullInvoiceData?.length > 0
              ? "md:grid-cols-[auto_1fr] lg:grid-cols-2"
              : ""
              } w-full gap-4`}
          >
            {/* Donut Chart  */}
            {isLoading ? (
              <DonutChartSkeleton variant="green" />
            ) : (
              <Card className="relative  grid shadow-[0_10px_20px_-5px_rgba(0,0,0,0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] dark:shadow-md dark:hover:shadow-lg group transition-all duration-500 border-t-3 border-green-500/80 overflow-hidden dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border dark:border-green-700/20 hover:dark:border-green-700/50">
                {/* Softened gradient overlay on hover - both light and dark mode */}
                <div className="absolute inset-0 bg-gradient-to-br from-green-50/30 via-green-50/10 to-transparent opacity-0 group-hover:opacity-100 dark:from-green-800/40 dark:via-green-900/30 dark:to-transparent dark:group-hover:opacity-30 transition-opacity duration-500"></div>

                {/* Dark mode subtle gradient - always visible */}
                <div className="absolute inset-0 bg-gradient-to-br from-green-950/3 via-transparent to-transparent opacity-0 dark:from-green-800/30 dark:opacity-30 transition-opacity duration-500"></div>

                {/* Softened card glow effect on hover */}
                <div className="absolute -inset-0.5 bg-gradient-to-r from-green-300/0 via-green-300/10 to-green-300/0 rounded-lg blur opacity-0 group-hover:opacity-100 dark:from-green-700/0 dark:via-green-700/20 dark:to-green-700/0 dark:group-hover:opacity-50 transition duration-500 group-hover:duration-200"></div>

                {/* Background pattern */}
                <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.05] dark:group-hover:opacity-[0.08] transition-opacity duration-500"></div>

                <div className="relative">
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center justify-between">
                      <span className="text-green-700 group-hover:text-green-800 dark:text-white dark:group-hover:text-white transition-colors duration-300 font-bold">
                        {t("overview.container-invoice-status.title")}
                      </span>
                      <span className="text-slate-700 text-sm font-normal dark:text-white/90 dark:group-hover:text-white transition-colors duration-300">
                        $
                      </span>
                    </CardTitle>
                    <CardDescription className="text-slate-600 group-hover:text-slate-800 dark:text-white/85 dark:group-hover:text-white transition-colors duration-300">
                      {t("overview.container-invoice-status.description")}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <DonutChart
                      data={fullContainerInvoice}
                      title=""
                      description=""
                      totalLabel="Container Invoices"
                      open={`${Math.round(data?.data?.payment?.fullInvoiceAmount || 0)}`}
                    />
                    <div className="mt-4 grid grid-cols-1 gap-2">
                      <div className="flex items-center justify-center flex-col bg-red-50/90 dark:bg-red-500/5 p-3 rounded-lg border border-red-100 dark:border-red-500/10 group-hover:shadow-sm dark:group-hover:bg-red-500/10 dark:group-hover:border-red-500/20 transition-all duration-300">
                        <div className="text-xs text-slate-700 dark:text-white dark:group-hover:text-white">
                          {t("overview.invoice-status.due")}
                        </div>
                        <div className="text-xl font-bold text-red-700 dark:text-red-400 dark:group-hover:text-red-300 flex items-center gap-1 transition-colors duration-300">
                          <span className="text-sm">$</span>
                          {Math.round(
                            data?.data?.payment?.fullInvoiceAmount -
                            data?.data?.payment?.fullInvoicePaid || 0
                          ).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>
            )}

            {/* Bar Chart  */}
            {isLoading ? (
              <BarChartSkeleton variant="purple" />
            ) : (
              <>
                {fullInvoiceData?.length > 0 && (
                  <Card className="relative shadow-[0_10px_20px_-5px_rgba(0,0,0,0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] dark:shadow-md dark:hover:shadow-lg group transition-all duration-500 border-t-3 border-purple-500 dark:border-green-700/50 overflow-hidden  dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border hover:dark:border-green-700/40">
                    {/* Softened gradient overlay on hover - both light and dark mode */}
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-50/30 via-purple-50/10 to-transparent opacity-0 group-hover:opacity-100 dark:from-green-800/40 dark:via-green-900/30 dark:to-transparent dark:group-hover:opacity-50 transition-opacity duration-500"></div>

                    {/* Dark mode subtle gradient - always visible */}
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-950/5 via-transparent to-transparent opacity-0 dark:from-green-800/30 dark:opacity-30 transition-opacity duration-500"></div>

                    {/* Softened card glow effect on hover */}
                    <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-300/0 via-purple-300/15 to-purple-300/0 rounded-lg blur opacity-0 group-hover:opacity-100 dark:from-green-700/0 dark:via-green-700/20 dark:to-green-700/0 dark:group-hover:opacity-50 transition duration-500 group-hover:duration-200"></div>

                    {/* Background pattern */}
                    <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.08]"></div>

                    <div className="relative">
                      <CardHeader className="pb-2">
                        <CardTitle className="flex items-center justify-between">
                          <span className="text-purple-700 group-hover:text-purple-700 dark:text-white dark:group-hover:text-white transition-colors duration-300 font-bold">
                            {t("invoice-tab.bar-chart.title")}
                          </span>
                          <div className="p-2 rounded-full bg-purple-100 dark:bg-green-800/30 group-hover:bg-purple-200 dark:group-hover:bg-green-800/65 transition-colors duration-300 shadow-sm">
                            <TrendingUp className="h-5 w-5 text-purple-600 dark:text-white" />
                          </div>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <InvoiceBarChart
                          data={fullInvoiceData}
                          title=""
                          description={t("invoice-tab.bar-chart.description")}
                        />
                      </CardContent>
                    </div>
                  </Card>
                )}
              </>
            )}
          </div>
        </div>
      )}

      {/* Mix Invoice Section */}

      {showMixTabs && data?.data?.mix_invoice?.all > 0 && (
        <div className="flex flex-col gap-4 mt-10 pt-6 border-t border-slate-200 dark:border-slate-700/30 bg-gradient-to-r from-amber-50/30 to-transparent p-4 rounded-lg dark:from-amber-900/5 dark:to-transparent">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-1.5 h-8 bg-amber-500 dark:bg-amber-700 rounded-r mr-2 shadow-sm"></div>
              <h3 className="text-lg font-semibold text-slate-900 dark:text-white flex items-center gap-2">
                {t("invoice-tab.mix-invoice-section.title")}
              </h3>
            </div>
          </div>
          <div
            className={`grid ${mixInvoiceData?.length > 0
              ? "md:grid-cols-[auto_1fr] lg:grid-cols-2"
              : ""
              } w-full gap-4`}
          >
            {/* Donut Chart */}
            {isLoading ? (
              <DonutChartSkeleton variant="amber" />
            ) : (
              <Card className="relative shadow-[0_10px_20px_-5px_rgba(0,0,0,0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] dark:shadow-md dark:hover:shadow-lg group transition-all duration-500 border-t-3 border-amber-500/80 dark:border-green-700/20 overflow-hidden dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border hover:dark:border-green-700/50">
                {/* Softened gradient overlay on hover - both light and dark mode */}
                <div className="absolute inset-0 bg-gradient-to-br from-amber-50/30 via-amber-50/10 to-transparent opacity-0 group-hover:opacity-100 dark:from-green-800/40 dark:via-green-900/30 dark:to-transparent dark:group-hover:opacity-20 transition-opacity duration-500"></div>

                {/* Dark mode subtle gradient - always visible */}
                <div className="absolute inset-0 bg-gradient-to-br from-amber-950/3 via-transparent to-transparent opacity-0 dark:from-green-800/30 dark:opacity-30 transition-opacity duration-500"></div>

                {/* Softened card glow effect on hover */}
                <div className="absolute -inset-0.5 bg-gradient-to-r from-amber-300/0 via-amber-300/10 to-amber-300/0 rounded-lg blur opacity-0 group-hover:opacity-100 dark:from-green-700/0 dark:via-green-700/20 dark:to-green-700/0 dark:group-hover:opacity-50 transition duration-500 group-hover:duration-200"></div>

                {/* Background pattern */}
                <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.05] dark:group-hover:opacity-[0.08] transition-opacity duration-500"></div>

                <div className="relative">
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center justify-between">
                      <span className="text-amber-700 group-hover:text-amber-800 dark:text-white dark:group-hover:text-white transition-colors duration-300 font-bold">
                        {t("overview.mix-invoice-status.title")}
                      </span>
                      <span className="text-slate-700 text-sm font-normal dark:text-white/90 dark:group-hover:text-white transition-colors duration-300">
                        $
                      </span>
                    </CardTitle>
                    <CardDescription className="text-slate-600 group-hover:text-slate-800 dark:text-white/85 dark:group-hover:text-white transition-colors duration-300">
                      {t("overview.mix-invoice-status.description")}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <DonutChart
                      data={mixInvoiceChartData.map((item) => ({
                        ...item,
                        value: Math.round(item.value),
                      }))}
                      title=""
                      description=""
                      totalLabel="Mix Invoices"
                      open={`${Math.round(
                        data?.data?.payment?.mixInvoiceTotal || 0
                      )}`}
                      key={"mix-invoice"}
                    />
                    <div className="mt-4 grid grid-cols-1 gap-2">
                      <div className="flex justify-center items-center flex-col bg-red-50/90 dark:bg-red-500/5 p-3 rounded-lg border border-red-100 dark:border-red-500/10 group-hover:shadow-sm dark:group-hover:bg-red-500/10 dark:group-hover:border-red-500/20 transition-all duration-300">
                        <div className="text-xs text-slate-700 dark:text-white dark:group-hover:text-white">
                          {t("overview.invoice-status.due")}
                        </div>
                        <div className="text-xl font-bold text-red-700 dark:text-red-400 dark:group-hover:text-red-300 flex items-center gap-1 transition-colors duration-300">
                          <span className="text-sm">$</span>
                          {Math.round(
                            data?.data?.payment?.mixInvoiceDue || 0
                          ).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>
            )}

            {/* Bar Chart */}
            {isLoading ? (
              <BarChartSkeleton variant="purple" />
            ) : (
              <Card className="relative shadow-[0_10px_20px_-5px_rgba(0,0,0,0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] dark:shadow-md dark:hover:shadow-lg group transition-all duration-500 border-t-3 border-purple-400 overflow-hidden  dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border dark:border-green-800/30 hover:dark:border-green-700/50">
                {/* Gradient overlays */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-50/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 dark:from-green-800/40 dark:via-green-900/30 dark:to-transparent dark:group-hover:opacity-50 transition-opacity duration-500"></div>
                <div className="absolute inset-0 bg-gradient-to-br from-purple-950/10 via-transparent to-transparent opacity-0 dark:from-green-800/30 dark:opacity-30 transition-opacity duration-500"></div>
                <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-300/0 via-purple-300/10 to-purple-300/0 rounded-lg blur opacity-0 group-hover:opacity-100 dark:from-green-700/0 dark:via-green-700/20 dark:to-green-700/0 dark:group-hover:opacity-50 transition duration-500 group-hover:duration-200"></div>
                <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.08]"></div>

                <div className="relative">
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center justify-between">
                      <span className="group-hover:text-purple-600 dark:text-purple-300 dark:group-hover:text-purple-200 transition-colors duration-300">
                        {t("mix-invoice-tab.bar-chart.title")}
                      </span>
                      <div className="p-2 rounded-full bg-purple-50 dark:bg-purple-950/50 group-hover:bg-purple-100 dark:group-hover:bg-purple-800/30 transition-colors duration-300 shadow-sm">
                        <TrendingUp className="h-5 w-5 text-purple-500 dark:text-purple-300 dark:group-hover:text-purple-200" />
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <InvoiceBarChart
                      data={mixInvoiceData}
                      title=""
                      description={t("mix-invoice-tab.bar-chart.description")}
                    />
                  </CardContent>
                </div>
              </Card>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default InvoiceTab;
