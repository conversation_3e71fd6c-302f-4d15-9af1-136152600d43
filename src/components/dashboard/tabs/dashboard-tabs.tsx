"use client";
import React, { useState } from "react";
import { useTranslations } from "next-intl";
import CustomTabs from "@/components/Common_UI/custom-tabs";
import VehicleTab from "./vehicle-tab";
import OverviewTab from "./overview-tab";
import ShipmentTab from "./shipment-tab";
import InvoiceTab from "./invoice-tab";
import {
  BusFront,
  Ship,
  DollarSign,
  Package,
  LayoutDashboard,
  TableOfContents,
} from "lucide-react";
import "../dashboard-css.css";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export default function DashboardTabs() {
  const t = useTranslations("dashboard");
  const [activeTab, setActiveTab] = useState("overview");
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const showMixTabs = false;
  // Define base tab configurations for all tabs
  const allTabConfigs = [
    {
      value: "overview",
      label: t("tabs.overview"),
      icon: <LayoutDashboard className="h-4 w-4 mr-2" />,
      activeClass:
        "bg-gradient-to-r from-green-50 to-green-100/50 dark:from-green-900/80 dark:to-green-950/80 dark:backdrop-blur-sm shadow-sm dark:shadow-green-900/20",
      hoverClass:
        "hover:text-green-700 dark:hover:text-green-200 hover:bg-green-50/30 dark:hover:bg-green-900/15 transition-all duration-300",
      activeIconClass: "text-green-600 dark:text-green-300",
    },
    {
      value: "vehicle",
      label: t("tabs.vehicles"),
      icon: <BusFront className="h-4 w-4 mr-2" />,
      activeClass:
        "bg-gradient-to-r from-green-50 to-green-100/50 dark:from-green-900/80 dark:to-green-950/80 dark:backdrop-blur-sm shadow-sm dark:shadow-green-900/20",
      hoverClass:
        "hover:text-green-700 dark:hover:text-green-200 hover:bg-green-50/30 dark:hover:bg-green-900/15 transition-all duration-300",
      activeIconClass: "text-green-600 dark:text-green-300",
    },
    {
      value: "shipment",
      label: t("tabs.shipments"),
      icon: <Ship className="h-4 w-4 mr-2" />,
      activeClass:
        "bg-gradient-to-r from-green-50 to-green-100/50 dark:from-green-900/80 dark:to-green-950/80 dark:backdrop-blur-sm shadow-sm dark:shadow-green-900/20",
      hoverClass:
        "hover:text-green-700 dark:hover:text-green-200 hover:bg-green-50/30 dark:hover:bg-green-900/15 transition-all duration-300",
      activeIconClass: "text-green-600 dark:text-green-300",
    },
    {
      value: "invoice",
      label: t("tabs.invoices"),
      icon: <DollarSign className="h-4 w-4 mr-2" />,
      activeClass:
        "bg-gradient-to-r from-green-50 to-green-100/50 dark:from-green-900/80 dark:to-green-950/80 dark:backdrop-blur-sm shadow-sm dark:shadow-green-900/20",
      hoverClass:
        "hover:text-green-700 dark:hover:text-green-200 hover:bg-green-50/30 dark:hover:bg-green-900/15 transition-all duration-300",
      activeIconClass: "text-green-600 dark:text-green-300",
    },
    {
      value: "calculator",
      label: t("tabs.calculator"),
      icon: <Package className="h-4 w-4 mr-2" />,
      activeClass:
        "bg-gradient-to-r from-green-50 to-green-100/50 dark:from-green-900/80 dark:to-green-950/80 dark:backdrop-blur-sm shadow-sm dark:shadow-green-900/20",
      hoverClass:
        "hover:text-green-700 dark:hover:text-green-200 hover:bg-green-50/30 dark:hover:bg-green-900/15 transition-all duration-300",
      activeIconClass: "text-green-600 dark:text-green-300",
    },
  ];

  // Remove calculator tab if the customer is mix/mix_halfcut
  const filteredTabConfigs = allTabConfigs.filter((config) => {
    if (
      (config.value === "mix-invoice" || config.value === "calculator") &&
      !showMixTabs
    ) {
      return false;
    }
    return true;
  });

  // Generate tab triggers from the filtered configurations.
  const enhancedTabsTriggers = filteredTabConfigs.map((config) => ({
    value: config.value,
    label: (
      <div className="flex items-center justify-center w-full transition-all duration-300">
        <span
          className={`custom-tab-icon ${config.activeIconClass} transition-colors duration-300`}
        >
          {config.icon}
        </span>
        <span className="custom-tab-text font-medium">{config.label}</span>
      </div>
    ),
    className: `relative overflow-hidden group font-medium ${config.hoverClass} rounded-lg transition-all duration-300`,
    activeClassName: `font-semibold ${config.activeClass} ring-1 ring-inset ring-green-200/50 dark:ring-green-500/20`,
    data: { config },
    click: () => setActiveTab(config.value),
  }));

  // Build tab content. Only add Calculator tab content if it should be shown.
  const tabsContent = [
    { children: <OverviewTab />, value: "overview" },
    { children: <VehicleTab />, value: "vehicle" },
    { children: <ShipmentTab />, value: "shipment" },
    { children: <InvoiceTab />, value: "invoice" },
  ];

  const currentTabConfig = filteredTabConfigs.find(
    (config) => config.value === activeTab
  );

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setDropdownOpen(false);
  };

  return (
    <div className="dashboard-tabs-container">
      <CustomTabs
        defaultValue="overview"
        value={activeTab}
        onValueChange={setActiveTab}
        tabsClassName="dashboard-tabs-root"
      >
        <div className="pt-4">
          <div className="hidden lg:flex flex-1">
            <CustomTabs.TabList
              tabsTriggers={enhancedTabsTriggers}
              tabsListClassName={`grid w-full  ${showMixTabs ? "lg:grid-cols-6" : "lg:grid-cols-4"
                } gap-2`}
            />
          </div>

          <div className="flex lg:hidden flex-1 items-center justify-between mx-2 sm:mx-3">
            <div className="flex items-center">
              {currentTabConfig?.icon}
              <span className="font-medium text-primary dark:text-primary/80">
                {currentTabConfig?.label}
              </span>
            </div>

            <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
              <DropdownMenuTrigger asChild>
                <Button
                  className="flex items-center text-primary bg-primary/10 hover:primary/10 dark:hover:primary/20 transition-all duration-300"
                  variant="outline"
                  size="icon"
                >
                  <TableOfContents className="w-5 h-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[calc(100vw-20rem)] sm:w-24 md:w-24 border border-primary shadow-xl"
                align="end"
                sideOffset={8}
              >
                {filteredTabConfigs.map((config) => (
                  <DropdownMenuItem
                    key={config.value}
                    onClick={() => handleTabChange(config.value)}
                    className={`flex items-center justify-between cursor-pointer transition-colors duration-200 ${activeTab === config.value
                        ? "bg-primary/10 dar text-primary"
                        : "hover:bg-primary/20 dark:hover:bg-primary/30"
                      }`}
                  >
                    <div className="flex items-center">
                      <span
                        className={`${activeTab === config.value
                            ? "text-green-600 dark:text-green-300"
                            : ""
                          }`}
                      >
                        {config.icon}
                      </span>
                      <span className="font-medium">{config.label}</span>
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <div className="mt-4">
          <CustomTabs.TabContent tabsContent={tabsContent} />
        </div>
      </CustomTabs>
    </div>
  );
}
