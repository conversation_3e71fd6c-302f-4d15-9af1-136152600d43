"use client";
import { Ship } from "lucide-react";
import Link from "next/link";
import { LucideProps } from "lucide-react";
import { ForwardRefExoticComponent, RefAttributes } from "react";
import { ShipmentSummery } from "../summeries/shipment-summery";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { useTranslations } from "next-intl";
import { FullStatCard } from "../stat-card-full";
import { useDashboard } from "@/components/dashboard/dashboard-services";
import {
  SummarySkeleton,
  StatCardSkeleton,
} from "@/components/dashboard/skeletons/dashboard-skeletons";

interface CardData {
  title: string;
  icon: ForwardRefExoticComponent<
    Omit<LucideProps, "ref"> & RefAttributes<SVGSVGElement>
  >;
  value: number | string | undefined;
  url: string;
  subValues?: any;
}

const ShipmentTab = () => {
  const t = useTranslations("dashboard");
  const { data, isLoading } = useDashboard();
  const cardData: CardData[] = [
    {
      title: t("shipment-tab.total-shipment"),
      icon: Ship,
      value: data?.totalCount?.shipment,
      url: "/en/shipments/all",
    },
  ];
  // const fullContainerInvoice = data?.data?.payment
  //   ? [
  //       {
  //         name: t("overview.full-open-container-invoice-chart.open"),
  //         value: data?.data?.payment?.fullInvoiceAmount || 0,
  //         color: "hsl(var(--chart-1))",
  //       },
  //       {
  //         name: t("overview.full-open-container-invoice-chart.paid"),
  //         value: data?.data?.payment?.fullInvoicePaid || 0,
  //         color: "hsl(var(--chart-2))",
  //       },
  //       {
  //         name: t("overview.full-open-container-invoice-chart.due"),
  //         value:
  //           data?.data?.payment?.fullInvoiceAmount -
  //             data?.data?.payment?.fullInvoicePaid || 0,
  //         color: "hsl(var(--chart-3))",
  //       },
  //     ]
  //   : [
  //       {
  //         name: t("overview.full-open-container-invoice-chart.all"),
  //         value: 0,
  //         color: "hsl(var(--chart-1))",
  //       },
  //       {
  //         name: t("overview.full-open-container-invoice-chart.paid"),
  //         value: 0,
  //         color: "hsl(var(--chart-2))",
  //       },
  //       {
  //         name: t("overview.full-open-container-invoice-chart.due"),
  //         value: 0,
  //         color: "hsl(var(--chart-3))",
  //       },
  //     ];

  return (
    <div className="flex flex-col gap-4">
      {/* Shipment Card */}
      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-1">
        {isLoading ? (
          <StatCardSkeleton variant="info" />
        ) : (
          cardData.map((card, index) => (
            <Link key={index} href={card.url || "#"} passHref>
              <div className="relative group">
                {/* Hover glow effect for dark mode */}
                <div className="absolute -inset-0.5 bg-gradient-to-r from-indigo-700/0 via-indigo-700/20 to-indigo-700/0 rounded-lg blur opacity-0 group-hover:opacity-30 dark:group-hover:opacity-30 transition duration-500 group-hover:duration-200"></div>

                <FullStatCard
                  title={card.title}
                  icon={card.icon}
                  value={card.value}
                  subValues={card.subValues}
                  className="transition-all duration-300 dark:hover:border-green-700/30 group-hover:shadow-md dark:group-hover:shadow-lg relative z-10"
                />
              </div>
            </Link>
          ))
        )}
      </div>

      <div className="grid">
        {/* Shipment Summary Card */}
        {isLoading ? (
          <div className="col-span-5 md:col-span-5">
            <SummarySkeleton variant="indigo" />
          </div>
        ) : (
          <Card className="col-span-5 md:col-span-5 relative shadow-[0_10px_20px_-5px_rgba(0,0,0,0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] group transition-all duration-500 border-t-3 border-indigo-500 overflow-hidden dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border dark:border-green-700/20 hover:dark:border-green-500/20">
            {/* Softened gradient overlay on hover - light mode remains unchanged */}
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-50/30 via-indigo-50/10 to-transparent opacity-0 group-hover:opacity-100 dark:group-hover:opacity-0 transition-opacity duration-500"></div>

            {/* Dark mode subtle gradient - always visible */}
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-950/5 via-transparent to-transparent opacity-0 dark:from-blue-950/3 dark:opacity-10 transition-opacity duration-500"></div>

            {/* Softened card glow effect on hover */}
            <div className="absolute -inset-0.5 bg-gradient-to-r from-indigo-300/0 via-indigo-300/15 to-indigo-300/0 rounded-lg blur opacity-0 group-hover:opacity-100 dark:from-green-300/0 dark:via-green-300/5 dark:to-green-300/0 dark:group-hover:opacity-30 transition duration-500 group-hover:duration-200"></div>

            {/* Background pattern */}
            <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.05]"></div>

            <div className="relative">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center justify-between">
                  <span className="text-indigo-700 group-hover:text-indigo-700 dark:text-white dark:group-hover:text-white transition-colors duration-300 font-bold">
                    {t("overview.shipment-summery.title")}
                  </span>
                  <div className="p-2 rounded-full bg-indigo-100 dark:bg-green-800/40 group-hover:bg-indigo-200 dark:group-hover:bg-green-800/95 transition-colors duration-300 shadow-sm">
                    <Ship className="h-5 w-5 text-indigo-600 dark:text-white" />
                  </div>
                </CardTitle>
                <CardDescription className="text-slate-600 group-hover:text-slate-800 dark:text-white/85 dark:group-hover:text-white transition-colors duration-300">
                  {t("overview.summaries.shipment.description")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ShipmentSummery />
              </CardContent>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ShipmentTab;
