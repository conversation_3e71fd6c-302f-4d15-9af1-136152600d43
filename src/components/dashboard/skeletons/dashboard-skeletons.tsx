import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>it<PERSON>,
  CardDescription,
} from "../../ui/card";
import { Skeleton } from "../../ui/skeleton";

// DonutChart Skeleton component
export const DonutChartSkeleton = React.memo(
  ({ variant = "green" }: { variant?: "green" | "amber" | "purple" }) => {
    const baseColor = {
      green: "border-green-400 dark:border-green-900/30",
      amber: "border-amber-400 dark:border-green-900/30",
      purple: "border-purple-400 dark:border-green-900/30",
    };

    return (
      <Card className={`relative shadow-md border-t-3 ${baseColor[variant]}`}>
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-4 w-4 rounded-full" />
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-48 mt-1" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[200px] relative flex items-center justify-center">
            <Skeleton className="h-[160px] w-[160px] rounded-full" />
            <div className="absolute">
              <Skeleton className="h-10 w-10 rounded-full" />
            </div>
          </div>
          <div className="mt-4 grid grid-cols-2 gap-2">
            <div className="p-3 rounded-lg border">
              <Skeleton className="h-3 w-12 mb-2" />
              <Skeleton className="h-6 w-8" />
            </div>
            <div className="p-3 rounded-lg border">
              <Skeleton className="h-3 w-12 mb-2" />
              <Skeleton className="h-6 w-8" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
);

// Summary Skeleton component
export const SummarySkeleton = React.memo(
  ({ variant = "blue" }: { variant?: "blue" | "indigo" }) => {
    const baseColor = {
      blue: "border-blue-400 dark:border-green-900/30",
      indigo: "border-indigo-400 dark:border-green-900/30",
    };

    return (
      <Card
        className={`relative shadow-md border-t-3 ${baseColor[variant]} h-full`}
      >
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center justify-between">
            <Skeleton className="h-5 w-32" />
            <div className="p-2 rounded-full">
              <Skeleton className="h-5 w-5 rounded-full" />
            </div>
          </CardTitle>
          <CardDescription>
            <Skeleton className="h-4 w-48 mt-1" />
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between ">
              <Skeleton className="h-4 w-24" />
              <div className="flex space-x-4">
                <Skeleton className="h-6 w-8" />
                <Skeleton className="h-6 w-8" />
                <Skeleton className="h-6 w-8" />
                <Skeleton className="h-6 w-8" />
              </div>
            </div>

            <div className="w-full h-px bg-slate-200 dark:bg-green-900/20" />

            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-24" />
              <div className="flex space-x-4">
                <Skeleton className="h-6 w-8" />
                <Skeleton className="h-6 w-8" />
                <Skeleton className="h-6 w-8" />
                <Skeleton className="h-6 w-8" />
              </div>
            </div>

            <div className="w-full h-px bg-slate-200 dark:bg-green-900/20" />

            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-24" />
              <div className="flex space-x-4">
                <Skeleton className="h-6 w-8" />
                <Skeleton className="h-6 w-8" />
                <Skeleton className="h-6 w-8" />
                <Skeleton className="h-6 w-8" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
);

// Action Items Skeleton
export const ActionItemsSkeleton = React.memo(() => {
  return (
    <Card className="relative shadow-md">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <Skeleton className="h-5 w-32" />
          <div className="h-1.5 w-1.5 rounded-full bg-blue-400 dark:bg-green-800/40 animate-pulse"></div>
        </CardTitle>
        <CardDescription>
          <Skeleton className="h-4 w-48 mt-1" />
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card
              key={i}
              className="p-4 relative overflow-hidden shadow-sm border-l-3"
            >
              <div className="relative">
                <div className="flex items-center justify-between">
                  <div>
                    <Skeleton className="h-4 w-24 mb-2" />
                    <Skeleton className="h-7 w-16 mt-1 mb-1" />
                    <Skeleton className="h-5 w-14" />
                  </div>
                  <div className="h-12 w-12 rounded-full flex items-center justify-center">
                    <Skeleton className="h-6 w-6 rounded-full" />
                  </div>
                </div>
                <div className="mt-3">
                  <Skeleton className="h-4 w-36" />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </CardContent>
    </Card>
  );
});

// ProgressCard Skeleton
export const ProgressCardSkeleton = React.memo(
  ({
    variant = "primary",
  }: {
    variant?: "primary" | "success" | "warning";
  }) => {
    const baseColor = {
      primary: "border-blue-400 dark:border-green-900/30",
      success: "border-green-400 dark:border-green-900/30",
      warning: "border-amber-400 dark:border-green-900/30",
    };

    return (
      <Card className={`relative shadow-md border-t-3 ${baseColor[variant]}`}>
        <div className="p-5">
          <div className="flex flex-col space-y-3">
            <div className="flex items-center justify-between">
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-4 w-4 rounded-full" />
            </div>
            <Skeleton className="h-4 w-48" />
            <div className="flex items-center justify-between mt-2">
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-6 w-20" />
            </div>
            <Skeleton className="h-4 w-full rounded-full mt-2" />
          </div>
        </div>
        <ProgressCardSkeletonEffect />
      </Card>
    );
  }
);

// BarChart Skeleton
export const BarChartSkeleton = React.memo(
  ({ variant = "purple" }: { variant?: "purple" | "yellow" }) => {
    const baseColor = {
      purple: "border-purple-400 dark:border-green-900/30",
      yellow: "border-yellow-400 dark:border-green-900/30",
    };

    return (
      <Card className={`relative shadow-md border-t-3 ${baseColor[variant]}`}>
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center justify-between">
            <Skeleton className="h-5 w-32" />
            <div className="p-2 rounded-full">
              <Skeleton className="h-5 w-5 rounded-full" />
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-4 w-48 mb-4" />
          <div className="h-[200px] relative">
            <div className="flex justify-between items-end h-[170px] mt-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="flex flex-col items-center space-y-2">
                  <div className="flex space-x-1">
                    <Skeleton
                      className={`w-5 h-${10 + Math.floor(Math.random() * 20)}`}
                    />
                    <Skeleton
                      className={`w-5 h-${5 + Math.floor(Math.random() * 15)}`}
                    />
                  </div>
                  <Skeleton className="h-3 w-8" />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
);

// PieChart Skeleton
export const PiChartSkeleton = React.memo(
  ({ variant = "purple" }: { variant?: "purple" }) => {
    const baseColor = {
      purple: "border-purple-400 dark:border-green-900/30",
    };

    return (
      <Card
        className={`relative shadow-md border-t-3 ${baseColor[variant]} h-full`}
      >
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center justify-between">
            <Skeleton className="h-5 w-32" />
            <div className="p-2 rounded-full">
              <Skeleton className="h-5 w-5 rounded-full" />
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[200px] relative flex items-center justify-center">
            <Skeleton className="h-[140px] w-[140px] rounded-full" />
            <div className="absolute">
              <Skeleton className="h-10 w-10 rounded-full" />
            </div>
          </div>
          <div className="mt-2">
            <Skeleton className="h-4 w-48" />
          </div>
        </CardContent>
      </Card>
    );
  }
);

// StatCard Skeleton
export const StatCardSkeleton = React.memo(
  ({
    variant = "primary",
  }: {
    variant?: "primary" | "info" | "success" | "warning";
  }) => {
    const baseColor = {
      primary: "border-blue-400 dark:border-green-100/5",
      info: "border-indigo-400 dark:border-green-100/5",
      success: "border-green-400 dark:border-green-100/5",
      warning: "border-amber-400 dark:border-green-100/5",
    };

    return (
      <Card className={`relative shadow-md border-l-3 ${baseColor[variant]}`}>
        <CardContent className="p-6">
          <div className="flex justify-between items-center">
            <div>
              <Skeleton className="h-4 w-24 mb-2" />
              <Skeleton className="h-8 w-16 mt-2" />
              <div className="flex items-center gap-1 mt-2">
                <Skeleton className="h-3 w-10" />
                <Skeleton className="h-3 w-10" />
              </div>
            </div>
            <div className="rounded-full p-3 bg-slate-100 dark:bg-green-900/10">
              <Skeleton className="h-6 w-6 rounded-full" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
);

// MetricsCardSkeleton
export const MetricsCardSkeleton = React.memo(() => {
  return (
    <Card className="relative shadow-md">
      <div className="relative p-4">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-4 w-24 mb-2" />
            <Skeleton className="h-7 w-16 mt-1" />
          </div>
          <div className="p-2 rounded-full">
            <Skeleton className="h-5 w-5 rounded-full" />
          </div>
        </div>
      </div>
    </Card>
  );
});

// Add display names to the memoized components
DonutChartSkeleton.displayName = "DonutChartSkeleton";
SummarySkeleton.displayName = "SummarySkeleton";
ActionItemsSkeleton.displayName = "ActionItemsSkeleton";
ProgressCardSkeleton.displayName = "ProgressCardSkeleton";
BarChartSkeleton.displayName = "BarChartSkeleton";
PiChartSkeleton.displayName = "PiChartSkeleton";
StatCardSkeleton.displayName = "StatCardSkeleton";
MetricsCardSkeleton.displayName = "MetricsCardSkeleton";

// ProgressCard Skeleton effect component
export const ProgressCardSkeletonEffect = React.memo(() => (
  <div
    className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent dark:via-green-700/15 animate-pulse"
    style={{ animationDuration: "1.5s" }}
  />
));

ProgressCardSkeletonEffect.displayName = "ProgressCardSkeletonEffect";
