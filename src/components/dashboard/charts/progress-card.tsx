import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

interface ProgressCardProps {
  title: string;
  description?: string;
  value: number;
  max: number;
  className?: string;
  variant?: "default" | "primary" | "success" | "warning" | "danger" | "info";
  formatValue?: (value: number) => string;
  formatMax?: (max: number) => string;
  icon?: React.ReactNode;
}

export function ProgressCard({
  title,
  description,
  value,
  max,
  className,
  variant = "default",
  formatValue = (v) => v.toString(),
  formatMax = (m) => m.toString(),
  icon,
}: ProgressCardProps) {
  const t = useTranslations("dashboard");
  const percentage = max > 0 ? (value / max) * 100 : 0;

  const getProgressColor = () => {
    switch (variant) {
      case "primary":
        return "bg-blue-500 dark:bg-blue-600/50";
      case "success":
        return "bg-emerald-500 dark:bg-emerald-600/50";
      case "warning":
        return "bg-amber-500 dark:bg-amber-600/50";
      case "danger":
        return "bg-red-500 dark:bg-red-600/50";
      case "info":
        return "bg-blue-500 dark:bg-blue-600/50";
      default:
        return "bg-green-500 dark:bg-green-600/50";
    }
  };

  const getTextColor = () => {
    switch (variant) {
      case "primary":
        return "text-blue-600 dark:text-blue-400/70";
      case "success":
        return "text-emerald-600 dark:text-emerald-400/70";
      case "warning":
        return "text-amber-600 dark:text-amber-400/50";
      case "danger":
        return "text-red-600 dark:text-red-400/50";
      case "info":
        return "text-blue-600 dark:text-blue-400/50";
      default:
        return "text-green-600 dark:text-green-400/50";
    }
  };

  return (
    <Card
      className={cn(
        "shadow-sm hover:shadow-md transition-all duration-500",
        className
      )}
    >
      <div className="h-[180px] flex flex-col">
        <div className="flex-none">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-base font-medium">{title}</CardTitle>
              {icon && <div className="text-muted-foreground">{icon}</div>}
            </div>
            {description && (
              <CardDescription className="line-clamp-2">
                {description}
              </CardDescription>
            )}
          </CardHeader>
        </div>

        <div className="px-6 flex-grow flex items-center">
          <div className="text-2xl font-bold">
            <span className={cn("", getTextColor())}>{formatValue(value)}</span>
            <span className="text-sm text-muted-foreground font-normal ml-1">
              / {formatMax(max)}
            </span>
          </div>
        </div>

        <div className="px-6 pb-4 flex-none">
          <div className="flex items-center justify-between text-sm mb-1">
            <span className="font-medium text-slate-600 dark:text-slate-300">
              {t("progress-card.completion")}
            </span>
            <span className="font-medium">{Math.round(percentage)}%</span>
          </div>
          <div className="h-2 bg-slate-100 dark:bg-slate-700 rounded-full overflow-hidden">
            <div
              className={cn(
                "h-full rounded-full transition-all duration-500",
                getProgressColor()
              )}
              style={{ width: `${percentage}%` }}
            />
          </div>
        </div>
      </div>
    </Card>
  );
}
