import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { useTranslations } from "next-intl";
import { <PERSON>, <PERSON><PERSON>hart, CartesianGrid, Tooltip, XAxis } from "recharts";

interface InvoiceBarChartProps {
  data: {
    name: string;
    total: number;
    payment: number;
  }[];
  title: string;
  description?: string;
}

export function InvoiceBarChart({ data, title, description }: InvoiceBarChartProps) {
  const t = useTranslations('dashboard')
  const chartConfig = {
    total: {
      label: t(`bar-chart-tooltip.first`),
      color: "hsl(var(--chart-1))",
    },
    payment: {
      label: t(`bar-chart-tooltip.second`),
      color: "hsl(var(--chart-2))",
    },
  } satisfies ChartConfig
  return (
    <Card className="shadow-md hover:shadow-lg transition-all duration-300">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="h-full">
          <ChartContainer config={chartConfig}>
            <BarChart accessibilityLayer data={data} barCategoryGap={0}>
              <CartesianGrid vertical={false} />
              <XAxis
                dataKey="name"
                stroke="#888888"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <ChartTooltip
                cursor={false}
                content={<ChartTooltipContent indicator="dashed" />}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'var(--sidebar-background)',
                  borderColor: 'var(--border)',
                  borderRadius: '6px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                }}
              />
              <Bar dataKey="total" fill="hsl(var(--chart-2))" radius={[4, 4, 0, 0]} width={2} barSize={15} />
              <Bar dataKey="payment" fill="hsl(var(--chart-3))" radius={[4, 4, 0, 0]} width={2} barSize={15} />
            </BarChart>
          </ChartContainer>
        </div>
      </CardContent>
    </Card>
  );
} 