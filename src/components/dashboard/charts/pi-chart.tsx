"use client";

import * as React from "react";
import { <PERSON>, Label, Pie, Pie<PERSON><PERSON> } from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ChartConfig, ChartContainer } from "@/components/ui/chart";

interface ChartData {
  name: string;
  value: number;
  fill: string;
}

interface InvoiceChartProps {
  data: ChartData[];
  config?: ChartConfig;
  title?: string;
  description?: string;
  totalLabel?: string;
}

export function PiChart({
  data,
  config: customConfig,
  title = "Chart",
  description,
  totalLabel = "Total",
}: InvoiceChartProps) {
  const total = data.reduce((acc, item) => acc + item.value, 0);

  // Create a default config if none is provided
  const defaultConfig: ChartConfig = {
    value: {
      label: totalLabel,
    },
    ...data.reduce((acc, item, index) => {
      acc[item.name] = {
        label: item.name.charAt(0).toUpperCase() + item.name.slice(1),
        color: `hsl(var(--chart-${index + 1}))`,
      };
      return acc;
    }, {} as Record<string, { label: string; color: string }>),
  };

  const chartConfig = customConfig || defaultConfig;

  // Group data into rows of 2 for better display
  const dataRows: ChartData[][] = [];
  for (let i = 0; i < data.length; i += 2) {
    dataRows.push(data.slice(i, i + 2));
  }

  return (
    <Card className="h-full transition-all hover:shadow-md">
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-medium">{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="pb-2">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[250px]"
        >
          <PieChart width={300} height={200}>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              innerRadius={40}
              outerRadius={80}
              paddingAngle={2}
              dataKey="value"
              nameKey="name"
              label={({
                cx,
                cy,
                midAngle,
                innerRadius,
                outerRadius,
                value,
              }) => {
                const RADIAN = Math.PI / 180;
                const radius = 25 + innerRadius + (outerRadius - innerRadius);
                const x = cx + radius * Math.cos(-midAngle * RADIAN);
                const y = cy + radius * Math.sin(-midAngle * RADIAN);

                // Only show percentage for items with significant value
                if (value / total > 0.05) {
                  return (
                    <text
                      x={x}
                      y={y}
                      textAnchor={x > cx ? "start" : "end"}
                      dominantBaseline="central"
                      className="text-xs font-medium fill-foreground"
                    >
                      {`${Math.round((value / total) * 100)}%`}
                    </text>
                  );
                }
                return null;
              }}
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={entry.fill || `hsl(var(--chart-${index + 1}))`}
                />
              ))}
              <Label
                content={({ viewBox }) => {
                  if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                    return (
                      <text
                        x={viewBox.cx}
                        y={viewBox.cy}
                        textAnchor="middle"
                        dominantBaseline="middle"
                        className="fill-foreground"
                      >
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy}
                          className="text-lg font-bold"
                        >
                          {Number(total).toLocaleString()}
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 20}
                          className="text-xs fill-muted-foreground"
                        >
                          {totalLabel}
                        </tspan>
                      </text>
                    );
                  }
                  return null;
                }}
              />
            </Pie>
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex flex-col gap-2 text-xs pt-0">
        {dataRows.map((row, rowIndex) => (
          <div key={rowIndex} className="flex justify-around w-full">
            {row.map((item, index) => (
              <div key={index} className="flex items-center gap-1">
                <span
                  className="w-2 h-2 rounded-full"
                  style={{
                    backgroundColor:
                      item.fill ||
                      `hsl(var(--chart-${
                        dataRows[rowIndex].indexOf(item) + rowIndex * 2 + 1
                      }))`,
                  }}
                />
                <span>
                  {item.name.charAt(0).toUpperCase() + item.name.slice(1)}:{" "}
                  {item.value.toLocaleString(undefined, {
                    maximumFractionDigits: 0,
                  })}
                </span>
              </div>
            ))}
          </div>
        ))}
      </CardFooter>
    </Card>
  );
}
