import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "../ui/card";
import { cn } from "@/lib/utils";
import { cva } from "class-variance-authority";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

interface SubValues {
  open?: number;
  paid?: number;
  label1?: string;
  label2?: string;
}

interface StatCardProps {
  title: string;
  icon?: React.ComponentType | any;
  value?: string | number;
  subValues?: SubValues;
  className?: string;
  variant?: "default" | "primary" | "success" | "warning" | "danger" | "info";
  valuePrefix?: string;
  valueSuffix?: string;
  loading?: boolean;
  valueStyle?: string;
}

const cardVariants = cva(
  "group transition-all duration-300 ease-in-out hover:shadow-lg relative overflow-hidden h-[140px] border backdrop-blur-[2px] w-full",
  {
    variants: {
      variant: {
        default:
          "bg-gradient-to-br from-white via-white to-red-50/20 dark:from-green-200/10 dark:to-green-100/0 border-slate-200/80 dark:border-green-500/5 hover:border-green-400/60 dark:hover:border-green-500/10",
        primary:
          "bg-gradient-to-br from-white via-white to-blue-50/20 dark:from-slate-900 dark:via-slate-900 dark:to-blue-950/5 border-l-4 border-l-primary border-slate-200/80 dark:border-slate-800/50 hover:border-blue-300/60 dark:hover:border-blue-800/30",
        success:
          "bg-gradient-to-br from-white via-white to-emerald-50/20 dark:from-slate-900 dark:via-slate-900 dark:to-emerald-950/5 border-l-4 border-l-emerald-500 border-slate-200/80 dark:border-slate-800/50 hover:border-emerald-300/60 dark:hover:border-emerald-800/30",
        warning:
          "bg-gradient-to-br from-white via-white to-amber-50/20 dark:from-slate-900 dark:via-slate-900 dark:to-amber-950/5 border-l-4 border-l-amber-500 border-slate-200/80 dark:border-slate-800/50 hover:border-amber-300/60 dark:hover:border-amber-800/30",
        danger:
          "bg-gradient-to-br from-white via-white to-red-50/20 dark:from-slate-900 dark:via-slate-900 dark:to-red-950/5 border-l-4 border-l-red-500 border-slate-200/80 dark:border-slate-800/50 hover:border-red-300/60 dark:hover:border-red-800/30",
        info: "bg-gradient-to-br from-white via-white to-cyan-50/20 dark:from-slate-900 dark:via-slate-900 dark:to-cyan-950/5 border-l-4 border-l-blue-500 border-slate-200/80 dark:border-slate-800/50 hover:border-cyan-300/60 dark:hover:border-cyan-800/30",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

export const FullStatCard = ({
  title,
  icon: Icon,
  value,
  subValues,
  className,
  variant = "default",
  valuePrefix,
  valueSuffix,
  loading = false,
  valueStyle = "text-3xl",
}: StatCardProps) => {
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Get accent color based on variant
  const getAccentColor = () => {
    switch (variant) {
      case "primary":
        return "bg-primary ";
      case "success":
        return "bg-emerald-500";
      case "warning":
        return "bg-amber-500";
      case "danger":
        return "bg-red-500";
      case "info":
        return "bg-blue-500";
      default:
        return "bg-green-500 dark:bg-green-600/50";
    }
  };

  // Get text color based on variant
  const getTextColor = () => {
    switch (variant) {
      case "primary":
        return "text-primary";
      case "success":
        return "text-emerald-600 dark:text-emerald-400";
      case "warning":
        return "text-amber-600 dark:text-amber-400";
      case "danger":
        return "text-red-600 dark:text-red-400";
      case "info":
        return "text-blue-600 dark:text-blue-400";
      default:
        return "text-green-600 dark:text-green-400/60";
    }
  };

  // Get icon background color
  const getIconBgColor = () => {
    switch (variant) {
      case "primary":
        return "bg-primary/10 dark:bg-primary/10";
      case "success":
        return "bg-emerald-500/10 dark:bg-emerald-500/10";
      case "warning":
        return "bg-amber-500/10 dark:bg-amber-500/10";
      case "danger":
        return "bg-red-500/10 dark:bg-red-500/10";
      case "info":
        return "bg-blue-500/10 dark:bg-blue-500/10";
      default:
        return "bg-green-500/10 dark:bg-green-500/10";
    }
  };

  // Get shimmer gradient for loading state
  const getShimmerGradient = () => {
    return mounted && theme === "dark"
      ? "bg-gradient-to-r from-slate-800 via-slate-700 to-slate-800"
      : "bg-gradient-to-r from-gray-100 via-gray-200 to-gray-100";
  };

  // Get background pattern based on variant
  const getBackgroundPattern = () => {
    switch (variant) {
      case "primary":
        return "bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#1e293b_1px,transparent_1px)]";
      case "success":
        return "bg-[radial-gradient(#dcfce7_1px,transparent_1px)] dark:bg-[radial-gradient(#022c22_1px,transparent_1px)]";
      case "warning":
        return "bg-[radial-gradient(#fef3c7_1px,transparent_1px)] dark:bg-[radial-gradient(#3f2f00_1px,transparent_1px)]";
      case "danger":
        return "bg-[radial-gradient(#fee2e2_1px,transparent_1px)] dark:bg-[radial-gradient(#450a0a_1px,transparent_1px)]";
      case "info":
        return "bg-[radial-gradient(#e0f2fe_1px,transparent_1px)] dark:bg-[radial-gradient(#082f49_1px,transparent_1px)]";
      default:
        return "bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#1e293b_1px,transparent_1px)]";
    }
  };

  // Get border style based on variant
  const getBorderStyle = () => {
    switch (variant) {
      case "primary":
        return "border-t-4 border-t-primary dark:border-t-green-500/20 hover:dark:border-t-green-500/30";
      case "success":
        return "border-t-4 border-t-emerald-500";
      case "warning":
        return "border-t-4 border-t-amber-500";
      case "danger":
        return "border-t-4 border-t-red-500";
      case "info":
        return "border-t-4 border-t-blue-500";
      default:
        return "border-t-4 border-t-green-500 dark:border-t-green-500/20 hover:dark:border-t-green-500/30 ";
    }
  };

  return (
    <Card
      className={cn(
        cardVariants({ variant }),
        className,
        "shadow-md hover:shadow-xl rounded-lg transition-all duration-300",
        getBorderStyle()
      )}
    >
      {/* Background pattern */}
      <div
        className={cn(
          "absolute inset-0 [background-size:16px_16px] opacity-[0.08] dark:opacity-[0.05] rounded-lg",
          getBackgroundPattern()
        )}
      />

      {/* Subtle gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-green-100/10 dark:from-slate-900/20 dark:via-transparent dark:to-green-900/3 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-lg" />

      {/* Decorative corner accent */}
      <div
        className={cn(
          "absolute -right-12 -top-12 w-24 h-24 rotate-45 transform transition-transform duration-500 ease-out group-hover:scale-125 group-hover:rotate-[60deg] opacity-10 group-hover:opacity-20",
          getAccentColor()
        )}
      />

      <div className="flex h-full">
        {/* Left accent bar for visual interest */}
        <div
          className={cn(
            "w-1 h-full opacity-70 group-hover:opacity-100 transition-opacity duration-300",
            getAccentColor()
          )}
        />

        <div className="flex-1 flex flex-col">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 p-4 pb-0 relative z-10">
            <CardTitle className="text-base font-medium transition-colors duration-300 flex items-center">
              {loading ? (
                <div
                  className={cn(
                    "h-4 w-32 rounded animate-pulse",
                    getShimmerGradient()
                  )}
                />
              ) : (
                <>
                  {Icon && (
                    <div
                      className={cn(
                        "rounded-full p-1.5 mr-2 flex items-center justify-center transition-all duration-300 ease-in-out group-hover:scale-110",
                        getIconBgColor()
                      )}
                    >
                      <Icon
                        className={cn(
                          "h-4 w-4 transition-colors duration-300",
                          getTextColor()
                        )}
                      />
                    </div>
                  )}
                  <span>{title}</span>
                </>
              )}
            </CardTitle>

            {loading ? (
              <div
                className={cn(
                  "h-9 w-9 rounded-full animate-pulse",
                  getShimmerGradient()
                )}
              />
            ) : (
              <div
                className={cn(
                  "rounded-full p-2.5 flex items-center justify-center transition-all duration-300 ease-in-out hover:scale-110 shadow-sm",
                  getIconBgColor()
                )}
              >
                {Icon ? (
                  <Icon
                    className={cn(
                      "h-5 w-5 transition-colors duration-300",
                      getTextColor()
                    )}
                  />
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className={cn(
                      "h-5 w-5 transition-colors duration-300",
                      getTextColor()
                    )}
                  >
                    <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
                  </svg>
                )}
              </div>
            )}
          </CardHeader>

          <CardContent className="relative z-10 p-4 pt-2 flex flex-col flex-1">
            {loading ? (
              <>
                <div
                  className={cn(
                    "h-10 w-28 rounded animate-pulse mb-auto",
                    getShimmerGradient()
                  )}
                />
                <div className="text-xs flex justify-between mt-auto pt-2 border-t border-green-100/50 dark:border-green-900/20">
                  <div
                    className={cn(
                      "h-12 w-20 rounded animate-pulse",
                      getShimmerGradient()
                    )}
                  />
                  <div
                    className={cn(
                      "h-12 w-20 rounded animate-pulse",
                      getShimmerGradient()
                    )}
                  />
                </div>
              </>
            ) : (
              <>
                <div
                  className={cn(
                    "font-bold tracking-tight flex items-baseline mt-2",
                    getTextColor(),
                    valueStyle
                  )}
                >
                  {valuePrefix && (
                    <span className="text-sm mr-0.5 opacity-70">
                      {valuePrefix}
                    </span>
                  )}
                  {value || ""}
                  {valueSuffix && (
                    <span className="text-sm ml-0.5 opacity-70">
                      {valueSuffix}
                    </span>
                  )}
                </div>

                {subValues ? (
                  <div className="text-xs flex justify-between mt-auto pt-3 border-t border-green-100/50 dark:border-green-900/20">
                    <div
                      className={cn(
                        "flex flex-col items-center rounded-lg px-3 py-2 transition-all duration-300 ease-in-out shadow-sm",
                        variant === "default"
                          ? "bg-green-50/40 dark:bg-green-900/5 hover:bg-green-50 dark:hover:bg-green-900/10"
                          : variant === "primary"
                          ? "bg-blue-50/40 dark:bg-blue-900/5 hover:bg-blue-50 dark:hover:bg-blue-900/10"
                          : variant === "success"
                          ? "bg-emerald-50/40 dark:bg-emerald-900/5 hover:bg-emerald-50 dark:hover:bg-emerald-900/10"
                          : variant === "warning"
                          ? "bg-amber-50/40 dark:bg-amber-900/5 hover:bg-amber-50 dark:hover:bg-amber-900/10"
                          : variant === "danger"
                          ? "bg-red-50/40 dark:bg-red-900/5 hover:bg-red-50 dark:hover:bg-red-900/10"
                          : "bg-cyan-50/40 dark:bg-cyan-900/5 hover:bg-cyan-50 dark:hover:bg-cyan-900/10"
                      )}
                    >
                      <span
                        className={cn(
                          "font-bold text-lg transition-colors duration-300",
                          getTextColor()
                        )}
                      >
                        {subValues.open || 0}
                      </span>
                      <span className="text-xs text-slate-600 dark:text-slate-400 transition-colors duration-300">
                        {subValues.label1 || "Open"}
                      </span>
                    </div>
                    <div
                      className={cn(
                        "flex flex-col items-center rounded-lg px-3 py-2 transition-all duration-300 ease-in-out shadow-sm",
                        variant === "default"
                          ? "bg-green-50/40 dark:bg-green-900/5 hover:bg-green-50 dark:hover:bg-green-900/10"
                          : variant === "primary"
                          ? "bg-blue-50/40 dark:bg-blue-900/5 hover:bg-blue-50 dark:hover:bg-blue-900/10"
                          : variant === "success"
                          ? "bg-emerald-50/40 dark:bg-emerald-900/5 hover:bg-emerald-50 dark:hover:bg-emerald-900/10"
                          : variant === "warning"
                          ? "bg-amber-50/40 dark:bg-amber-900/5 hover:bg-amber-50 dark:hover:bg-amber-900/10"
                          : variant === "danger"
                          ? "bg-red-50/40 dark:bg-red-900/5 hover:bg-red-50 dark:hover:bg-red-900/10"
                          : "bg-cyan-50/40 dark:bg-cyan-900/5 hover:bg-cyan-50 dark:hover:bg-cyan-900/10"
                      )}
                    >
                      <span
                        className={cn(
                          "font-bold text-lg transition-colors duration-300",
                          getTextColor()
                        )}
                      >
                        {subValues.paid || 0}
                      </span>
                      <span className="text-xs text-slate-600 dark:text-slate-400 transition-colors duration-300">
                        {subValues.label2 || "Paid"}
                      </span>
                    </div>
                  </div>
                ) : (
                  <div className="mt-auto"></div>
                )}
              </>
            )}
          </CardContent>
        </div>
      </div>
    </Card>
  );
};
