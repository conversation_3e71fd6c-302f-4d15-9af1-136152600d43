"use client";
import React, { useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { toast } from "sonner";
import DashboardTabs from "./tabs/dashboard-tabs";
import { Download, RefreshCw } from "lucide-react";
import { Card } from "@/components/ui/card";
import { LoadingSpinner } from "../Common_UI/loading";

export default function DashboardClient() {
  const t = useTranslations("dashboard");
  const contentRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const generatePDF = async () => {
    const element = contentRef.current;
    if (!element) return;

    try {
      setIsGeneratingPDF(true);
      toast.info("Generating PDF, please wait...");

      // Store original styles
      const originalOverflow = document.body.style.overflow;
      const originalHeight = element.style.height;
      const originalMaxHeight = element.style.maxHeight;
      const originalPosition = element.style.position;

      // Check if we're in dark mode
      const isDarkMode = document.documentElement.classList.contains("dark");

      // Temporarily modify styles to capture full content
      document.body.style.overflow = "visible";
      element.style.height = "auto";
      element.style.maxHeight = "none";
      element.style.position = "relative";

      // Get all nested scrollable elements and store their original styles
      const scrollableElements = element.querySelectorAll(
        '[style*="overflow"]'
      );
      const originalStyles = Array.from(scrollableElements).map((el) => ({
        element: el as HTMLElement,
        overflow: (el as HTMLElement).style.overflow,
        maxHeight: (el as HTMLElement).style.maxHeight,
        height: (el as HTMLElement).style.height,
      }));

      // Temporarily modify all scrollable elements
      originalStyles.forEach((item) => {
        item.element.style.overflow = "visible";
        item.element.style.maxHeight = "none";
        item.element.style.height = "auto";
      });

      // Wait a small amount of time for any dynamic content to render
      await new Promise((resolve) => setTimeout(resolve, 500));

      const canvas = await html2canvas(element, {
        scale: 2,
        useCORS: true,
        logging: false,
        allowTaint: true,
        scrollY: -window.scrollY,
        windowWidth: document.documentElement.offsetWidth,
        windowHeight: document.documentElement.offsetHeight,
        onclone: (clonedDoc) => {
          // Ensure all elements in the cloned document are fully expanded
          const clonedElement = clonedDoc.body.querySelector(
            '[data-pdf-content="true"]'
          );

          if (clonedElement) {
            (clonedElement as HTMLElement).style.height = "auto";
            (clonedElement as HTMLElement).style.maxHeight = "none";
            (clonedElement as HTMLElement).style.overflow = "visible";
          }

          // Ensure consistent rendering by forcing either light or dark mode
          if (isDarkMode) {
            // Ensure dark mode is consistently applied to the clone
            clonedDoc.documentElement.classList.add("dark");

            // Set background color for body to ensure proper dark mode rendering
            clonedDoc.body.style.backgroundColor = "#0a0a0a";
            clonedDoc.body.style.color = "#ffffff";

            // We don't need to manually process dark mode classes
            // Just ensuring the dark class is on the documentElement is sufficient
            // for Tailwind's dark mode to work properly
          } else {
            // Ensure light mode is consistently applied
            clonedDoc.documentElement.classList.remove("dark");
            clonedDoc.body.style.backgroundColor = "#ffffff";
            clonedDoc.body.style.color = "#0a0a0a";
          }
        },
      });

      // Restore original styles
      document.body.style.overflow = originalOverflow;
      element.style.height = originalHeight;
      element.style.maxHeight = originalMaxHeight;
      element.style.position = originalPosition;

      // Restore all scrollable elements' original styles
      originalStyles.forEach((item) => {
        item.element.style.overflow = item.overflow;
        item.element.style.maxHeight = item.maxHeight;
        item.element.style.height = item.height;
      });

      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF({
        orientation: "portrait",
        unit: "mm",
        format: "a4",
      });

      const imgWidth = pdf.internal.pageSize.getWidth();
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      let heightLeft = imgHeight;
      let position = 0;

      // Add first page
      pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
      heightLeft -= pdf.internal.pageSize.getHeight();

      // Add new pages if content overflows
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, "PNG", 0, position, imgWidth, imgHeight);
        heightLeft -= pdf.internal.pageSize.getHeight();
      }

      pdf.save("dashboard.pdf");
      toast.success("PDF generated successfully!");
    } catch (error) {
      console.error("PDF generation error:", error);
      toast.error("Error generating PDF. Please try again.");
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    // Simulate refresh by waiting a moment then reloading the page
    setTimeout(() => {
      router.refresh();
      setIsRefreshing(false);
    }, 1000);
  };

  return (
    <div className="p-1 sm:p-4 md:p-4 lg:p-4 h-[calc(100vh-70px)] overflow-y-auto bg-background/50">
      <Card className="p-1 sm:p-6 md:p-6 lg:p-6 shadow-sm">
        <div className="flex-col flex">
          <div className="flex-1 space-y-6" data-pdf-content="true">
            <div className="flex flex-row items-center justify-between gap-4 px-2 pt-1">
              <h2 className="text-1xl sm:text-3xl md:text-3xl font-bold tracking-tight text-primary">
                {t("title")}
              </h2>
              <div className="flex items-center gap-3 flex-wrap justify-end">
                <Button
                  onClick={handleRefresh}
                  variant="outline"
                  size="icon"
                  disabled={isRefreshing}
                  className="bg-primary/20 dark:bg-primary/50 text-primary dark:text-primary/80"
                >
                  <RefreshCw
                    className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
                  />
                </Button>
                <Button
                  onClick={generatePDF}
                  disabled={isGeneratingPDF}
                  className="bg-primary/20 dark:bg-primary/50 text-primary dark:text-primary/80"
                  variant="outline"
                  size="icon"
                >
                  {isGeneratingPDF ? (
                    <LoadingSpinner className="h-5 w-5" />
                  ) : (
                    <Download className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
            <div ref={contentRef}>
              <DashboardTabs />
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
