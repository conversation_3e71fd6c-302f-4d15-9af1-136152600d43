"use client";
export type ShipmentSummaryData = {
  location_id: string;
  location_name: string;
  at_loading: number;
  arrived: number;
  on_the_way: number;
  total: number;
};
export type VehicleSummaryData = {
  id: string;
  location_name: string;
  auction_unpaid: number;
  auction_paid: number;
  on_the_way: number;
  on_hand_no_title: number;
  on_hand_with_title: number;
  on_hand_with_load: number;
  shipped: number;
  location_id: string;
};

import { useQuery } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
import { useSession } from "next-auth/react";

export const getCustomerDashboardStatistic = async (
  fetchClient: ReturnType<typeof useFetchClient>
): Promise<ShipmentSummaryData[]> => {
  const response = await fetchClient(`/v2/statistics/customer-dashboard`);

  return response.data;
};

export const getDashboardStatistic = async (
  fetchClient: ReturnType<typeof useFetchClient>
): Promise<ShipmentSummaryData[]> => {
  const response = await fetchClient(`/v2/statistics/dashboard`);
  return response.data;
};

export const useDashboard = () => {
  const session = useSession();
  const fetchClient = useFetchClient();
  return useQuery<any>({
    queryKey: ["dashboard"],
    queryFn: () => {
      if (session.data?.user_type === "customer_of_customer") {
        return getCustomerDashboardStatistic(fetchClient);
      } else if (session.data?.user_type === "customer") {
        return getDashboardStatistic(fetchClient);
      } else {
        return {};
      }
    },
    enabled: !!session.data?.user_type,
  });
};
