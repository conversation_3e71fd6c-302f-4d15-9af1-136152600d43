/* Dashboard tabs styling */

.dashboard-tabs-container {
  --tab-radius: 0.5rem;
  position: relative;
}

/* Subtle background glow effect */
.dashboard-tabs-container::before {
  content: '';
  position: absolute;
  inset: -4px;
  background: linear-gradient(to right, rgba(226, 232, 240, 0), rgba(226, 232, 240, 0.3), rgba(226, 232, 240, 0));
  border-radius: 1rem;
  filter: blur(10px);
  opacity: 0.5;
  z-index: -1;
}

/* Tab container */
.dashboard-tabs-root {
  border: 1px solid #e2e8f0;
  padding: 0.375rem;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.025);
  backdrop-filter: blur(4px);
}

/* Dark mode styles */
:root[class~="dark"] .dashboard-tabs-root {
  border: 1px solid rgba(30, 95, 49, 0.32);
  background: rgba(30, 95, 49, 0.05);
}

/* Tab list container */
.dashboard-tabs-root [role="tablist"] {
  background-color: rgba(241, 245, 249, 0.8);
  padding: 0.25rem;
  border-radius: 0.5rem;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Dark mode styles for tab list */
:root[class~="dark"] .dashboard-tabs-root [role="tablist"] {
  background-color: rgba(30, 95, 49, 0.32);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Tab item styling */
.dashboard-tabs-root [role="tab"] {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: var(--tab-radius);
  position: relative;
  overflow: hidden;
  padding: 0.375rem;
}

/* Tab content styling */
.dashboard-tabs-root [role="tabpanel"] {
  padding-top: 0.5rem;
}

/* Tab active state */
.dashboard-tabs-root [data-state="active"] {
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dashboard-tabs-root [data-state="active"] .custom-tab-text {
  font-weight: 600;
}

/* Tab inactive state */
.dashboard-tabs-root [data-state="inactive"] {
  background: transparent;
  opacity: 0.85;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard-tabs-root [data-state="inactive"]:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Dark mode hover adjustments */
:root[class~="dark"] .dashboard-tabs-root [data-state="inactive"]:hover {
  background: rgba(30, 95, 49, 0.15);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Tab bottom indicator animation */
.dashboard-tabs-root [role="tab"]::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  transform: translateX(-50%);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard-tabs-root [data-state="active"]::before {
  width: 100%;
}

/* Tab icon animation */
.dashboard-tabs-root .custom-tab-icon {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard-tabs-root [data-state="active"] .custom-tab-icon {
  transform: scale(1.1);
}

/* Color-specific styling for each tab type */
.dashboard-tabs-root [data-value="overview"][data-state="active"]::before,
.dashboard-tabs-root [data-value="vehicle"][data-state="active"]::before,
.dashboard-tabs-root [data-value="shipment"][data-state="active"]::before,
.dashboard-tabs-root [data-value="invoice"][data-state="active"]::before,
.dashboard-tabs-root [data-value="mix-invoice"][data-state="active"]::before {
  background: linear-gradient(to right, #10b981, #34d399);
}

/* Focus state for accessibility */
.dashboard-tabs-root [role="tab"]:focus-visible {
  outline: 2px solid rgba(16, 185, 129, 0.6);
  outline-offset: 2px;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .dashboard-tabs-container::before {
    background: linear-gradient(to right, rgba(30, 95, 49, 0), rgba(30, 95, 49, 0.3), rgba(30, 95, 49, 0));
  }

  .dashboard-tabs-root {
    background: rgba(30, 95, 49, 0.05);
    border-color: rgba(30, 95, 49, 0.32);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .dashboard-tabs-root [role="tablist"] {
    background-color: rgba(30, 95, 49, 0.32);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .dashboard-tabs-root [data-state="inactive"]:hover {
    background: rgba(30, 95, 49, 0.15);
    box-shadow: 0 1px 12px -3px rgba(0, 0, 0, 0.3), 0 1px 4px -1px rgba(0, 0, 0, 0.1);
  }

  /* Glow effects for active tabs in dark mode */
  .dashboard-tabs-root [data-value="overview"][data-state="active"],
  .dashboard-tabs-root [data-value="vehicle"][data-state="active"],
  .dashboard-tabs-root [data-value="shipment"][data-state="active"],
  .dashboard-tabs-root [data-value="invoice"][data-state="active"],
  .dashboard-tabs-root [data-value="mix-invoice"][data-state="active"] {
    background: linear-gradient(to right, rgba(30, 95, 49, 0.4), rgba(30, 95, 49, 0.2));
  }
}
