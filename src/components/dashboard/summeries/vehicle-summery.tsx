"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ArrowUpDown, Loader, Car, MapPin, SquareSigma } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { colorSystem } from "@/lib/constant";
import { useDashboard } from "@/components/dashboard/dashboard-services";

export type VehicleSummaryData = {
  id: string;
  location_name: string;
  auction_unpaid: number;
  auction_paid: number;
  on_the_way: number;
  on_hand_no_title: number;
  on_hand_with_title: number;
  on_hand_with_load: number;
  shipped: number;
  location_id: string;
};

export function VehicleSummery() {
  const t = useTranslations("dashboard");

  // Define column groups for better organization with direct text

  const columns: ColumnDef<VehicleSummaryData>[] = [
    {
      accessorKey: "location_name",
      header: ({ column }) => (
        <div className="flex items-center justify-start">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-semibold px-1"
          >
            <MapPin className="h-4 w-4 mr-2 text-primary" />
            <span>{t("overview.vehicle-summery.location")}</span>
            <ArrowUpDown className="ml-2 h-4 w-4 opacity-70" />
          </Button>
        </div>
      ),
      cell: ({ row }) => (
        <div className="font-medium flex items-center">
          <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
          <span className="truncate max-w-[120px]">
            {row.getValue("location_name")}
          </span>
        </div>
      ),
      size: 65,
    },
    {
      accessorKey: "auction_unpaid",
      header: ({ column }) => (
        <div className="text-center">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-semibold p-0 h-auto hover:bg-muted/80"
          >
            <div className="flex flex-col items-center">
              <span className="whitespace-nowrap">
                {t("overview.vehicle-summery.auction-unpaid")}
              </span>
              <ArrowUpDown className="h-3 w-3 mt-0.5 opacity-70" />
            </div>
          </Button>
        </div>
      ),
      cell: ({ row }) => {
        const value = row.getValue("auction_unpaid") as number;
        return (
          <Link
            href={`/en/vehicles/auction_unpaid/?loc=${row.original.location_id}`}
            className="hover:underline flex justify-center items-center"
          >
            <Badge
              variant={value > 0 ? "default" : "outline"}
              className={cn(
                "font-semibold",
                value > 0
                  ? `${colorSystem.auction_unpaid.bg} ${colorSystem.auction_unpaid.txt}`
                  : ""
              )}
            >
              {value}
            </Badge>
          </Link>
        );
      },
      size: 60,
    },
    {
      accessorKey: "auction_paid",
      header: ({ column }) => (
        <div className="text-center">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-semibold p-0 h-auto hover:bg-muted/80"
          >
            <div className="flex flex-col items-center">
              <span className="whitespace-nowrap">
                {t("overview.vehicle-summery.auction-paid")}
              </span>
              <ArrowUpDown className="h-3 w-3 mt-0.5 opacity-70" />
            </div>
          </Button>
        </div>
      ),
      cell: ({ row }) => {
        const value = row.getValue("auction_paid") as number;
        return (
          <Link
            href={`/en/vehicles/auction_paid/?loc=${row.original.location_id}`}
            className="hover:underline flex justify-center items-center"
          >
            <Badge
              variant={value > 0 ? "default" : "outline"}
              className={cn(
                "font-semibold",
                value > 0
                  ? `${colorSystem.auction_paid.bg} ${colorSystem.auction_paid.txt}`
                  : ""
              )}
            >
              {value}
            </Badge>
          </Link>
        );
      },
      size: 60,
    },
    {
      accessorKey: "on_the_way",
      header: ({ column }) => (
        <div className="text-center">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-semibold p-0 h-auto hover:bg-muted/80"
          >
            <div className="flex flex-col items-center">
              <span className="whitespace-nowrap">
                {t("overview.vehicle-summery.on-the-way")}
              </span>
              <ArrowUpDown className="h-3 w-3 mt-0.5 opacity-70" />
            </div>
          </Button>
        </div>
      ),
      cell: ({ row }) => {
        const value = row.getValue("on_the_way") as number;
        return (
          <Link
            href={`/en/vehicles/on_the_way/?loc=${row.original.location_id}`}
            className="hover:underline flex justify-center items-center"
          >
            <Badge
              variant={value > 0 ? "default" : "outline"}
              className={cn(
                "font-semibold",
                value > 0
                  ? `${colorSystem.on_the_way.bg} ${colorSystem.on_the_way.txt}`
                  : ""
              )}
            >
              {value}
            </Badge>
          </Link>
        );
      },
      size: 50,
    },
    {
      accessorKey: "on_hand_no_title",
      header: ({ column }) => (
        <div className="text-center">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-semibold p-0 h-auto hover:bg-muted/80"
          >
            <div className="flex flex-col items-center">
              <span className="whitespace-nowrap">
                {t("overview.vehicle-summery.on-the-hand-no")}
              </span>
              <ArrowUpDown className="h-3 w-3 mt-0.5 opacity-70" />
            </div>
          </Button>
        </div>
      ),
      cell: ({ row }) => {
        const value = row.getValue("on_hand_no_title") as number;
        return (
          <Link
            href={`/en/vehicles/on_hand_no_title/?loc=${row.original.location_id}`}
            className="hover:underline flex justify-center items-center"
          >
            <Badge
              variant={value > 0 ? "default" : "outline"}
              className={cn(
                "font-semibold",
                value > 0
                  ? `${colorSystem.on_hand_no_title.bg} ${colorSystem.on_hand_no_title.txt}`
                  : ""
              )}
            >
              {value}
            </Badge>
          </Link>
        );
      },
      size: 60,
    },
    {
      accessorKey: "on_hand_with_title",
      header: ({ column }) => (
        <div className="text-center">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-semibold p-0 h-auto hover:bg-muted/80"
          >
            <div className="flex flex-col items-center">
              <span className="whitespace-nowrap">
                {t("overview.vehicle-summery.on-the-hand-with")}
              </span>
              <ArrowUpDown className="h-3 w-3 mt-0.5 opacity-70" />
            </div>
          </Button>
        </div>
      ),
      cell: ({ row }) => {
        const value = row.getValue("on_hand_with_title") as number;
        return (
          <Link
            href={`/en/vehicles/on_hand_with_title/?loc=${row.original.location_id}`}
            className="hover:underline flex justify-center items-center"
          >
            <Badge
              variant={value > 0 ? "default" : "outline"}
              className={cn(
                "font-semibold",
                value > 0
                  ? `${colorSystem.on_hand_with_title.bg} ${colorSystem.on_hand_with_title.txt}`
                  : ""
              )}
            >
              {value}
            </Badge>
          </Link>
        );
      },
      size: 60,
    },
    {
      accessorKey: "on_hand_with_load",
      header: ({ column }) => (
        <div className="text-center">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-semibold p-0 h-auto hover:bg-muted/80"
          >
            <div className="flex flex-col items-center">
              <span className="whitespace-nowrap">
                {t("overview.vehicle-summery.on-the-hand-with-load")}
              </span>
              <ArrowUpDown className="h-3 w-3 mt-0.5 opacity-70" />
            </div>
          </Button>
        </div>
      ),
      cell: ({ row }) => {
        const value = row.getValue("on_hand_with_load") as number;
        return (
          <Link
            href={`/en/vehicles/on_hand_with_load/?loc=${row.original.location_id}`}
            className="hover:underline flex justify-center items-center"
          >
            <Badge
              variant={value > 0 ? "default" : "outline"}
              className={cn(
                "font-semibold",
                value > 0
                  ? `${colorSystem.on_hand_with_load.bg} ${colorSystem.on_hand_with_load.txt}`
                  : ""
              )}
            >
              {value}
            </Badge>
          </Link>
        );
      },
      size: 60,
    },
    {
      accessorKey: "shipped",
      header: ({ column }) => (
        <div className="text-center">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-semibold p-0 h-auto hover:bg-muted/80"
          >
            <div className="flex flex-col items-center">
              <span className="whitespace-nowrap">
                {t("overview.vehicle-summery.shipped")}
              </span>
              <ArrowUpDown className="h-3 w-3 mt-0.5 opacity-70" />
            </div>
          </Button>
        </div>
      ),
      cell: ({ row }) => {
        const value = row.getValue("shipped") as number;
        return (
          <Link
            href={`/en/vehicles/shipped/?loc=${row.original.location_id}`}
            className="hover:underline flex justify-center items-center"
          >
            <Badge
              variant={value > 0 ? "default" : "outline"}
              className={cn(
                "font-semibold",
                value > 0
                  ? `${colorSystem.shipped.bg} ${colorSystem.shipped.txt}`
                  : ""
              )}
            >
              {value}
            </Badge>
          </Link>
        );
      },
      size: 50,
    },
    {
      id: "total",
      header: ({ column }) => (
        <div className="text-center">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-semibold p-0 h-auto hover:bg-muted/80"
          >
            <div className="flex flex-col items-center">
              <span className="whitespace-nowrap">
                {t("overview.vehicle-summery.total")}
              </span>
              <ArrowUpDown className="h-3 w-3 mt-0.5 opacity-70" />
            </div>
          </Button>
        </div>
      ),
      cell: ({ row }) => {
        const total =
          (row.getValue("on_the_way") as number) +
          (row.getValue("on_hand_no_title") as number) +
          (row.getValue("on_hand_with_title") as number) +
          (row.getValue("on_hand_with_load") as number) +
          (row.getValue("shipped") as number) +
          (row.getValue("auction_paid") as number) +
          (row.getValue("auction_unpaid") as number);
        return (
          <Link
            href={`/en/vehicles/all/?loc=${row.original.location_id}`}
            className="hover:underline flex justify-center items-center"
          >
            <Badge variant="secondary" className="font-semibold">
              {total}
            </Badge>
          </Link>
        );
      },
      size: 55,
    },
  ];

  const [sorting, setSorting] = React.useState<SortingState>([
    { id: "location_name", desc: false },
  ]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});

  const { data, isLoading } = useDashboard();
  const vehicle = (data?.data?.vehicle as VehicleSummaryData[]) || [];
  // Calculate totals
  const totals = React.useMemo(() => {
    return vehicle.reduce(
      (acc, row) => ({
        auction_unpaid: acc.auction_unpaid + row.auction_unpaid,
        auction_paid: acc.auction_paid + row.auction_paid,
        on_the_way: acc.on_the_way + row.on_the_way,
        on_hand_no_title: acc.on_hand_no_title + row.on_hand_no_title,
        on_hand_with_title: acc.on_hand_with_title + row.on_hand_with_title,
        on_hand_with_load: acc.on_hand_with_load + row.on_hand_with_load,
        shipped: acc.shipped + row.shipped,
        total:
          acc.total +
          row.on_the_way +
          row.on_hand_no_title +
          row.on_hand_with_title +
          row.on_hand_with_load +
          row.shipped +
          row.auction_paid +
          row.auction_unpaid,
      }),
      {
        auction_unpaid: 0,
        auction_paid: 0,
        on_the_way: 0,
        on_hand_no_title: 0,
        on_hand_with_title: 0,
        on_hand_with_load: 0,
        shipped: 0,
        total: 0,
      }
    );
  }, [data, vehicle]);

  const table = useReactTable({
    data: vehicle,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
    columnResizeMode: "onChange",
  });

  if (isLoading)
    return (
      <div className="flex justify-center items-center h-80 w-full">
        <Card className="w-full h-full flex items-center justify-center  dark:border-green-500/10">
          <CardContent className="flex flex-col items-center justify-center p-6">
            <Loader className="h-8 w-8 animate-spin text-primary dark:text-green-400 mb-2" />
            <p className="text-sm text-muted-foreground dark:text-green-100/80">
              {t("overview.vehicle-summery.loading")}
            </p>
          </CardContent>
        </Card>
      </div>
    );

  if (vehicle.length === 0) {
    return (
      <div className="flex justify-center items-center h-80 w-full">
        <Card className="w-full h-full flex items-center justify-center  dark:border-green-500/10">
          <CardContent className="flex flex-col items-center justify-center p-6">
            <Car className="h-8 w-8 text-muted-foreground dark:text-green-400 mb-2" />
            <p className="text-sm text-muted-foreground dark:text-green-100/80">
              {t("overview.vehicle-summery.no-data")}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border shadow-sm dark:border-green-500/10 dark:shadow-green-900/30">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader className="bg-muted/60  sticky top-0 z-10">
              {/* Column Group Headers */}
              <TableRow className="border-b-2 border-border dark:border-green-500/10">
                {/* Location column header (spans 1 row, 1 column) */}
                <TableHead
                  className="py-2 text-center font-semibold bg-muted/70  border-r-2 border-border/30 dark:border-green-500/10"
                  colSpan={1}
                  rowSpan={2}
                  style={{ width: 180 }}
                >
                  <div className="flex items-center justify-center h-full">
                    <Button
                      variant="ghost"
                      onClick={() =>
                        table
                          .getColumn("location_name")
                          ?.toggleSorting(
                            table.getColumn("location_name")?.getIsSorted() ===
                              "asc"
                          )
                      }
                      className="font-semibold px-1 hover:bg-muted/80  dark:text-white"
                    >
                      <MapPin className="h-4 w-4 mr-2 text-primary dark:text-green-300" />
                      <span>{t("overview.vehicle-summery.location")}</span>
                      <ArrowUpDown className="ml-2 h-4 w-4 opacity-70" />
                    </Button>
                  </div>
                </TableHead>

                {/* Auction group (spans 1 row, 2 columns) */}
                <TableHead
                  className="py-2 text-center font-semibold bg-muted/70  border-r-2 border-border/70 dark:border-green-500/10"
                  colSpan={2}
                >
                  <div className="flex items-center justify-center">
                    <span className="dark:text-white">
                      {t("overview.vehicle-summery.auction")}
                    </span>
                  </div>
                </TableHead>

                {/* On The Way column header (spans 1 row, 1 column)  */}
                <TableHead
                  className="py-2 text-center font-semibold bg-muted/70  border-r-2 border-border/70 dark:border-green-500/10"
                  colSpan={1}
                  rowSpan={2}
                  style={{ width: 90 }}
                >
                  <div className="flex items-center justify-center">
                    <span className="dark:text-white">
                      {t("overview.vehicle-summery.on-the-way")}
                    </span>
                  </div>
                </TableHead>

                {/* On Hand group (spans 1 row, 3 columns) */}
                <TableHead
                  className="py-2 text-center font-semibold bg-muted/70  border-r-2 border-border/70 dark:border-green-500/10"
                  colSpan={3}
                >
                  <div className="flex items-center justify-center">
                    <span className="dark:text-white">
                      {t("overview.vehicle-summery.on-hand")}
                    </span>
                  </div>
                </TableHead>

                {/* Shipping group (spans 1 row, 2 columns) */}
                <TableHead
                  className="py-2 text-center font-semibold bg-muted/70  border-r-2 border-border/70 dark:border-green-500/10"
                  colSpan={1}
                >
                  <div className="flex items-center justify-center">
                    <span className="dark:text-white">
                      {t("overview.vehicle-summery.shipping")}
                    </span>
                  </div>
                </TableHead>

                {/* Total column header (spans 1 row, 1 column) */}
                <TableHead
                  className="py-2 text-center font-semibold bg-muted/70 "
                  colSpan={1}
                  rowSpan={2}
                  style={{ width: 90 }}
                >
                  <div className="flex items-center justify-center h-full">
                    <span className="dark:text-white">
                      {t("overview.vehicle-summery.total")}
                    </span>
                  </div>
                </TableHead>
              </TableRow>

              {/* Individual Column Headers */}
              <TableRow className="border-b-2 border-border dark:border-green-500/10">
                {/* Auction Unpaid */}
                <TableHead className="py-2 text-center font-medium text-sm border-r border-border/50 dark:border-green-500/10">
                  <Button
                    variant="ghost"
                    onClick={() =>
                      table
                        .getColumn("auction_unpaid")
                        ?.toggleSorting(
                          table.getColumn("auction_unpaid")?.getIsSorted() ===
                            "asc"
                        )
                    }
                    className="font-medium text-xs p-0 h-auto hover:bg-muted/80  dark:text-white"
                  >
                    <div className="flex flex-col items-center">
                      <span className="whitespace-nowrap">
                        {t("overview.vehicle-summery.unpaid")}
                      </span>
                      <ArrowUpDown className="h-3 w-3 mt-0.5 opacity-70" />
                    </div>
                  </Button>
                </TableHead>

                {/* Auction Paid */}
                <TableHead className="py-2 text-center font-medium text-sm border-r-2 border-border/70 dark:border-green-500/10">
                  <Button
                    variant="ghost"
                    onClick={() =>
                      table
                        .getColumn("auction_paid")
                        ?.toggleSorting(
                          table.getColumn("auction_paid")?.getIsSorted() ===
                            "asc"
                        )
                    }
                    className="font-medium text-xs p-0 h-auto   dark:text-white"
                  >
                    <div className="flex flex-col items-center">
                      <span className="whitespace-nowrap">
                        {t("overview.vehicle-summery.paid")}
                      </span>
                      <ArrowUpDown className="h-3 w-3 mt-0.5 opacity-70" />
                    </div>
                  </Button>
                </TableHead>

                {/* On Hand No Title */}
                <TableHead className="py-2 text-center font-medium text-sm border-r border-border/50 dark:border-green-500/10">
                  <Button
                    variant="ghost"
                    onClick={() =>
                      table
                        .getColumn("on_hand_no_title")
                        ?.toggleSorting(
                          table.getColumn("on_hand_no_title")?.getIsSorted() ===
                            "asc"
                        )
                    }
                    className="font-medium text-xs p-0 h-auto hover:bg-muted/80  dark:text-white"
                  >
                    <div className="flex flex-col items-center">
                      <span className="whitespace-nowrap">
                        {t("overview.vehicle-summery.no-title")}
                      </span>
                      <ArrowUpDown className="h-3 w-3 mt-0.5 opacity-70" />
                    </div>
                  </Button>
                </TableHead>

                {/* On Hand With Title */}
                <TableHead className="py-2 text-center font-medium text-sm border-r border-border/50 dark:border-green-500/10">
                  <Button
                    variant="ghost"
                    onClick={() =>
                      table
                        .getColumn("on_hand_with_title")
                        ?.toggleSorting(
                          table
                            .getColumn("on_hand_with_title")
                            ?.getIsSorted() === "asc"
                        )
                    }
                    className="font-medium text-xs p-0 h-auto hover:bg-muted/80  dark:text-white"
                  >
                    <div className="flex flex-col items-center">
                      <span className="whitespace-nowrap">
                        {t("overview.vehicle-summery.with-title")}
                      </span>
                      <ArrowUpDown className="h-3 w-3 mt-0.5 opacity-70" />
                    </div>
                  </Button>
                </TableHead>

                {/* On Hand With Load */}
                <TableHead className="py-2 text-center font-medium text-sm border-r-2 border-border/70 dark:border-green-500/10">
                  <Button
                    variant="ghost"
                    onClick={() =>
                      table
                        .getColumn("on_hand_with_load")
                        ?.toggleSorting(
                          table
                            .getColumn("on_hand_with_load")
                            ?.getIsSorted() === "asc"
                        )
                    }
                    className="font-medium text-xs p-0 h-auto hover:bg-muted/80  dark:text-white"
                  >
                    <div className="flex flex-col items-center">
                      <span className="whitespace-nowrap">
                        {t("overview.vehicle-summery.with-load")}
                      </span>
                      <ArrowUpDown className="h-3 w-3 mt-0.5 opacity-70" />
                    </div>
                  </Button>
                </TableHead>

                {/* Shipped */}
                <TableHead className="py-2 text-center font-medium text-sm border-r-2 border-border/70 dark:border-green-500/10">
                  <Button
                    variant="ghost"
                    onClick={() =>
                      table
                        .getColumn("shipped")
                        ?.toggleSorting(
                          table.getColumn("shipped")?.getIsSorted() === "asc"
                        )
                    }
                    className="font-medium text-xs p-0 h-auto hover:bg-muted/80  dark:text-white"
                  >
                    <div className="flex flex-col items-center">
                      <span className="whitespace-nowrap">
                        {t("overview.vehicle-summery.shipped")}
                      </span>
                      <ArrowUpDown className="h-3 w-3 mt-0.5 opacity-70" />
                    </div>
                  </Button>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row, index) => (
                  <TableRow
                    key={row.id}
                    className={`${
                      index % 2 === 0 ? " dark:bg-transparent" : "bg-muted/20 "
                    } hover:bg-muted/50  transition-colors`}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell
                        key={cell.id}
                        className="dark:text-white dark:border-green-500/10"
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="text-center h-24 dark:text-white"
                  >
                    No results.
                  </TableCell>
                </TableRow>
              )}
              {/* Totals Row */}
              <TableRow className="font-bold">
                <TableCell className="flex items-center gap-2">
                  <SquareSigma className="h-4 w-4 text-muted-foreground" />
                  {t("overview.vehicle-summery.total")}
                </TableCell>
                <TableCell>
                  <Link
                    href={`en/vehicles/auction_unpaid?per_page=${totals.auction_unpaid}`}
                    className="hover:underline flex justify-center items-center"
                  >
                    {totals.auction_unpaid}
                  </Link>
                </TableCell>
                <TableCell>
                  <Link
                    href={`en/vehicles/auction_paid?per_page=${totals.auction_paid}`}
                    className="hover:underline flex justify-center items-center"
                  >
                    {totals.auction_paid}
                  </Link>
                </TableCell>
                <TableCell>
                  <Link
                    href={`en/vehicles/on_the_way?per_page=${totals.on_the_way}`}
                    className="hover:underline flex justify-center items-center"
                  >
                    {totals.on_the_way}
                  </Link>
                </TableCell>
                <TableCell>
                  <Link
                    href={`en/vehicles/on_hand_no_title?per_page=${totals.on_hand_no_title}`}
                    className="hover:underline flex justify-center items-center"
                  >
                    {totals.on_hand_no_title}
                  </Link>
                </TableCell>

                <TableCell>
                  <Link
                    href={`en/vehicles/on_hand_with_title?per_page=${totals.on_hand_with_title}`}
                    className="hover:underline flex justify-center items-center"
                  >
                    {totals.on_hand_with_title}
                  </Link>
                </TableCell>

                <TableCell>
                  <Link
                    href={`en/vehicles/on_hand_with_load?per_page=${totals.on_hand_with_load}`}
                    className="hover:underline flex justify-center items-center"
                  >
                    {totals.on_hand_with_load}
                  </Link>
                </TableCell>

                <TableCell>
                  <Link
                    href={`en/vehicles/shipped?per_page=${totals.shipped}`}
                    className="hover:underline flex justify-center items-center"
                  >
                    {totals.shipped}
                  </Link>
                </TableCell>

                <TableCell>
                  <Link
                    href={`en/vehicles/all?per_page=${totals.total}`}
                    className="hover:underline flex justify-center items-center"
                  >
                    {totals.total}
                  </Link>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}

export default VehicleSummery;
