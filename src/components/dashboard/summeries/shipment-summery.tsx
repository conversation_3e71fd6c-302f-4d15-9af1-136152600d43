"use client";

import React from "react";
import {
  ColumnDef,
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  flexRender,
  SortingState,
  ColumnFiltersState,
  VisibilityState,
} from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ArrowUpDown, Loader, Ship, Anchor, Truck } from "lucide-react";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { colorSystem } from "@/lib/constant";
import { useDashboard } from "@/components/dashboard/dashboard-services";

export type ShipmentSummaryData = {
  location_id: string;
  location_name: string;
  at_loading: number;
  arrived: number;
  on_the_way: number;
  total: number;
};

export function ShipmentSummery() {
  const t = useTranslations("dashboard");
  const { data, isLoading } = useDashboard();

  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});

  const shipment = React.useMemo(
    () => (data?.data?.shipment as ShipmentSummaryData[]) || [],
    [data?.data?.shipment]
  );
  // Calculate totals
  const totals = React.useMemo(() => {
    return shipment.reduce(
      (acc, row) => ({
        at_loading: acc.at_loading + row.at_loading,
        arrived: acc.arrived + row.arrived,
        on_the_way: acc.on_the_way + row.on_the_way,
        total: acc.total + row.total,
      }),
      {
        at_loading: 0,
        arrived: 0,
        on_the_way: 0,
        total: 0,
      }
    );
  }, [shipment]);

  const columns: ColumnDef<ShipmentSummaryData>[] = [
    {
      accessorKey: "location_name",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="font-semibold hover:bg-muted/80 dark:hover:bg-green-100/5 dark:text-white"
        >
          {t("overview.shipment-summery.location")}
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="font-medium flex items-center">
          <Truck className="h-4 w-4 mr-2 text-muted-foreground dark:text-green-400/50" />
          {row.getValue("location_name")}
        </div>
      ),
    },
    {
      accessorKey: "at_loading",
      header: ({ column }) => (
        <div className="text-center">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-semibold text-sm px-2 hover:bg-muted/80 dark:hover:bg-green-100/5 dark:text-white"
          >
            {t("overview.shipment-summery.at-loading")}
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        </div>
      ),
      cell: ({ row }) => {
        const value = row.getValue("at_loading") as number;
        return (
          <div className="text-center">
            <Link
              href={`en/shipments/at_loading?loc=${row.original.location_id}`}
              className="hover:underline inline-block"
            >
              <Badge
                variant={value > 0 ? "default" : "outline"}
                className={cn(
                  "font-semibold",
                  value > 0
                    ? `${colorSystem.at_loading.bg} ${colorSystem.at_loading.txt}`
                    : "dark:text-white dark:border-green-500/20"
                )}
              >
                <Truck className="h-3 w-3 mr-1" />
                {value}
              </Badge>
            </Link>
          </div>
        );
      },
      size: 100,
    },
    {
      accessorKey: "on_the_way",
      header: ({ column }) => (
        <div className="text-center">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-semibold text-sm px-2 hover:bg-muted/80 dark:hover:bg-green-100/5 dark:text-white"
          >
            {t("overview.shipment-summery.on-the-way")}
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        </div>
      ),
      cell: ({ row }) => {
        const value = row.getValue("on_the_way") as number;
        return (
          <div className="text-center">
            <Link
              href={`en/shipments/on_the_way?loc=${row.original.location_id}`}
              className="hover:underline inline-block"
            >
              <Badge
                variant={value > 0 ? "default" : "outline"}
                className={cn(
                  "font-semibold",
                  value > 0
                    ? `${colorSystem.on_the_way.bg} ${colorSystem.on_the_way.txt}`
                    : "dark:text-white dark:border-green-500/20"
                )}
              >
                <Ship className="h-3 w-3 mr-1" />
                {value}
              </Badge>
            </Link>
          </div>
        );
      },
      size: 100,
    },
    {
      accessorKey: "arrived",
      header: ({ column }) => (
        <div className="text-center">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-semibold text-sm px-2 hover:bg-muted/80 dark:hover:bg-green-100/5 dark:text-white"
          >
            {t("overview.shipment-summery.arrived")}
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        </div>
      ),
      cell: ({ row }) => {
        const value = row.getValue("arrived") as number;
        return (
          <div className="text-center">
            <Link
              href={`en/shipments/arrived?loc=${row.original.location_id}`}
              className="hover:underline inline-block"
            >
              <Badge
                variant={value > 0 ? "default" : "outline"}
                className={cn(
                  "font-semibold",
                  value > 0
                    ? `${colorSystem.arrived.bg} ${colorSystem.arrived.txt}`
                    : "dark:text-white dark:border-green-500/20"
                )}
              >
                <Anchor className="h-3 w-3 mr-1" />
                {value}
              </Badge>
            </Link>
          </div>
        );
      },
      size: 100,
    },

    {
      accessorKey: "total",
      header: ({ column }) => (
        <div className="text-center">
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="font-semibold text-sm px-2 hover:bg-muted/80 dark:hover:bg-green-100/5 dark:text-white"
          >
            {t("overview.shipment-summery.total")}
            <ArrowUpDown className="ml-1 h-3 w-3" />
          </Button>
        </div>
      ),
      cell: ({ row }) => {
        const value = row.getValue("total") as number;
        return (
          <div className="text-center">
            <Link
              href={`en/shipments/all/?loc=${row.original.location_id}`}
              className="hover:underline inline-block"
            >
              <Badge
                variant="secondary"
                className="font-semibold dark:text-white dark:bg-green-500/30 dark:hover:bg-green-500/40"
              >
                {value}
              </Badge>
            </Link>
          </div>
        );
      },
      size: 100,
    },
  ];

  const table = useReactTable({
    data: shipment,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
    columnResizeMode: "onChange",
  });

  if (isLoading)
    return (
      <div className="flex justify-center items-center h-80 w-full">
        <Card className="w-full h-full flex items-center justify-center dark:bg-green-500/5 dark:border-green-500/10">
          <CardContent className="flex flex-col items-center justify-center p-6">
            <Loader className="h-8 w-8 animate-spin text-primary dark:text-green-400 mb-2" />
            <p className="text-sm text-muted-foreground dark:text-green-100/80">
              {t("overview.shipment-summery.loading")}
            </p>
          </CardContent>
        </Card>
      </div>
    );

  if (shipment.length === 0) {
    return (
      <div className="flex justify-center items-center h-80 w-full">
        <Card className="w-full h-full flex items-center justify-center  dark:border-green-500/10">
          <CardContent className="flex flex-col items-center justify-center p-6">
            <Ship className="h-8 w-8 text-muted-foreground dark:text-green-400 mb-2" />
            <p className="text-sm text-muted-foreground dark:text-green-100/80">
              {t("overview.shipment-summery.no-data")}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border shadow-sm dark:border-green-500/20 ">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader className="bg-muted/50  sticky top-0 z-10">
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow
                  key={headerGroup.id}
                  className="hover:bg-muted/50  dark:border-green-500/20"
                >
                  {headerGroup.headers.map((header) => (
                    <TableHead
                      key={header.id}
                      className="py-3 dark:text-white"
                      style={{
                        width:
                          header.column.getSize() === 160
                            ? "auto"
                            : header.column.getSize(),
                      }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row, i) => (
                  <TableRow
                    key={row.id}
                    className={cn(
                      "transition-colors hover:bg-muted/50 ",
                      i % 2 === 0
                        ? "bg-background dark:bg-transparent"
                        : "bg-muted/20 "
                    )}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell
                        key={cell.id}
                        className="py-3 dark:text-white dark:border-green-500/30"
                        style={{
                          width:
                            cell.column.getSize() === 160
                              ? "auto"
                              : cell.column.getSize(),
                        }}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="text-center h-24 dark:text-white"
                  >
                    {t("overview.shipment-summery.no-results")}
                  </TableCell>
                </TableRow>
              )}
              <TableRow className="bg-muted/30  font-bold sticky bottom-0 z-10 border-t-2 dark:border-green-500/20">
                <TableCell className="py-3 dark:text-white">
                  <div className="font-medium flex items-center">
                    <Ship className="h-4 w-4 mr-2 text-primary dark:text-green-300/80" />
                    {t("overview.shipment-summery.total")}
                  </div>
                </TableCell>
                <TableCell className="text-center py-3">
                  <Link
                    href={`en/shipments/at_loading?per_page=${totals.at_loading}`}
                    className="hover:underline inline-block"
                  >
                    <Badge
                      variant={totals.at_loading > 0 ? "default" : "outline"}
                      className={cn(
                        "font-semibold",
                        totals.at_loading > 0
                          ? `${colorSystem.at_loading.bg} ${colorSystem.at_loading.txt}`
                          : "dark:text-white dark:border-green-500/20"
                      )}
                    >
                      <Truck className="h-3 w-3 mr-1" />
                      {totals.at_loading}
                    </Badge>
                  </Link>
                </TableCell>
                <TableCell className="text-center py-3">
                  <Link
                    href={`en/shipments/on_the_way?per_page=${totals.on_the_way}`}
                    className="hover:underline inline-block"
                  >
                    <Badge
                      variant={totals.on_the_way > 0 ? "default" : "outline"}
                      className={cn(
                        "font-semibold",
                        totals.on_the_way > 0
                          ? `${colorSystem.on_the_way.bg} ${colorSystem.on_the_way.txt}`
                          : "dark:text-white dark:border-green-500/20"
                      )}
                    >
                      <Ship className="h-3 w-3 mr-1" />
                      {totals.on_the_way}
                    </Badge>
                  </Link>
                </TableCell>
                <TableCell className="text-center py-3">
                  <Link
                    href={`en/shipments/arrived?per_page=${totals.arrived}`}
                    className="hover:underline inline-block"
                  >
                    <Badge
                      variant={totals.arrived > 0 ? "default" : "outline"}
                      className={cn(
                        "font-semibold",
                        totals.arrived > 0
                          ? `${colorSystem.arrived.bg} ${colorSystem.arrived.txt}`
                          : "dark:text-white dark:border-green-500/20"
                      )}
                    >
                      <Anchor className="h-3 w-3 mr-1" />
                      {totals.arrived}
                    </Badge>
                  </Link>
                </TableCell>

                <TableCell className="text-center py-3">
                  <Link
                    href={`en/shipments/all?per_page=${totals.total}`}
                    className="hover:underline inline-block"
                  >
                    <Badge
                      variant="secondary"
                      className="font-semibold text-foreground dark:text-white dark:bg-green-500/30 dark:hover:bg-green-500/20"
                    >
                      {totals.total}
                    </Badge>
                  </Link>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
