"use client";
import React from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { usePathname, useRouter } from "@/i18n/routing";
import { useLocale, useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import { geistMono, geistSans, notoSansArabic } from "@/lib/Fonts";
import Image from "next/image";
import { cn } from "@/lib/utils";

export default function Language({ className }: { className?: string }) {
  const pathname = usePathname();
  const router = useRouter();
  const locale = useLocale();
  const t = useTranslations("lang");
  const params = useParams();

  function chnageLocale(value: string) {
    router.replace(
      // @ts-expect-error -- TypeScript will validate that only known `params`
      { pathname, params },
      { locale: value }
    );
  }

  return (
    <Select
      defaultValue={locale}
      onValueChange={chnageLocale}
      dir={locale === "ar" ? "rtl" : "ltr"}
    >
      <SelectTrigger
        className={cn(
          `rounded-full bg-primary/10 transition-colors ${geistSans.variable} ${
            geistMono.variable
          } antialiased ${
            locale === "ar" ? notoSansArabic.className : ""
          } [&>svg]:hidden md:[&>svg]:block`,
          className
        )}
      >
        <SelectValue placeholder="Language">
          <div className="flex justify-center items-center gap-x-2 w-full h-full">
            <Image
              src={`/flags/${locale}-flag.svg`}
              alt={locale}
              className="w-full h-full object-cover rounded-full md:w-5 md:h-5 md:rounded-sm transition-all duration-200"
              width={40}
              height={40}
            />
            <span className="hidden md:inline text-xs md:text-sm font-medium">
              {t(`${locale}`)}
            </span>
          </div>
        </SelectValue>
      </SelectTrigger>
      <SelectContent
        className={`${geistSans.variable} ${geistMono.variable} antialiased ${
          locale === "ar" ? notoSansArabic.className : ""
        } `}
      >
        <SelectGroup>
          <SelectLabel>{t("label")}</SelectLabel>
          {["en", "ru", "ka", "ar"].map((code) => (
            <SelectItem
              key={code}
              value={code}
              dir={locale === "ar" ? "rtl" : "ltr"}
            >
              <div className="flex justify-center items-center gap-x-2">
                <Image
                  src={`/flags/${code}-flag.svg`}
                  alt={code}
                  className="h-4 w-4 rounded-sm transition-all duration-100"
                  width={40}
                  height={40}
                />
                <span className="text-xs md:text-sm font-medium">
                  {t(`${code}`)}
                </span>
              </div>
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}
