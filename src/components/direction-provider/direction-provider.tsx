"use client";
import { useDirection } from "@/hooks/useDirection";
import { DirectionProvider } from "@radix-ui/react-direction";
import { SessionProvider } from "next-auth/react";
import React from "react";

export default function AppDirectionProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { dir } = useDirection();
  return (
    <SessionProvider>
      <DirectionProvider dir={dir}>{children}</DirectionProvider>
    </SessionProvider>
  );
}
