"use client";

import React, { forwardRef } from "react";
import { AgGridReact, AgGridReactProps } from "ag-grid-react";

import "ag-grid-enterprise";
import { LicenseManager, themeQuartz } from "ag-grid-enterprise";

LicenseManager.setLicenseKey(
  "ag-Grid_Evaluation_License_Not_for_Production_100Devs30_August_2037__MjU4ODczMzg3NzkyMg==9e93ed5f03b0620b142770f2594a23a2"
);

const AgGridDataTable = forwardRef<AgGridReact, AgGridReactProps>(
  (props, ref) => {
    const myTheme = themeQuartz.withParams({
      accentColor: "hsl(var(--primary))",
      backgroundColor: "hsl(var(--background))",
      foregroundColor: "hsl(var(--foreground))",
      headerFontSize: 14,
      spacing: 6,
      wrapperBorderRadius: 12,
      borderRadius: "var(--radius)",
      headerBackgroundColor: "hsl(var(--sidebar-background))",
      inputBackgroundColor: "hsl(var(--sidebar-background))",
      selectCellBackgroundColor: "hsl(var(--sidebar-background))",
      inputDisabledBackgroundColor: "hsl(var(--sidebar-background))",
      inputBorderRadius: "calc(var(--radius) - 3px)",
      inputPaddingStart: 10,
      checkboxBorderRadius: "calc(var(--radius) - 4px)",
      borderColor: "hsl(var(--sidebar-border))",
      checkboxCheckedShapeColor: "white",
      // columnBorder: "1px solid hsl(var(--sidebar-border) / 0.5)",
      fontFamily: {
        googleFont: "Inter, Inter Fallback",
      }
    });

    return (
      <>
        <style jsx global>{`
          .ag-input-field-input.ag-text-area-input {
            padding: 10px;
          }
          .ag-cell[col-id="ag-Grid-SelectionColumn"] {
            display: flex;
            align-items: center;
          }
        `}</style>
        <AgGridReact {...props} ref={ref} theme={myTheme} loadThemeGoogleFonts={false} />
      </>
    );
  }
);
AgGridDataTable.displayName = "AgGridDataTable";

export default AgGridDataTable;
