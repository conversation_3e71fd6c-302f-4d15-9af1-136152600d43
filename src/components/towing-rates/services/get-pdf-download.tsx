"use client";

import { useMutation } from "@tanstack/react-query";
import { Download, Loader } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useFetchClient } from "@/utils/axios";

const downloadPdf = async (
  activeTab: string,
  fetchClient: ReturnType<typeof useFetchClient>
) => {
  try {
    const response = await fetchClient(
      `/v2/towing-rates-v2/${
        activeTab === "complete" ? "towingRatePdf" : "halfCutRatePdf"
      }`,
      {
        responseType: "blob",
        headers: {
          Accept: "application/pdf",
        },
      }
    );

    const url = window.URL.createObjectURL(new Blob([response.data]));
    const a = document.createElement("a");
    a.href = url;
    a.download =
      activeTab === "complete"
        ? "complete-towing-rates.pdf"
        : "half-cut-towing-rates.pdf";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  } catch (e: any) {
    if (e.message === "Network Error") toast.error("No Internet Connection");
  }
};

const DownloadButton = ({ activeTab }: { activeTab: string }) => {
  const fetchClient = useFetchClient();
  const mutation = useMutation({
    mutationFn: () => downloadPdf(activeTab, fetchClient),
    onSuccess: () => toast.success("Download Successful!"),
    onError: () => {
      toast.error("Download Failed!");
    },
  });

  return (
    <Button
      variant={"outline"}
      size={"icon"}
      onClick={() => mutation.mutate()}
      disabled={mutation.isPending}
    >
      {mutation.isPending ? <Loader className="animate-spin" /> : <Download />}
    </Button>
  );
};

export default DownloadButton;
