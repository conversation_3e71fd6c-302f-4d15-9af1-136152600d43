"use server";

import api from "@/utils/axios-server";

type TowingRateParamType = {
  tab: string;
  page: number;
  per_page: number;
  search: string;
  exactMatch: boolean;
  filterData: string;
};

export async function getTowingRates({
  params,
}: {
  params: TowingRateParamType;
}) {
  try {
    const response = await api.get(`/v2/towing-rates-v2`, {
      params: { ...params },
    });
    return response.data;
  }catch (error: any) {
    throw new Error(error?.response?.data?.message);
  }
}
