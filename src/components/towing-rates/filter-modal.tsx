"use client";
import * as React from "react";
import { Sidebar } from "@/components/ui/sidebar";
import { RotateCcw } from "lucide-react";
import { Button } from "@/components/ui/button";

import FilterCollapse from "@/components/vehicles/filter-collapse";

import { ContainerSelector } from "@/components/Common_UI/auto-complete";

import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useGetAutoComplete } from "@/utils/use-get-autocomplete";
import { useTranslations } from "next-intl";
import { formatNumberByLocale } from "@/utils/helper-function";
import { localesTypes } from "@/i18n/routing";

export function FilterModel({}: React.ComponentProps<typeof Sidebar> & {}) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const urlParam = useParams();
  const t = useTranslations("filter-modal");
  const params = new URLSearchParams(Array.from(searchParams.entries()));
  // State to control collapsible open/close behavior
  const [openCollapse, toggleCollapse] = React.useState<string[]>([]);
  const clearSelectionsRef = React.useRef<(() => void) | null>(null);

  const {
    data: bkgData,
    isLoading: bgkIsLoading,
    refetch: bkgRefetch,
    handleSearch: handleStateSearch,
  } = useGetAutoComplete({
    model: "loading_states",
    column: "name",
    key: "states",
  });
  const {
    data: locData,
    isLoading: locIsLoading,
    refetch: locRefetch,
    handleSearch: handleLocSearch,
  } = useGetAutoComplete({
    column: "name",
    model: "locations",
    key: "loc",
  });
  const {
    data: branchData,
    isLoading: branchIsLoading,
    refetch: branchRefetch,
    handleSearch: handleBranchSearch,
  } = useGetAutoComplete({
    model: "loading_branches",
    column: "name",
    key: "branches",
  });

  const {
    data: cityData,
    isLoading: cityIsLoading,
    refetch: cityRefetch,
    handleSearch: handleCitySearch,
  } = useGetAutoComplete({
    model: "loading_cities",
    column: "city_name",
    key: "cities",
  });

  const handleSelectionStates = (selected: any[]) => {
    params.delete("states");
    selected?.forEach((select) => params.append("states", select.id));
    router.push(`?${params}`);
  };
  const handleSelectionBranches = (selected: any[]) => {
    params.delete("branches");
    selected?.forEach((select) => params.append("branches", select.id));
    router.push(`?${params}`);
  };
  const handleSelectionPointOfLocation = (selected: any[]) => {
    params.delete("loc");
    selected?.forEach((select) => params.append("loc", select.id));
    router.push(`?${params}`);
  };
  const handleSelectionPointOfCities = (selected: any[]) => {
    params.delete("city");
    selected?.forEach((select) => params.append("city", select.id));
    router.push(`?${params}`);
  };

  const handleClearFilters = () => {
    params.delete("states");
    params.delete("loc");
    params.delete("branches");
    params.delete("city");

    router.push(`?${params}`);
  };
  return (
    <div className="mt-1 p-2 w-72">
      <div className="flex flex-row pl-3 pt-2 justify-between py-2">
        <h2 className="text-lg font-medium">{t("filters")}</h2>
        <Button
          onClick={() => {
            clearSelectionsRef.current?.();
            handleClearFilters();
          }}
          variant={"link"}
          size={"icon"}
          className="clear-filters"
        >
          <RotateCcw />
        </Button>
      </div>
      <div className="flex flex-col p-2 gap-2">
        <FilterCollapse
          label={t("mix-shipping-rates-modal.state")}
          className="p-2"
          isOpen={openCollapse?.includes("states")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("states")
                ? prev.filter((item) => item !== "states")
                : [...prev, "states"]
            )
          }
        >
          <ContainerSelector
            data={
              bkgData?.data?.map((item: any) => ({
                ...item,
                checked: false,
              })) || []
            }
            isLoading={bgkIsLoading}
            onFetch={bkgRefetch}
            onSearch={handleStateSearch}
            onSelectionChange={handleSelectionStates}
            selectedLabel={(count) =>
              `${t("checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            unselectedLabel={(count) =>
              `${t("not-checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            labelKey="name"
            valueKey="id"
            clearSelectionsRef={clearSelectionsRef}
            searchParamsKey="states"
          />
        </FilterCollapse>
        <FilterCollapse
          label={t("mix-shipping-rates-modal.branch")}
          className="p-2"
          isOpen={openCollapse?.includes("branches")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("branches")
                ? prev.filter((item) => item !== "branches")
                : [...prev, "branches"]
            )
          }
        >
          <ContainerSelector
            data={
              branchData?.data?.map((item: any) => ({
                ...item,
                checked: false,
              })) || []
            }
            isLoading={branchIsLoading}
            onFetch={branchRefetch}
            onSearch={handleBranchSearch}
            onSelectionChange={handleSelectionBranches}
            selectedLabel={(count) =>
              `${t("checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            unselectedLabel={(count) =>
              `${t("not-checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            labelKey="name"
            valueKey="id"
            clearSelectionsRef={clearSelectionsRef}
            searchParamsKey="branches"
          />
        </FilterCollapse>
        <FilterCollapse
          label={t("mix-shipping-rates-modal.cities")}
          className="p-2"
          isOpen={openCollapse?.includes("city")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("city")
                ? prev.filter((item) => item !== "city")
                : [...prev, "city"]
            )
          }
        >
          <ContainerSelector
            data={
              cityData?.data?.map((item: any) => ({
                ...item,
                checked: false,
              })) || []
            }
            isLoading={cityIsLoading}
            onFetch={cityRefetch}
            onSearch={handleCitySearch}
            onSelectionChange={handleSelectionPointOfCities}
            selectedLabel={(count) =>
              `${t("checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            unselectedLabel={(count) =>
              `${t("not-checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            labelKey="city_name"
            valueKey="id"
            clearSelectionsRef={clearSelectionsRef}
            searchParamsKey="city"
          />
        </FilterCollapse>
        <FilterCollapse
          label={t("mix-shipping-rates-modal.point-of-loading")}
          className="p-2"
          isOpen={openCollapse?.includes("point_of_loading")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("point_of_loading")
                ? prev.filter((item) => item !== "point_of_loading")
                : [...prev, "point_of_loading"]
            )
          }
        >
          <ContainerSelector
            data={
              locData?.data?.map((item: any) => ({
                ...item,
                checked: false,
              })) || []
            }
            isLoading={locIsLoading}
            onFetch={locRefetch}
            onSearch={handleLocSearch}
            onSelectionChange={handleSelectionPointOfLocation}
            selectedLabel={(count) =>
              `${t("checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            unselectedLabel={(count) =>
              `${t("not-checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            labelKey="name"
            valueKey="id"
            clearSelectionsRef={clearSelectionsRef}
            searchParamsKey="loc"
          />
        </FilterCollapse>
      </div>
    </div>
  );
}
