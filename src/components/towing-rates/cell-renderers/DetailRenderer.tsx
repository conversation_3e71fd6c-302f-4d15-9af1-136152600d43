import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import type { CustomCellRendererProps } from "ag-grid-react";
import { useTranslations } from "next-intl";
import { type FunctionComponent } from "react";

type Branch = {
  branch_name: string;
  city_name: string;
  ga: number | null;
  ca: number | null;
  tx: number | null;
  nj: number | null;
  ba: number | null;
};

export const DetailsRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 0,
  });
  const t = useTranslations("towing-rate-datatable.details");
  const columnHeaders = [
    t("number"),
    t("branch"),
    t("cityName"),
    t("ga"),
    t("ca"),
    t("tx"),
    t("nj"),
    t("bal"),
  ];
  return (
    <div className="h-auto">
      <Table className="table-responsive">
        <TableHeader>
          <TableRow>
            {columnHeaders.map((header) => (
              <TableHead key={header} className="rtl:text-right">{header}</TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data?.branches?.map((branch: Branch, index: number) => (
            <TableRow
              key={index}
              className={index % 2 === 0 ? "row-even" : "row-odd"}
            >
              <TableCell>{index + 1}</TableCell>
              <TableCell>{branch.branch_name}</TableCell>
              <TableCell>{branch.city_name}</TableCell>
              <TableCell>
                {branch?.ga ? formatter.format(branch.ga) : ""}
              </TableCell>
              <TableCell>
                {branch?.ca ? formatter.format(branch.ca) : ""}
              </TableCell>
              <TableCell>
                {branch?.tx ? formatter.format(branch.tx) : ""}
              </TableCell>
              <TableCell>
                {branch?.nj ? formatter.format(branch.nj) : ""}
              </TableCell>
              <TableCell>
                {branch?.ba ? formatter.format(branch.ba) : ""}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
