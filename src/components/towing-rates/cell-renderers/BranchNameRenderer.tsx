import type { CustomCellRendererProps } from "ag-grid-react";
import { type FunctionComponent } from "react";

export const BranchNameRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        {data?.branch_name}
      </div>
    </div>
  );
};
