"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";

import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import {
  type FunctionComponent,
  useCallback,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import { StateRenderer } from "./StateRenderer";
import { DetailsRenderer } from "./DetailRenderer";
import useSidebarConfig from "../sidebarConfig";
import { useResponsive } from "@/hooks/use-mobile";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);
type TowingRate = {
  stateid: number;
  state_name: string;
  branch_name: string;
  city_name: string;
  ga: number | null;
  ca: number | null;
  tx: number | null;
  nj: number | null;
  ba: number | null;
};
interface Props {
  gridTheme?: string;
  isDarkMode?: boolean;
  records: {
    page: number;
    per_page: number;
    total: number;
    data: TowingRate[];
  } | null;
}

export const TowingRatesDataTable: FunctionComponent<Props> = ({
  records,
}: Props) => {
  const gridRef = useRef<AgGridReact>(null);
  const t = useTranslations("towing-rate-datatable");
  const sidebarConfig = useSidebarConfig();
  const { isMobile } = useResponsive();
  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: "#",
        cellDataType: "text",
        valueGetter: (params) => {
          if (params.node) {
            return params.node.rowIndex ? params.node.rowIndex + 1 : 1;
          }
        },
        maxWidth: 50,
        hide: isMobile,
      },
      {
        field: "state_name",
        headerName: t("header.state-name"),
        cellDataType: "text",
        cellRenderer: StateRenderer,
        minWidth: isMobile ? 100 : 900,
      },
    ],
    [isMobile, t]
  );

  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
    }),
    []
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  const [quickFilterText] = useState<string>();

  const detailCellRenderer = useCallback(DetailsRenderer, []);
  const selectionColumnDef = useMemo(() => {
    return {
      minWidth: 44,
    };
  }, []);
  const { isRTL } = useDirection();

  return (
    <>
      <AgGridDataTable
        enableRtl={isRTL ? true : false}
        ref={gridRef}
        selectionColumnDef={selectionColumnDef}
        columnDefs={colDefs}
        rowData={records?.data || []}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        masterDetail
        detailCellRenderer={detailCellRenderer}
        detailCellRendererParams={{ t }}
        quickFilterText={quickFilterText}
        rowSelection={isMobile ? "single" : "multiple"}
        colResizeDefault="shift"
        headerHeight={60}
        sideBar={sidebarConfig}
        rowClassRules={{
          "row-even": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 === 0
                ? false
                : true
              : true;
          },
          "row-odd": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 !== 0
                ? false
                : true
              : true;
          },
        }}
        detailRowAutoHeight
        suppressHorizontalScroll={isMobile}
      />
    </>
  );
};
