import { useDirection } from "@/hooks/useDirection";
import type { CustomCellRendererProps } from "ag-grid-react";
import { ChevronLeft, ChevronRight, MapPin, Building2 } from "lucide-react";
import { useEffect, useState, type FunctionComponent } from "react";
import { useResponsive } from "@/hooks/use-mobile";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { ScrollArea } from "@/components/ui/scroll-area";

interface Branch {
  branch_name: string;
  city_name: string;
  ga?: number;
  ca?: number;
  tx?: number;
  nj?: number;
  ba?: number;
}

export const StateRenderer: FunctionComponent<CustomCellRendererProps> = ({
  value,
  node,
  api,
  data,
}) => {
  const [isExpanded, setIsExpanded] = useState(node.expanded);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const { isRTL } = useDirection();
  const { isMobile } = useResponsive();

  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 0,
  });

  useEffect(() => {
    const expandListener = () => setIsExpanded(node.expanded);
    node.addEventListener("expandedChanged", expandListener);
    return () => node.removeEventListener("expandedChanged", expandListener);
  }, []); // Remove node from dependency array to prevent infinite re-renders

  const handleClick = () => {
    if (isMobile) {
      setIsSheetOpen(true);
    } else {
      api.setRowNodeExpanded(node, !node.expanded, true);
    }
  };

  const stateLabels = {
    ga: "Georgia",
    ca: "California",
    tx: "Texas",
    nj: "New Jersey",
    ba: "Baltimore",
  };

  const getStateColor = (state: string) => {
    const colors = {
      ga: "bg-blue-500/10 text-blue-600",
      ca: "bg-green-500/10 text-green-600",
      tx: "bg-red-500/10 text-red-600",
      nj: "bg-purple-500/10 text-purple-600",
      ba: "bg-orange-500/10 text-orange-600",
    };
    return (
      colors[state as keyof typeof colors] || "bg-gray-500/10 text-gray-600"
    );
  };

  const getHighestRate = (branch: Branch) => {
    const rates = [
      { state: "ga", value: branch.ga },
      { state: "ca", value: branch.ca },
      { state: "tx", value: branch.tx },
      { state: "nj", value: branch.nj },
      { state: "ba", value: branch.ba },
    ];

    const validRates = rates.filter((rate) => rate.value && rate.value > 0);
    if (validRates.length === 0) return null;

    return validRates.reduce((max, current) =>
      current.value! > max.value! ? current : max
    );
  };

  return (
    <>
      <div
        className="flex flex-col justify-center h-full select-text leading-5 hover:cursor-pointer md:hover:cursor-default lg:hover:cursor-default"
        onClick={handleClick}
      >
        <div className="flex w-full justify-between items-center overflow-hidden text-ellipsis whitespace-nowrap px-2">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">{value}</span>
          </div>
          {!isMobile &&
            (!isRTL ? (
              <ChevronRight
                className={`ml-2 h-4 w-4 transition-all duration-200 ${
                  isExpanded ? "rotate-90" : ""
                }`}
              />
            ) : (
              <ChevronLeft
                className={`ml-2 h-4 w-4 transition-all duration-200 ${
                  isExpanded ? "-rotate-90" : ""
                }`}
              />
            ))}
        </div>
      </div>

      <Drawer open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <DrawerContent className="h-[80vh] rounded-t-3xl border-t-2 border-primary px-4">
          <DrawerHeader className="sr-only">
            <DrawerTitle>State Details</DrawerTitle>
            <DrawerDescription>
              Detailed branch information for {value}
            </DrawerDescription>
          </DrawerHeader>

          <div className="flex flex-col h-full">
            <div className="px-1 py-2">
              <div className="flex items-center gap-2 mb-2">
                <Building2 className="w-5 h-5 text-primary" />
                <span className="font-semibold text-base">{value}</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <MapPin className="w-4 h-4" />
                <span>{data?.branches?.length || 0} branches available</span>
              </div>
            </div>

            <ScrollArea className="flex-1">
              <div className="space-y-3 pb-4">
                {data?.branches?.map((branch: Branch, index: number) => {
                  const highestRate = getHighestRate(branch);

                  return (
                    <Card
                      key={index}
                      className="border border-primary/50 bg-card/30 backdrop-blur-sm"
                    >
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
                              <span className="text-[10px] font-medium text-primary">
                                {index + 1}
                              </span>
                            </div>
                            <div className="flex flex-col">
                              <span className="text-sm font-medium text-foreground">
                                {branch.branch_name}
                              </span>
                              <div className="flex items-center gap-1">
                                <MapPin className="w-3 h-3 text-muted-foreground" />
                                <span className="text-xs text-muted-foreground">
                                  {branch.city_name}
                                </span>
                              </div>
                            </div>
                          </div>

                          {highestRate && (
                            <div className="text-right">
                              <div className="text-sm font-bold text-primary">
                                {formatter.format(highestRate.value!)}
                              </div>
                              <div className="text-[10px] text-muted-foreground">
                                Best Rate
                              </div>
                            </div>
                          )}
                        </div>
                      </CardHeader>

                      <CardContent className="pt-0">
                        <div className="space-y-2">
                          <div className="text-[10px] font-medium text-muted-foreground mb-2">
                            STATE RATES
                          </div>

                          <div className="grid grid-cols-2 gap-2">
                            {Object.entries(stateLabels).map(([key, label]) => {
                              const value = branch[
                                key as keyof Branch
                              ] as number;
                              const isHighest = highestRate?.state === key;

                              return (
                                <div
                                  key={key}
                                  className="flex items-center justify-between p-2 rounded-lg bg-muted/20"
                                >
                                  <div className="flex items-center gap-2">
                                    <Badge
                                      className={`${getStateColor(
                                        key
                                      )} border-none text-[9px] px-1.5 py-0.5`}
                                      variant="outline"
                                    >
                                      {key.toUpperCase()}
                                    </Badge>
                                    <span className="text-[10px] text-muted-foreground">
                                      {label}
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    {value && value > 0 ? (
                                      <>
                                        <span
                                          className={`text-xs font-medium ${
                                            isHighest
                                              ? "text-primary"
                                              : "text-foreground"
                                          }`}
                                        >
                                          {formatter.format(value)}
                                        </span>
                                        {isHighest && (
                                          <div className="w-1.5 h-1.5 bg-primary rounded-full"></div>
                                        )}
                                      </>
                                    ) : (
                                      <span className="text-xs text-muted-foreground">
                                        N/A
                                      </span>
                                    )}
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}

                {(!data?.branches || data.branches.length === 0) && (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <div className="w-16 h-16 bg-muted/30 rounded-full flex items-center justify-center mb-4">
                      <Building2 className="w-8 h-8 text-muted-foreground/50" />
                    </div>
                    <p className="text-sm text-muted-foreground mb-1">
                      No branches available
                    </p>
                    <p className="text-xs text-muted-foreground/70">
                      Check back later for updated branch information
                    </p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
};
