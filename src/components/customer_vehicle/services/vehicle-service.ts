"use server";

import api from "@/utils/axios-server";
import { removeMatchingValue } from "@/utils/helper-function";

type CustomerVehicleParamType = {
  page: number;
  per_page: number;
  search: string;
  exactMatch: boolean;
  filterData: string;
};

export async function getCustomerVehicle({
  params,
}: {
  params: CustomerVehicleParamType;
}) {
  removeMatchingValue(params, "search", ["undefined", ""]);
  try {
    const response = await api.get(`/v2/customer-of-customer-vehicle`, {
      params: { ...params },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message);
  }
}

// Keep the old function for backward compatibility if needed
export async function getCustomerVehicleLegacy() {
  try {
    const response = await api.get(`/v2/customer-of-customer-vehicle`);
    return response.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message);
  }
}
