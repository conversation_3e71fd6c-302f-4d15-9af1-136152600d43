"use server";
import { transformObject } from "@/lib/transferObject";
import { getCustomerVehicle } from "./vehicle-service";
// Define allowed keys for filtering
const allowedKeys = [
  "vin",
  "make",
  "model",
  "year",
  "lot_number",
  "price_from",
  "price_to",
  "state",
  "customer_name",
  "auction_name",
];

// Define dynamic mapping for search parameters
const mapping = {
  vin: "vin",
  make: "make",
  model: "model",
  year: "year",
  lot_number: "lot_number",
  price_from: "price.min",
  price_to: "price.max",
  state: "carstate",
  customer_name: "customer_fullname",
  auction_name: "auction_name",
};

export async function loadCustomerVehicleData(
  searchParams: any,
  params: {
    page: number;
    per_page: number;
    search?: string;
    filterData?: string;
  }
) {
  try {
    const transformedFilters = transformObject(
      searchParams,
      allowedKeys,
      mapping
    );

    const result = await getCustomerVehicle({
      params: {
        page: params.page,
        per_page: params.per_page,
        search: params.search || "",
        exactMatch: false,
        filterData:
          Object.keys(transformedFilters).length !== 0
            ? JSON.stringify(transformedFilters)
            : "",
      },
    });

    const response = {
      data: Array.isArray(result.data) ? result.data : [],
      total: result.total || 0,
      page: params.page,
      per_page: params.per_page,
      success: true,
    };

    return response;
  } catch (error) {
    console.error(error);
    return {
      data: [],
      total: 0,
      page: params.page,
      per_page: params.per_page,
      success: false,
    };
  }
}
