"use client";
import PaginationComponent from "@/components/Common_UI/use-paginition";
import { transformObject } from "@/lib/transferObject";
import React from "react";
import { useResponsive } from "@/hooks/use-mobile";
import { CustomerVehicleDataTableMobile } from "./customer_vehicle_datatable-mobile";
import { CustomerVehicleDataTable } from "./vehicles-data-table";
import { loadCustomerVehicleData } from "./services/customer-vehicle-action";

// Define allowed keys (Only these fields will be processed)
const allowedKeys = [
  "con",
  "bkg",
  "status",
  "from_loading_date",
  "to_loading_date",
  "from_etd",
  "to_etd",
  "from_eta",
  "to_eta",
  "loc",
];

// Define dynamic mapping (Customize as needed)
const mapping = {
  con: "id",
  bkg: "booking_id",
  status: "status",
  from_loading_date: "loading_date.from",
  to_loading_date: "loading_date.to",
  from_etd: `bookings.vessels.etd.from`,
  to_etd: "bookings.vessels.etd.to",
  from_eta: "bookings.eta.from",
  to_eta: "bookings.eta.to",
  loc: "bookings.vessels.port_of_loading",
};

interface VehiclePageClientProps {
  initialRecords: any;
  searchParams: any;
}

const CustomerVehicleClient: React.FC<VehiclePageClientProps> = ({
  initialRecords,
  searchParams,
}) => {
  const boundLoadMoreData = loadCustomerVehicleData.bind(null, searchParams);
  const { isMobile } = useResponsive();

  const initialParams = {
    search: searchParams?.search || "",
    filterData:
      Object.keys(transformObject(searchParams, allowedKeys, mapping))
        .length !== 0
        ? JSON.stringify(transformObject(searchParams, allowedKeys, mapping))
        : "",
  };

  const componentKey = [
    searchParams?.search || "no-search",
    initialParams.filterData || "no-filters",
    searchParams?.page || "1",
  ].join("-");

  return (
    <>
      {isMobile ? (
        <div className="h-[calc(100vh-160px)]">
          <CustomerVehicleDataTableMobile
            key={componentKey}
            records={initialRecords}
            onLoadMoreData={boundLoadMoreData}
            initialParams={initialParams}
          />
        </div>
      ) : (
        <div className="flex flex-col h-[calc(100vh-140px)]">
          <div className="flex-1">
            <CustomerVehicleDataTable records={initialRecords} />
          </div>
          <div className="flex justify-center items-center py-4">
            <PaginationComponent
              count={initialRecords?.total || 0}
              pageSize={initialRecords?.per_page || 0}
            />
          </div>
        </div>
      )}
    </>
  );
};

export default CustomerVehicleClient;
