"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";

import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import { useMemo, useRef, useState } from "react";
import AgGridDataTable from "../ag-grid/ag-grid-data-table";
import { AuctionRenderer } from "./cell-renderers/AuctionRenderer";
import { CenterCell } from "./cell-renderers/CenterCell";
import { VehicleRenderer } from "./cell-renderers/VehicleRenderer";
import { ContainerRenderer } from "./cell-renderers/ContainerRenderer";
import { StatusRenderer } from "./cell-renderers/StatusRenderer";
import { sidebarConfig } from "./sidebarConfig";
import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import { Props } from "./services/config";
import { PriceRenderer } from "./cell-renderers/PriceRenderer";
import { ShippingCostRenderer } from "./cell-renderers/ShippingCostRenderer";
import { TotalChargesRenderer } from "./cell-renderers/TotalChargesRenderer";
import { PaidAmountRenderer } from "./cell-renderers/PaidAmountRenderer";
import { StorageChargeRenderer } from "./cell-renderers/StorageChargeRenderer";
import { UnpaidAmountRenderer } from "./cell-renderers/UnpaidAmountRenderer";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);

const paginationPageSizeSelector = [5, 10, 20];
export const CustomerVehicleDataTable = ({
  records,
  gridRefProps,
  exportColDefs,
}: Props) => {
  const gridRef = useRef<AgGridReact>(null);
  const t = useTranslations("datatable");

  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: "#",
        cellDataType: "text",
        minWidth: 50,
        maxWidth: 50,
        cellStyle: {
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        },
        valueGetter: (params) => {
          if (params.node) {
            return params.node.rowIndex ? params.node.rowIndex + 1 : 1;
          }
        },
      },

      {
        headerName: t("header.vehicle"),
        cellDataType: "text",
        cellRenderer: VehicleRenderer,
        minWidth: 300,
      },

      {
        headerName: t("header.status"),
        minWidth: 100,
        cellRenderer: StatusRenderer,
      },

      {
        headerName: t("header.container"),
        cellRenderer: ContainerRenderer,
        minWidth: 220,
      },
      {
        headerName: t("header.dates"),
        minWidth: 170,
        cellRenderer: CenterCell,
      },
      {
        headerName: t("header.auction"),
        cellRenderer: AuctionRenderer,
        minWidth: 130,
      },
      {
        headerName: t("header.vehicle-price"),
        cellRenderer: PriceRenderer,
        minWidth: 130,
      },
      {
        headerName: t("header.ship-cost"),
        cellRenderer: ShippingCostRenderer,
        minWidth: 130,
      },
      {
        headerName: t("header.storage-charge"),
        cellRenderer: StorageChargeRenderer,
        minWidth: 130,
      },
      {
        headerName: t("header.total-amount"),
        cellRenderer: TotalChargesRenderer,
        minWidth: 130,
      },
      {
        headerName: t("header.paid-amount"),
        cellRenderer: PaidAmountRenderer,
        minWidth: 130,
      },
      {
        headerName: t("header.due-balance"),
        cellRenderer: UnpaidAmountRenderer,
        minWidth: 130,
      },
    ],
    [t]
  );

  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
      flex: 1,
    }),
    []
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  const [quickFilterText] = useState<string>();

  const selectionColumnDef = useMemo(() => {
    return {
      minWidth: 44,
    };
  }, []);
  const { isRTL } = useDirection();

  return (
    <>
      <AgGridDataTable
        enableRtl={isRTL ? true : false}
        ref={gridRefProps || gridRef}
        selectionColumnDef={selectionColumnDef}
        columnDefs={exportColDefs || colDefs}
        rowData={records?.data || []}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        pagination={false}
        paginationPageSize={records?.per_page ?? 20}
        paginationPageSizeSelector={paginationPageSizeSelector}
        detailCellRendererParams={{ t }}
        quickFilterText={quickFilterText}
        rowHeight={72}
        colResizeDefault="shift"
        headerHeight={60}
        sideBar={sidebarConfig}
        rowClassRules={{
          "row-even": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 === 0
                ? false
                : true
              : true;
          },
          "row-odd": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 !== 0
                ? false
                : true
              : true;
          },
        }}
        detailRowAutoHeight
      />
    </>
  );
};
