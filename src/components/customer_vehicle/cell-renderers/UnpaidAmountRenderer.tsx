import { useMemo } from "react";
import { UnpaidAmountSummary } from "./UnPaidAmountComponent";

// Updated UnpaidAmountRenderer to use the new component
export const UnpaidAmountRenderer = ({ data }: { data: any }) => {
  const vehicleData = useMemo(() => {
    const customerId = data?.customer_of_customer_id;
    const vehicleId = data?.vechicles?.id;
    const vehiclePrice = data?.vechicles?.price || 0;
    const customerProfit = data?.vechicles?.customer_profit || 0;
    const storageCharges = data?.vechicles?.storage_charges ?? 0;

    return {
      customerId,
      vehicleId,
      vehiclePrice,
      customerProfit,
      storageCharges,
    };
  }, [data]);

  const mixShippingData = data?.vechicles?.mix_shipping_vehicles?.[0] || {};

  return (
    <UnpaidAmountSummary
      vehicleId={vehicleData.vehicleId}
      customerId={vehicleData.customerId}
      vehicleData={{
        vehiclePrice: vehicleData.vehiclePrice,
        customerProfit: vehicleData.customerProfit,
        storageCharges: vehicleData.storageCharges,
      }}
      mixShippingData={mixShippingData}
    />
  );
};
