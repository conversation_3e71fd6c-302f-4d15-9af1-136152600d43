import { Separator } from "@/components/ui/separator";
import { formatOnlyDate } from "@/utils/helper-function";
import type { CustomCellRendererProps } from "ag-grid-react";
import { useTranslations } from "next-intl";
import { type FunctionComponent } from "react";

export const ContainerRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("datatable");
  return (
    <div className="flex flex-col justify-center h-full   select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[58px] pr-2">
          {t("vehicle-details.body.container_number")}:
        </div>{" "}
        {data?.vechicles?.containers?.container_number}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[58px] pr-1">
          {t("vehicle-details.body.loaded")}:
        </div>{" "}
        {data?.vechicles?.containers?.loading_date
          ? formatOnlyDate(data?.vechicles?.containers?.loading_date)
          : ""}
      </div>
    </div>
  );
};
