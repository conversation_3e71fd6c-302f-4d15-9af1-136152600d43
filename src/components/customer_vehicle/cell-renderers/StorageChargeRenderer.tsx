import type { CustomCellRendererProps } from "ag-grid-react";
import { type FunctionComponent } from "react";

const currencyFormatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  maximumFractionDigits: 2,
});


export const StorageChargeRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data }) => {
  return (
    <div className="flex items-center h-full select-text">
      <div className="rounded-md px-2 flex leading-[22px] text-xs font-semibold overflow-hidden bg-green-500/10 text-green-500">
        {currencyFormatter.format(Number(data?.vechicles?.storage_charges) ?? 0)}
      </div>
    </div>
  );
};
