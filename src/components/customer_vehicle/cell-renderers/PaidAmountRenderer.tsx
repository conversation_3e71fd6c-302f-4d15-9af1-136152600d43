import { useMemo } from "react";
import { PaymentSummary } from "./PaymentSummery";

export const PaidAmountRenderer = ({ data }: { data: any }) => {
  const vehicleData = useMemo(() => {
    const customerId = data?.customer_of_customer_id;
    const vehicleId = data?.vechicles?.id;
    const vehiclePrice = data?.vechicles?.price || 0;
    const customerProfit = data?.vechicles?.customer_profit || 0;

    return {
      customerId,
      vehicleId,
      vehiclePrice,
      customerProfit,
    };
  }, [data]);

  return (
    <PaymentSummary
      vehicleId={vehicleData.vehicleId}
      customerId={vehicleData.customerId}
      vehiclePrice={vehicleData.vehiclePrice}
      customerProfit={vehicleData.customerProfit}
    />
  );
};
