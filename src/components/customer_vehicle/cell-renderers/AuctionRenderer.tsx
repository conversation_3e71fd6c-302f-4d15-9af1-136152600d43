import CopartLogo from "@/components/Common_UI/coport-logo";
import I<PERSON><PERSON><PERSON> from "@/components/Common_UI/iaai-logo";
import type { CustomCellRendererProps } from "ag-grid-react";
import { Check, CircleHelp, X } from "lucide-react";
import { useTranslations } from "next-intl";

import { type FunctionComponent } from "react";

export const AuctionRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("datatable");
  return (
    <div className="flex flex-col justify-center items-center w-full h-full leading-5">
      {data?.vechicles?.auction_name?.toLowerCase() === "copart" ||
      data?.vechicles?.auction_name?.toLowerCase() === "coport" ? (
        <CopartLogo />
      ) : (
        <IAAILogo />
      )}
      <div className="rounded-md  flex leading-[22px] text-xs items-center font-semibold overflow-hidden">
        {t("vehicle-details.body.is-key")}
        <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
          {data?.vechicles?.is_key_present ? (
            <Check className="text-blue-500 w-4 h-4" />
          ) : data?.vechicles?.is_key_present === "null" ? (
            <CircleHelp />
          ) : (
            <X className="text-red-500 w-4 h-4" />
          )}
        </div>
      </div>
    </div>
  );
};
