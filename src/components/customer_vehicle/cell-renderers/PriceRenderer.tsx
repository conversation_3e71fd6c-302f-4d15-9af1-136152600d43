import { colorSystem, ColorSystemKey } from "@/lib/constant";
import type { CustomCellRendererProps } from "ag-grid-react";
import { type FunctionComponent } from "react";

export const PriceRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const colors = colorSystem[data?.vechicles?.carstate as ColorSystemKey] || {
    bg: "bg-green-500/10",
    txt: "text-green-500",
  };
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 2,
    minimumFractionDigits: 0,
  });
  return (
    <div className="flex items-center h-full select-text">
      <div
        className={`rounded-md px-2 flex leading-[22px] text-xs font-semibold overflow-hidden ${colors.bg} ${colors.txt}`}
      >
        {formatter.format(data?.vechicles?.price || 0)}
      </div>
    </div>
  );
};
