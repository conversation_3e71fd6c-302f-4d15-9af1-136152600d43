import { useMemo, useState } from "react";
import type { CustomCellRendererProps } from "ag-grid-react";
import Image from "next/image";
import { type FunctionComponent } from "react";
import { getGoogleDriveImageSizeUrl, getImageSizeUrl } from "@/utils/imageURL";
import CustomDialog from "@/components/Common_UI/custom-dialog";
import CarouselComponent, {
  useDotButton,
} from "@/components/Common_UI/custom-carousel";
import { toast } from "sonner";
import { useGetVehicleImages } from "@/components/vehicles/vehicle-client-fetching";
import { SliderMainItem } from "@/components/ui/extension/carousel";
import ZoomImage from "@/components/Common_UI/zoom-image";
import {
  GoogleDriveImagesCarousel,
  GoogleDriveImagesThumbsCarousel,
} from "@/components/vehicles/vehicle-images-from-google-drive";
import { useQueryClient } from "@tanstack/react-query";
import {
  Drawer,
  DrawerContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>er<PERSON><PERSON><PERSON>,
} from "@/components/ui/drawer";
import { CarFront, CircleCheck, CircleX, Hash } from "lucide-react";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";
import { useDirection } from "@/hooks/useDirection";
import { useResponsive } from "@/hooks/use-mobile";
import { Badge } from "@/components/ui/badge";
import { removeUnderScore } from "@/utils/commons";
import { colorSystem, ColorSystemKey } from "@/lib/constant";
import { PaymentSummary } from "./PaymentSummery";
import { UnpaidAmountSummary } from "./UnPaidAmountComponent";
import { formatDate } from "date-fns";
import { useTranslations } from "next-intl";

export const VehicleRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const [openCarousel, setOpenCarousel] = useState(false);
  const [currentImage, setCurrentImage] = useState("");
  const isGoogleImages =
    data?.auction_photos_link?.includes("drive.google.com") ||
    data?.photo_link?.includes("drive.google.com")
      ? true
      : false;
  const [api, setApi] = useState();
  const { selectedIndex } = useDotButton(api);
  const queryClient = useQueryClient();
  const { isRTL } = useDirection();
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const { isMobile } = useResponsive();
  const { images } = useGetVehicleImages({
    vehicleId: data?.vehicle_id,
    isGoogleDrive: isGoogleImages,
  });
  const t = useTranslations("datatable.vehicle-drawer");
  const colors = colorSystem[data?.carstate as ColorSystemKey] || {
    bg: "bg-green-500/10",
    txt: "text-green-500",
  };
  const handleImageClicked = (imageUrl: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setOpenCarousel(true);
    setCurrentImage(imageUrl);
  };

  const handleCopyClick = () => {
    if (currentImage) {
      navigator.clipboard.writeText(
        getImageSizeUrl({ url: currentImage, size: 1024 })
      );
      toast.success("Image URL copied!");
    }
  };

  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 2,
  });

  const [downloadLink, setDownloadLink] = useState("");
  const handleDownloadLink = (imagelurl: string) => {
    setDownloadLink(imagelurl);
  };

  const singleDownload =
    images?.google_images.photo && images.images.length === 0
      ? getGoogleDriveImageSizeUrl({
          url: queryClient.getQueryData<string>([
            "vehicle-image",
            images?.google_images.photo[selectedIndex],
          ]),
        })
      : getImageSizeUrl({ url: downloadLink, size: 1024 });
  const toNumber = (value: unknown): number => Number(value) || 0;
  const transportationFee = useMemo(() => {
    const mixShippingData = data?.vechicles?.mix_shipping_vehicles?.[0];
    if (!mixShippingData) return 0;

    const charges = mixShippingData.mix_shipping_vehicle_charges || [];
    const totalCharges = charges.reduce(
      (sum: number, charge: any) => sum + toNumber(charge.value),
      0
    );

    return (
      toNumber(mixShippingData.freight) +
      toNumber(mixShippingData.vat_and_custom) +
      toNumber(mixShippingData.tow_amount) +
      toNumber(mixShippingData.clearance) +
      toNumber(data?.vechicles?.customer_profit) +
      totalCharges
    );
  }, [data?.vechicles?.mix_shipping_vehicles]);

  const totalCharges = useMemo(() => {
    const mixShippingData = data?.vechicles?.mix_shipping_vehicles?.[0];
    let transportationFee = 0;

    if (mixShippingData) {
      const charges = mixShippingData.mix_shipping_vehicle_charges || [];
      const totalMixCharges = charges.reduce(
        (sum: number, charge: any) => sum + toNumber(charge.value),
        0
      );

      transportationFee =
        toNumber(mixShippingData.freight) +
        toNumber(mixShippingData.vat_and_custom) +
        toNumber(mixShippingData.tow_amount) +
        toNumber(mixShippingData.clearance) +
        totalMixCharges;
    }

    const vehiclePrice = toNumber(data?.vechicles?.price);
    const customerProfit = toNumber(data?.vechicles?.customer_profit);

    return vehiclePrice + customerProfit + transportationFee;
  }, [
    data?.vechicles?.mix_shipping_vehicles,
    data?.vechicles?.price,
    data?.customer_profit,
  ]);
  const vehicleData = useMemo(() => {
    const customerId = data?.customer_of_customer_id;
    const vehicleId = data?.vechicles?.id;
    const vehiclePrice = data?.vechicles?.price || 0;
    const customerProfit = data?.vechicles?.customer_profit || 0;

    return {
      customerId,
      vehicleId,
      vehiclePrice,
      customerProfit,
    };
  }, [data]);

  const unpaidAmountData = useMemo(() => {
    const customerId = data?.customer_of_customer_id;
    const vehicleId = data?.vechicles?.id;
    const vehiclePrice = data?.vechicles?.price || 0;
    const customerProfit = data?.vechicles?.customer_profit || 0;
    const storageCharges = data?.vechicles?.storage_charges ?? 0;

    return {
      customerId,
      vehicleId,
      vehiclePrice,
      customerProfit,
      storageCharges,
    };
  }, [data]);

  const mixShippingData = data?.vechicles?.mix_shipping_vehicles?.[0] || {};
  const detailRows = [
    {
      label: t("status"),
      value: (
        <div className="flex justify-end">
          <Badge
            className={`${colors.bg} ${colors.txt} border-none text-[8px] flex items-center gap-2`}
            variant="outline"
          >
            {data?.carstate === "shipped" && !data?.containers?.container_number
              ? "ON THE WAY"
              : removeUnderScore(data?.vechicles?.carstate)?.toUpperCase()}
          </Badge>
        </div>
      ),
    },
    {
      label: t("auction-name"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.vechicles?.auction_name || "."}
        </span>
      ),
    },
    {
      label: t("year"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.vechicles?.year}
        </span>
      ),
    },
    {
      label: t("make"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.vechicles?.make}
        </span>
      ),
    },
    {
      label: t("model"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.vechicles?.model}
        </span>
      ),
    },
    {
      label: t("color"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.vechicles?.color}
        </span>
      ),
    },
    {
      label: t("lot-number"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.vechicles?.lot_number}
        </span>
      ),
    },
    {
      label: t("is-key"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {" "}
          {data?.vechicles?.is_key_present ? (
            <CircleCheck className="w-4 h-4 text-primary" />
          ) : (
            <CircleX className="w-4 h-4 text-red-600" />
          )}
        </span>
      ),
    },
    {
      label: t("vehicle-price"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          <div className="rounded-md px-2 flex leading-[22px] text-xs font-semibold overflow-hidden bg-green-500/10 text-green-500">
            {formatter.format(data?.vechicles?.price)}
          </div>
        </span>
      ),
    },
    {
      label: t("ship-cost"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          <div className="rounded-md px-2 flex leading-[22px] text-xs font-semibold overflow-hidden bg-green-500/10 text-green-500">
            {formatter.format(transportationFee)}
          </div>
        </span>
      ),
    },
    {
      label: t("storage-charge"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          <div className="rounded-md px-2 flex leading-[22px] text-xs font-semibold overflow-hidden bg-green-500/10 text-green-500">
            {formatter.format(data?.vechicles?.storage_charges)}
          </div>
        </span>
      ),
    },
    {
      label: t("total-amount"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          <div className="rounded-md px-2 flex leading-[22px] text-xs font-semibold overflow-hidden bg-green-500/10 text-green-500">
            {formatter.format(totalCharges)}
          </div>
        </span>
      ),
    },
    {
      label: t("paid-amount"),
      value: (
        <div className="flex items-center gap-2 justify-end text-xs ml-auto">
          <PaymentSummary
            vehicleId={vehicleData.vehicleId}
            customerId={vehicleData.customerId}
            vehiclePrice={vehicleData.vehiclePrice}
            customerProfit={vehicleData.customerProfit}
          />
        </div>
      ),
    },
    {
      label: t("due-balance"),
      value: (
        <UnpaidAmountSummary
          vehicleId={unpaidAmountData.vehicleId}
          customerId={unpaidAmountData.customerId}
          vehicleData={{
            vehiclePrice: unpaidAmountData.vehiclePrice,
            customerProfit: unpaidAmountData.customerProfit,
            storageCharges: unpaidAmountData.storageCharges,
          }}
          mixShippingData={mixShippingData}
        />
      ),
    },
    {
      label: t("loading-date"),
      value: (
        <div className="flex items-center gap-2 justify-end text-xs">
          {data?.vechicles?.containers?.loading_date &&
            formatDate(
              data?.vechicles?.containers?.loading_date,
              "MMM d, yyyy"
            )}
        </div>
      ),
    },
    {
      label: "ETA",
      value: (
        <div className="flex items-center gap-2 justify-end text-xs">
          {data?.vechicles?.containers?.bookings?.eta &&
            formatDate(
              data?.vechicles?.containers?.bookings?.eta,
              "MMM d, yyyy"
            )}
        </div>
      ),
    },
    {
      label: "ETD",
      value: (
        <div className="flex items-center gap-2 justify-end text-xs">
          {data?.vechicles?.containers?.bookings?.vessels?.etd &&
            formatDate(
              data?.vechicles?.containers?.bookings?.vessels?.etd,
              "MMM d, yyyy"
            )}
        </div>
      ),
    },
  ];

  const handleDivClick = () => {
    if (isMobile) {
      setIsSheetOpen(true);
    }
  };

  return (
    <>
      <div className="flex items-center h-full">
        <div
          className="flex items-center justify-between"
          onClick={handleDivClick}
        >
          <Image
            src={`${
              data?.vechicles?.cover_photo
                ? getImageSizeUrl({
                    url: data?.vechicles?.cover_photo,
                    size: 100,
                  })
                : "/placeholder.svg"
            }`}
            alt={"sample"}
            height={50}
            width={50}
            className={`rounded-md ${
              data?.vechicles?.cover_photo
                ? ""
                : "dark:brightness-[0.2] dark:grayscale hover:cursor-pointer"
            }`}
            onClick={(e) =>
              handleImageClicked(data?.vechicles?.cover_photo || "", e)
            }
          />
          <div className="flex flex-col justify-center h-full select-text leading-5 flex-1">
            <div className="flex items-center gap-1.5">
              <div className="w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                {data?.vechicles?.vin}
              </div>
            </div>
            <div>
              <p className="max-w-64 overflow-hidden text-ellipsis whitespace-nowrap font-normal px-2 text-primary/70">
                {data?.vechicles?.year} {data?.vechicles?.make}{" "}
                {data?.vechicles?.model} {data?.vechicles?.color}
              </p>
            </div>
          </div>
        </div>
        <CustomDialog
          openModal={openCarousel}
          setOpenModal={setOpenCarousel}
          title={`Vehicle Images ${data?.vechicles?.vin}`}
          handleCopyClick={handleCopyClick}
          downloadLink={singleDownload}
          isGoogleDrive={images?.google_images.photo.length > 0}
        >
          <>
            {images.images.length > 0 && (
              <CarouselComponent
                images={images.images}
                isGoogleDrive={false}
                onImageClick={handleDownloadLink}
                Component={ImagesCarousel}
                Thumbs={GoogleDriveImagesThumbsCarousel}
                selected={0}
                setApi={setApi}
              />
            )}
            {images?.google_images.photo && images.images.length === 0 && (
              <CarouselComponent
                images={images?.google_images.photo}
                isGoogleDrive={true}
                Component={GoogleDriveImagesCarousel}
                Thumbs={GoogleDriveImagesThumbsCarousel}
                selected={0}
                setApi={setApi}
              />
            )}
          </>
        </CustomDialog>
      </div>
      <Drawer open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <DrawerContent
          className="h-[80vh] rounded-t-3xl border-t-2 border-primary px-4 mb-1"
          dir={isRTL ? "rtl" : "ltr"}
        >
          <DrawerHeader className="sr-only">
            <DrawerTitle>Vehicle Details</DrawerTitle>
            <DrawerDescription>
              Detailed information about {data?.vin}
            </DrawerDescription>
          </DrawerHeader>
          <div className="flex flex-col h-full">
            <div className="px-1 py-2">
              <div className="flex items-center gap-2">
                <span className="font-semibold text-xs">
                  {data?.vechicles?.year} {data?.vechicles?.make}{" "}
                  {data?.vechicles?.model} {data?.vechicles?.color}
                </span>
              </div>
              <div className="flex items-center gap-2 justify-between py-2">
                <span className="flex items-center text-xs text-primary">
                  <Hash className="w-4 h-4" />
                  {data?.vechicles?.lot_number}
                </span>
                <div className="flex items-center gap-2">
                  <CarFront className="w-4 h-4 text-primary" />
                  <span className="text-xs">{data?.vechicles?.vin}</span>
                </div>
              </div>
            </div>
            <div className="flex-1 overflow-y-auto">
              <div className="rounded-lg overflow-hidden border">
                <Table className="mb-4">
                  <TableBody>
                    {detailRows.map((row, index) => (
                      <TableRow
                        key={row.label}
                        className={`${
                          index % 2 === 0 ? "bg-primary/5" : "bg-primary/10"
                        } `}
                      >
                        <TableCell className="py-1 px-2 text-xs">
                          {row.label}
                        </TableCell>
                        <TableCell className="py-1 px-2 text-right text-xs">
                          {row.value}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
};

function ImagesCarousel({
  img,
  onImageClick,
}: {
  img: any;
  onImageClick?: (imageUrl: string) => void;
}) {
  return (
    <SliderMainItem
      className="bg-transparent w-full h-full"
      onMouseEnter={() => onImageClick?.(img.url || "")}
    >
      <div
        tabIndex={0}
        className=" flex items-center justify-center h-full w-full"
      >
        <ZoomImage file={img} isGoogleDrive={false} />
      </div>
    </SliderMainItem>
  );
}
