import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DollarSign, CreditCard, Loader2, List } from "lucide-react";
import { useState, useEffect, useMemo, useCallback } from "react";
import { useFetchClient } from "@/utils/axios";
import { useTranslations } from "next-intl";
import { toast } from "sonner";

interface Payment {
  id: number;
  payment_amount: number;
  payment_date: string;
  created_at: string;
}

interface PaymentSummaryProps {
  vehicleId?: number;
  customerId?: number;
  vehiclePrice?: number;
  customerProfit?: number;
  className?: string;
  showAsButton?: boolean;
  buttonText?: string;
}

export const PaymentSummary = ({
  vehicleId,
  customerId,
  className = "",
  showAsButton = false,
  buttonText,
}: PaymentSummaryProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoadingPayments, setIsLoadingPayments] = useState(false);
  const [payments, setPayments] = useState<Payment[]>([]);

  const t = useTranslations("customer_of_customer_payments");
  const fetchClient = useFetchClient();

  const formatter = useMemo(
    () =>
      new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
        maximumFractionDigits: 2,
        minimumFractionDigits: 0,
      }),
    []
  );

  const calculations = useMemo(() => {
    const totalPaid = payments.reduce(
      (sum, payment) => sum + Number(payment.payment_amount),
      0
    );

    return { totalPaid };
  }, [payments]);

  const fetchPayments = useCallback(async () => {
    if (!customerId || !vehicleId) return;

    setIsLoadingPayments(true);
    try {
      const response = await fetchClient(
        `/v2/payments-v2/get-one-vehicle-payments`,
        {
          method: "GET",
          params: {
            vehicle_id: vehicleId,
          },
        }
      );

      if (response.data?.result && response.data?.data) {
        setPayments(response.data.data);
      }
    } catch (error) {
      toast.error("Error fetching payments" + error);
      setPayments([]);
    } finally {
      setIsLoadingPayments(false);
    }
  }, [customerId, vehicleId, fetchClient]);

  useEffect(() => {
    if (isDialogOpen && vehicleId && customerId) {
      fetchPayments();
    }
  }, [isDialogOpen, fetchPayments, vehicleId, customerId]);

  const handleDialogOpen = useCallback(() => {
    setIsDialogOpen(true);
  }, []);

  if (showAsButton) {
    return (
      <>
        <button
          onClick={handleDialogOpen}
          className={`text-blue-500 hover:text-blue-700 underline text-sm ${className}`}
          disabled={!vehicleId || !customerId}
        >
          {buttonText || "View Payments"}
        </button>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="w-[95vw] max-w-[95vw] sm:max-w-[650px] p-0 h-[90vh] max-h-[90vh] sm:h-[500px] sm:max-h-[700px] sm:min-h-[500px]">
            <DialogHeader className="px-3 sm:px-6 flex justify-between items-center flex-row h-[50px] sm:h-[60px] min-h-[50px] sm:min-h-[60px] border-b">
              <DialogTitle className="text-lg sm:text-xl flex items-center gap-2">
                <DollarSign className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
                <span className="truncate">{t("title")}</span>
              </DialogTitle>
            </DialogHeader>

            <div className="px-3 sm:px-6 pb-3 sm:pb-6 overflow-y-auto flex-1">
              <Tabs defaultValue="list-customer-payment" className="w-full">
                <TabsList className="grid grid-cols-1 mb-4 w-full">
                  <TabsTrigger
                    value="list-customer-payment"
                    className="flex items-center gap-1 data-[state=active]:text-primary text-sm sm:text-base"
                  >
                    <CreditCard className="h-4 w-4" />
                    <span className="truncate">
                      {t("tabs.payment-history")}
                    </span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="list-customer-payment" className="mt-0">
                  <Card>
                    <CardHeader className="pb-3 sm:pb-6">
                      <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                        <List className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
                        <span className="truncate">
                          {t("payment-history.title")}
                        </span>
                      </CardTitle>
                      {payments.length > 0 && (
                        <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0 text-xs sm:text-sm text-muted-foreground">
                          <span>
                            {t("payment-history.total-payment")}:{" "}
                            {payments.length}
                          </span>
                          <span className="font-medium">
                            {t("payment-history.total-paid")}:{" "}
                            {formatter.format(calculations.totalPaid)}
                          </span>
                        </div>
                      )}
                    </CardHeader>
                    <CardContent className="pt-0">
                      {isLoadingPayments ? (
                        <div className="flex items-center justify-center py-8">
                          <Loader2 className="h-6 w-6 animate-spin mr-2" />
                          <span className="text-sm sm:text-base">
                            {t("loading")}
                          </span>
                        </div>
                      ) : payments.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                          <CreditCard className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-2 opacity-50" />
                          <p className="text-sm sm:text-base">{t("no-data")}</p>
                        </div>
                      ) : (
                        <div className="border rounded-md overflow-hidden">
                          {/* Desktop Table View */}
                          <div className="hidden sm:block">
                            <div className="bg-secondary">
                              <Table>
                                <TableHeader>
                                  <TableRow>
                                    <TableHead className="h-10 w-1/3">
                                      {t("payment-history.payment-date")}
                                    </TableHead>
                                    <TableHead className="h-10 w-1/3">
                                      {t("payment-history.amount")}
                                    </TableHead>
                                    <TableHead className="h-10 w-1/3">
                                      {t("payment-history.created-at")}
                                    </TableHead>
                                  </TableRow>
                                </TableHeader>
                              </Table>
                            </div>

                            <div className="max-h-[150px] overflow-y-auto">
                              <Table>
                                <TableBody>
                                  {payments.map((payment, index) => (
                                    <TableRow
                                      key={payment.id ?? `payment-${index}`}
                                    >
                                      <TableCell className="py-3 w-1/3">
                                        {new Date(
                                          payment.payment_date
                                        ).toLocaleDateString()}
                                      </TableCell>
                                      <TableCell className="font-medium py-3 w-1/3">
                                        {formatter.format(
                                          Number(payment.payment_amount)
                                        )}
                                      </TableCell>
                                      <TableCell className="text-muted-foreground py-3 w-1/3">
                                        {new Date(
                                          payment.created_at
                                        ).toLocaleDateString()}{" "}
                                        {new Date(
                                          payment.created_at
                                        ).toLocaleTimeString()}
                                      </TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>
                          </div>

                          {/* Mobile Card View */}
                          <div className="block sm:hidden">
                            <div className="max-h-[300px] overflow-y-auto space-y-2 p-3">
                              {payments.map((payment, index) => (
                                <div
                                  key={payment.id ?? `payment-${index}`}
                                  className="bg-secondary/20 rounded-lg p-3 space-y-2"
                                >
                                  <div className="flex justify-between items-center">
                                    <span className="text-xs text-muted-foreground">
                                      {t("payment-history.payment-date")}
                                    </span>
                                    <span className="text-sm font-medium">
                                      {new Date(
                                        payment.payment_date
                                      ).toLocaleDateString()}
                                    </span>
                                  </div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-xs text-muted-foreground">
                                      {t("payment-history.amount")}
                                    </span>
                                    <span className="text-sm font-bold text-primary">
                                      {formatter.format(
                                        Number(payment.payment_amount)
                                      )}
                                    </span>
                                  </div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-xs text-muted-foreground">
                                      {t("payment-history.created-at")}
                                    </span>
                                    <span className="text-xs text-muted-foreground">
                                      {new Date(
                                        payment.created_at
                                      ).toLocaleDateString()}{" "}
                                      {new Date(
                                        payment.created_at
                                      ).toLocaleTimeString([], {
                                        hour: "2-digit",
                                        minute: "2-digit",
                                      })}
                                    </span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </DialogContent>
        </Dialog>
      </>
    );
  }

  return (
    <div
      className={`flex flex-col justify-center items-center text-xs w-full h-full leading-5 ${className}`}
    >
      <div className="flex items-center gap-1 mb-1">
        <div
          className="rounded-md flex leading-[22px] text-xs font-semibold overflow-hidden hover:cursor-pointer transition-all duration-200"
          onClick={handleDialogOpen}
        >
          <div className="py-1 px-2 bg-blue-600/10 text-xs text-blue-500 dark:text-blue-500 flex items-center min-w-0">
            <span className="truncate">
              {formatter.format(calculations.totalPaid)}
            </span>
          </div>
        </div>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="w-[95vw] max-w-[95vw] sm:max-w-[650px] p-0 h-[60vh] max-h-[60vh] sm:h-[500px] sm:max-h-[700px] sm:min-h-[500px] rounded-md">
          <DialogHeader className="px-3 sm:px-6 flex justify-between items-center flex-row h-[50px] sm:h-[60px] min-h-[50px] sm:min-h-[60px] border-b">
            <DialogTitle className="text-lg sm:text-xl flex items-center gap-2">
              <DollarSign className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
              <span className="truncate">{t("title")}</span>
            </DialogTitle>
          </DialogHeader>

          <div className="px-3 sm:px-6 pb-3 sm:pb-6 overflow-y-auto flex-1">
            <Tabs defaultValue="list-customer-payment" className="w-full">
              <TabsList className="grid grid-cols-1 mb-4 w-full">
                <TabsTrigger
                  value="list-customer-payment"
                  className="flex items-center gap-1 data-[state=active]:text-primary text-sm sm:text-base"
                >
                  <CreditCard className="h-4 w-4" />
                  <span className="truncate">{t("tabs.payment-history")}</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="list-customer-payment" className="mt-0">
                <Card>
                  <CardHeader className="pb-3 sm:pb-6">
                    <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                      <List className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
                      <span className="truncate">
                        {t("payment-history.title")}
                      </span>
                    </CardTitle>
                    {payments.length > 0 && (
                      <div className="flex flex-col sm:flex-row sm:justify-between gap-1 sm:gap-0 text-xs sm:text-sm text-muted-foreground">
                        <span>
                          {t("payment-history.total-payment")}:{" "}
                          {payments.length}
                        </span>
                        <span className="font-medium">
                          {t("payment-history.total-paid")}:{" "}
                          {formatter.format(calculations.totalPaid)}
                        </span>
                      </div>
                    )}
                  </CardHeader>
                  <CardContent className="pt-0">
                    {isLoadingPayments ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin mr-2" />
                        <span className="text-sm sm:text-base">
                          {t("loading")}
                        </span>
                      </div>
                    ) : payments.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        <CreditCard className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-2 opacity-50" />
                        <p className="text-sm sm:text-base">{t("no-data")}</p>
                      </div>
                    ) : (
                      <div className="border rounded-md overflow-hidden">
                        {/* Desktop Table View */}
                        <div className="hidden sm:block">
                          <div className="bg-secondary">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead className="h-10 w-1/3">
                                    {t("payment-history.payment-date")}
                                  </TableHead>
                                  <TableHead className="h-10 w-1/3">
                                    {t("payment-history.amount")}
                                  </TableHead>
                                  <TableHead className="h-10 w-1/3">
                                    {t("payment-history.created-at")}
                                  </TableHead>
                                </TableRow>
                              </TableHeader>
                            </Table>
                          </div>

                          <div className="max-h-[150px] overflow-y-auto">
                            <Table>
                              <TableBody>
                                {payments.map((payment, index) => (
                                  <TableRow
                                    key={payment.id ?? `payment-${index}`}
                                  >
                                    <TableCell className="py-3 w-1/3">
                                      {new Date(
                                        payment.payment_date
                                      ).toLocaleDateString()}
                                    </TableCell>
                                    <TableCell className="font-medium py-3 w-1/3">
                                      {formatter.format(
                                        Number(payment.payment_amount)
                                      )}
                                    </TableCell>
                                    <TableCell className="text-muted-foreground py-3 w-1/3">
                                      {new Date(
                                        payment.created_at
                                      ).toLocaleDateString()}{" "}
                                      {new Date(
                                        payment.created_at
                                      ).toLocaleTimeString()}
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        </div>

                        {/* Mobile Card View */}
                        <div className="block sm:hidden">
                          <div className="max-h-[300px] overflow-y-auto space-y-2 p-3">
                            {payments.map((payment, index) => (
                              <div
                                key={payment.id ?? `payment-${index}`}
                                className="bg-secondary/20 rounded-lg p-3 space-y-2"
                              >
                                <div className="flex justify-between items-center">
                                  <span className="text-xs text-muted-foreground">
                                    {t("payment-history.payment-date")}
                                  </span>
                                  <span className="text-sm font-medium">
                                    {new Date(
                                      payment.payment_date
                                    ).toLocaleDateString()}
                                  </span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-xs text-muted-foreground">
                                    {t("payment-history.amount")}
                                  </span>
                                  <span className="text-sm font-bold text-primary">
                                    {formatter.format(
                                      Number(payment.payment_amount)
                                    )}
                                  </span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-xs text-muted-foreground">
                                    {t("payment-history.created-at")}
                                  </span>
                                  <span className="text-xs text-muted-foreground">
                                    {new Date(
                                      payment.created_at
                                    ).toLocaleDateString()}{" "}
                                    {new Date(
                                      payment.created_at
                                    ).toLocaleTimeString([], {
                                      hour: "2-digit",
                                      minute: "2-digit",
                                    })}
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
