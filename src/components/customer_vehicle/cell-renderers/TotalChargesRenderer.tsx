import type { CustomCellRendererProps } from "ag-grid-react";
import { type FunctionComponent, useMemo } from "react";

const currencyFormatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  maximumFractionDigits: 2,
});

const toNumber = (value: unknown): number => Number(value) || 0;

export const TotalChargesRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data }) => {
  const totalCharges = useMemo(() => {
    const mixShippingData = data?.vechicles?.mix_shipping_vehicles?.[0];
    let transportationFee = 0;

    if (mixShippingData) {
      const charges = mixShippingData.mix_shipping_vehicle_charges || [];
      const totalMixCharges = charges.reduce(
        (sum: number, charge: any) => sum + toNumber(charge.value),
        0
      );

      transportationFee =
        toNumber(mixShippingData.freight) +
        toNumber(mixShippingData.vat_and_custom) +
        toNumber(mixShippingData.tow_amount) +
        toNumber(mixShippingData.clearance) +
        totalMixCharges;
    }

    const vehiclePrice = toNumber(data?.vechicles?.price);
    const customerProfit = toNumber(data?.vechicles?.customer_profit);

    return vehiclePrice + customerProfit + transportationFee;
  }, [
    data?.vechicles?.mix_shipping_vehicles,
    data?.vechicles?.price,
    data?.customer_profit,
  ]);

  return (
    <div className="flex items-center h-full select-text">
      <div className="rounded-md px-2 flex leading-[22px] text-xs font-semibold overflow-hidden bg-green-500/10 text-green-500">
        {currencyFormatter.format(totalCharges)}
      </div>
    </div>
  );
};
