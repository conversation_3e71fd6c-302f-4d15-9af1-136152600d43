import { Separator } from "@/components/ui/separator";
import { formatOnlyDate } from "@/utils/helper-function";
import type { CustomCellRendererProps } from "ag-grid-react";
import { useTranslations } from "next-intl";
import { type FunctionComponent } from "react";

export const CenterCell: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("datatable.body");
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[44px]">{t("eta")}:</div>{" "}
        {data?.vechicles?.containers?.bookings?.eta
          ? formatOnlyDate(data?.vechicles?.containers?.bookings?.eta)
          : ""}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[44px]">{t("etd")}:</div>{" "}
        {data?.vechicles?.containers?.bookings?.vessels?.etd
          ? formatOnlyDate(data?.vechicles?.containers?.bookings?.vessels?.etd)
          : ""}
      </div>
    </div>
  );
};
