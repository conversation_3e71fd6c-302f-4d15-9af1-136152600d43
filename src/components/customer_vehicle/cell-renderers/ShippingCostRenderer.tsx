import type { CustomCellRendererProps } from "ag-grid-react";
import { type FunctionComponent, useMemo } from "react";

const currencyFormatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  maximumFractionDigits: 2,
});

const toNumber = (value: unknown): number => Number(value) || 0;

export const ShippingCostRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data }) => {
  const transportationFee = useMemo(() => {
    const mixShippingData = data?.vechicles?.mix_shipping_vehicles?.[0];
    if (!mixShippingData) return 0;

    const charges = mixShippingData.mix_shipping_vehicle_charges || [];
    const totalCharges = charges.reduce(
      (sum: number, charge: any) => sum + toNumber(charge.value),
      0
    );

    return (
      toNumber(mixShippingData.freight) +
      toNumber(mixShippingData.vat_and_custom) +
      toNumber(mixShippingData.tow_amount) +
      toNumber(mixShippingData.clearance) +
      toNumber(data?.vechicles?.customer_profit) +
      totalCharges
    );
  }, [data?.vechicles?.mix_shipping_vehicles]);

  return (
    <div className="flex items-center h-full select-text">
      <div className="rounded-md px-2 flex leading-[22px] text-xs font-semibold overflow-hidden bg-green-500/10 text-green-500">
        {currencyFormatter.format(transportationFee)}
      </div>
    </div>
  );
};
