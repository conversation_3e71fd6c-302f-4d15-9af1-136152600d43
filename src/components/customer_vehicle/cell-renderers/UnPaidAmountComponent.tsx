import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DollarSign, CreditCard, Loader2, List } from "lucide-react";
import { useState, useEffect, useMemo, useCallback } from "react";
import { useFetchClient } from "@/utils/axios";
import { useTranslations } from "next-intl";
import { toast } from "sonner";

interface Payment {
  id: number;
  payment_amount: number;
  payment_date: string;
  created_at: string;
}

interface PaymentSummaryProps {
  vehicleId?: number;
  customerId?: number;
  vehiclePrice?: number;
  customerProfit?: number;
  className?: string;
  showAsButton?: boolean;
  buttonText?: string;
}

export const PaymentSummary = ({
  vehicleId,
  customerId,
  className = "",
  showAsButton = false,
  buttonText,
}: PaymentSummaryProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoadingPayments, setIsLoadingPayments] = useState(false);
  const [payments, setPayments] = useState<Payment[]>([]);

  const t = useTranslations("customer_of_customer_payments");
  const fetchClient = useFetchClient();

  const formatter = useMemo(
    () =>
      new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
        maximumFractionDigits: 2,
        minimumFractionDigits: 0,
      }),
    []
  );

  const calculations = useMemo(() => {
    const totalPaid = payments.reduce(
      (sum, payment) => sum + Number(payment.payment_amount),
      0
    );

    return { totalPaid };
  }, [payments]);

  const fetchPayments = useCallback(async () => {
    if (!customerId || !vehicleId) return;

    setIsLoadingPayments(true);
    try {
      const response = await fetchClient(
        `/v2/payments-v2/get-one-vehicle-payments`,
        {
          method: "GET",
          params: {
            vehicle_id: vehicleId,
          },
        }
      );

      if (response.data?.result && response.data?.data) {
        setPayments(response.data.data);
      }
    } catch (error) {
      toast.error("Error fetching payments" + error);
      setPayments([]);
    } finally {
      setIsLoadingPayments(false);
    }
  }, [customerId, vehicleId, fetchClient]);

  useEffect(() => {
    if (isDialogOpen && vehicleId && customerId) {
      fetchPayments();
    }
  }, [isDialogOpen, fetchPayments, vehicleId, customerId]);

  const handleDialogOpen = useCallback(() => {
    setIsDialogOpen(true);
  }, []);

  if (showAsButton) {
    return (
      <>
        <button
          onClick={handleDialogOpen}
          className={`text-blue-500 hover:text-blue-700 underline text-sm ${className}`}
          disabled={!vehicleId || !customerId}
        >
          {buttonText || "View Payments"}
        </button>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="sm:max-w-[650px] p-0 h-[500px] max-h-[700px] min-h-[500px]">
            <DialogHeader className="px-6 flex justify-between items-center flex-row h-[60px] min-h-[60px]">
              <DialogTitle className="text-xl flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-primary" />
                {t("title")}
              </DialogTitle>
            </DialogHeader>

            <div
              className="px-6 pb-6 overflow-y-auto flex-1"
              style={{
                height: "540px",
                maxHeight: "540px",
                minHeight: "540px",
              }}
            >
              <Tabs defaultValue="list-customer-payment" className="w-full">
                <TabsList className="grid grid-cols-1 mb-4">
                  <TabsTrigger
                    value="list-customer-payment"
                    className="flex items-center gap-1 data-[state=active]:text-primary"
                  >
                    <CreditCard className="h-4 w-4" />
                    {t("tabs.payment-history")}
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="list-customer-payment" className="mt-0">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <List className="h-5 w-5 text-primary" />
                        {t("payment-history.title")}
                      </CardTitle>
                      {payments.length > 0 && (
                        <div className="flex justify-between text-sm text-muted-foreground">
                          <span>
                            {t("payment-history.total-payment")}:{" "}
                            {payments.length}
                          </span>
                          <span>
                            {t("payment-history.total-paid")}:{" "}
                            {formatter.format(calculations.totalPaid)}
                          </span>
                        </div>
                      )}
                    </CardHeader>
                    <CardContent>
                      {isLoadingPayments ? (
                        <div className="flex items-center justify-center py-8">
                          <Loader2 className="h-6 w-6 animate-spin mr-2" />
                          {t("loading")}
                        </div>
                      ) : payments.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                          <CreditCard className="h-12 w-12 mx-auto mb-2 opacity-50" />
                          <p>{t("no-data")}</p>
                        </div>
                      ) : (
                        <div className="border rounded-md overflow-hidden">
                          <div className="bg-secondary">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead className="h-10 w-1/3">
                                    {t("payment-history.payment-date")}
                                  </TableHead>
                                  <TableHead className="h-10 w-1/3">
                                    {t("payment-history.amount")}
                                  </TableHead>
                                  <TableHead className="h-10 w-1/3">
                                    {t("payment-history.created-at")}
                                  </TableHead>
                                </TableRow>
                              </TableHeader>
                            </Table>
                          </div>

                          <div className="max-h-[150px] overflow-y-auto">
                            <Table>
                              <TableBody>
                                {payments.map((payment, index) => (
                                  <TableRow
                                    key={payment.id ?? `payment-${index}`}
                                  >
                                    <TableCell className="py-3 w-1/3">
                                      {new Date(
                                        payment.payment_date
                                      ).toLocaleDateString()}
                                    </TableCell>
                                    <TableCell className="font-medium py-3 w-1/3">
                                      {formatter.format(
                                        Number(payment.payment_amount)
                                      )}
                                    </TableCell>
                                    <TableCell className="text-muted-foreground py-3 w-1/3">
                                      {new Date(
                                        payment.created_at
                                      ).toLocaleDateString()}{" "}
                                      {new Date(
                                        payment.created_at
                                      ).toLocaleTimeString()}
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </DialogContent>
        </Dialog>
      </>
    );
  }

  return (
    <div
      className={`flex flex-col justify-center items-center text-xs w-full h-full leading-5 ${className}`}
    >
      <div className="flex items-center gap-1 mb-1">
        <div
          className="rounded-md flex leading-[22px] text-xs font-semibold overflow-hidden hover:cursor-pointer transition-all duration-200"
          onClick={handleDialogOpen}
        >
          <div className="py-1 px-2 bg-blue-600/10 text-xs text-blue-500 dark:text-blue-500 flex items-center">
            {formatter.format(calculations.totalPaid)}
          </div>
        </div>
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[650px] p-0 h-[500px] max-h-[700px] min-h-[500px]">
          <DialogHeader className="px-6 flex justify-between items-center flex-row h-[60px] min-h-[60px]">
            <DialogTitle className="text-xl flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-primary" />
              {t("title")}
            </DialogTitle>
          </DialogHeader>

          <div
            className="px-6 pb-6 overflow-y-auto flex-1"
            style={{ height: "540px", maxHeight: "540px", minHeight: "540px" }}
          >
            <Tabs defaultValue="list-customer-payment" className="w-full">
              <TabsList className="grid grid-cols-1 mb-4">
                <TabsTrigger
                  value="list-customer-payment"
                  className="flex items-center gap-1 data-[state=active]:text-primary"
                >
                  <CreditCard className="h-4 w-4" />
                  {t("tabs.payment-history")}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="list-customer-payment" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <List className="h-5 w-5 text-primary" />
                      {t("payment-history.title")}
                    </CardTitle>
                    {payments.length > 0 && (
                      <div className="flex justify-between text-sm text-muted-foreground">
                        <span>
                          {t("payment-history.total-payment")}:{" "}
                          {payments.length}
                        </span>
                        <span>
                          {t("payment-history.total-paid")}:{" "}
                          {formatter.format(calculations.totalPaid)}
                        </span>
                      </div>
                    )}
                  </CardHeader>
                  <CardContent>
                    {isLoadingPayments ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin mr-2" />
                        {t("loading")}
                      </div>
                    ) : payments.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        <CreditCard className="h-12 w-12 mx-auto mb-2 opacity-50" />
                        <p>{t("no-data")}</p>
                      </div>
                    ) : (
                      <div className="border rounded-md overflow-hidden">
                        <div className="bg-secondary">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead className="h-10 w-1/3">
                                  {t("payment-history.payment-date")}
                                </TableHead>
                                <TableHead className="h-10 w-1/3">
                                  {t("payment-history.amount")}
                                </TableHead>
                                <TableHead className="h-10 w-1/3">
                                  {t("payment-history.created-at")}
                                </TableHead>
                              </TableRow>
                            </TableHeader>
                          </Table>
                        </div>

                        <div className="max-h-[150px] overflow-y-auto">
                          <Table>
                            <TableBody>
                              {payments.map((payment, index) => (
                                <TableRow
                                  key={payment.id ?? `payment-${index}`}
                                >
                                  <TableCell className="py-3 w-1/3">
                                    {new Date(
                                      payment.payment_date
                                    ).toLocaleDateString()}
                                  </TableCell>
                                  <TableCell className="font-medium py-3 w-1/3">
                                    {formatter.format(
                                      Number(payment.payment_amount)
                                    )}
                                  </TableCell>
                                  <TableCell className="text-muted-foreground py-3 w-1/3">
                                    {new Date(
                                      payment.created_at
                                    ).toLocaleDateString()}{" "}
                                    {new Date(
                                      payment.created_at
                                    ).toLocaleTimeString()}
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Updated PaidAmountRenderer to use the new component
export const PaidAmountRenderer = ({ data }: { data: any }) => {
  const vehicleData = useMemo(() => {
    const customerId = data?.customer_of_customer_id;
    const vehicleId = data?.vechicles?.id;
    const vehiclePrice = data?.vechicles?.price || 0;
    const customerProfit = data?.vechicles?.customer_profit || 0;

    return {
      customerId,
      vehicleId,
      vehiclePrice,
      customerProfit,
    };
  }, [data]);

  return (
    <PaymentSummary
      vehicleId={vehicleData.vehicleId}
      customerId={vehicleData.customerId}
      vehiclePrice={vehicleData.vehiclePrice}
      customerProfit={vehicleData.customerProfit}
    />
  );
};

// UnpaidAmountSummary Component
interface UnpaidAmountSummaryProps {
  vehicleId?: number;
  customerId?: number;
  vehicleData?: {
    vehiclePrice: number;
    customerProfit: number;
    storageCharges: number;
  };
  mixShippingData?: any;
  className?: string;
  showAsButton?: boolean;
  buttonText?: string;
}

export const UnpaidAmountSummary = ({
  vehicleId,
  customerId,
  vehicleData,
  mixShippingData,
  className = "",
  showAsButton = false,
  buttonText,
}: UnpaidAmountSummaryProps) => {
  const [payments, setPayments] = useState<Payment[]>([]);
  const fetchClient = useFetchClient();

  const formatter = useMemo(
    () =>
      new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
        maximumFractionDigits: 2,
        minimumFractionDigits: 0,
      }),
    []
  );

  const calculations = useMemo(() => {
    const totalPaid = payments.reduce(
      (sum, payment) => sum + Number(payment.payment_amount),
      0
    );

    const mixShippingCharges =
      mixShippingData?.mix_shipping_vehicle_charges || [];
    const totalMixShippingCharges = mixShippingCharges.reduce(
      (sum: number, charge: any) => sum + (Number(charge.value) || 0),
      0
    );

    const transportationFee =
      (Number(mixShippingData?.freight) || 0) +
      (Number(mixShippingData?.vat_and_custom) || 0) +
      (Number(mixShippingData?.tow_amount) || 0) +
      (Number(mixShippingData?.clearance) || 0) +
      totalMixShippingCharges;

    const totalCharges =
      (vehicleData?.vehiclePrice || 0) +
      (vehicleData?.customerProfit || 0) +
      transportationFee +
      (vehicleData?.storageCharges || 0);

    const unPaid = totalCharges - totalPaid;

    return { unPaid, totalCharges, totalPaid };
  }, [payments, vehicleData, mixShippingData]);

  const fetchPayments = useCallback(async () => {
    if (!customerId || !vehicleId) return;
    try {
      const response = await fetchClient(
        `/v2/payments-v2/get-one-vehicle-payments`,
        {
          method: "GET",
          params: {
            vehicle_id: vehicleId,
          },
        }
      );

      if (response.data?.result && response.data?.data) {
        setPayments(response.data.data);
      }
    } catch (error) {
      toast.error("Error fetching payments" + error);
      setPayments([]);
    }
  }, [customerId, vehicleId, fetchClient]);

  useEffect(() => {
    fetchPayments();
  }, [fetchPayments]);

  if (showAsButton) {
    return (
      <button
        className={`text-sm font-medium ${
          calculations.unPaid <= 1
            ? "text-green-600 hover:text-green-700"
            : "text-red-600 hover:text-red-700"
        } ${className}`}
        disabled={!vehicleId || !customerId}
      >
        {buttonText || formatter.format(calculations.unPaid)}
      </button>
    );
  }

  return (
    <div
      className={`flex flex-col justify-center items-center text-xs w-full h-full leading-5 ${className}`}
    >
      <div className="flex items-center gap-1 mb-1">
        <div className="rounded-md flex leading-[22px] text-xs font-semibold overflow-hidden hover:cursor-pointer transition-all duration-200">
          <div
            className={`py-1 px-2 ${
              calculations.unPaid <= 1
                ? "bg-green-600/10 text-green-500 dark:text-green-300"
                : "bg-red-600/10 text-red-500 dark:text-red-300"
            } flex items-center`}
          >
            {formatter.format(calculations.unPaid)}
          </div>
        </div>
      </div>
    </div>
  );
};
