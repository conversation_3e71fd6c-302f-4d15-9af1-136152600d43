"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";

import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import {
  type FunctionComponent,
  RefObject,
  useCallback,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import useSidebarConfig from "../sidebarConfig";
import { PaymentRenderer } from "./PaymentRenderer";
import { PayementMethodRenderer } from "./PayementMethodRenderer";
import { AmountRenderer } from "./AmountRenderer";
import { TransactionRenderer } from "./TransactionRenderer";
import { RemainAppliedAmountRenderer } from "./RemainAppliedAmountRenderer";
import { ExchangeRateRenderer } from "./ExchangeRateRenderer";
import DetailCellRenderer from "./detailCellRenderer";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);
export type AuctionPaymentTye = {
  id: number;
  vin: string;
  lot_number: string;
  price: number;
  payments: any;
};

type Props = {
  gridTheme?: string;
  isDarkMode?: boolean;
  records: {
    page: number;
    per_page: number;
    total: number;
    data: AuctionPaymentTye[];
  } | null;
  gridRefProps?: RefObject<AgGridReact | null>;
  exportColDefs?: ColDef[];
};

const paginationPageSizeSelector = [5, 10, 20];

export const AuctionPayementsDataTable: FunctionComponent<Props> = ({
  records,
  gridRefProps,
  exportColDefs,
}: Props) => {
  const gridRef = useRef<AgGridReact>(null);
  const t = useTranslations("auction-payment-datatable");
  const sidebarConfig = useSidebarConfig();
  const detailCellRenderer = useCallback(DetailCellRenderer, []);
  const [colDefs] = useState<ColDef[]>([
    {
      headerName: "#",
      cellRenderer: (params: any) => {
        return !params.node?.rowIndex ? 1 : params.node?.rowIndex + 1;
      },
      minWidth: 60,
      maxWidth: 60,
    },
    {
      field: "payment",
      headerName: t("header.payment"),
      cellDataType: "text",
      cellRenderer: PaymentRenderer,
      minWidth: 170,
    },
    {
      field: "payment_method",
      headerName: t("header.payment-method"),
      minWidth: 120,
      cellRenderer: PayementMethodRenderer,
      valueGetter: (params) => {
        return params.data?.payments?.[0]?.state;
      },
    },
    {
      field: "amount",
      headerName: t("header.amount"),
      cellRenderer: AmountRenderer,
      minWidth: 170,
    },
    {
      field: "transaction_fee",
      headerName: t("header.transaction-fee"),
      minWidth: 120,
      cellRenderer: TransactionRenderer,
    },
    {
      field: "unapplied_amount",
      headerName: t("header.unapplied-amount"),
      minWidth: 170,
      cellRenderer: RemainAppliedAmountRenderer,
    },
    {
      field: "exchange_rate",
      headerName: t("header.exchange-rate"),
      minWidth: 170,
      cellRenderer: ExchangeRateRenderer,
    },
  ]);

  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
    }),
    []
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  const [quickFilterText] = useState<string>();

  const selectionColumnDef = useMemo(() => {
    return {
      minWidth: 44,
    };
  }, []);
  const { isRTL } = useDirection();

  return (
    <>
      <AgGridDataTable
        enableRtl={isRTL ? true : false}
        ref={gridRefProps || gridRef}
        selectionColumnDef={selectionColumnDef}
        columnDefs={exportColDefs || colDefs}
        rowData={records?.data || []}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        pagination={false}
        paginationPageSize={records?.per_page || 20}
        paginationPageSizeSelector={paginationPageSizeSelector}
        masterDetail
        detailCellRenderer={detailCellRenderer}
        detailCellRendererParams={{ t }}
        quickFilterText={quickFilterText}
        rowHeight={72}
        colResizeDefault="shift"
        headerHeight={60}
        sideBar={sidebarConfig}
        detailRowHeight={328}
      />
    </>
  );
};
