"use client";
import { CustomCellRendererProps } from "ag-grid-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { formatCurrency } from "@/lib/utils";
import {
  CircleDollarSign,
  FileText,
  ArrowUpDown,
  Hash,
  MessageSquare,
} from "lucide-react";
import React, { FunctionComponent } from "react";
import { useTranslations } from "next-intl";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import { LoadingSpinner } from "@/components/Common_UI/loading";

import { DetailCellData, PaymentDetail } from "../services/config";
import { usePaymentDetails } from "../services/payment-queries";

const DetailCellRenderer: FunctionComponent<
  CustomCellRendererProps<DetailCellData>
> = ({ data }) => {
  const t = useTranslations("auction-payment-datatable.payment_details");

  const { data: paymentDetails = [], isLoading } = usePaymentDetails(data?.id);

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">{t("no_data_available")}</p>
      </div>
    );
  }

  if (isLoading)
    return (
      <div className="w-full h-64 space-y-3 flex justify-center items-center">
        <LoadingSpinner className="h-10 w-10" />
      </div>
    );

  const getPaymentReference = (payment: PaymentDetail) => {
    switch (payment.type) {
      case "shipment":
        return payment?.shipmentInvoice?.invoice_number;
      case "clearance":
        return `PGLCB${payment?.clearanceInvoice?.id}`;
      case "exit_claim_charge":
        return `PGLE${payment?.exitClaimCharge?.id}`;
      case "detention_charge":
        return `PGLD${payment?.detentionCharge?.id}`;
      case "delivery_charge":
        return `PGLDO${payment?.deliveryChargeInvoice?.id}`;
      case "single_vcc":
        return `PGLV${payment?.singleVcc?.id}`;
      case "clear_log":
        return `PGLC${payment?.logInvoice?.id}`;
      default:
        return null;
    }
  };

  const renderPaymentAllocations = (payment: PaymentDetail) => {
    const total = payment.payment_allocations.reduce(
      (sum, allocation) => sum + parseFloat(allocation.amount),
      0
    );

    return (
      <div className="w-[280px] space-y-3">
        <div className="space-y-2">
          {payment.payment_allocations.map((allocation) => (
            <div
              key={allocation.id}
              className="flex items-center justify-between py-1"
            >
              <span className="text-sm capitalize text-muted-foreground">
                {allocation.type.replace(/_/g, " ")}
              </span>
              <span className="font-medium">
                {formatCurrency(allocation.amount)}
              </span>
            </div>
          ))}
        </div>
        <Separator />
        <div className="flex items-center justify-between pt-1">
          <span className="text-sm font-semibold">Total</span>
          <span className="font-semibold">
            {formatCurrency(total.toString())}
          </span>
        </div>
      </div>
    );
  };

  return (
    <div className="px-6">
      <Card className="border-none shadow-none">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg font-semibold">
            <FileText className="h-5 w-5 text-primary" />
            {t("label")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative rounded-lg border bg-card">
            <div className="sticky top-0 bg-card z-20 border-b">
              <Table>
                <TableHeader>
                  <TableRow className="hover:bg-transparent">
                    <TableHead className="h-11 px-4 text-sm w-[200px] text-center">
                      <div className="flex items-center gap-2 font-semibold">
                        <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
                        {t("payment-details.type")}
                      </div>
                    </TableHead>
                    <TableHead className="h-11 px-4 text-sm w-[280px] text-center">
                      <div className="flex items-center gap-2 font-semibold">
                        <Hash className="h-4 w-4 text-muted-foreground" />
                        {t("payment-details.reference")}
                      </div>
                    </TableHead>
                    <TableHead className="h-11 px-4 text-sm w-[200px] text-center">
                      <div className="flex items-center gap-2 font-semibold">
                        <CircleDollarSign className="h-4 w-4 text-muted-foreground" />
                        {t("payment-details.applied")}
                      </div>
                    </TableHead>

                    <TableHead className="h-11 px-4 text-sm text-center">
                      <div className="flex items-center gap-2 font-semibold">
                        <MessageSquare className="h-4 w-4 text-muted-foreground" />
                        {t("payment-details.remark")}
                      </div>
                    </TableHead>
                  </TableRow>
                </TableHeader>
              </Table>
            </div>

            <div className="max-h-[400px] overflow-auto">
              <Table>
                <TableBody>
                  {paymentDetails.length > 0 ? (
                    paymentDetails.map((payment) => (
                      <TableRow
                        key={payment.id}
                        className="hover:bg-muted/50 transition-colors"
                      >
                        <TableCell className="px-4 py-3 w-[200px] ">
                          <span className="capitalize font-medium">
                            {payment.type.replace(/_/g, " ")}
                          </span>
                        </TableCell>
                        <TableCell className="px-4 py-3 w-[300px]  ">
                          {payment.type === "shipment" ? (
                            <>
                              <div className="flex items-center gap-2 text-sm ">
                                <span className="text-muted-foreground">
                                  Inv#:
                                </span>
                                <span className="font-medium select-text">
                                  {getPaymentReference(payment) || "-"}
                                </span>
                              </div>
                              <div className="flex items-center gap-2 text-sm ">
                                <span className="text-muted-foreground">
                                  Cnt#:
                                </span>
                                <span className="font-medium select-text">
                                  {payment?.shipmentInvoice?.containers
                                    ?.container_number || "-"}
                                </span>
                              </div>
                            </>
                          ) : payment.type === "mix" ? (
                            <div className="space-y-1">
                              <div className="flex items-center gap-2 text-sm ">
                                <span className="text-muted-foreground">
                                  VIN:
                                </span>
                                <span className="font-medium select-text">
                                  {payment?.mixShippingVehicle?.vehicles?.vin ||
                                    "-"}
                                </span>
                              </div>
                              <div className="flex items-center gap-2 text-sm ">
                                <span className="text-muted-foreground">
                                  Lot:
                                </span>
                                <span className="font-medium select-text">
                                  {payment?.mixShippingVehicle?.vehicles
                                    ?.lot_number || "-"}
                                </span>
                              </div>
                            </div>
                          ) : payment.type === "auction" ? (
                            <div className="space-y-1">
                              <div className="flex items-center gap-2 text-sm ">
                                <span className="text-muted-foreground">
                                  VIN:
                                </span>
                                <span className="font-medium select-text">
                                  {payment?.vehicle?.vin || "-"}
                                </span>
                              </div>
                              <div className="flex items-center gap-2 text-sm ">
                                <span className="text-muted-foreground">
                                  Lot:
                                </span>
                                <span className="font-medium select-text">
                                  {payment?.vehicle?.lot_number || "-"}
                                </span>
                              </div>
                            </div>
                          ) : (
                            <div className="flex items-center gap-2 text-sm ">
                              <span className="text-muted-foreground">
                                Inv#:
                              </span>
                              <span className="font-medium select-text">
                                {getPaymentReference(payment) || "-"}
                              </span>
                            </div>
                          )}
                        </TableCell>
                        <TableCell className="px-4 py-3 w-[200px] ">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="cursor-help font-medium text-primary">
                                  {formatCurrency(payment.amount_applied)}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent
                                side="right"
                                className="p-4 bg-popover border rounded-lg shadow-lg"
                                sideOffset={5}
                              >
                                <div className="font-medium mb-2 pb-2 border-b">
                                  Payment Breakdown
                                </div>
                                {renderPaymentAllocations(payment)}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>

                        <TableCell className="px-4 py-3  max-w-[200px]">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="cursor-help truncate text-sm">
                                  {payment.payment_remark
                                    ? payment.payment_remark.length > 30
                                      ? payment.payment_remark.substring(
                                          0,
                                          30
                                        ) + "..."
                                      : payment.payment_remark
                                    : "-"}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent
                                side="left"
                                className="p-4 bg-popover border rounded-lg shadow-lg max-w-[400px]"
                                sideOffset={5}
                              >
                                <div className="font-medium mb-2 pb-2 border-b">
                                  Remark Details
                                </div>
                                <div className="text-sm text-muted-foreground leading-relaxed">
                                  {payment.payment_remark ||
                                    "No remark available"}
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="h-24 text-center">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <FileText className="h-8 w-8 mb-2" />
                          <p>No payment details available</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DetailCellRenderer;
