import type { CustomCellRendererProps } from "ag-grid-react";
import { type FunctionComponent } from "react";

export const TransactionRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data }) => {
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: data.currency,
    maximumFractionDigits: 2,
  });
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="rounded-md flex leading-[22px] text-xs font-semibold overflow-hidden">
        <div className="px-2 bg-green-600/10 text-green-500 dark:text-green-300">
          {formatter.format(data?.transaction_fee)}
        </div>
      </div>
    </div>
  );
};
