import { Separator } from "@/components/ui/separator";
import type { CustomCellRendererProps } from "ag-grid-react";
import { useTranslations } from "next-intl";
import { type FunctionComponent } from "react";

export const PayementMethodRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data }) => {
  const t = useTranslations("auction-payment-datatable.body");
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis">
        <div className="min-w-[70px] text-muted-foreground">
          {t("currency")}:
        </div>
        <div className="">{data.currency}</div>
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis">
        <div className="min-w-[70px] text-muted-foreground">
          {t("payment_method")}:
        </div>
        <div className="truncate">{data.payment_method}</div>
      </div>
    </div>
  );
};
