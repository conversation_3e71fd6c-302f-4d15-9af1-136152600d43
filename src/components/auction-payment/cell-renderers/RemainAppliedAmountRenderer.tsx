import type { CustomCellRendererProps } from "ag-grid-react";

import { type FunctionComponent } from "react";

export const RemainAppliedAmountRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data }) => {
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: data.currency,
    maximumFractionDigits: 2,
  });
  const charges =
    Number(data?.amount) +
    Number(data?.transaction_fee) +
    Number(data?.inapplicable_amount);
  const unpaid = charges - Number(data?.amount_applied);
  return (
    <div className="flex flex-col justify-center items-center w-full h-full leading-5">
      <div className="rounded-md flex leading-[22px] text-xs font-semibold overflow-hidden">
        <div className="px-2 bg-blue-600/10 text-blue-500 dark:text-blue-300">
          {formatter.format(data?.amount_applied)}
        </div>
        <div
          className={`
            px-2 ${
              unpaid > 1
                ? "bg-red-600/10 text-red-400"
                : "bg-green-600/10 text-green-500 dark:text-green-300"
            }`}
        >
          {formatter.format(unpaid)}
        </div>
      </div>
    </div>
  );
};
