"use client";
import React, { useMemo } from "react";
import { ColDef } from "ag-grid-community";
import { useTranslations } from "next-intl";
import { getAuctionPayment } from "./auction-payment-service";
import { AuctionPaymentTye } from "../cell-renderers/auction-payment-data-table";
import { ExportModal } from "@/components/Common_UI/export-modal";
import { toast } from "sonner";

type AuctionPaymentExportProps = {
  records: {
    page: number;
    per_page: number;
    total: number;
    data: AuctionPaymentTye[];
  };
};

export default function AuctionPaymentExport({
  records,
}: AuctionPaymentExportProps) {
  const t = useTranslations("export-modal");

  // Column definitions for AG Grid
  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        field: `id`,
        headerName: "Payment Number",
        valueGetter: (params) => `PGLPN${params?.data?.id}`,
      },
      {
        field: "transaction_number",
        headerName: "Transaction Number",
      },
      {
        field: "payment_method",
        headerName: "Payment Method",
      },
      {
        headerName: "VIN",
        valueGetter: (params) => {
          const payments = params.data.payments || [];
          return payments
            .map((payment: any) => payment?.vehicle?.vin || "")
            .filter((vin: any) => vin)
            .join("\n");
        },
      },

      { field: "amount", headerName: "Amount" },
      { field: "amount_applied", headerName: "Amount Applied" },
      {
        field: "remaining_amount",
        headerName: "Remaining Amount",
        valueGetter: (params) => {
          const charges =
            Number(params?.data?.amount) +
            Number(params?.data?.transaction_fee) +
            Number(params?.data?.inapplicable_amount);
          return charges - Number(params?.data?.amount_applied);
        },
      },
      { field: "currency", headerName: "Currency" },
      { field: "state", headerName: "Status" },
      {
        field: "exchange_rate",
        headerName: "Exchange Rate",
      },
    ],
    []
  );

  // Function to fetch all auction payment data
  const fetchAllAuctionPayments = async (): Promise<AuctionPaymentTye[]> => {
    const response = await getAuctionPayment({
      params: {
        state: "",
        page: 1,
        per_page: records.total,
        search: "",
        exactMatch: false,
        filterData: "",
      },
    });
    return response.data;
  };
  // Translations for the export modal
  const exportTranslations = {
    title: t("title"),
    exportData: t("sub-title"),
    subTitle: t("sub-title"),
    currentData: t("current-data"),
    allData: t("all-data"),
    cancel: t("cancel"),
    export: t("export"),
  };

  return (
    <ExportModal
      columnDefs={colDefs}
      currentData={records.data}
      exportFileName="Auction Payments Data"
      fetchAllData={fetchAllAuctionPayments}
      totalItems={records.total}
      translations={exportTranslations}
      triggerButtonPosition="relative"
      onExportSuccess={() => toast("Auction Payments Export Completed")}
    />
  );
}
