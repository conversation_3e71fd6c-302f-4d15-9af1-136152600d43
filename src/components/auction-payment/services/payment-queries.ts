"use client";
import { useQuery } from "@tanstack/react-query";
import { PaymentDetail } from "./config";
import { useFetchClient } from "@/utils/axios";

async function fetchPaymentDetails(
  paymentId: number | undefined,
  fetchClient: ReturnType<typeof useFetchClient>
) {
  if (!paymentId) throw new Error("Payment ID is required");
  const res = await fetchClient(
    `/v2/auction-payments-v2/payments/${paymentId}`
  );
  if (res.data?.result && res.data?.data) {
    return res.data.data;
  }
  throw new Error("Failed to fetch payment details");
}

export function usePaymentDetails(paymentId: number | undefined) {
  const fetchClient = useFetchClient();
  return useQuery<PaymentDetail[]>({
    queryKey: ["paymentDetails", paymentId],
    queryFn: () => fetchPaymentDetails(paymentId, fetchClient),
    enabled: !!paymentId,
    gcTime: 1 * 60 * 60 * 1000, // Keep data in cache for 1 hour
    staleTime: 10 * 60 * 1000, // Consider data fresh for 10 minutes
  });
}
