"use server";
import { transformObject } from "@/lib/transferObject";
import { getAuctionPayment } from "./auction-payment-service";
// Define allowed keys (Only these fields will be processed)
const allowedKeys = [
  "from_created_at",
  "to_created_at",
  "from_updated_at",
  "to_updated_at",
];

// Define dynamic mapping (Customize as needed)
const mapping = {
  from_created_at: `created_at.from`,
  to_created_at: "created_at.to",
  from_updated_at: "updated_at.from",
  to_updated_at: "updated_at.to",
};

export async function loadAuctionPaymentData(
  baseState: string,
  searchParams: any,
  params: {
    page: number;
    per_page: number;
    search?: string;
    filterData?: string;
    status?: string;
  }
) {
  try {
    const transformedFilters = transformObject(
      searchParams,
      allowedKeys,
      mapping
    );

    const result = await getAuctionPayment({
      params: {
        state: baseState,
        page: params.page,
        per_page: params.per_page,
        search: params.search || "",
        exactMatch: false,
        filterData:
          Object.keys(transformedFilters).length !== 0
            ? JSON.stringify(transformedFilters)
            : "",
      },
    });

    const response = {
      data: Array.isArray(result.data) ? result.data : [],
      total: result.total || 0,
      page: params.page,
      per_page: params.per_page,
      success: true,
    };

    return response;
  } catch (error) {
    console.error(error);
    return {
      data: [],
      total: 0,
      page: params.page,
      per_page: params.per_page,
      success: false,
    };
  }
}
