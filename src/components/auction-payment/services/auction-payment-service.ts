"use server";
import api from "@/utils/axios-server";
import { removeMatchingValue } from "@/utils/helper-function";
type PaymentParamType = {
  state: string;
  page: number;
  per_page: number;
  search: string;
  exactMatch: boolean;
  filterData: string;
};

export async function getAuctionPayment({
  params,
}: {
  params: PaymentParamType;
}) {
  removeMatchingValue(params, "state", ["1620278055.png", "undefined"]);
  try {
    const response = await api.get(`/v2/auction-payments-v2`, {
      params: { ...params },
    });

    return response.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message);
  }
}
