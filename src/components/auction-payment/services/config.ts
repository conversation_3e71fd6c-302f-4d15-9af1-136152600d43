import { ColDef } from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import { RefObject } from "react";

// Payment detail type for API responses
export interface PaymentDetail {
  id: number;
  amount_applied: string;
  exchange_rate: number;
  type: string;
  state: string;
  customer_tran_id: number;
  shipment_invoice_id: number | null;
  clearance_invoice_id: number | null;
  payment_date: string;
  mix_shipping_vehicle_id: number | null;
  log_invoice_id: number | null;
  single_vcc_id: number | null;
  exit_claim_charge_id: number | null;
  detention_charge_id: number | null;
  delivery_charge_invoice_id: number | null;
  payment_remark: string;
  vehicle_id: number | null;
  created_at: string;
  created_by: number | null;
  updated_at: string | null;
  updated_by: number | null;
  deleted_at: string | null;
  deleted_by: number | null;
  shipmentInvoice?: { 
    invoice_number?: string;
    containers?: {
      container_number?: string;
    }
  };
  mixShippingVehicle?: {
    vehicles?: {
      vin?: string;
      lot_number?: string;
    };
  };
  vehicle?:{
    vin?: string;
    lot_number?: string;
  };
  clearanceInvoice?: { id?: number };
  exitClaimCharge?: { id?: number };
  detentionCharge?: { id?: number };
  deliveryChargeInvoice?: { id?: number };
  singleVcc?: { id?: number };
  logInvoice?: { 
    invoice_number?: string;
    id?: number;
  };
  payment_allocations: { id: number; type: string; amount: string }[];
}

export interface DetailCellData {
  id: number;
  payment_method: string;
  state: string;
  transaction_number: string;
  amount: string;
  amount_applied: string;
  currency: string;
  payment_date: string;
  remark: string;
}

// Payment parameter type for API requests
export type PaymentParamType = {
  status: string;
  page: number;
  per_page: number;
  search: string;
  exactMatch: boolean;
  filterData: string;
};

// Page props type for Next.js pages
export type PageProps = {
    params: Promise<any>;
    searchParams: any;
  };


// Main payment type definition
export type PaymentType = {
  id: number;
  amount_applied: string;
  exchange_rate: number;
  type: string;
  state: string;
  customer_tran_id: number;
  shipment_invoice_id: number | null;
  clearance_invoice_id: number | null;
  payment_date: string;
  mix_shipping_vehicle_id: number | null;
  log_invoice_id: number | null;
  single_vcc_id: number | null;
  exit_claim_charge_id: number | null;
  detention_charge_id: number | null;
  delivery_charge_invoice_id: number | null;
  payment_remark: string;
  vehicle_id: number | null;
  created_at: string;
  created_by: number | null;
  updated_at: string | null;
  updated_by: number | null;
  deleted_at: string | null;
  deleted_by: number | null;
  shipmentInvoice?: { 
    invoice_number: string;
  };
  mixShippingVehicle?: {
    vehicles?: {
      vin: string;
      lot_number: string;
    };
  };
  vehicle?: {
    vin: string;
    lot_number: string;
  };
  clearanceInvoice?: { 
    invoice_number: string;
  };
  exitClaimChargeInvoice?: { 
    invoice_number: string;
  };
  detentionChargeInvoice?: { 
    invoice_number: string;
  };
  deliveryChargeInvoice?: { 
    invoice_number: string;
  };
  singleVccInvoice?: { 
    invoice_number: string;
  };
  logInvoice?: { 
    invoice_number: string;
  };
  payment_allocations: {
    id: number;
    type: string;
    amount: string;
  }[];
};

// Props type for payment components
export type PaymentProps = {
  gridTheme?: string;
  isDarkMode?: boolean;
  records: {
    page: number;
    per_page: number;
    total: number;
    data: PaymentType[] | PaymentQueryType[];
  };
  gridRefProps?: RefObject<AgGridReact<any> | null>;
  exportColDefs?: ColDef[];
  onGridReady?: (params: any) => void;
  onFilterChanged?: (params: any) => void;
  onSortChanged?: (params: any) => void;
  onSelectionChanged?: (params: any) => void;
};

// Payment query type for data fetching
export type PaymentQueryType = {
  skip: number;
  take: number;
  where: Record<string, any>;
  orderBy?: Record<string, any>;
  include: {
    companies: { select: { name: boolean } };
    customer: { select: { fullname: boolean } };
    attachments: boolean;
  };
}; 