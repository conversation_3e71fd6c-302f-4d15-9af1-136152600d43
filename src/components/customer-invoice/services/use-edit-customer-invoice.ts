"use client";
import { useMutation } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
import { CustomerVehicleInvoiceType } from "../customer-invoice-form";
export const useEditCustomerVehicleInvoice = () => {
  const fetchClient = useFetchClient();

  const { mutate: editCustomerVehicleInvoice, isPending } = useMutation({
    mutationFn: async (data: CustomerVehicleInvoiceType & { id?: number }) =>
      await fetchClient(
        `/v2/customer-vehicle-inovice/${data.id}`,
        {
          data: data,
          method: "PATCH",

        }
      ),
  });
  return { editCustomerVehicleInvoice, isPending };
};
