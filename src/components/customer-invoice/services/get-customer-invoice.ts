"use server";

import axios from "@/utils/axios-server";
import { removeMatchingValue } from "@/utils/helper-function";

type customerType = {
  status: string;
  page: number;
  per_page: number;
  search: string;
};

export async function getCusotmerInvoice({ params }: { params: customerType }) {
  try {
    // removing undefined and 1620278055.png from status
    removeMatchingValue(params, "status", ["1620278055.png", "undefined"]);
    const response = await axios.get(`/v2/customer-vehicle-inovice`, {
      params: { ...params },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message);
  }
}
