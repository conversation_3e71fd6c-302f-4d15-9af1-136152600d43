"use client";
import { useMutation } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
import { CustomerVehicleInvoiceType } from "../customer-invoice-form";
export const useAddCustomerVehicleInvoice = () => {
  const fetchClient = useFetchClient();

  const { mutate: addCustomerVehicleInvoice, isPending } = useMutation({
    mutationFn: async (data: CustomerVehicleInvoiceType) =>
      await fetchClient(`/v2/customer-vehicle-inovice`, {
        data: data,
        method: "POST",
      }),
  });
  return { addCustomerVehicleInvoice, isPending };
};
