"use client";
import { useMutation } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
export const useDeleteCustomerVehicleInvoice = () => {
  const fetchClient = useFetchClient();

  const { mutate: deleteCustomerVehicleInvoice, isPending } = useMutation({
    mutationFn: async (data: number[]) =>
      await fetchClient(`/v2/customer-vehicle-inovice/${data}`, {
        method: "DELETE",
      }),
  });
  return { deleteCustomerVehicleInvoice, isPending };
};
