"use client";
import { useFetchClient } from "@/utils/axios";
import { useQuery } from "@tanstack/react-query";

export type VehicleOption = {
  value: string;
  label: string;
};
export function useGetOneCustomerInvoice(id: number) {
  const fetchClient = useFetchClient()
  const { data, isLoading } = useQuery({
    queryKey: ['customer-invoice', id],
    queryFn: async () => {
      try {
        const res = await fetchClient(`/v2/customer-vehicle-inovice/${id}`, {
          method: "GET"
        })
        return res.data?.data
      } catch (error) {
        console.error("Error fetching customer invoice:", error)
        return []
      }
    },
  })

  return { customerInvoice: data, isLoading }
}