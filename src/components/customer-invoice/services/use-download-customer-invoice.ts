"use client";
import { useMutation } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";

export const useDownloadCustomerInvoice = () => {
  const fetchClient = useFetchClient();
  // download/:id
  const { mutate: downloadCustomerInvoice, isPending } = useMutation({
    mutationFn: async (item: any) => {
      const res = await fetchClient(`/v2/customer-vehicle-inovice/download/${item.id}`,
        { responseType: "blob" }
      );
      const filename = `invoice ${item?.id}.pdf`;
      const url = window.URL.createObjectURL(new Blob([res.data]));
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
  });
  return { downloadCustomerInvoice, isPending };
};
