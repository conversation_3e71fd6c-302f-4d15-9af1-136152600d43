import { Card } from '@/components/ui/card';
import React from 'react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import Image from 'next/image';
import { useGetOneCustomerInvoice } from '../services/use-get-one-customer-invoice';
import { format } from 'date-fns';
import { useTranslations } from 'next-intl';
import { LoadingSpinner } from '@/components/Common_UI/loading';

const copartOrIAAI = ['copart', 'iaai']
export default function CustomerInvoiceDetails({ id }: { id: number }) {

  const t = useTranslations("customer-invoice");
  const { customerInvoice, isLoading } = useGetOneCustomerInvoice(id)

  if (isLoading) return <div className='flex justify-center items-center h-[60vh]'><LoadingSpinner /></div>
  const invoiceType = getInvoiceLogo(customerInvoice)
  const newTotalAmount = customerInvoice?.customer_vehicle?.reduce((acc: number, cur: any) => {
    if (getInvoiceLogo(customerInvoice).type === "pgl") {
      const subTotal = cur?.mix_vehicle?.mix_shipping_vehicle_charges?.reduce((acc1: number, cur: any) => acc1 + cur.value, 0) ||
        0
      return acc + (subTotal + (cur?.mix_vehicle?.vehicles?.price ?? 0))
    }
    else {
      return acc + cur?.mix_vehicle?.vehicles?.price || 0
    }
  }, 0)

  const trasferFee = (newTotalAmount * 0.004)
  const tatol = invoiceType.type === "pgl" ? trasferFee + newTotalAmount : newTotalAmount
  return (

    <Card className="container mx-auto p-4 max-w-5xl shadow-none">
      <div className="flex flex-col md:flex-row justify-between items-center mb-8 gap-4">
        <div className="flex items-center flex-col pl-3">
          <Image src={invoiceType.url} width={250} height={250} alt='PGL Logo' />
        </div>
        <Card className="w-full md:w-auto shadow-none">
          <div className=" px-4 py-2 text-center font-semibold">{t('details.client_info')}</div>
          <table className="min-w-full border-collapse">
            <tbody>
              <tr className="border ">
                <td className="border  px-4 py-2">{t('details.invoice_no')}</td>
                <td className="border  px-4 py-2">{customerInvoice?.id}</td>
              </tr>
              <tr className="border ">
                <td className="border  px-4 py-2">{t('details.date')}</td>
                <td className="border  px-4 py-2">{customerInvoice?.date ? format(customerInvoice.date, "yyyy-MM-dd") : ''}</td>
              </tr>
              <tr className="border ">
                <td className="border  px-4 py-2">{t('details.receiver_name')}</td>
                <td className="border  px-4 py-2">{customerInvoice?.receiver_name}</td>
              </tr>
              <tr className="border ">
                <td className="border  px-4 py-2">{t('details.customer_id')}</td>
                <td className="border  px-4 py-2">{customerInvoice?.customer_id}</td>
              </tr>
            </tbody>
          </table>
        </Card>
      </div>
      <div className="space-y-4">
        <div className="overflow-x-auto">
          <Table className="border-collapse w-full shadow-none">
            <TableHeader>
              <TableRow className="border">
                <TableHead className="border  px-4 py-2 text-sm text-start"> <span className="font-semibold">{t('details.sale_date')}</span></TableHead>
                <TableHead className="border  px-4 py-2 text-sm text-start"> <span className="font-semibold">{t('details.description')}</span></TableHead>
                <TableHead className="border  px-4 py-2 text-sm text-start"> <span className="font-semibold">#VIN</span></TableHead>
                <TableHead className="border  px-4 py-2 text-sm text-start"> <span className="font-semibold">{t('details.amount')}</span></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>

              {customerInvoice?.customer_vehicle?.reverse()?.map((item: any, index1: number) => {
                const copartOrIaai = copartOrIAAI.includes(invoiceType.type)
                const description = `${item?.mix_vehicle?.vehicles?.year ?? ''} ${item?.mix_vehicle?.vehicles?.make ?? ''} ${item?.mix_vehicle?.vehicles?.model ?? ''} ${item?.mix_vehicle?.vehicles?.color ?? ''
                  }`

                if (copartOrIaai) return <InvoiceDataTable
                  key={index1}
                  sale_date={customerInvoice?.sale_date}
                  vin={item?.mix_vehicle?.vehicles.vin}
                  description={`${description} (Auction Price)`}
                  price={item?.mix_vehicle?.vehicles.price}
                />

                return item?.mix_vehicle?.mix_shipping_vehicle_charges?.length > 0
                  ?
                  <React.Fragment key={index1}>
                    {/* Always render one more row after charges */}
                    <InvoiceDataTable
                      key={`${item?.mix_vehicle?.vehicles?.vin}-base-row`}
                      item={null}
                      sale_date={customerInvoice?.sale_date}
                      vin={item?.mix_vehicle?.vehicles?.vin}
                      description={`${description} (Auction Price)`}
                      price={item?.mix_vehicle?.vehicles?.price}
                    />
                    {item.mix_vehicle.mix_shipping_vehicle_charges?.filter((item: any) => item.value > 0).map((el: any, index: number) => (

                      <InvoiceDataTable
                        key={`charge-${index}`}
                        item={el}
                        sale_date={customerInvoice?.sale_date}
                        vin={item?.mix_vehicle?.vehicles?.vin}
                        description={undefined}
                        price={0}
                      />
                    ))}

                  </React.Fragment>
                  : (
                    <InvoiceDataTable
                      key={`${item?.mix_vehicle?.vehicles?.vin}-no-charge`}
                      item={null}
                      sale_date={customerInvoice?.sale_date}
                      vin={item?.mix_vehicle?.vehicles?.vin}
                      description={`${description} (Auction Price)`}
                      price={item?.mix_vehicle?.vehicles?.price}
                    />
                  )
              })}
              {invoiceType.type === "pgl" && <TableRow className="border  font-bold ">
                <TableCell colSpan={3} className="border  px-4 py-2 text-right">
                  {t('details.bank_transfer')}
                </TableCell>
                <TableCell className="border  px-4 py-2 text-right">${trasferFee.toFixed(2).toLocaleString()}</TableCell>
              </TableRow>}
              <TableRow className="border  font-bold ">
                <TableCell colSpan={3} className="border  px-4 py-2 text-right">
                  {t('details.total')}
                </TableCell>
                <TableCell className="border  px-4 py-2 text-right">${tatol.toFixed(2).toLocaleString()}</TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div></div>
      <div className='p-2 my-2'>
        <p>{t('details.payment')}</p>
        {
          invoiceType.type === "pgl" ? <>
            <p>{t('details.port_loading')}</p>
          </> : <>
            <p>{t('details.line1')}</p>
            <p>{t('details.line2')}</p>
            <p>{t('details.buyer')} {extractBuyerNameAndLot(customerInvoice?.customer_vehicle, "buyer")}</p>
            <p>{t('details.lot_stock')} {extractBuyerNameAndLot(customerInvoice?.customer_vehicle, "lot")}</p>
            <p>{t('details.port_loading')}</p>
            <p>{t(`details.${invoiceType.type === "iaai" ? "iaai" : "copart"}_discharge`)}</p>
            <p>{t('details.prepayment')}</p>
            <p>{t(`details.${invoiceType.type === "iaai" ? "iaai" : "copart"}_address`)}</p>
          </>
        }
      </div>
      <Card className='shadow-none '>
        <div className=" px-4 py-2 flex justify-center items-center">
          <span className="font-semibold">{t('details.wire_info')}</span>
        </div>
      </Card>
      <div className='flex flex-col gap-y-1 p-2'>
        <p>{t('details.bank_name')} {customerInvoice?.bank_account?.bank_name}</p>
        <p>{t('details.account_name')} {customerInvoice?.bank_account?.account_number}</p>
        <p>{t('details.account')} {customerInvoice?.bank_account?.account_name} </p>
        {/* <p>{t('details.routing_one')}</p>
        <p>{t('details.routing_two')}</p>
        <p>{t('details.swift_code')} {customerInvoice?.bank_account?.bic_code}</p>
        <p>{t('details.bank_add')} {customerInvoice?.bank_account?.ibn}</p>
        <p>{t('details.company_add')}</p> */}
      </div>
    </Card >

  )
}
function extractBuyerNameAndLot(item: any, field: "buyer" | "lot") {
  if (field === "buyer") return item?.map((el: any) => el.mix_vehicle?.vehicles.buyer_number)?.join(",")
  if (field === "lot") return item?.map((el: any) => el.mix_vehicle?.vehicles.lot_number)?.join(",")
}
type chargesTableType = {
  item?: any,
  sale_date: string,
  vin: string,
  description?: string,
  price: number
}
function InvoiceDataTable({ item, sale_date, vin, description, price }: chargesTableType) {
  console.log(item, price);

  return <TableRow className="border ">
    <TableCell className="border  px-4 py-2 text-sm text-start">{sale_date ? format(sale_date, "yyyy-MM-dd") : ''}</TableCell>
    <TableCell className="border  px-4 py-2 text-sm text-start">{description ? description : item?.name?.replace(/_/g, ' ')}</TableCell>
    <TableCell className="border  px-4 py-2 text-sm text-start">{vin}</TableCell>
    <TableCell className="border  px-4 py-2 text-sm text-start">{`${price !== 0 ? `${price}` : item?.value}`}</TableCell>
  </TableRow>

}

function getInvoiceLogo(invoice: any): { url: string; type: string } {
  const vehicle = invoice?.customer_vehicle;
  if (!vehicle) return { url: "/logo-250.png", type: "pgl" };

  const isAuction = vehicle.every((item: any) => item.is_auction === true)
  const isShipment = vehicle.every((item: any) => item.is_shipment === true)

  const auctionNames = vehicle.reduce((acc: string[], cur: any) => {
    const name = cur.mix_vehicle?.vehicles?.auction_name;
    if (name) acc.push(name.toLowerCase());
    return acc;
  }, [])
    .filter(Boolean) as string[];

  if (isAuction) {
    const copart = auctionNames.every((item: any) => item.toLowerCase() === "copart")
    const iaai = auctionNames.every((item: any) => item.toLowerCase() === "iaai")
    console.log(isAuction && !isShipment, isAuction, isShipment);
    if (copart) return { url: "/coport2.png", type: "copart" }
    else if (iaai) return { url: "/iaai.png", type: "iaai" };
    else return { url: "/logo-250.png", type: "pgl" };
  }
  else return { url: "/logo-250.png", type: "pgl" };
}

