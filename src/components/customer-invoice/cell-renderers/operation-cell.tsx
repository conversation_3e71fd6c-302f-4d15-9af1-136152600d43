import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuShortcut, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { CustomCellRendererProps } from 'ag-grid-react'
import { Edit2Icon, EllipsisVertical, Eye, Trash } from 'lucide-react'
import React, { FunctionComponent, useState } from 'react'
import CustomerOfCustomerForm from '../customer-invoice-form'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import { useDirection } from '@/hooks/useDirection'
import { toast } from 'sonner'
import { LoadingSpinner } from '@/components/Common_UI/loading'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'
import { useDeleteCustomerVehicleInvoice } from '../services/use-delete-customer-invoice'
import CustomerInvoiceDetails from './customer-invoice-details'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'

export const OperationCell: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const [open, setOpen] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [showDetails, setShowDetails] = useState(false)
  const { dir, isRTL } = useDirection();
  const t = useTranslations("customer-invoice");
  const router = useRouter()
  const { deleteCustomerVehicleInvoice, isPending } = useDeleteCustomerVehicleInvoice();
  const onLogout = () => {
    deleteCustomerVehicleInvoice([data.id], {
      onSuccess: () => {
        router.refresh()
        toast.success('Customer of customer deleted successfully!');
        setIsOpen(false);
      },
      onError: () => {
        toast.error('Deletion failed. Please try again.');
      }
    })
  }
  return (
    <>
      <DropdownMenu >
        <DropdownMenuTrigger asChild disabled={data?.deleted_at}>
          <Button variant="link" size={'icon'}><EllipsisVertical className="w-16 h-16" /></Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-24">
          <DropdownMenuGroup>
            <DropdownMenuItem onClick={() => setOpen(true)}>
              {t('body.edit')}
              <DropdownMenuShortcut className={`${isRTL ? "mr-auto ml-0" : ""}`}><Edit2Icon className="w-4 h-4" /></DropdownMenuShortcut>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setShowDetails(true)}>
              {t('body.details')}
              <DropdownMenuShortcut className={`${isRTL ? "mr-auto ml-0" : ""}`}><Eye className="w-4 h-4" /></DropdownMenuShortcut>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setIsOpen(true)}>
              {t('body.delete')}
              <DropdownMenuShortcut className={`${isRTL ? "mr-auto ml-0" : ""}`}><Trash className="w-4 h-4" /></DropdownMenuShortcut>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
      <CustomerOfCustomerForm open={open} setOpen={setOpen}
        formData={data}
      />
      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className={`${isRTL ? "text-right" : ""}`}>
              {t('body.delete')}
            </AlertDialogTitle>
            <AlertDialogDescription className={`${isRTL ? "text-right" : ""}`}>
              {t('body.delete-description')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter dir={dir} className={`${isRTL ? "gap-2" : ""}`}>
            <AlertDialogCancel>{t('body.cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={onLogout} >
              {isPending ? <LoadingSpinner className="h-5 w-5" /> : t('body.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog open={showDetails} onOpenChange={setShowDetails} key={"customer-form"}>
        <DialogContent className="md:min-w-[768px] max-w-[968px] max-h-[80vh] overflow-auto">
          <DialogHeader>
            <DialogTitle>
              {t('details.title')}
            </DialogTitle>
          </DialogHeader>
          <CustomerInvoiceDetails id={data.id} />
        </DialogContent>
      </Dialog>
    </>
  )
}
