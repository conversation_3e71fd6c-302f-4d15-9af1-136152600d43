import type { CustomCellRendererProps } from "ag-grid-react";
import { PrinterChe<PERSON> } from "lucide-react";
import { type FunctionComponent } from "react";
import { LoadingSpinner } from "@/components/Common_UI/loading";
import { Button } from "@/components/ui/button";
import { useDownloadCustomerInvoice } from "../services/use-download-customer-invoice";

export const CustomerDownloadRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const { downloadCustomerInvoice, isPending } = useDownloadCustomerInvoice();
  return (
    <div className="flex items-center h-full gap-2 group">
      <div className="pt-1">
        <Button
          size={"icon"}
          variant={"outline"}
          onClick={() => downloadCustomerInvoice(data)}
          className=" h-8 w-8 "
        >
          {isPending ? (
            <LoadingSpinner className="h-5 w-5 p-0" />
          ) : (
            <PrinterCheck className="h-4 w-4 p-0" />
          )}
        </Button>
      </div>

    </div>
  );
};
