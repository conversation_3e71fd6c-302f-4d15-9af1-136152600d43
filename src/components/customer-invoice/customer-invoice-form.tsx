"use client";
import React, { useEffect, useRef, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from "../ui/dialog";
import { Label } from "../ui/label";
import { Input } from "../ui/input";

import { Card } from "../ui/card";
import { Button } from "../ui/button";
import { useAddCustomerVehicleInvoice } from "./services/use-add-customer-invoice";
import { z } from "zod";
import { Controller, ControllerRenderProps, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { LoadingSpinner } from "../Common_UI/loading";
import { useEditCustomerVehicleInvoice } from "./services/use-edit-customer-invoice";
import { useRouter } from "next/navigation";
import { CalendarIcon, } from "lucide-react";
import { useTranslations } from "next-intl";
import { useDirection } from "@/hooks/useDirection";
import { Calendar } from "../ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { MultiSelect } from "../Common_UI/multi-select";
import { useFetchClient } from "@/utils/axios";
import { useSession } from "next-auth/react";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import AutoComplete from "../Common_UI/auto-complate";

const VehicleSchema = z.object({
  id: z.number(),
  type: z.enum(["Auction", "Shipment", "Both"]),
  row_id: z.number().optional(),
});
const FormFieldSchema = z.object({
  customer_id: z.string().optional(),
  receiver_name: z.string().optional(),
  vehicles: z.array(VehicleSchema),
  date: z.date().optional(),
  sale_date: z.date().optional(),
  bank_account_id: z.number()
});
export type CustomerVehicleInvoiceType = z.infer<typeof FormFieldSchema>;
export default function CustomerInvoiceForm({
  open,
  setOpen,
  formData,
}: {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  formData?: any;
}) {
  const defultValues = {
    customer_id: formData?.customer_id,
    receiver_name: formData?.receiver_name,
    date: formData?.date ? new Date(formData?.date) : '',
    sale_date: formData?.date ? new Date(formData?.date) : '',
    vehicles: formData?.vehicles?.map((el: any) => ({ id: el.mix_id, row_id: el.row_id, type: el.is_auction && !el.is_shipment ? "Auction" : el.is_shipment && !el.is_auction ? "Shipment" : "Both" }))?.filter((el: any) => el.mix_id !== null),
    bank_account_id: formData?.bank_account_id
  } as CustomerVehicleInvoiceType

  const session = useSession()
  const router = useRouter();
  const { addCustomerVehicleInvoice, isPending } = useAddCustomerVehicleInvoice();
  const { editCustomerVehicleInvoice, isPending: isPendingEdit } =
    useEditCustomerVehicleInvoice();
  const typeOptions = ["Auction", "Shipment", "Both"];
  const t = useTranslations("customer-invoice");
  const isEidtSession = Boolean(formData?.id);
  const { isRTL } = useDirection();
  const form = useForm<CustomerVehicleInvoiceType>({
    resolver: zodResolver(FormFieldSchema),
    defaultValues: isEidtSession
      ? defultValues
      : {
        date: new Date(),
        customer_id: String(session.data?.profile.companies.code ?? ""),
        receiver_name: String(session.data?.profile.companies.name ?? ""),
      },
  });
  const fetchClient = useFetchClient();

  const onSubmit = async (
    data: CustomerVehicleInvoiceType & { id?: number }
  ) => {
    try {
      if (!isEidtSession) {
        addCustomerVehicleInvoice(data, {
          onSuccess: () => {
            toast.success("Customer Vehicle Invoice added successfully!");
            setOpen(false);
            form.reset();
            router.refresh();
          },
          onError: (er: any) => {
            toast.error(er.response?.data?.message || "Addition failed. Please try again.");
          },
        });
      } else {
        data.id = formData?.id;
        editCustomerVehicleInvoice(data, {
          onSuccess: () => {
            toast.success("Customer Vehicle Invoice edited successfully!");
            setOpen(false);
            form.reset();
            router.refresh();
          },
          onError: (er: any) => {
            toast.error(er.response?.data?.message || "Eidtion failed. Please try again.");
          },
        });
      }
    } catch (error: any) {
      toast.error(
        "Operation failed. Please try again.",
        error?.response?.data?.message
      );
    }

  };
  const handleError = (error: any) => {
    toast.error("Operation failed. Please try again." + error);
    toast.error("Operation failed. Please try again." + error);
  };
  const getVehicles = async (value: string) => {
    if (!value) return []
    const vehicles = await fetchClient("/v2/customer-vehicle-inovice/vehicle-auto-complete", {
      params: {
        query: value,
      },
    })
    if (vehicles.data?.data[0]?.vehicles) {
      form.setValue("sale_date", new Date(vehicles.data?.data[0]?.vehicles?.purchased_at))
    }
    return vehicles.data?.data?.map((item: any) => ({ value: `${item.id}`, label: item.vehicles.vin })) || [];
  }
  return (
    <Dialog open={open} onOpenChange={setOpen} key={"customer-form"}>
      <DialogContent className="min-w-[968px]">
        <DialogHeader>
          <DialogTitle>
            {t('title')}
          </DialogTitle>
        </DialogHeader>
        <Card className="shadow-none border-none p-2">
          <form
            onSubmit={form.handleSubmit(onSubmit, handleError)}
            className="space-y-2 max-w-3xl mx-auto py-2 "
            dir={isRTL ? "rtl" : "ltr"}
          >
            <div className="flex flex-row gap-2">

              <div className="w-full" >
                <Label htmlFor={"customer_id"}>{t('datatable.customer_id')}</Label>
                <Input
                  id={"customer_id"}
                  type={"text"}
                  {...form.register("customer_id")}
                  required
                />
              </div>
              <div className="w-full">
                <Label htmlFor={"receiver_name"}>{t('datatable.receiver_name')}</Label>
                <Input
                  id={"receiver_name"}
                  type={"text"}
                  {...form.register("receiver_name")}
                  required
                />
              </div>
            </div>
            <div className="flex flex-row gap-2">
              <div className="w-full">
                <Label >{t('datatable.created_date')}</Label>
                <Controller
                  name="date"
                  control={form.control}
                  render={({ field }) => (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>

                  )}
                />
              </div>
              <div className="w-full">
                <Label >
                  {t('datatable.purchase_date')}
                </Label>
                <Controller
                  name="sale_date"
                  control={form.control}
                  render={({ field }) => (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>

                  )}
                />
              </div>
            </div>
            <Controller
              name="bank_account_id"
              control={form.control}
              render={({ field, fieldState: { error } }) => {
                return (
                  <AutoComplete
                    url="/v2/autocomplete"
                    label={t('datatable.bank')}
                    className={`iceberg`}
                    fieldName="bank_name"
                    customeName="bank_name"
                    field={field}
                    error={error}
                    column={'bank_name'}
                    modal={'bank_accounts'}
                  />
                );
              }}
            />
            <div className="w-full">
              <Controller
                name="vehicles"
                control={form.control}
                render={({ field }) => (
                  <VehicleSelectorField
                    field={field}
                    typeOptions={typeOptions}
                    formData={formData}
                    getVehicles={getVehicles}
                  />
                )}
              />
            </div>
            <div
              className={`flex gap-2 justify-end ${isRTL ? "flex-row-reverse" : "flex-row"
                }`}
            >
              <Button
                type="button"
                onClick={() => {
                  setOpen(false);
                  form.reset();
                }}
                variant={"outline"}
              >
                {t("body.cancel")}
              </Button>
              <Button type="submit">
                {isPending || isPendingEdit ? (
                  <LoadingSpinner className="h-5 w-5" />
                ) : (
                  t("body.submit")
                )}
              </Button>
            </div>
          </form>
        </Card>
      </DialogContent >
    </Dialog >
  );
}
type VehicleType = {
  id: number;
  type: string;
};
type Props = {
  vehicleData: VehicleType[];
  typeOptions: string[];
  handleTypeChange: (id: number, newType: string) => void;
  labelCache: React.MutableRefObject<Record<string, string>>;
};
function VehicleTypeSelector({
  vehicleData,
  typeOptions,
  handleTypeChange,
  labelCache,
}: Props) {
  const t = useTranslations("customer-invoice");
  const id = useId();

  return (
    <div className="space-y-6 max-h-[300px] overflow-y-auto">
      {vehicleData.map((v) => (
        <fieldset key={v.id} className="flex flex-row items-center gap-2 justify-between">
          <div>

            <legend className="text-foreground  font-medium">
              {t('datatable.vehicles')}: <span className="px-3"> {labelCache.current[v.id] ?? v.id}</span>
            </legend>
          </div>
          <div>


            <RadioGroup
              className="flex flex-wrap gap-2"
              value={v.type}
              onValueChange={(value) => handleTypeChange(v.id, value)}
            >
              {typeOptions.map((type) => (
                <div
                  key={`${id}-${v.id}-${type}`}
                  className="border-input has-data-[state=checked]:border-primary/50 relative flex flex-col items-start gap-4 rounded-md border p-3 shadow-xs outline-none"
                >
                  <div className="flex items-center gap-2">
                    <RadioGroupItem
                      id={`${id}-${v.id}-${type}`}
                      value={type}
                      className="after:absolute after:inset-0"
                    />
                    <Label htmlFor={`${id}-${v.id}-${type}`}>{t(`datatable.${type?.toLowerCase()}`)}</Label>
                  </div>
                </div>
              ))}
            </RadioGroup>
          </div>
        </fieldset>
      ))}
    </div>
  );
}
let globalId = 0;
function useId() {
  const idRef = useRef<string>(undefined);
  if (!idRef.current) {
    idRef.current = `id-${++globalId}`;
  }
  return idRef.current;
}

type VehicleSelectorFieldProps = {
  field: ControllerRenderProps<any, "vehicles">;
  typeOptions: string[];
  formData: any;
  getVehicles: (value: string) => Promise<{ value: string; label: string }[]>;
};

type labelType = { value: string; label: string }
const VehicleSelectorField = ({
  field,
  typeOptions,
  formData,
  getVehicles,
}: VehicleSelectorFieldProps) => {
  const [vehicleData, setVehicleData] = useState<{ id: number; type: string }[]>([]);
  const [labels, setLabels] = useState<labelType[]>([]);
  const labelCache = useRef<Record<string, string>>({});

  useEffect(() => {
    labels.forEach((item) => {
      labelCache.current[item.value] = item.label;
    });
  }, [labels]);

  useEffect(() => {
    if (Array.isArray(field.value)) {
      setVehicleData(field.value);
    }
  }, [field.value]);
  useEffect(() => {
    formData?.vehicles
      ?.filter((el: any) => el.mix_id !== null)
      .forEach((item: any) => {
        labelCache.current[item.mix_id] = item.vin;
      })
  }, [formData?.vehicles]);
  const handleVehicleChange = (selectedIds: string[]) => {
    const selected = selectedIds.map((idStr) => Number(idStr));
    const updated = selected.map((id) => {
      const existing = vehicleData.find((v) => v.id === id);
      return existing || { id, type: "both" };
    });

    setVehicleData(updated);
    field.onChange(updated);
  };

  const handleTypeChange = (id: number, newType: string) => {
    const updated = vehicleData.map((v) =>
      v.id === id ? { ...v, type: newType } : v
    );
    setVehicleData(updated);
    field.onChange(updated);
  };

  const selectedIds = vehicleData.map((v) => String(v.id));

  return (
    <div className="space-y-4">
      <VehicleTypeSelector
        vehicleData={vehicleData}
        typeOptions={typeOptions}
        handleTypeChange={handleTypeChange}
        labelCache={labelCache}
      />
      <MultiSelect
        selectedValues={selectedIds}
        onSelectedValuesChange={handleVehicleChange}
        getData={getVehicles}
        defaultValue={
          formData?.vehicles
            ?.filter((el: any) => el.mix_id !== null)
            .map((el: any) => ({
              label: el.vin,
              value: `${el.mix_id}`,
            })) || []
        }
        setLabel={setLabels}
      />
    </div>
  );
};
