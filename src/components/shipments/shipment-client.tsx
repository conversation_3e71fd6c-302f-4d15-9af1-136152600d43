"use client";
import PaginationComponent from "@/components/Common_UI/use-paginition";
import { transformObject } from "@/lib/transferObject";
import React from "react";
import { useResponsive } from "@/hooks/use-mobile";
import { ShipmentDataTableMobile } from "./shipment-data-table-mobile";
import { ShipmentDataTable } from "./cell-renderers/shipment-data-table";
import { loadShipmentData } from "./services/shipment-action";

// Define allowed keys (Only these fields will be processed)
const allowedKeys = [
  "con",
  "bkg",
  "status",
  "from_loading_date",
  "to_loading_date",
  "from_etd",
  "to_etd",
  "from_eta",
  "to_eta",
  "loc",
];

// Define dynamic mapping (Customize as needed)
const mapping = {
  con: "id",
  bkg: "booking_id",
  status: "status",
  from_loading_date: "loading_date.from",
  to_loading_date: "loading_date.to",
  from_etd: `bookings.vessels.etd.from`,
  to_etd: "bookings.vessels.etd.to",
  from_eta: "bookings.eta.from",
  to_eta: "bookings.eta.to",
  loc: "bookings.vessels.port_of_loading",
};

interface VehiclePageClientProps {
  initialRecords: any;
  baseState: string;
  searchParams: any;
}

const ShipmentClient: React.FC<VehiclePageClientProps> = ({
  initialRecords,
  baseState,
  searchParams,
}) => {
  const boundLoadMoreData = loadShipmentData.bind(
    null,
    baseState,
    searchParams
  );
  const { isMobile } = useResponsive();

  const initialParams = {
    search: searchParams?.search || "",
    filterData:
      Object.keys(transformObject(searchParams, allowedKeys, mapping))
        .length !== 0
        ? JSON.stringify(transformObject(searchParams, allowedKeys, mapping))
        : "",
  };

  const componentKey = [
    searchParams?.search || "no-search",
    baseState || "no-status",
    initialParams.filterData || "no-filters",
    searchParams?.page || "1",
  ].join("-");

  return (
    <>
      {isMobile ? (
        <div className="h-[calc(100vh-160px)] ">
          <ShipmentDataTableMobile
            key={componentKey}
            records={initialRecords}
            onLoadMoreData={boundLoadMoreData}
            initialParams={initialParams}
          />
        </div>
      ) : (
        <>
          <div className="">
            <div className="h-[calc(100vh-188px)] ">
              <ShipmentDataTable records={initialRecords} />
              <PaginationComponent
                count={initialRecords?.total || 0}
                pageSize={initialRecords?.per_page || 0}
              />
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default ShipmentClient;
