import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format, isValid, parseISO } from "date-fns";
import { useGetOne } from "../services/useGetOne";
import { Card } from "@/components/ui/card";
import { LoadingSpinner } from "@/components/Common_UI/loading";
import { RefObject } from "react";
const formatDate = (date: string, formatString = "yyyy-MM-dd") => {
  const parsedDate = date && isValid(new Date(date)) ? parseISO(date) : ""; // assuming input is a valid ISO string
  return date && isValid(parsedDate) ? format(parsedDate, formatString) : "";
};
function CustomerShipmentBillOfLoading({
  id,
  ref,
}: {
  id?: number;
  ref?: RefObject<HTMLDivElement | null>;
}) {
  const { isLoading, data } = useGetOne(id ?? 0);

  let weight = 0;
  const sumWeight = (weightValue: any) => {
    weight = weight + +weightValue;
  };
  const destinationIDsToShowReceiverName = [22, 28];
  if (isLoading) {
    return (
      <Card className="min-w-full border-none h-[730px] shadow-sm flex justify-center items-center">
        <LoadingSpinner className="h-9 w-9" />
      </Card>
    );
  }
  return (
    <div
      className="container mx-auto p-4 print:m-0 print:p-0 dark:bg-white dark:text-black"
      ref={ref}
    >
      {/* Header */}
      <div className="mb-6 flex items-center relative gap-4">
        <img
          src={"/logo-250.png"}
          alt="PGL Logo"
          // className="h-[70px] print:h-[70px]"
          width={300}
          height={200}
        />
        <h1 className="text-xl  font-semibold print:text-base">
          PGL Bill of Lading / Shipping Instructions
        </h1>
      </div>

      {/* Main Information Table */}
      <Table className="w-full dark:bg-white dark:text-black">
        <TableBody>
          <TableRow className="border-none">
            <TableCell className="border-2 p-1" rowSpan={6}>
              <div className="font-semibold">SHIPPER / EXPORTER</div>
              {data?.shipping_documents?.shipper_exporter || ""}
              <br />
              {data?.shipping_documents?.shipper_street_address || ""}
              <br />
              {`${data?.shipping_documents?.shipper_city || ""}, 
                ${data?.shipping_documents?.shipper_state || ""}, 
                ${data?.shipping_documents?.shipper_zip_code || ""}`}
              <br />
              {data?.shipping_documents?.shipper_email_address || ""}
              <br />
              Phone: {data?.shipping_documents?.shipper_phone_number || ""} -
              Fax: {data?.shipping_documents?.shipper_fax_number || ""}
            </TableCell>
            <TableCell className="border-2 p-1">
              <div className="font-semibold">BOOKING NUMBER</div>
              {data?.bookings?.booking_number || ""}
              {data?.booking_suffix ? "-" + data?.booking_suffix : ""}
            </TableCell>
            <TableCell className="border-2 p-1">
              <div className="font-semibold">BILL OF LADING NO.</div>
              {data?.bill_of_loading_number || ""}
            </TableCell>
          </TableRow>
          <TableRow className="border-none">
            <TableCell className="border-2 p-1" colSpan={2} rowSpan={2}>
              <div className="font-semibold">
                VESSEL / VOYAGE# / STEAMSHIP LINE / FLAG
              </div>
              {`${data?.bookings?.vessels?.name || ""}/ ${
                data?.voyage_number || ""
              }#/
                ${data?.bookings?.vessels?.steamshiplines?.name || ""}/${
                data?.flag || ""
              }`}
            </TableCell>
          </TableRow>
          <TableRow className="border-none" />
          <TableRow className="border-none">
            <TableCell className="border-2 p-1">
              <div className="font-semibold">PLACE OF RECEIPT</div>
              {data?.place_receipt || ""}
            </TableCell>
            <TableCell className="border-2 p-1">
              <div className="font-semibold">COUNTRY OF ORIGIN</div>
              {data?.country_origin || ""}
            </TableCell>
          </TableRow>
          <TableRow className="border-none">
            <TableCell className="border-2 p-1" rowSpan={5} colSpan={2}>
              <div className="font-semibold">FORWARDING AGENT</div>
              {data?.shipping_documents?.f_agent || ""}
              <br />
              {data?.shipping_documents?.f_street_address || ""}
              <br />
              {`${data?.shipping_documents?.f_city || ""}, 
                ${data?.shipping_documents?.f_state || ""}, 
                ${data?.shipping_documents?.f_zip_code || ""}`}
              <br />
              Phone: {data?.shipping_documents?.f_phone_number || ""}/ Email:
              {data?.shipping_documents?.f_email_address || ""}
            </TableCell>
          </TableRow>
          <TableRow className="border-none" />
          <TableRow className="border-none">
            <TableCell className="border-2 p-1" rowSpan={5}>
              <div className="font-semibold">CONSIGNEE</div>
              {data?.companies?.consignee || ""}
              <br />
              {data?.companies?.consignee_street || ""}
              <br />
              P. O. Box Number: {data?.companies?.consignee_box || ""}
              <br />
              {`${data?.companies?.consignee_poc || ""}, 
                ${data?.companies?.consignee_email || ""}`}{" "}
              - Phone:
              {data?.companies?.consignee_phone || ""}
              <br />
              {`${data?.companies?.consignee_city || ""}, 
                ${data?.companies?.consignee_zip_code || ""}, 
                ${data?.companies?.consignee_country || ""}`}
            </TableCell>
          </TableRow>
          <TableRow className="border-none" />
          <TableRow className="border-none" />
          <TableRow className="border-none">
            <TableCell className="border-2 p-1">
              <div className="font-semibold">PORT OF LOADING</div>
              {data?.bookings?.vessels?.locations?.name || ""}
            </TableCell>
            <TableCell className="border-2 p-1">
              <div className="font-semibold">PORT OF DISCHARGE</div>
              {data?.bookings?.destinations?.name || ""}
            </TableCell>
          </TableRow>
          <TableRow className="border-none">
            <TableCell className="border-2 p-1">
              <div className="font-semibold">ETD AT PORT OF LOADING</div>
              {formatDate(data?.bookings?.vessels?.etd)}
            </TableCell>
            <TableCell className="border-2 p-1">
              <div className="font-semibold">ETA AT PORT OF DISCHARGE</div>
              {formatDate(data?.bookings?.eta)}
            </TableCell>
          </TableRow>
          <TableRow className="border-none">
            <TableCell className="border-2 p-1" rowSpan={3}>
              <div className="font-semibold">NOTIFY PARTY</div>
              {data?.companies?.notify_party || ""}
              <br />
              {data?.companies?.notify_streetn || ""}
              <br />
              P. O. Box Number: {data?.companies?.notify_boxn || ""}
              <br />
              {`${data?.companies?.notify_city || ""}, 
                ${data?.companies?.notify_zip || ""}, 
                ${data?.companies?.notify_country || ""}`}
              <br />
              {`${data?.companies?.notify_poc || ""}, 
                ${data?.companies?.notify_email || ""}`}{" "}
              - Phone: {data?.companies?.notify_phone || ""}
            </TableCell>
            <TableCell className="border-2 p-1" rowSpan={3} colSpan={2}>
              <div className="font-semibold">
                EXPORT REFERENCES, DOMESTIC ROUTING AND INSTRUCTIONS
              </div>
              {data?.pglrNumber || ""}
              <br />
              {data?.exportInstructions || ""}
              <br />
              {data?.invoice_number || ""}
              <br />
              {data?.exportInstructions || ""}
            </TableCell>
          </TableRow>
          <TableRow className="border-none" />
          <TableRow className="border-none" />
          <TableRow className="border-none" />
          <TableRow className="border-none" />
          <TableRow className="border-none">
            <TableCell
              className="border-2 p-1 text-center font-semibold"
              colSpan={3}
            >
              PARTICULARS FURNISHED BY SHIPPER
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      {/* Shipment Details Table */}
      <Table className="w-full border-collapse mt-4 dark:bg-white dark:text-black">
        <TableHeader>
          <TableRow className="border-none">
            <TableHead className="border-2 p-1 text-center dark:bg-white dark:text-black">
              MARKS & NUMBERS
            </TableHead>
            <TableHead className="border-2 p-1 text-center dark:bg-white dark:text-black">
              Description of Packages and Goods
            </TableHead>
            {destinationIDsToShowReceiverName.includes(
              data?.bookings?.destinations?.id
            ) && (
              <TableHead className="border-2 p-1 text-center dark:bg-white dark:text-black">
                Receiver Name
              </TableHead>
            )}
            <TableHead className="border-2 p-1 text-center dark:bg-white dark:text-black">
              Gross Weight KGs
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow className="border-none">
            <TableCell className="border-2 p-1 text-center" rowSpan={9}>
              CONTAINER NO.: <br />
              {data?.container_number || ""}
              <br />
              Container Size/Type:
              <br />
              {data?.bookings?.size || ""}
              <br />
              SEAL Number:
              <br />
              {data?.seal_number || ""}
              <br />
              HS Code:
              <br />
              <br />
              AES ITN Number:
              <br />
              {data?.aes_itn_number || ""}
              <br />
              SCAC Code:
              <br />
              {data?.bookings?.vessels?.scac_code || ""}
              <br />
              MEASUREMENT:
              <br />
              {data?.measurement || ""}
            </TableCell>
            <TableCell className="border-2 p-1" rowSpan={8}>
              <h5 className="text-center">
                {data?.no_units_load || ""} Listed below
              </h5>
              {data?.vehicles?.map((vehicle: any, index: number) => (
                <div key={index}>
                  {index + 1} ) {vehicle?.year || ""} {vehicle?.make || ""}{" "}
                  {vehicle?.model || ""} VIN# {vehicle?.vin || ""}
                  <br />
                </div>
              ))}
            </TableCell>
            {destinationIDsToShowReceiverName.includes(
              data?.bookings?.destinations?.id
            ) && (
              <TableCell className="border-2 p-1" rowSpan={8}>
                {data?.vehicles?.map((vehicle: any, index: number) => (
                  <div key={index}>
                    {index + 1} ) {vehicle?.receiver_name ?? "N/A"}
                    <br />
                  </div>
                ))}
              </TableCell>
            )}
            <TableCell className="border-2 p-1 text-center" rowSpan={8}>
              {data?.vehicles?.map((vehicle: any, index: number) => {
                sumWeight(vehicle?.weight);
                return (
                  <div key={index}>
                    {index + 1} ) {vehicle?.weight || ""}
                    <br />
                  </div>
                );
              })}
            </TableCell>
          </TableRow>
          <TableRow className="border-none" />
          <TableRow className="border-none" />
          <TableRow className="border-none" />
          <TableRow className="border-none" />
          <TableRow className="border-none" />
          <TableRow className="border-none" />
          <TableRow className="border-none" />
          <TableRow className="border-none">
            <TableCell className="border-2 p-1">TOTAL Weight in KGs</TableCell>
            <TableCell
              className="border-2 p-1 text-center"
              colSpan={
                destinationIDsToShowReceiverName.includes(
                  data?.bookings?.destinations?.id
                )
                  ? 3
                  : 2
              }
            >
              {weight || ""}
            </TableCell>
          </TableRow>
          <TableRow className="border-none">
            <TableCell className="border-2 p-1 font-semibold" colSpan={4}>
              Basic Instructions :
            </TableCell>
          </TableRow>

          <TableRow className="border-none">
            <TableCell className="border-2 p-1" colSpan={4}>
              The listed commodities were exported from the United States in
              accordance with the export administrative regulations. Diversion
              contrary to the U.S. Law is prohibited.
            </TableCell>
          </TableRow>
          <TableRow className="border-none">
            <TableCell className="border-2 p-2" colSpan={4}>
              Hereby certify having received the above described shipment in
              outwardly good condition from the shipper shown in section
              (Shipper/Exporter) for forwarding to the ultimate consignee shown
              in the section (Consignee) above in witness whereof, the ______
              nonnegotiable FCR&apos;s have been signed, and if one (1) is
              accomplished by delivery of goods, issuance of a delivery order or
              by some other means, the others shall be avoided if required by
              the freight forwarder One (1) original FCR must be surrendered.
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      {/* Print-specific styles */}
      <style jsx global>{`
        @media print {
          @page {
            size: A4;
            margin: 10mm;
          }
          body {
            font-weight: bold;
            font-size: 11px;
            color: black;
          }
        }
      `}</style>
    </div>
  );
}

export default CustomerShipmentBillOfLoading;
