"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";

import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import {
  type FunctionComponent,
  RefObject,
  useMemo,
  useRef,
  useState,
} from "react";

import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import { BookingRenderer } from "./BookingRenderer";
import { PrintRenderer } from "./PrintRenderer";
import { LocationRenderer } from "./LocationRenderer";
import { CenterCell } from "./CenterCell";
import { UnitsRenderer } from "./UnitsRenderer";

import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import { StatusRenderer } from "./StatusRenderer";
import useSidebarConfig from "../sidebarConfig";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);
export type ShipmentType = {
  id: number;
  container_number: string;
  booking_suffix: string;
  aes_itn_number: string;
  tracking_contatiner: string;
  container_id_update_date: string;
  bill_of_loading_number: string;
  seal_number: string;
  actions: string;
  measurement: string;
  no_units_load: string;
  invoice_number: string;
  cover_photo: string;
  companies: {
    select: {
      name: string;
    };
  };
  bookings: {
    select: {
      eta: string;
      booking_number: string;
      port_of_discharge: string;
      destinations: {
        select: {
          name: string;
        };
      };
      size: string;
      vessels: {
        select: {
          etd: string;
          port_of_loading: string;
          locations: {
            select: {
              name: string;
            };
          };
          steamshiplines: {
            select: {
              name: string;
            };
          };
        };
      };
    };
  };
  containers_images: {
    where: {
      size: number;
    };
    select: {
      id: string;
      container_id: string;
      name: string;
      url: string;
      size: string;
    };
  };
  amount: string;
  photo_link: string;
  status: string;
  pin_in: string;
  pin_out: string;
  ingate: string;
  clearance_invoice_link: string;
  loading_date: string;
};

interface Props {
  gridTheme?: string;
  isDarkMode?: boolean;
  records: {
    page: number;
    per_page: number;
    total: number;
    data: ShipmentType[];
  } | null;
  gridRefProps?: RefObject<AgGridReact | null>;
  exportColDefs?: ColDef[];
}

const paginationPageSizeSelector = [5, 10, 20];

export const ShipmentDataTable: FunctionComponent<Props> = ({
  records,
  gridRefProps,
  exportColDefs,
}: Props) => {
  const gridRef = useRef<AgGridReact>(null);
  const t = useTranslations("shipment-datatable");
  const sidebarConfig = useSidebarConfig();
  const [colDefs] = useState<ColDef[]>([
    {
      headerName: "#",
      cellDataType: "text",
      minWidth: 50,
      cellStyle: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      },
      valueGetter: (params) => {
        if (params.node) {
          return params.node.rowIndex ? params.node.rowIndex + 1 : 1;
        }
      },
    },

    {
      field: "booking",
      headerName: t("header.booking"),
      cellDataType: "text",
      cellRenderer: BookingRenderer,
      minWidth: 200,
    },

    {
      field: "status",
      headerName: t("header.status"),
      cellRenderer: StatusRenderer,
      minWidth: 110,
    },
    {
      field: "locations",
      headerName: t("header.locations"),
      minWidth: 190,
      cellRenderer: LocationRenderer,
    },
    {
      field: "units",
      headerName: t("header.units"),
      minWidth: 250,
      cellRenderer: UnitsRenderer,
    },
    {
      field: "dates",
      headerName: t("header.dates"),
      minWidth: 190,
      cellRenderer: CenterCell,
    },

    {
      field: "track",
      headerName: "Print",
      cellRenderer: PrintRenderer,
      minWidth: 170,
    },
  ]);

  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
      flex: 1,
    }),
    []
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  const [quickFilterText] = useState<string>();

  const selectionColumnDef = useMemo(() => {
    return {
      minWidth: 44,
    };
  }, []);
  const { isRTL } = useDirection();

  return (
    <>
      <AgGridDataTable
        enableRtl={isRTL ? true : false}
        ref={gridRefProps || gridRef}
        selectionColumnDef={selectionColumnDef}
        columnDefs={exportColDefs || colDefs}
        rowData={records?.data || []}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        pagination={false}
        paginationPageSize={20}
        paginationPageSizeSelector={paginationPageSizeSelector}
        masterDetail
        detailCellRendererParams={{ t }}
        quickFilterText={quickFilterText}
        rowHeight={85}
        colResizeDefault="shift"
        headerHeight={60}
        sideBar={sidebarConfig}
        rowClassRules={{
          "row-even": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 === 0
                ? false
                : true
              : true;
          },
          "row-odd": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 !== 0
                ? false
                : true
              : true;
          },
        }}
        detailRowHeight={328}
      />
    </>
  );
};
