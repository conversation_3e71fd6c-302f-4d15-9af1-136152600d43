import React, { useState } from "react";
import { useShipmentImage } from "../services/useShipmentImages";
import { Skeleton } from "@/components/ui/skeleton";
import { Info } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import CarouselComponent, { useDotButton } from "../../Common_UI/custom-carousel";
import CustomDialog from "@/components/Common_UI/custom-dialog";
import { getGoogleDriveImageSizeUrl } from "@/utils/imageURL";
import { SliderMainItem } from "@/components/ui/extension/carousel";
import ZoomImage from "@/components/Common_UI/zoom-image";
import { useTranslations } from "next-intl";
import { useQueryClient } from "@tanstack/react-query";

export default function ImagesInGoogleDriveRenderer({
  fileId,
  images,
  open,
  className,
  selectedItem
}: {
  fileId: string;
  images: any[];
  open: boolean;
  className?: string;
  selectedItem: number
}) {
  const { data: imageUrl, isLoading, error } = useShipmentImage(fileId, open);
  const [openCarousel, setOpenCarousel] = useState(false);
  const [downloadLink, setDownloadLink] = useState("");
  const t = useTranslations("shipment-datatable");
  const [api, setApi] = useState();
  const { selectedIndex } = useDotButton(api);
  const queryClient = useQueryClient();


  const handleCopyLink = (imagelurl: string) => {
    console.warn(downloadLink);

    setDownloadLink(imagelurl);
  };

  // Fix the carousel click handler - don't check for className anymore
  const clickOnCarousel = () => {
    setOpenCarousel(true);
  };

  // Set a default container class if none provided
  const containerClass =
    className ||
    "h-40 w-full aspect-square flex justify-center items-center border rounded-md";

  if (error)
    return (
      <div className={containerClass}>
        <Skeleton className="w-full h-full">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size={"icon"}>
                  <Info />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Error loading image</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </Skeleton>
      </div>
    );

  if (isLoading)
    return (
      <div className={containerClass}>
        <Skeleton className="w-full h-full" />
      </div>
    );

  return (
    <>
      {/* Make sure the entire container is clickable */}
      <div className={containerClass} onClick={clickOnCarousel}>
        <img
          src={`${imageUrl}`}
          alt="shipment"
          className="rounded-sm object-cover w-full h-full"
        />
      </div>
      <CustomDialog
        openModal={openCarousel}
        setOpenModal={() => setOpenCarousel(false)}
        title={t("shipment_gallary.title")}
        isGoogleDrive={true}
        downloadLink={getGoogleDriveImageSizeUrl({
          url: queryClient.getQueryData<string>(["shipmentImage", images[selectedIndex]]) || "",
          size: 1024,
        })}
      >
        <CarouselComponent
          images={images}
          isGoogleDrive={true}
          onImageClick={handleCopyLink}
          Component={GoogleDriveImagesCarousel}
          Thumbs={GoogleDriveImagesThumbsCarousel}
          selected={selectedItem}
          setApi={setApi}
        />
      </CustomDialog>
    </>
  );
}

function GoogleDriveImagesCarousel({
  fileId,
  index,
}: {
  fileId: string;
  index: number;
}) {
  const { data: imageUrl, isLoading, error } = useShipmentImage(fileId, false);
  const t = useTranslations("shipment-datatable");

  if (isLoading)
    return (
      <div className="relative flex justify-center items-center h-[100vh] w-[100vw]">
        <Skeleton className="min-w-[80vw] min-h-[90vh] flex justify-center items-center" />
      </div>
    );

  if (error)
    return (
      <div className="relative flex justify-center items-center h-[100vh] w-[100vw]">
        {t("shipment_gallary.error_images_title")}
      </div>
    );

  return (
    <SliderMainItem className="bg-transparent w-full h-full">
      <div
        tabIndex={0} // Make div focusable
        className="flex items-center justify-center h-full w-full"
      >
        <ZoomImage
          file={{
            url: imageUrl || "#",
            id: `${index}`,
            name: `${imageUrl}` + index,
          }}
          isGoogleDrive={true}
        />
      </div>
    </SliderMainItem>
  );
}

export function GoogleDriveImagesThumbsCarousel({
  fileId,
  index,
}: {
  fileId: string;
  index: number;
}) {
  const { data: imageUrl, isLoading, error } = useShipmentImage(fileId, false);
  const t = useTranslations("shipment-datatable");

  if (isLoading)
    return (
      <div className="relative flex justify-center items-center h-full w-full">
        <Skeleton className="w-full h-full" />
      </div>
    );

  if (error)
    return (
      <div className="relative flex justify-center items-center h-full w-full">
        {t("shipment_gallary.error_images_title")}
      </div>
    );

  return (
    <img
      src={
        getGoogleDriveImageSizeUrl({
          url: imageUrl,
          size: 1024,
        }) || "/placeholder.svg"
      }
      alt={`Vehicle Image ${index}`}
      className="object-cover h-full w-full"
    />
  );
}
