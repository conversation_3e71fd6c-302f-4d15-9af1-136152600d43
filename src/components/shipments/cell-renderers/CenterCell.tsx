import { Separator } from "@/components/ui/separator";
import type { CustomCellRendererProps } from "ag-grid-react";
import { formatDate } from "date-fns";
import { useTranslations } from "next-intl";
import { type FunctionComponent } from "react";

export const CenterCell: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("shipment-datatable.body");
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[70px]">{t("eta")}:</div>
        {data?.bookings?.eta
          ? formatDate(data?.bookings?.eta, "yyyy MMM dd")
          : ""}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[70px]">{t("etd")}:</div>
        {data?.bookings?.vessels?.etd
          ? formatDate(data?.bookings?.vessels?.etd, "yyyy MMM dd")
          : ""}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[70px]">Loading:</div>
        {data?.bookings?.vessels?.etd
          ? formatDate(data?.loading_date, "yyyy MMM dd")
          : ""}
      </div>
    </div>
  );
};
