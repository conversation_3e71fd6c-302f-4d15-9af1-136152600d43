import { Separator } from "@/components/ui/separator";
import type { CustomCellRendererProps } from "ag-grid-react";
import { type FunctionComponent } from "react";
import { useTranslations } from "next-intl";

export const LocationRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("shipment-datatable.body");
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[44px]">{t("from")}:</div>
        {data?.bookings?.vessels?.locations?.name}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[44px]">{t("to")}:</div>
        {data?.bookings?.destinations?.name}
      </div>
    </div>
  );
};
