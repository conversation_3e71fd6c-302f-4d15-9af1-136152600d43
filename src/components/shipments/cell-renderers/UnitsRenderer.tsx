import { Separator } from "@/components/ui/separator";
import type { CustomCellRendererProps } from "ag-grid-react";
import { useTranslations } from "next-intl";
import { type FunctionComponent } from "react";

export const UnitsRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("shipment-datatable.body");
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[44px]">{t("units")}:</div> {data?.no_units_load}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[44px]">{t("size")}:</div> {data?.bookings?.size}
      </div>
    </div>
  );
};
