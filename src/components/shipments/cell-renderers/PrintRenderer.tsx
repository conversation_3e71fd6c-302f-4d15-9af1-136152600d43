import { CustomTooltip } from "@/components/Common_UI/custom-tooltip";
import { Button } from "@/components/ui/button";
import type { CustomCellRendererProps } from "ag-grid-react";
import { LinkIcon, Loader2, <PERSON>er<PERSON>he<PERSON>, Ship } from "lucide-react";
import { useRef, useState, type FunctionComponent } from "react";
import { LoadingSpinner } from "@/components/Common_UI/loading";
import Link from "next/link";
import { useTranslations } from "next-intl";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import CustomerShipmentBillOfLoading from "./shipment-preview";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { toast } from "sonner";

const handleTracking = (shippingLine: string, containerNumber: string) => {
  switch (shippingLine) {
    case "MAERSK":
      return `https://www.maersk.com/tracking/${containerNumber}`;
    case "ONE":
      return "https://ecomm.one-line.com/one-ecom/manage-shipment/cargo-tracking";
    case "MSC":
      return "https://www.msc.com/en/track-a-shipment?agencyPath=mwi";
    case "HMM":
      return "https://www.hmm21.com/e-service/general/trackNTrace/TrackNTrace.do";
    case "CMA CGM":
      return "https://www.cma-cgm.com/ebusiness/tracking";
    case "Hapag-Lloyd":
      return `https://www.hapag-lloyd.com/en/online-business/track/track-by-container-solution.html?container=${containerNumber}`;
    case "Yang Ming":
      return `https://e-solution.yangming.com/e-service/track_trace/track_trace_cargo_tracking.aspx`;
    case "Evergreen":
      return `https://ct.shipmentlink.com/servlet/TDB1_CargoTracking.do`;
    default:
      return "";
  }
};

export const PrintRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [isPreview, setIsPreview] = useState<boolean>(false);
  const printRef = useRef<HTMLDivElement>(null);

  const generatePDF = async () => {
    const element = printRef.current;
    if (!element) return;

    try {
      setIsGeneratingPDF(true);
      toast.info("Generating PDF, please wait...");

      const canvas = await html2canvas(element, {
        scale: 2,
        useCORS: true,
        logging: false,
      });

      const imgData = canvas.toDataURL("image/png");
      const pdf = new jsPDF({
        orientation: "portrait",
        unit: "px",
        format: "a4",
      });

      const width = pdf.internal.pageSize.getWidth();
      const height = (canvas.height * width) / canvas.width;

      pdf.addImage(imgData, "PNG", 0, 0, width, height);
      pdf.save("dashboard.pdf");
      toast.success("PDF generated successfully!");
    } catch (error) {
      toast.error("Error generating PDF: " + error);
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const t = useTranslations("sidebar");

  const trackingUrl = handleTracking(
    data?.bookings?.vessels?.steamshiplines?.name,
    data?.container_number
  );

  const hasValidTracking = [
    "MAERSK",
    "ONE",
    "MSC",
    "HMM",
    "CMA CGM",
    "Hapag-Lloyd",
    "Yang Ming",
    "Evergreen",
  ].includes(data?.bookings?.vessels?.steamshiplines?.name);
  return (
    <div className="flex flex-row gap-1 items-center justify-center h-full select-text leading-5">
      <CustomTooltip tooltip="Print">
        <Button
          variant={"outline"}
          size={"icon"}
          onClick={() => {
            setIsPreview(true);
          }}
          className="bg-green-500/10 dark:bg-green-500/10 text-green-500 dark:text-green-500"
        >
          {false ? (
            <LoadingSpinner className="w-4 h-4 p-0" />
          ) : (
            <PrinterCheck className="w-4 h-4 hover:cursor-pointer" />
          )}
        </Button>
      </CustomTooltip>
      {/* Clearance Invoice Link */}
      <CustomTooltip tooltip="Clearance invoice link">
        {data?.clearance_invoice_link ? (
          <Link href={data.clearance_invoice_link} target="_blank">
            <Button
              variant="outline"
              size="icon"
              className="bg-blue-500/10 dark:bg-blue-500/10 text-blue-500 dark:text-blue-500"
            >
              <LinkIcon className="w-4 h-4" />
            </Button>
          </Link>
        ) : (
          <Link href={"#"} target="_blank">
            <Button
              variant="outline"
              size="icon"
              className="bg-red-500/10 dark:bg-red-500/10 text-red-600 dark:text-red-600"
            >
              <LinkIcon className="w-4 h-4" />
            </Button>
          </Link>
        )}
      </CustomTooltip>

      {/* Tracking Container Link */}
      <CustomTooltip tooltip="Tracking Container Link">
        {trackingUrl && hasValidTracking ? (
          <Link href={trackingUrl} target="_blank">
            <Button
              variant="outline"
              size="icon"
              className="bg-blue-500/10 dark:bg-blue-500/10 text-blue-500 dark:text-blue-500"
            >
              <Ship className="w-4 h-4" />
            </Button>
          </Link>
        ) : (
          <Button
            variant="outline"
            size="icon"
            className="bg-red-500/10 dark:bg-red-500/10 text-red-500 dark:text-red-500"
            disabled
          >
            <Ship className="w-4 h-4 opacity-50" />
          </Button>
        )}
      </CustomTooltip>
      <Dialog open={isPreview} onOpenChange={setIsPreview} modal>
        <DialogContent className="sm:min-w-[968px] shadow-sm">
          <DialogHeader>
            <DialogTitle>{t("nav.shipments.label")}</DialogTitle>
          </DialogHeader>
          <Card className="max-h-[768px] overflow-y-auto shadow-sm border-none">
            <CardContent>
              <CustomerShipmentBillOfLoading id={data.id} ref={printRef} />
            </CardContent>
          </Card>
          <DialogFooter>
            <Button type="submit" onClick={generatePDF}>
              {isGeneratingPDF ? <Loader2 className="animate-spin" /> : "PDF"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
