import React, { useState, useRef } from "react";
import CarouselComponent from "../../Common_UI/custom-carousel";
import CustomDialog from "@/components/Common_UI/custom-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { getImageSizeUrl } from "@/utils/imageURL";
import { GoogleDriveImagesThumbsCarousel } from "./imagesInGoogleDriveRenderer";
import { SliderMainItem } from "@/components/ui/extension/carousel";
import ZoomImage from "@/components/Common_UI/zoom-image";

export default function SytemImages({
  image,
  imagesArray,
  onSelectImage,
  title,
  className = "",
  clickedItem,
}: {
  image: { url: string; id: number };
  imagesArray: { url: string; id: number }[];
  onSelectImage?: (imageUrl: string) => void;
  title: string;
  className?: string;
  clickedItem: number;
}) {
  const [openCarousel, setOpenCarousel] = useState(false);
  const [loading, setLoading] = useState(true);
  const [select, setSelect] = useState(false);
  const [imageLink, setImageLink] = useState("");
  const [downloadLink, setDownloadLink] = useState("");
  const clickTimer = useRef<NodeJS.Timeout | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const handleCopyLink = (imageUrl: string) => {
    setImageLink(imageUrl);
    setDownloadLink(imageUrl);
  };

  const handleCopyClick = () => {
    navigator.clipboard.writeText(
      getImageSizeUrl({ url: imageLink, size: 1024 })
    );
  };

  const handleImageClick = () => {
    if (clickTimer.current) {
      // Double click detected
      clearTimeout(clickTimer.current);
      clickTimer.current = null;

      // Select image on double click
      const imageUrl = getImageSizeUrl({
        url: image.url,
        size: 1024,
      });
      onSelectImage?.(imageUrl);
      setSelect(!select);
    } else {
      // Set timer for potential single click
      clickTimer.current = setTimeout(() => {
        // Single click action - open carousel
        setOpenCarousel(true);
        clickTimer.current = null;
      }, 250); // 250ms is a common double-click detection threshold
    }
  };

  return (
    <div
      ref={containerRef}
      className={`relative aspect-square w-36 ${className}`}
    >
      {loading && (
        <div className="absolute inset-0 flex justify-center items-center w-full h-full">
          <Skeleton className="w-full h-full rounded-md" />
        </div>
      )}

      {select && (
        <div className="absolute top-2 left-2 z-50">
          <input
            type="checkbox"
            className="w-3 h-3 border-2 accent-primary rounded-md text-foreground"
            checked={select}
            readOnly
            style={{ backgroundColor: "var(--primary)" }}
          />
        </div>
      )}

      <div
        className={`w-full h-full flex cursor-pointer justify-center rounded-sm items-center ${select ? "border-2 border-primary" : "border border-border"
          }`}
        key={image.id}
        onClick={handleImageClick}
      >
        <img
          src={`${process.env.NEXT_PUBLIC_MINIO_ENDPOINT}${image.url}`}
          alt="shipment"
          className={`rounded-md object-cover w-full h-full ${loading ? "opacity-0" : "opacity-100"
            }`}
          onLoad={() => setLoading(false)}
          onError={() => setLoading(false)}
        />
      </div>

      <CustomDialog
        openModal={openCarousel}
        setOpenModal={() => setOpenCarousel(false)}
        title={title}
        handleCopyClick={handleCopyClick}
        downloadLink={getImageSizeUrl({ url: downloadLink, size: 1024 })}
      >
        <CarouselComponent
          images={imagesArray}
          isGoogleDrive={false}
          onImageClick={handleCopyLink}
          Thumbs={GoogleDriveImagesThumbsCarousel}
          Component={ImagesCarousel}
          selected={clickedItem}
          setApi={() => { }} // Set the API to null
        />
      </CustomDialog>
    </div>
  );
}

function ImagesCarousel({
  img,
  onImageClick,
}: {
  img: any;
  onImageClick?: (imageUrl: string) => void;
}) {
  return (
    <SliderMainItem
      className="bg-transparent w-full h-full"
      onMouseEnter={() => onImageClick?.(img.url || "")}
    >
      <div
        tabIndex={0} // Make div focusable
        className="flex items-center justify-center h-full w-full"
      >
        <ZoomImage file={img} isGoogleDrive={false} />
      </div>
    </SliderMainItem>
  );
}
