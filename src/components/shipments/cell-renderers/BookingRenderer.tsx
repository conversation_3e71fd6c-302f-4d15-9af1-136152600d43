import ImageGallary from "@/components/Common_UI/image-gallary";
import type { CustomCellRendererProps } from "ag-grid-react";
import Image from "next/image";
import { useState, type FunctionComponent } from "react";
import { useGetContainerImageIds } from "../services/use-image-ids";
import { LoadingSpinner } from "@/components/Common_UI/loading";
import SytemImages from "./sytemImages";
import { useDownloadMultipleImages } from "@/hooks/use-download-mutiple-images";
import { getImageSizeUrl } from "@/utils/imageURL";
import { toast } from "sonner";
import { downloadGoogleDriveImages } from "@/hooks/use-download-image";
import { useFetchClient } from "@/utils/axios";
import { useTranslations } from "next-intl";
import { useResponsive } from "@/hooks/use-mobile";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Hash, Ship, LinkIcon } from "lucide-react";
import { formatDate } from "date-fns";
import { Button } from "@/components/ui/button";
import { CustomTooltip } from "@/components/Common_UI/custom-tooltip";
import Link from "next/link";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import CustomDialog from "@/components/Common_UI/custom-dialog";
import CarouselComponent, {
  useDotButton,
} from "@/components/Common_UI/custom-carousel";
import { SliderMainItem } from "@/components/ui/extension/carousel";
import ZoomImage from "@/components/Common_UI/zoom-image";
import {
  GoogleDriveImagesCarousel,
  GoogleDriveImagesThumbsCarousel,
} from "@/components/vehicles/vehicle-images-from-google-drive";

const handleTracking = (shippingLine: string, containerNumber: string) => {
  switch (shippingLine) {
    case "MAERSK":
      return `https://www.maersk.com/tracking/${containerNumber}`;
    case "ONE":
      return "https://ecomm.one-line.com/one-ecom/manage-shipment/cargo-tracking";
    case "MSC":
      return "https://www.msc.com/en/track-a-shipment?agencyPath=mwi";
    case "HMM":
      return "https://www.hmm21.com/e-service/general/trackNTrace/TrackNTrace.do";
    case "CMA CGM":
      return "https://www.cma-cgm.com/ebusiness/tracking";
    case "Hapag-Lloyd":
      return `https://www.hapag-lloyd.com/en/online-business/track/track-by-container-solution.html?container=${containerNumber}`;
    case "Yang Ming":
      return `https://e-solution.yangming.com/e-service/track_trace/track_trace_cargo_tracking.aspx`;
    case "Evergreen":
      return `https://ct.shipmentlink.com/servlet/TDB1_CargoTracking.do`;
    default:
      return "";
  }
};

export const BookingRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("shipment-datatable");
  const [open, setOpen] = useState(false);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const { isMobile } = useResponsive();
  const [openCarousel, setOpenCarousel] = useState(false);
  const [currentImage, setCurrentImage] = useState("");
  const [downloadLink, setDownloadLink] = useState("");
  const [api, setApi] = useState();
  const { selectedIndex } = useDotButton(api);

  const {
    data: imageIds,
    isLoading,
    refetch,
    error,
  } = useGetContainerImageIds(data?.id, open);

  const {
    handleDownload,
    handleSelect,
    isZipping,
    handleDownloadAll,
    isZippingAll,
    selectedImages,
    setSelectedImages,
  } = useDownloadMultipleImages();
  const fetchClient = useFetchClient();

  const hanndleAllDownload = async () => {
    if (selectedImages.length !== 0) {
      handleDownload();
    } else {
      if (data?.photo_link) {
        const match = data?.photo_link.match(/folders\/([^?]+)/);
        let folderId;
        if (match) {
          try {
            setIsDownloading(true);
            folderId = match[1];
            await downloadGoogleDriveImages(
              folderId,
              "/v2/shipment-v2/google-drive/download-images",
              "shipment-images",
              fetchClient
            );
            toast.success("Images downloaded successfully!");
          } catch (error) {
            throw error;
          } finally {
            setIsDownloading(false);
          }
        }
      } else {
        handleDownloadAll(
          imageIds?.images?.map((image: any) =>
            getImageSizeUrl({ url: image?.url, size: 1024 })
          )
        );
      }
    }
  };

  const isGoogleImages =
    data?.photo_link?.includes("drive.google.com") &&
    data?.containers_images?.length <= 0
      ? true
      : false;

  const handleOpen = () => {
    if (isMobile) {
      setIsSheetOpen(true);
    } else if (isGoogleImages) {
      window.open(data?.photo_link, "_blank");
    } else {
      setSelectedImages([]);
      refetch();
      setOpen(true);
    }
  };

  const handleImageClicked = (imageUrl: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (isMobile) {
      if (isGoogleImages) {
        window.open(data?.photo_link, "_blank");
      } else {
        setOpenCarousel(true);
        setCurrentImage(imageUrl);
        refetch();
      }
    }
  };

  const handleCopyClick = () => {
    if (currentImage) {
      navigator.clipboard.writeText(
        getImageSizeUrl({ url: currentImage, size: 1024 })
      );
      toast.success("Image URL copied!");
    }
  };

  const handleDownloadLink = (imageUrl: string) => {
    setDownloadLink(imageUrl);
  };

  const singleDownload = downloadLink
    ? getImageSizeUrl({ url: downloadLink, size: 1024 })
    : imageIds?.images && imageIds.images.length > 0
    ? getImageSizeUrl({
        url: imageIds.images[selectedIndex]?.url || imageIds.images[0]?.url,
        size: 1024,
      })
    : getImageSizeUrl({ url: currentImage, size: 1024 });

  const trackingUrl = handleTracking(
    data?.bookings?.vessels?.steamshiplines?.name,
    data?.container_number
  );

  const hasValidTracking = [
    "MAERSK",
    "ONE",
    "MSC",
    "HMM",
    "CMA CGM",
    "Hapag-Lloyd",
    "Yang Ming",
    "Evergreen",
  ].includes(data?.bookings?.vessels?.steamshiplines?.name);

  const detailRows = [
    {
      label: "Status",
      value: (
        <Badge
          className="bg-blue-500/10 text-blue-500 border-none text-[8px]"
          variant="outline"
        >
          {data?.status?.toUpperCase() || "ACTIVE"}
        </Badge>
      ),
    },
    {
      label: "Booking Number",
      value: <span className="text-xs">{data?.bookings?.booking_number}</span>,
    },
    {
      label: "Container Number",
      value: <span className="text-xs">{data?.container_number}</span>,
    },
    {
      label: "From",
      value: (
        <span className="text-xs">
          {data?.bookings?.vessels?.locations?.name || "-"}
        </span>
      ),
    },
    {
      label: "To",
      value: (
        <span className="text-xs">
          {data?.bookings?.destinations?.name || "-"}
        </span>
      ),
    },
    {
      label: "Units",
      value: <span className="text-[10px]">{data?.no_units_load || "-"}</span>,
    },
    {
      label: "Size",
      value: <span className="text-xs">{data?.bookings?.size || "-"}</span>,
    },
    {
      label: "Container Type",
      value: <span className="text-xs">{data?.container_type || "-"}</span>,
    },
    {
      label: "ETD",
      value: (
        <span className="text-xs">
          {data?.bookings?.vessels?.etd
            ? formatDate(data?.bookings?.vessels?.etd, "MMM d, yyyy")
            : "-"}
        </span>
      ),
    },
    {
      label: "ETA",
      value: (
        <span className="text-xs">
          {data?.bookings?.eta
            ? formatDate(data?.bookings?.eta, "MMM d, yyyy")
            : "-"}
        </span>
      ),
    },
    {
      label: "Loading Date",
      value: (
        <span className="text-xs">
          {data?.loading_date
            ? formatDate(data?.loading_date, "MMM d, yyyy")
            : "-"}
        </span>
      ),
    },

    {
      label: "Clearance Invoice",
      value: (
        <CustomTooltip tooltip="Clearance invoice link">
          {data?.clearance_invoice_link ? (
            <Link href={data.clearance_invoice_link} target="_blank">
              <Button
                variant="outline"
                size="sm"
                className="bg-blue-500/10 text-blue-500 h-7 w-7 p-0"
              >
                <LinkIcon className="w-3 h-3" />
              </Button>
            </Link>
          ) : (
            <Button
              variant="outline"
              size="sm"
              className="bg-red-500/10 text-red-600 h-7 w-7 p-0"
              disabled
            >
              <LinkIcon className="w-3 h-3" />
            </Button>
          )}
        </CustomTooltip>
      ),
    },
    {
      label: "Tracking Container",
      value: (
        <CustomTooltip tooltip="Tracking Container Link">
          {trackingUrl && hasValidTracking ? (
            <Link href={trackingUrl} target="_blank">
              <Button
                variant="outline"
                size="sm"
                className="bg-blue-500/10 text-blue-500 h-7 w-7 p-0"
              >
                <Ship className="w-3 h-3" />
              </Button>
            </Link>
          ) : (
            <Button
              variant="outline"
              size="sm"
              className="bg-red-500/10 text-red-500 h-7 w-7 p-0"
              disabled
            >
              <Ship className="w-3 h-3 opacity-50" />
            </Button>
          )}
        </CustomTooltip>
      ),
    },
  ];
  console.log(data);

  return (
    <>
      <div className="flex items-center h-full gap-2 group cursor-pointer">
        <div
          className="flex items-center h-full gap-2 group cursor-pointer"
          onClick={() => handleOpen()}
        >
          <Image
            src={`${
              data?.cover_photo
                ? getImageSizeUrl({ url: data?.cover_photo, size: 250 })
                : "/placeholder1.jpg"
            }`}
            alt={"sample"}
            height={60}
            width={60}
            className={`rounded-sm hover:cursor-pointer`}
            onClick={(e) => handleImageClicked(data?.cover_photo || "", e)}
          />
          <div className="flex flex-col justify-center h-full select-text leading-5 flex-1">
            <div className="flex items-center gap-1.5">
              <div className="w-full overflow-hidden text-ellipsis whitespace-nowrap font-normal px-2">
                {data?.bookings?.booking_number}
              </div>
            </div>
            <div className="w-full overflow-hidden text-ellipsis whitespace-nowrap font-normal px-2 text-primary/70">
              {data?.container_number}
            </div>
          </div>
        </div>
      </div>

      {/* CustomDialog */}
      <CustomDialog
        openModal={openCarousel}
        setOpenModal={setOpenCarousel}
        title={`Shipment Images - ${data?.bookings?.booking_number}`}
        handleCopyClick={handleCopyClick}
        downloadLink={singleDownload}
        isGoogleDrive={imageIds?.fileIdsArray?.length > 0}
      >
        <div className="flex items-center justify-center h-full w-full">
          <>
            {imageIds?.images && imageIds?.images?.length > 0 && (
              <CarouselComponent
                images={imageIds.images}
                isGoogleDrive={false}
                onImageClick={handleDownloadLink}
                Component={ShipmentImagesCarousel}
                Thumbs={GoogleDriveImagesThumbsCarousel}
                selected={0}
                setApi={setApi}
              />
            )}
            {imageIds?.fileIdsArray && imageIds?.fileIdsArray?.length > 0 && (
              <CarouselComponent
                images={imageIds?.fileIdsArray}
                isGoogleDrive={true}
                Component={GoogleDriveImagesCarousel}
                Thumbs={GoogleDriveImagesThumbsCarousel}
                selected={0}
                setApi={setApi}
              />
            )}

            {error?.name && (
              <p className="font-semibold text-center capitalize">
                {t("shipment_gallary.error_images_title")}
              </p>
            )}
            {imageIds?.message && (
              <p className="font-semibold text-center capitalize">
                {t("shipment_gallary.no_images_title")}
              </p>
            )}
          </>
        </div>
      </CustomDialog>

      {/* Desktop Modal */}
      {!isMobile && (
        <ImageGallary
          open={open}
          onOpenChange={setOpen}
          title={t("shipment_gallary.title")}
          containerClass={
            isLoading || error?.name || imageIds?.message
              ? " min-h-[700px] flex justify-center items-center"
              : " grid grid-cols-7 gap-4 overflow-auto relative max-h-[750px] min-w-[900px] max-w-[1268px]"
          }
          parentClass="max-h-[850px] min-w-[900px] max-w-[1268px] min-h-[700px] overflow-hidden py-4"
          handleDownload={hanndleAllDownload}
          isZippingAll={isZipping || isZippingAll || isDownloading}
        >
          {isLoading ? (
            <div className="flex max-h-[900px] min-h-[700px] w-full max-w-[1268px] justify-center items-center">
              <LoadingSpinner />
            </div>
          ) : (
            <>
              {/* {imageIds?.fileIdsArray &&
                imageIds?.fileIdsArray?.map((fileId: string, index: number) => (
                  <ImagesInGoogleDriveRenderer
                    key={index}
                    fileId={fileId}
                    images={imageIds?.fileIdsArray}
                    open={open}
                    className="h-40 w-full aspect-square hover:cursor-pointer"
                    selectedItem={index}
                  />
                ))} */}
              {imageIds?.images &&
                imageIds?.images?.map(
                  (image: { url: string; id: number }, index: number) => (
                    <SytemImages
                      imagesArray={imageIds?.images}
                      image={image}
                      key={image.id}
                      onSelectImage={handleSelect}
                      title={"Shipment Images"}
                      clickedItem={index}
                    />
                  )
                )}
              {error?.name && (
                <p className="font-semibold text-center capitalize">
                  {t("shipment_gallary.error_images_title")}
                </p>
              )}
              {imageIds?.message && (
                <p className="font-semibold text-center capitalize">
                  {t("shipment_gallary.no_images_title")}
                </p>
              )}
            </>
          )}
        </ImageGallary>
      )}

      {/* Mobile Sheet */}
      {isMobile && (
        <Drawer open={isSheetOpen} onOpenChange={setIsSheetOpen}>
          <DrawerContent className="h-[80vh] rounded-t-3xl border-t-2 border-primary px-4">
            <DrawerHeader className="sr-only">
              <DrawerTitle>{t("shipment_gallary.title")}</DrawerTitle>
              <DrawerDescription>
                Shipment Details - {data?.bookings?.booking_number}
              </DrawerDescription>
            </DrawerHeader>
            <div className="flex flex-col h-full">
              <div className="px-1 py-2">
                <div className="flex items-center gap-2">
                  <span className="font-semibold text-xs">
                    {data?.bookings?.booking_number}
                  </span>
                </div>
                <div className="flex items-center gap-2 justify-between py-2">
                  <span className="flex items-center text-xs text-primary">
                    <Hash className="w-4 h-4" />
                    {data?.container_number}
                  </span>
                  <div className="flex items-center gap-2">
                    <Ship className="w-4 h-4 text-primary" />
                    <span className="text-xs">
                      {data?.bookings?.vessels?.vessel_name || "N/A"}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex-1 overflow-y-auto">
                <div className="rounded-lg overflow-hidden border">
                  <Table>
                    <TableBody>
                      {detailRows.map((row, index) => (
                        <TableRow
                          key={row.label}
                          className={`${
                            index % 2 === 0 ? "bg-primary/5" : "bg-primary/10"
                          } `}
                        >
                          <TableCell className="py-1 px-2 text-xs">
                            {row.label}
                          </TableCell>
                          <TableCell className="py-1 px-2 text-right text-xs">
                            {row.value}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </div>
          </DrawerContent>
        </Drawer>
      )}
    </>
  );
};

function ShipmentImagesCarousel({
  img,
  onImageClick,
}: {
  img: any;
  onImageClick?: (imageUrl: string) => void;
}) {
  return (
    <SliderMainItem
      className="bg-transparent w-full h-full"
      onMouseEnter={() => onImageClick?.(img.url || "")}
    >
      <div
        tabIndex={0}
        className=" flex items-center justify-center h-full w-full"
      >
        <ZoomImage file={img} isGoogleDrive={false} />
      </div>
    </SliderMainItem>
  );
}
