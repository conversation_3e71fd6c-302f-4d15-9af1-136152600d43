"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";

import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  ServerSideRowModelModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import {
  type FunctionComponent,
  RefObject,
  useMemo,
  useRef,
  useState,
} from "react";

import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import useSidebarConfig from "./sidebarConfig";
import { BookingRenderer } from "./cell-renderers/BookingRenderer";
import { useServerSideDatasource } from "@/hooks/use-infinite-scroll";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ServerSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);

export type ShipmentType = {
  id: number;
  container_number: string;
  booking_suffix: string;
  aes_itn_number: string;
  tracking_contatiner: string;
  container_id_update_date: string;
  bill_of_loading_number: string;
  seal_number: string;
  actions: string;
  measurement: string;
  no_units_load: string;
  invoice_number: string;
  cover_photo: string;
  companies: {
    select: {
      name: string;
    };
  };
  bookings: {
    select: {
      eta: string;
      booking_number: string;
      port_of_discharge: string;
      destinations: {
        select: {
          name: string;
        };
      };
      size: string;
      vessels: {
        select: {
          etd: string;
          port_of_loading: string;
          locations: {
            select: {
              name: string;
            };
          };
          steamshiplines: {
            select: {
              name: string;
            };
          };
        };
      };
    };
  };
  containers_images: {
    where: {
      size: number;
    };
    select: {
      id: string;
      container_id: string;
      name: string;
      url: string;
      size: string;
    };
  };
  amount: string;
  photo_link: string;
  status: string;
  pin_in: string;
  pin_out: string;
  ingate: string;
  clearance_invoice_link: string;
  loading_date: string;
};

interface Props {
  gridTheme?: string;
  isDarkMode?: boolean;
  records: {
    page: number;
    per_page: number;
    total: number;
    data: ShipmentType[];
  } | null;
  gridRefProps?: RefObject<AgGridReact | null>;
  exportColDefs?: ColDef[];
  onLoadMoreData: (params: {
    page: number;
    per_page: number;
    search?: string;
    filterData?: string;
    status?: string;
  }) => Promise<any>;
  initialParams?: {
    search?: string;
    filterData?: string;
    status?: string;
  };
}

export const ShipmentDataTableMobile: FunctionComponent<Props> = ({
  gridRefProps,
  exportColDefs,
  onLoadMoreData,
  initialParams,
}: Props) => {
  const gridRef = useRef<any>(null);
  const t = useTranslations("shipment-datatable");
  const sidebarConfig = useSidebarConfig();

  const { onGridReady } = useServerSideDatasource({
    onLoadMoreData,
    initialParams,
    pageSize: 20,
  });

  const [colDefs] = useState<ColDef[]>([
    {
      field: "booking",
      headerName: t("header.booking"),
      cellDataType: "text",
      cellRenderer: BookingRenderer,
      minWidth: 200,
    },
  ]);

  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
      flex: 1,
    }),
    []
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  const [quickFilterText] = useState<string>();
  const { isRTL } = useDirection();

  return (
    <>
      <AgGridDataTable
        enableRtl={isRTL}
        ref={gridRefProps || gridRef}
        columnDefs={exportColDefs || colDefs}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        rowModelType="serverSide"
        onGridReady={onGridReady}
        rowBuffer={0}
        cacheBlockSize={20}
        maxBlocksInCache={5}
        pagination={false}
        masterDetail={false}
        quickFilterText={quickFilterText}
        rowHeight={120}
        colResizeDefault="shift"
        headerHeight={50}
        sideBar={sidebarConfig}
        rowClassRules={{
          "row-even": (params) =>
            params.node.rowIndex ? params.node.rowIndex % 2 !== 0 : true,
          "row-odd": (params) =>
            params.node.rowIndex ? params.node.rowIndex % 2 === 0 : true,
        }}
        suppressHorizontalScroll={true}
        suppressColumnVirtualisation={true}
      />
    </>
  );
};
