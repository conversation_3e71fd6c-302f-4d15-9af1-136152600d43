"use client";
import * as React from "react";
import { Sidebar } from "@/components/ui/sidebar";
import { RotateCcw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import FilterCollapse from "@/components/vehicles/filter-collapse";
import { ContainerSelector } from "@/components/Common_UI/auto-complete";
import { addDays } from "date-fns";
import { DateRange } from "react-day-picker";
import { CustomDateRangePicker } from "@/components/Common_UI/customer-range-date";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useGetAutoComplete } from "@/utils/use-get-autocomplete";
import { useTranslations } from "next-intl";
import { formatNumberByLocale } from "@/utils/helper-function";
import { localesTypes } from "@/i18n/routing";
type optionType = { label: string; value: string };
const ToggleOptionsData: optionType[] = [
  { value: "at_loading", label: "At Loading" },
  { value: "on_the_way", label: "On The Way" },
  { value: "arrived", label: "Arrived" },
];

export function FilterModel({}: React.ComponentProps<typeof Sidebar> & {}) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const params = new URLSearchParams(Array.from(searchParams.entries()));
  const t = useTranslations("filter-modal");
  const urlParam = useParams();
  // State to control collapsible open/close behavior
  const [openCollapse, toggleCollapse] = React.useState<string[]>([]);
  const clearSelectionsRef = React.useRef<(() => void) | null>(null);
  const [date, setDate] = React.useState<DateRange>(function () {
    const from = params.get("from");
    const to = params.get("to");
    return {
      from: from ? new Date(from) : new Date(2023, 0, 20),
      to: to ? new Date(to) : addDays(new Date(2023, 0, 20), 20),
    };
  });

  const {
    data: containerData,
    isLoading: containerLoading,
    refetch: conatinerRefetch,
    handleSearch: handleContainerSearch,
  } = useGetAutoComplete({
    column: "container_number",
    model: "containers",
    key: "con",
  });
  const {
    data: bkgData,
    isLoading: bgkIsLoading,
    refetch: bkgRefetch,
    handleSearch: handleBookingSearch,
  } = useGetAutoComplete({
    model: "bookings",
    column: "booking_number",
    key: "bkg",
  });

  const handleSelectionContainer = (selected: any[]) => {
    params.delete("con");
    selected?.forEach((select) => params.append("con", select.id));
    router.push(`?${params}`);
  };
  const handleSelectionBookings = (selected: any[]) => {
    params.delete("bkg");
    selected?.forEach((select) => params.append("bkg", select.id));
    router.push(`?${params}`);
  };

  const handleSelectionVehicleStatus = (selected: any[]) => {
    params.delete("status");
    selected?.forEach((select) => params.append("status", select.value));
    router.push(`?${params}`);
  };

  const handleLoadingDate = (selected: DateRange) => {
    setDate(selected);
    if (selected.from && selected.to) {
      params.delete("from_loading_date");
      params.delete("to_loading_date");
      params.append(
        "from_loading_date",
        selected.from.toISOString().split("T")[0]
      );
      params.append("to_loading_date", selected.to.toISOString().split("T")[0]);
      router.push(`?${params}`);
    }
  };
  const handleETD = (selected: DateRange) => {
    setDate(selected);
    if (selected.from && selected.to) {
      params.delete("from_etd");
      params.delete("to_etd");
      params.append("from_etd", selected.from.toISOString().split("T")[0]);
      params.append("to_etd", selected.to.toISOString().split("T")[0]);
      router.push(`?${params}`);
    }
  };
  const handleETA = (selected: DateRange) => {
    setDate(selected);
    if (selected.from && selected.to) {
      params.delete("from_eta");
      params.delete("to_eta");
      params.append("from_eta", selected.from.toISOString().split("T")[0]);
      params.append("to_eta", selected.to.toISOString().split("T")[0]);
      router.push(`?${params}`);
    }
  };
  const handleClearFilters = () => {
    params.delete("bkg");
    params.delete("con");
    params.delete("status");
    params.delete("from_loading_date");
    params.delete("to_loading_date");
    params.delete("from_etd");
    params.delete("to_etd");
    params.delete("from_eta");
    params.delete("to_eta");
    router.push(`?${params}`);
  };
  return (
    <div className="mt-1 p-2 w-72">
      <div className="flex flex-row pl-3 pt-2 justify-between py-2">
        <h2 className="text-lg font-medium">{t("filters")}</h2>
        <Button
          onClick={() => {
            clearSelectionsRef.current?.();
            handleClearFilters();
          }}
          variant={"link"}
          size={"icon"}
          className="clear-filters"
        >
          <RotateCcw />
        </Button>
      </div>
      <div className="flex flex-col p-2 gap-2">
        <FilterCollapse
          label={t("shippment-filter-modal.booking")}
          className="p-2"
          isOpen={openCollapse?.includes("bookings")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("bookings")
                ? prev.filter((item) => item !== "bookings")
                : [...prev, "bookings"]
            )
          }
        >
          <ContainerSelector
            data={
              bkgData?.data?.map((item: any) => ({
                ...item,
                checked: false,
              })) || []
            }
            isLoading={bgkIsLoading}
            onFetch={bkgRefetch}
            onSearch={handleBookingSearch}
            onSelectionChange={handleSelectionBookings}
            selectedLabel={(count) => `Checked Items (${count})`}
            unselectedLabel={(count) => `Not Checked Items (${count})`}
            labelKey="id"
            valueKey="id"
            clearSelectionsRef={clearSelectionsRef}
            searchParamsKey="bkg"
          />
        </FilterCollapse>

        <FilterCollapse
          label={t("shippment-filter-modal.container")}
          className="p-2"
          isOpen={openCollapse?.includes("containers")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("containers")
                ? prev.filter((item) => item !== "containers")
                : [...prev, "containers"]
            )
          }
        >
          <ContainerSelector
            data={
              containerData?.data?.map((item: any) => ({
                ...item,
                checked: false,
              })) || []
            }
            isLoading={containerLoading}
            onFetch={conatinerRefetch}
            onSearch={handleContainerSearch}
            onSelectionChange={handleSelectionContainer}
            selectedLabel={(count) =>
              `${t("checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            unselectedLabel={(count) =>
              `${t("not-checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            labelKey="container_number"
            valueKey="id"
            clearSelectionsRef={clearSelectionsRef}
            searchParamsKey="con"
          />
        </FilterCollapse>

        <FilterCollapse
          label={t("shippment-filter-modal.status")}
          isOpen={openCollapse?.includes("status")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("status")
                ? prev.filter((item) => item !== "status")
                : [...prev, "status"]
            )
          }
        >
          <ContainerSelector
            data={
              ToggleOptionsData?.map((item: any) => ({
                ...item,
                checked: false,
              })) || []
            }
            onSelectionChange={handleSelectionVehicleStatus}
            selectedLabel={(count) =>
              `${t("checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            unselectedLabel={(count) =>
              `${t("not-checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            labelKey="label"
            valueKey="value"
            searchParamsKey="status"
            clearSelectionsRef={clearSelectionsRef}
          />
        </FilterCollapse>

        <FilterCollapse
          label={t("shippment-filter-modal.date-renge.label")}
          isOpen={openCollapse?.includes("Date Range")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("Date Range")
                ? prev.filter((item) => item !== "Date Range")
                : [...prev, "Date Range"]
            )
          }
        >
          <div className="flex flex-col gap-4 pt-3">
            <Label htmlFor="loading_date">
              {t("shippment-filter-modal.date-renge.loading-date")}
            </Label>
            <CustomDateRangePicker
              id="loading_date"
              date={date}
              onChange={(selected) => handleLoadingDate(selected as DateRange)}
            />
            <Label htmlFor="bookings.vessels.etd">
              {t("shippment-filter-modal.date-renge.etd")}
            </Label>
            <CustomDateRangePicker
              id="bookings.vessels.etd"
              date={date}
              onChange={(selected) => handleETD(selected as DateRange)}
            />
            <Label htmlFor="bookings.eta">
              {t("shippment-filter-modal.date-renge.eta")}
            </Label>
            <CustomDateRangePicker
              id="bookings.eta"
              date={date}
              onChange={(selected) => handleETA(selected as DateRange)}
            />
          </div>
        </FilterCollapse>
      </div>
    </div>
  );
}
