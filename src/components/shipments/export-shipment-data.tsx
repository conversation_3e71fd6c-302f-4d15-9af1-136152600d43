"use client";
import React, { useMemo } from "react";
import { ColDef } from "ag-grid-community";
import { formatDate } from "date-fns";
import { ShipmentType } from "./cell-renderers/shipment-data-table";
import { useTranslations } from "next-intl";
import { ExportModal } from "../Common_UI/export-modal";
import { getShipment } from "./services/shipment-service";
import { toast } from "sonner";

type Props = {
  records: {
    page: number;
    per_page: number;
    total: number;
    data: ShipmentType[];
  };
};

// Helper function to validate dates
const isValidDate = (date: any): boolean => {
  return date && !isNaN(new Date(date).getTime());
};

export default function ExportShipmentData({ records }: Props) {
  const t = useTranslations("export-modal");

  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        field: "booking_number",
        headerName: "Booking Number",
        valueGetter: (params) => params?.data?.bookings?.booking_number,
      },

      {
        field: "container_number",
        headerName: "Container Number",
      },

      {
        field: "pol_locations",
        headerName: "Point Of Loading",
        valueGetter: (params) =>
          params?.data?.bookings?.vessels?.locations?.name,
      },

      {
        field: "destination_name",
        headerName: "Destinations",
        valueGetter: (params) => params?.data?.bookings?.destinations?.name,
      },

      {
        field: "size",
        headerName: "Size",
        valueGetter: (params) => params?.data?.bookings?.size,
      },

      { field: "no_units_load", headerName: "Units" },

      {
        headerName: "Loading Date",
        valueGetter: (params) =>
          params?.data?.loading_date && isValidDate(params.data.loading_date)
            ? formatDate(params.data.loading_date, "yyyy MMM dd")
            : "",
      },

      {
        headerName: "ETD",
        valueGetter: (params) =>
          params?.data?.bookings?.vessels?.etd &&
          isValidDate(params.data.bookings.vessels.etd)
            ? formatDate(params.data.bookings.vessels.etd, "yyyy-MMM-dd")
            : "",
      },

      {
        headerName: "ETA",
        valueGetter: (params) =>
          params?.data?.bookings?.eta && isValidDate(params.data.bookings.eta)
            ? formatDate(params.data.bookings.eta, "yyyy-MMM-dd")
            : "",
      },
    ],
    []
  );

  const fetchAllData = async (): Promise<ShipmentType[]> => {
    const response = await getShipment({
      params: {
        status: "",
        page: 1,
        per_page: records.total,
        search: "",
        exactMatch: false,
        filterData: "",
      },
    });
    return response.data;
  };
  return (
    <ExportModal
      columnDefs={colDefs}
      currentData={records.data}
      exportFileName="Shipment Data"
      fetchAllData={fetchAllData}
      totalItems={records.total}
      translations={{
        title: t("title"),
        exportData: t("sub-title"),
        subTitle: t("sub-title"),
        currentData: t("current-data"),
        allData: t("all-data"),
        cancel: t("cancel"),
        export: t("export"),
      }}
      onExportSuccess={() => toast("Shipment Export Completed")}
    />
  );
}
