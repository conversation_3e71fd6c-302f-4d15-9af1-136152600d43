"use server";
import { transformObject } from "@/lib/transferObject";
import { getShipment } from "./shipment-service";
// Define allowed keys (Only these fields will be processed)
const allowedKeys = [
  "con",
  "bkg",
  "status",
  "from_loading_date",
  "to_loading_date",
  "from_etd",
  "to_etd",
  "from_eta",
  "to_eta",
  "loc",
];

// Define dynamic mapping (Customize as needed)
const mapping = {
  con: "id",
  bkg: "booking_id",
  status: "status",
  from_loading_date: "loading_date.from",
  to_loading_date: "loading_date.to",
  from_etd: `bookings.vessels.etd.from`,
  to_etd: "bookings.vessels.etd.to",
  from_eta: "bookings.eta.from",
  to_eta: "bookings.eta.to",
  loc: "bookings.vessels.port_of_loading",
};

export async function loadShipmentData(
  baseState: string,
  searchParams: any,
  params: {
    page: number;
    per_page: number;
    search?: string;
    filterData?: string;
    status?: string;
  }
) {
  try {
    const transformedFilters = transformObject(
      searchParams,
      allowedKeys,
      mapping
    );

    const result = await getShipment({
      params: {
        status: baseState,
        page: params.page,
        per_page: params.per_page,
        search: params.search || "",
        exactMatch: false,
        filterData:
          Object.keys(transformedFilters).length !== 0
            ? JSON.stringify(transformedFilters)
            : "",
      },
    });

    const response = {
      data: Array.isArray(result.data) ? result.data : [],
      total: result.total || 0,
      page: params.page,
      per_page: params.per_page,
      success: true,
    };

    return response;
  } catch (error) {
    console.error(error);
    return {
      data: [],
      total: 0,
      page: params.page,
      per_page: params.per_page,
      success: false,
    };
  }
}
