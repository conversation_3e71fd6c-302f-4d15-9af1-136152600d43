"use server";
import api from "@/utils/axios-server";
import { removeMatchingValue } from "@/utils/helper-function";
type ShipmentParamType = {
  status: string;
  page: number;
  per_page: number;
  search: string;
  exactMatch: boolean;
  filterData: string;
};

export async function getShipment({ params }: { params: ShipmentParamType }) {
  // removing undefined and 1620278055.png from status
  removeMatchingValue(params, "status", ["1620278055.png","undefined"]);
  
  try {
    const response = await api.get(`/v2/shipment-v2`, {
      params: { ...params },
    });

    return response.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message);
  }
}
export async function downloadShipmentImageOfGoogleDrive(id: string) {
  try {
    const response = await api.get(`/v2/shipment-v2/google-drive/download-images?=folderId=${id}`);
    return response.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message);
  }
}
