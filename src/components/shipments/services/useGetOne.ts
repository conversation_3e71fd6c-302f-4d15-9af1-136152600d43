"use client";
import { useQuery } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";

export function useGetOne(id: number) {
  const fetchClient = useFetchClient();

  const { data, isLoading } = useQuery({
    queryKey: ["shipment-one", id],
    queryFn: async () => {
      const res = await fetchClient(
        `/v2/shipment-v2/${id}`,
      );

      return res.data
    },
  });

  return { data, isLoading }
}
