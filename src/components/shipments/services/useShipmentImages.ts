"use client";
import { useQuery } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";

export function useShipmentImage(fileId: string, open: boolean) {
  const fetchClient = useFetchClient();

  return useQuery({
    queryKey: ["shipmentImage", fileId],
    queryFn: async () => {
      const res = await fetchClient(
        `/v2/shipment-v2/get-image-url?fileId=${fileId}`,
        {
          responseType: "blob",
        }
      );

      const blobUrl = URL.createObjectURL(res.data);
      return blobUrl;
    },
    enabled: !!fileId && open,
    staleTime: 10 * 60 * 1000,
  });
}
