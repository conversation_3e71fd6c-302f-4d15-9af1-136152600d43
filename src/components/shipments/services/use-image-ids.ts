import { useQuery } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";

export const useGetContainerImageIds = (
  container_id: number,
  open: boolean
) => {
  const fetchClient = useFetchClient();

  const { data, isLoading, refetch, error } = useQuery({
    queryKey: ["container-image-ids", container_id],
    queryFn: async () => {
      const res = await fetchClient(
        `/v2/shipment-v2/get-images?container_id=${container_id}`
      );
      return res.data;
    },
    enabled: !!container_id && open,
  });
  return { data, isLoading, refetch, error };
};
