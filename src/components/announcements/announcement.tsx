"use client";
import React, { useEffect } from "react";
import { useInView } from "react-intersection-observer";
import { Card, CardContent } from "../ui/card";
import { Input } from "../ui/input";
import AnnouncementCard from "./announcement-card";
import { AnnouncementSkeleton } from "./announcements-skeleton";
import { Separator } from "../ui/separator";
import Empty from "../Common_UI/empty";
import { useGetAnnouncements } from "./Service/useAnnoucements";
import { useTranslations } from "next-intl";

export default function Announcement() {
  const { ref, inView } = useInView({
    threshold: 0,
  });
  const [search, setSearch] = React.useState("");
  const { announcements, fetchNextPage, hasNextPage, isLoading } =
    useGetAnnouncements({ search });
  const t = useTranslations("datatable.announcements");
  useEffect(() => {
    if (inView) fetchNextPage();
  }, [inView, fetchNextPage]);

  if (isLoading)
    return (
      <Card className="min-h-[calc(100svh-66px)]  gird grid-rows-[auto_1fr]  w-full  rounded-sm">
        <div className="flex flex-col md:flex-row items-center px-6 py-3 justify-between space-y-2 pt-2">
          <h2 className="hidden md:text-2xl lg:text-3xl font-bold tracking-tight md:block">
            {t("label")}
          </h2>
          <div className="flex space-x-2 ">
            <Input
              placeholder={t("search")}
              type="text"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
        </div>
        <Separator className="my-4" />
        <CardContent className="grid gap-4 md:grid-cols-3 gird-cols-1 sm:grid-cols-2 place-items-center">
          <AnnouncementSkeleton />
          <AnnouncementSkeleton />
          <AnnouncementSkeleton />
        </CardContent>
      </Card>
    );

  return (
    <Card className="min-h-[calc(100svh-66px)]  gird grid-rows-[auto_1fr]  w-full  rounded-sm">
      <div className="flex flex-col md:flex-row items-center px-6 justify-between space-y-2 pt-2">
        <h2 className="text-3xl font-bold tracking-tight">{t("label")}</h2>
        <div className="flex space-x-2 w-56 ">
          <Input
            placeholder={t("search")}
            type="text"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
        </div>
      </div>
      <Separator className="my-4" />
      {Array.isArray(announcements) && announcements.length === 0 ? (
        <Empty data="announcement" />
      ) : (
        <>
          <CardContent className="grid gap-4 md:grid-cols-3 gird-cols-1 sm:grid-cols-2 place-items-center">
            {Array.isArray(announcements) ? (
              <>
                {announcements.map((item, index) => (
                  <AnnouncementCard key={index} data={item.announcement} />
                ))}
                {hasNextPage &&
                  Array.from({ length: 3 }).map((_, index) => (
                    <AnnouncementSkeleton key={index} ref={ref} />
                  ))}
              </>
            ) : (
              <>
                <AnnouncementSkeleton />
                <AnnouncementSkeleton />
                <AnnouncementSkeleton />
              </>
            )}
          </CardContent>
          {!hasNextPage && (
            <p className="text-center font-semibold py-4">{t("no-data")}</p>
          )}
        </>
      )}
    </Card>
  );
}
