"use client";
import React from "react";
import { Card, CardContent, CardDescription, CardHeader } from "../ui/card";
import Image from "next/image";
import { ArrowLeft } from "lucide-react";
import { Button } from "../ui/button";
import { useRouter } from "@/i18n/routing";
import { Separator } from "../ui/separator";
import FormatDate from "../Common_UI/format-date";
import { AnnouncementArayType } from "./announcement-types";
import { getImageSizeUrl } from "@/utils/imageURL";

export default function AnnouncementSingleCard({
  data,
}: {
  data: AnnouncementArayType[];
}) {
  const imageURL =
    getImageSizeUrl({ url: data[0].announcement.image }) !== ""
      ? getImageSizeUrl({ url: data[0].announcement.image })
      : "/placeholder.svg";
  const router = useRouter();
  return (
    <Card className="min-h-[calc(100svh-66px)] overflow-y-auto rounded-sm">
      <CardHeader className="px-2 pb-0 max-w-[768px] mx-auto">
        <div className="flex justify-between gap-x-2 items-center">
          <Button
            variant={"outline"}
            className="bg-primary text-white w-7 h-7"
            size={"icon"}
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 rtl:rotate-180" />
          </Button>
          <FormatDate
            date={
              data[0].created_at ? new Date(data[0].created_at) : new Date()
            }
          />
        </div>
        <h1 className="text-xl font-bold text-justify">
          {data[0].announcement.title}
        </h1>
      </CardHeader>
      <Separator className="my-4 max-w-[768px] mx-auto" />
      <CardContent className="p-2 max-w-[768px] mx-auto">
        <div className="flex justify-center w-full min-h-[300px]  rounded-md relative">
          <Image
            src={imageURL}
            alt="wellcome"
            fill
            className="object-cover rounded-md inset-1/2 relative"
          />
        </div>

        <h2 className="font-semibold py-3 text-justify">
          {data[0].announcement.title}
        </h2>

        <CardDescription
          dangerouslySetInnerHTML={{
            __html: data[0].announcement.description,
          }}
        ></CardDescription>

        {/* </CardDescription> */}
      </CardContent>
    </Card>
  );
}
