"use client";
import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  Card<PERSON>itle,
} from "../ui/card";
import Image from "next/image";
import { Button } from "../ui/button";
import { ArrowRight } from "lucide-react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import FormatDate from "../Common_UI/format-date";
import { AnnouncementType } from "./announcement-types";
import { getImageSizeUrl } from "@/utils/imageURL";
import { removeHtmlTags } from "@/utils/removeTags";

export default function AnnouncementCard({ data }: { data: AnnouncementType }) {
  const pathname = usePathname();
  const imageURL =
    getImageSizeUrl({ url: data.image }) !== ""
      ? getImageSizeUrl({ url: data.image })
      : "/placeholder.svg";

  return (
    <Card className="h-[400px]  w-full  rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all">
      <CardHeader className="relative p-0 w-full h-52">
        <Link href={`${pathname}/${data.id}`}>
          <Image
            src={imageURL as string}
            alt="Product"
            className=" object-cover"
            fill
          />
        </Link>
      </CardHeader>

      <CardContent className="p-4  space-y-4">
        <CardTitle className="line-clamp-2 leading-tight min-w-80 h-[40px] ">
          <Link href={`${pathname}/${data.id}`}>{data.title}</Link>
        </CardTitle>

        <div className="flex justify-between items-center">
          <CardDescription className="flex items-center gap-1 min-h-16 line-clamp-3">
            {data.excerpt ? data.excerpt : removeHtmlTags(data.description)}
          </CardDescription>
        </div>
        <CardFooter className="flex justify-between items-center p-0">
          <FormatDate date={data.created_at ? new Date(data.created_at) : new Date()} />
          <div className="flex items-center gap-1">
            <Link href={`${pathname}/${data.id}`}>
              <Button
                variant={"outline"}
                className="bg-primary text-white h-7 w-7"
                size={"icon"}
              >
                <ArrowRight className="h-4 w-4 rtl:rotate-180" />
              </Button>
            </Link>
          </div>
        </CardFooter>
      </CardContent>
    </Card>
  );
}
