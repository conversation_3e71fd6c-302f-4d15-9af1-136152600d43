"use client"
import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '../ui/card'
import { Separator } from '../ui/separator'
import { Skeleton } from '../ui/skeleton'

export default function AnnouncementSingleCardSkeleton() {
  return (
    <Card className='min-h-[calc(100svh-66px)] overflow-y-auto rounded-sm' >
      <CardHeader className='px-2 pb-0 max-w-[768px] mx-auto'>
        <div className='flex justify-between gap-x-2 items-center pb-2'>
          <Skeleton className=' text-white w-7 h-7' />
          <div className='flex gap-x-2 items-center'>
            <Skeleton className='h-4 w-16 ' /> <Skeleton className='text-foreground text-xs' />
          </div>
        </div>

        <Skeleton className='h-6 w-full ' />
      </CardHeader>
      <Separator className='px-2 mt-4 max-w-[750px] mx-auto' />
      <CardContent className='p-2 max-w-[768px] mx-auto '>

        <Skeleton className='flex my-3 justify-center w-full min-h-[200px]  rounded-md relative' />

        <Skeleton className='h-8 my-5 w-full' />
        <Skeleton className='h-72 my-3 w-full' />


      </CardContent>
    </Card>
  )
}
