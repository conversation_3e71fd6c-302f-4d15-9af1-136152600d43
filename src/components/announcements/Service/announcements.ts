"use server";
import axios from "@/utils/axios-server";
type fetchPropsTypes = { page?: string; search?: string };
export async function getAllCustomer({ page, search }: fetchPropsTypes) {
  try {
    const res = await axios.get("/v2/announcements/", {
      params: { page: Number(page), search },
    });
    if (res.status === 200) {
      return {
        data: res.data.data,
        total: res.data.total,
        page: res.data.page,
      };
    }
  } catch (error: any) {
    throw new Error(error?.response?.data?.message);
  }
}
export async function getOneCustomerAnnouncement({ id }: { id: number }) {
  try {
    const res = await axios.get(`/v2/announcements/${id}/`);
    if (res.status === 200) {
      return { data: res.data.data };
    }
  } catch (error: any) {
    throw new Error(error?.response?.data?.message);
  }
}