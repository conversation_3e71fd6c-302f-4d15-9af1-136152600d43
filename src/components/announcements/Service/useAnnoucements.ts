"use client";
import { useInfiniteQuery } from "@tanstack/react-query";
import { getAllCustomer } from "./announcements";
import { useSidebarCountContext } from "@/context/sidebar-count-context";
import { useMemo, useRef } from "react";

export function useGetAnnouncements({ search }: { search: string }) {
  const { markReadAnnouncements } = useSidebarCountContext();
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
  } = useInfiniteQuery({
    queryKey: ["announcement", search],
    queryFn: async ({ pageParam = 1 }) => {
      const response: any = await getAllCustomer({
        page: pageParam.toString(),
        search,
      });
      return {
        data: response?.data,
        total: response?.total,
        page: response?.page,
        per_page: 9,
      };
    },
    getNextPageParam: (lastPage) => {
      const currentPage = Number(lastPage.page);
      const totalPages = Math.ceil(lastPage.total / lastPage.per_page);
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    initialPageParam: 1,
  });

  const hasCalledMarkRead = useRef(false);

  const allPages = data?.pages || [];

  const newData = useMemo(() => {
    let unreadFound = false;

    const combined = allPages.reduce(
      (acc, item) => {
        const unread = item.data.filter((row: any) => !row.seen_at);
        if (unread.length > 0) unreadFound = true;

        return {
          data: [...acc.data, ...item.data],
          page: item.page,
          total: item.total,
          per_page: 9,
        };
      },
      { data: [], page: 1, total: 0, per_page: 9 }
    );

    if (unreadFound && !hasCalledMarkRead.current) {
      markReadAnnouncements();
      hasCalledMarkRead.current = true;
    }

    return combined;
  }, [allPages, markReadAnnouncements]);

  return {
    announcements: newData.data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
  };
}
