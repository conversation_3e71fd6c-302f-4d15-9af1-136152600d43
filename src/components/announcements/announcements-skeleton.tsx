import { Skeleton } from "@/components/ui/skeleton"
import { Card } from "../ui/card"
import React from "react"
type RefProp =
  | React.RefObject<HTMLDivElement | null>
  | ((node: HTMLDivElement | null) => void);
export function AnnouncementSkeleton({ref}:{ref?:RefProp}) {
  return (
    <Card className="flex-1 flex flex-col space-y-3 h-[400px] w-full  animate-pulse rounded-xl shadow-md overflow-hidden transition-all" ref={ref}>
      <Skeleton className="relative p-0 w-full h-52 animate-pulse " />
      <div className="px-3  space-y-3">
        <Skeleton className='line-clamp-2 animate-pulse leading-tight min-w-80 h-[40px]' />
        <Skeleton className="flex items-center animate-pulse gap-1 line-clamp-3 h-16 w-full" />
        <div className="flex justify-between items-center">
          <Skeleton className="h-6 mt-3 w-16 animate-pulse" />
          <Skeleton className="h-8 w-12 animate-pulse" />
        </div>
      </div>
    </Card>
  )
}