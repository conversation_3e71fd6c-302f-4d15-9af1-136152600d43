"use client";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
import { useTranslations } from "next-intl";
import {
  ShippingCalculationPayload,
  ShippingCalculationResponse,
} from "./config";

// Helper function to get translations (to be used in the query functions)
function useCalculatorTranslations() {
  return useTranslations("calculator.errors");
}

// Modify the query functions to use translations
export function useGetDestinations() {
  const t = useCalculatorTranslations();
  const fetchClient = useFetchClient();
  async function getDestinations() {
    const res = await fetchClient("/v2/mix-shipping-rate/destination");
    if (res.data?.result && res.data?.data) {
      return res.data.data;
    }
    throw new Error(t("fetch_destinations_failed"));
  }

  return useQuery({
    queryKey: ["destination"],
    queryFn: getDestinations,
    staleTime: 24 * 60 * 60 * 1000, // Keep data in cache for 1 day
    gcTime: 24 * 60 * 60 * 1000, // Keep data in cache for 1 day
  });
}

export function useGetStates() {
  const t = useCalculatorTranslations();
  const fetchClient = useFetchClient();

  async function geStates() {
    const res = await fetchClient("/v2/mix-shipping-rate/states");
    if (res.data?.result && res.data?.data) {
      return res.data.data;
    }
    throw new Error(t("fetch_states_failed"));
  }

  return useQuery({
    queryKey: ["states"],
    queryFn: geStates,
    staleTime: 24 * 60 * 60 * 1000, // Keep data in cache for 1 day
    gcTime: 24 * 60 * 60 * 1000, // Keep data in cache for 1 day
  });
}

export function useGetStateBranches(stateId: number) {
  const t = useCalculatorTranslations();
  const fetchClient = useFetchClient();

  async function getStateBranches(stateId: number) {
    const res = await fetchClient("/v2/mix-shipping-rate/branches", {
      params: { stateId },
    });
    if (res.data?.result && res.data?.data) {
      return res.data.data;
    }
    throw new Error(t("fetch_branches_failed"));
  }

  return useQuery({
    queryKey: ["stateBranches", stateId],
    queryFn: () => getStateBranches(stateId),
    enabled: stateId !== null && stateId !== 0,
  });
}

export function useGetBranchCities(branchId: number) {
  const t = useCalculatorTranslations();
  const fetchClient = useFetchClient();

  async function getBranchCities(branchId: number) {
    const res = await fetchClient("/v2/mix-shipping-rate/cities", {
      params: { branchId },
    });
    if (res.data?.result && res.data?.data) {
      return res.data.data;
    }
    throw new Error(t("fetch_cities_failed"));
  }

  return useQuery({
    queryKey: ["branchCities", branchId],
    queryFn: () => getBranchCities(branchId),
    enabled: branchId !== null && branchId !== 0,
  });
}

// Update the shipping calculation function to use translations
export const useCalculateShipping = () => {
  const t = useCalculatorTranslations();
  const fetchClient = useFetchClient();

  // Function that performs the actual API call
  const calculateShippingCost = async (payload: ShippingCalculationPayload) => {
    try {
      const response = await fetchClient("/v2/mix-shipping-rate/calculation", {
        method: "POST",
        data: payload,
      });

      if (response.status === 204) {
        throw new Error(t("no_shipping_rate"));
      }

      // Check if response.data exists
      if (!response.data) {
        throw new Error(t("empty_response"));
      }

      // Check if response.data.data exists
      if (!response.data.data) {
        // Log the actual structure received
        console.error(
          "Unexpected response structure:",
          JSON.stringify(response.data, null, 2)
        );
        throw new Error(t("missing_data"));
      }

      return response.data.data as ShippingCalculationResponse;
    } catch (error: any) {
      // If it's an axios error with a response
      if (error.response) {
        console.error("API Error Response:", {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
        });
      } else {
        console.error("API Call Error:", error.message || error);
      }
      throw error; // Re-throw the error for the mutation to catch
    }
  };

  return useMutation({
    mutationFn: calculateShippingCost,
  });
};
