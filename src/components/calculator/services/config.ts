// Define the interface for the calculation payload
export interface ShippingCalculationPayload {
    destination_id: string;
    loading_city_id: string;
    full_size_SUVs: boolean;
    manheim_adesa: boolean;
    major_accident: boolean;
  }
  
  // Define the interface for the calculation response
  export interface ShippingCalculationResponse {
    shipping: number;
    towing: number;
    tax_Duty: number;
    exchange_rate: number;
    full_size_SUVs: number;
    manheim_adesa: number;
    major_accident: number;
    more_than_10000_AED: number;
  }