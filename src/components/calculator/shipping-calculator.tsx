"use client";

import { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  MapPin,
  Navigation,
  AlertCircle,
  Check,
  Building2,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { Label } from "@/components/ui/label";
import { TooltipProvider } from "@/components/ui/tooltip";
import { toast } from "sonner";
import {
  useGetBranchCities,
  useGetDestinations,
  useGetStateBranches,
  useGetStates,
  useCalculateShipping,
} from "./services/calculator_queries";
import { ShippingCalculationPayload } from "./services/config";
import { useSession } from "next-auth/react";

type Destination = {
  id: number;
  name: string;
};

export function ShippingCalculator({ className }: { className?: string }) {
  const t = useTranslations("calculator");
  const session = useSession();
  const userDestinationId = (
    session?.data?.profile?.companies?.destinations as Destination
  )?.id;
  const [state, setState] = useState<number | null>(null);
  const [branch, setBranch] = useState<number | null>(null);
  const [city, setCity] = useState("");
  const [destination, setDestination] = useState(
    userDestinationId?.toString() || ""
  );
  const [options, setOptions] = useState({
    full_size_SUVs: false,
    manheim_adesa: false,
    major_accident: false,
  });
  const [total, setTotal] = useState<string | null>(null);
  const [showResultArea, setShowResultArea] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getDestinationCurrency = (destinationId: string) => {
    console.log("destination id outside :", destinationId);
    if (destinationId === "24") {
      console.log("destination id inside :", destinationId);
      return "ORM ";
    }
    return "$ ";
  };

  const {
    data: states,
    isLoading: isLoadingStates,
    isError: isStatesError,
  } = useGetStates();
  const {
    data: destinations,
    isLoading: isLoadingDestinations,
    isError: isDestinationsError,
  } = useGetDestinations();
  const {
    data: branches,
    isLoading: isLoadingBranches,
    isError: isBranchesError,
  } = useGetStateBranches(state ?? 0);
  const {
    data: cities,
    isLoading: isLoadingCities,
    isError: isCitiesError,
  } = useGetBranchCities(branch ?? 0);

  const calculateShippingMutation = useCalculateShipping();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!city || !destination) {
      setError(t("errors.select_required"));
      toast.error(t("errors.select_required"));
      return;
    }

    if (!showResultArea) {
      setShowResultArea(true);
    }

    setError(null);

    const payload: ShippingCalculationPayload = {
      destination_id: destination,
      loading_city_id: city,
      full_size_SUVs: options.full_size_SUVs,
      manheim_adesa: options.manheim_adesa,
      major_accident: options.major_accident,
    };

    calculateShippingMutation.mutate(payload, {
      onSuccess: (data) => {
        if (!data) {
          console.error("Received empty data from API");
          toast.error(t("errors.unexpected_data"));
          return;
        }

        // Calculate the total from the response data - use default values for any missing properties
        const shipping = data.shipping ?? 0;
        const towing = data.towing ?? 0;
        const tax_Duty = data.tax_Duty ?? 0;
        const exchange_rate = data.exchange_rate ?? 0;
        const full_size_SUVs = data.full_size_SUVs ?? 0;
        const manheim_adesa = data.manheim_adesa ?? 0;
        const major_accident = data.major_accident ?? 0;
        const more_than_10000_AED = data.more_than_10000_AED ?? 0;

        const total_cost =
          (shipping +
            towing +
            tax_Duty +
            full_size_SUVs +
            manheim_adesa +
            major_accident +
            more_than_10000_AED) *
          exchange_rate;

        setTotal(`${Math.round(total_cost)}`);
        toast.success(t("errors.calculation_success"));
      },
      onError: (error: any) => {
        if (error.response) {
          setError(
            t("errors.api_error", {
              status: error.response.status,
              message:
                error.response.data?.message || t("errors.calculation_failed"),
            })
          );
        } else if (error.request) {
          console.error("Request error:", error.request);
          setError(t("errors.no_response"));
        } else {
          setError(error.message || t("errors.try_again"));
        }

        toast.error(t("errors.calculation_failed"));
        setTotal(null);
      },
    });
  };

  const resetForm = () => {
    setState(null);
    setBranch(null);
    setCity("");
    setDestination("");
    setOptions({
      full_size_SUVs: false,
      manheim_adesa: false,
      major_accident: false,
    });
    setTotal(null);
    setError(null);
  };

  return (
    <TooltipProvider>
      <div
        className={cn(
          "max-w-3xl mx-auto flex items-center justify-center p-4",
          !className?.includes("!min-h-0") && "min-h-[calc(100vh-200px)]",
          className
        )}
      >
        <Card className="p-6 sm:p-8 shadow-md border-0 rounded-lg overflow-hidden w-full">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Form Header */}
            {/* <div className="border-b pb-4">
              <div className="flex items-center gap-2">
                <Calculator className="h-5 w-5 text-green-600" />
                <h3 className="text-xl font-semibold ">{t("header.title")}</h3>
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                {t("header.description")}
              </p>
            </div> */}

            {/* Location Selection */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center gap-1.5">
                  <MapPin className="h-3.5 w-3.5 text-green-600" />
                  {t("form.state")}
                </Label>
                {isStatesError ? (
                  <div className="text-sm text-red-500 flex items-center gap-1.5 p-2 bg-red-50 rounded border border-red-200">
                    <AlertCircle className="h-3.5 w-3.5" />
                    {t("errors.fetch_states_failed")}
                  </div>
                ) : (
                  <Select
                    value={state?.toString() || ""}
                    onValueChange={(value) => {
                      setState(parseInt(value));
                      setBranch(null);
                      setCity("");
                    }}
                  >
                    <SelectTrigger
                      className="w-full  border border-gray-200 
                      hover:border-green-300 focus:border-green-500 focus:ring-1 focus:ring-green-200 focus:ring-offset-0"
                    >
                      <SelectValue placeholder={t("form.state_placeholder")} />
                    </SelectTrigger>
                    <SelectContent
                      className="border border-gray-200 shadow-md animate-in fade-in-50 zoom-in-95 duration-100"
                      position="popper"
                      sideOffset={5}
                    >
                      {isLoadingStates ? (
                        <SelectItem value="loading">
                          {t("form.loading_states")}
                        </SelectItem>
                      ) : (
                        states?.map((state: { id: number; name: string }) => (
                          <SelectItem
                            value={state.id.toString()}
                            key={state.id}
                          >
                            {state.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                )}
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center gap-1.5">
                  <Building2 className="h-3.5 w-3.5 text-green-600" />
                  {t("form.branch")}
                </Label>
                {isBranchesError && state ? (
                  <div className="text-sm text-red-500 flex items-center gap-1.5 p-2 bg-red-50 rounded border border-red-200">
                    <AlertCircle className="h-3.5 w-3.5" />
                    {t("errors.fetch_branches_failed")}
                  </div>
                ) : (
                  <Select
                    value={branch?.toString() || ""}
                    onValueChange={(value) => {
                      setBranch(parseInt(value));
                      setCity("");
                    }}
                    disabled={!state}
                  >
                    <SelectTrigger
                      className="w-full  border border-gray-200 
                      hover:border-green-300 focus:border-green-500 focus:ring-1 focus:ring-green-200 focus:ring-offset-0"
                    >
                      <SelectValue placeholder={t("form.branch_placeholder")} />
                    </SelectTrigger>
                    <SelectContent
                      className="border border-gray-200 shadow-md animate-in fade-in-50 zoom-in-95 duration-100"
                      position="popper"
                      sideOffset={5}
                    >
                      {!state ? (
                        <SelectItem value="no-state" disabled>
                          {t("form.no_state_text")}
                        </SelectItem>
                      ) : isLoadingBranches ? (
                        <SelectItem value="loading">
                          {t("form.loading_branches")}
                        </SelectItem>
                      ) : (
                        branches?.map(
                          (branch: { id: number; name: string }) => (
                            <SelectItem
                              value={branch.id.toString()}
                              key={branch.id}
                            >
                              {branch.name}
                            </SelectItem>
                          )
                        )
                      )}
                    </SelectContent>
                  </Select>
                )}
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center gap-1.5">
                  <MapPin className="h-3.5 w-3.5 text-green-600" />
                  {t("form.city")}
                </Label>
                {isCitiesError && branch ? (
                  <div className="text-sm text-red-500 flex items-center gap-1.5 p-2 bg-red-50 rounded border border-red-200">
                    <AlertCircle className="h-3.5 w-3.5" />
                    {t("errors.fetch_cities_failed")}
                  </div>
                ) : (
                  <Select
                    value={city}
                    onValueChange={setCity}
                    disabled={!branch}
                  >
                    <SelectTrigger
                      className="w-full  border border-gray-200 
                      hover:border-green-300 focus:border-green-500 focus:ring-1 focus:ring-green-200 focus:ring-offset-0"
                    >
                      <SelectValue placeholder={t("form.city_placeholder")} />
                    </SelectTrigger>
                    <SelectContent
                      className="border border-gray-200 shadow-md animate-in fade-in-50 zoom-in-95 duration-100"
                      position="popper"
                      sideOffset={5}
                    >
                      {!state ? (
                        <SelectItem value="no-state" disabled>
                          {t("form.no_branch_text")}
                        </SelectItem>
                      ) : isLoadingCities ? (
                        <SelectItem value="loading">
                          {t("form.loading_cities")}
                        </SelectItem>
                      ) : (
                        cities?.map((city: { id: number; name: string }) => (
                          <SelectItem value={city.id.toString()} key={city.id}>
                            {city.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                )}
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center gap-1.5">
                  <Navigation className="h-3.5 w-3.5 text-green-600" />
                  {t("form.destination")}
                </Label>
                {isDestinationsError ? (
                  <div className="text-sm text-red-500 flex items-center gap-1.5 p-2 bg-red-50 rounded border border-red-200">
                    <AlertCircle className="h-3.5 w-3.5" />
                    {t("errors.fetch_destinations_failed")}
                  </div>
                ) : (
                  <Select value={destination} onValueChange={setDestination}>
                    <SelectTrigger
                      className="w-full  border border-gray-200 
                      hover:border-green-300 focus:border-green-500 focus:ring-1 focus:ring-green-200 focus:ring-offset-0"
                    >
                      <SelectValue
                        placeholder={t("form.destination_placeholder")}
                      />
                    </SelectTrigger>
                    <SelectContent
                      className="border border-gray-200 shadow-md animate-in fade-in-50 zoom-in-95 duration-100"
                      position="popper"
                      sideOffset={5}
                    >
                      {isLoadingDestinations ? (
                        <SelectItem value="loading">
                          {t("form.loading_destinations")}
                        </SelectItem>
                      ) : (
                        destinations?.map(
                          (destination: { id: number; name: string }) => (
                            <SelectItem
                              value={destination.id.toString()}
                              key={destination.id}
                            >
                              {destination.name}
                            </SelectItem>
                          )
                        )
                      )}
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>

            {/* Options
            <div className="space-y-3 border border-gray-100 rounded-lg p-4 bg-gray-50/50 dark:bg-transparent dark:border-gray-800">
              <h4 className="text-sm font-medium flex items-center gap-1.5">
                <Truck className="h-3.5 w-3.5 text-green-600" />
                {t("form.additional_services")}
              </h4>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center space-x-2 transition-colors duration-200 ease-in-out">
                      <Checkbox
                        id="full_size_SUVs"
                        checked={options.full_size_SUVs}
                        onCheckedChange={(checked) =>
                          setOptions((prev) => ({
                            ...prev,
                            full_size_SUVs: checked as boolean,
                          }))
                        }
                        className="data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500 transition-all duration-200"
                      />
                      <Label
                        htmlFor="full_size_SUVs"
                        className="text-sm cursor-pointer"
                      >
                        &nbsp;{t("form.full_size_suvs")}
                      </Label>
                    </div>
                  </TooltipTrigger>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center space-x-2 transition-colors duration-200 ease-in-out">
                      <Checkbox
                        id="manheim_adesa"
                        checked={options.manheim_adesa}
                        onCheckedChange={(checked) =>
                          setOptions((prev) => ({
                            ...prev,
                            manheim_adesa: checked as boolean,
                          }))
                        }
                        className="data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500 transition-all duration-200"
                      />
                      <Label
                        htmlFor="manheim_adesa"
                        className="text-sm cursor-pointer"
                      >
                        &nbsp;{t("form.manheim_adesa")}
                      </Label>
                    </div>
                  </TooltipTrigger>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center space-x-2 transition-colors duration-200 ease-in-out">
                      <Checkbox
                        id="major_accident"
                        checked={options.major_accident}
                        onCheckedChange={(checked) =>
                          setOptions((prev) => ({
                            ...prev,
                            major_accident: checked as boolean,
                          }))
                        }
                        className="data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500 transition-all duration-200"
                      />
                      <Label
                        htmlFor="major_accident"
                        className="text-sm cursor-pointer"
                      >
                        &nbsp;{t("form.major_accident")}
                      </Label>
                    </div>
                  </TooltipTrigger>
                </Tooltip>
              </div> */}
            {/* </div> */}

            {/* Error message */}
            {error && (
              <div className="bg-red-50 border border-red-100 text-red-600 p-3 rounded-md text-sm">
                {error}
              </div>
            )}

            {/* Result Display */}
            <div
              className="h-[80px] overflow-hidden transition-all duration-300 ease-in-out"
              style={{
                height: total ? "80px" : "0px",
                marginBottom: total ? "16px" : "0px",
              }}
            >
              {total && (
                <div className="bg-green-50 border border-green-100 rounded-lg p-4 animate-in fade-in duration-300 ease-in-out dark:bg-green-950/30 dark:border-green-900">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="bg-green-100 p-1.5 rounded-full dark:bg-green-900">
                        <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
                      </div>
                      <span className="font-medium text-gray-700 dark:text-gray-200">
                        {t("form.total_cost")}:
                      </span>
                    </div>
                    <span className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {getDestinationCurrency(destination)}
                      {total}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="grid grid-cols-1 sm:grid-cols-4 gap-3">
              <Button
                type="submit"
                className={cn(
                  "sm:col-span-3 bg-green-500 hover:bg-green-600 text-white h-11 rounded-md",
                  "transition-all duration-200 ease-in-out shadow-sm hover:shadow",
                  "flex items-center justify-center gap-2"
                )}
                disabled={
                  calculateShippingMutation.isPending || !city || !destination
                }
              >
                {calculateShippingMutation.isPending ? (
                  <>
                    <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>{t("form.loading_calculate")}</span>
                  </>
                ) : (
                  <span>{t("form.submit")}</span>
                )}
              </Button>

              <Button
                type="button"
                onClick={resetForm}
                variant="outline"
                className="h-11 border-gray-200  hover:bg-gray-50 hover:text-gray-800 transition-all duration-200 ease-in-out"
              >
                {t("form.reset")}
              </Button>
            </div>

            {/* Disclaimer */}
            <div
              className="flex items-start gap-3 text-xs text-gray-600 bg-gray-50 p-4 rounded-lg border border-gray-200
                           dark:text-gray-300 dark:bg-gray-800/10 dark:border-gray-700 dark:shadow-[0_4px_6px_-1px_rgba(0,0,0,0.2),0_2px_4px_-2px_rgba(0,0,0,0.1)]"
            >
              <AlertCircle className="h-4 w-4 text-amber-500 dark:text-amber-400 mt-0.5 flex-shrink-0" />
              <div>
                <span className="font-semibold text-gray-800 dark:text-amber-300">
                  {t("form.disclaimer_title")}:
                </span>{" "}
                {t("form.disclaimer_description")}
              </div>
            </div>
          </form>
        </Card>
      </div>
    </TooltipProvider>
  );
}
