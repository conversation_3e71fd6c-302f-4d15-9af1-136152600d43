import React from 'react'
import { Card, CardContent, CardDescription, CardHeader } from '../ui/card'
import { Skeleton } from '../ui/skeleton'
import { useParams } from 'next/navigation'
import { cn } from '@/lib/utils';
type RefProp =
  | React.RefObject<HTMLDivElement | null>
  | ((node: HTMLDivElement | null) => void);
export default function NotificationSkeleton({ref,className}:{ref?:RefProp,className?:string}) {
  const { locale } = useParams()
  return (
    <Card className={cn('h-56 mb-2 mr-2 relative',className)} dir={locale === 'ar' ? 'rtl' : 'ltr'} ref={ref}>
      <Skeleton className={`absolute top-4 ${locale === 'ar' ? "right-3" : "left-3"} h-5 w-5`} />
      <div className='pt-2 px-4'>
        <CardHeader className='py-2'>
          <Skeleton className='flex pb-2 gap-x-2 text-sm font-semibold rounded-sm h-8  m-0 p-0' />
          <div className='flex gap-x-2 items-center'>
            <Skeleton className='h-4 w-16 text-primary' /> <Skeleton className='text-foreground text-xs' />
          </div>
          <Skeleton className='text-sm font-normal h-4  w-32' />
        </CardHeader>
        <CardContent className=' text-sm text-justify '>
          <CardDescription className='pb-2'>
            <Skeleton className='pb-4 h-5 w-48 my-2' />
          </CardDescription>
          <Skeleton className='pb-2  h-16 w-full' />
        </CardContent>
      </div>
    </Card>)
}
