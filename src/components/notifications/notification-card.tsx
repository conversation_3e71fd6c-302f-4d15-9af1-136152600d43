import React, { ElementType } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
  CardDescription,
  CardContent,
} from "../ui/card";
import FormatDate from "../Common_UI/format-date";
import { useParams, useRouter } from "next/navigation";
import { useNotification } from "@/context/notification-context";
import { useSession } from "next-auth/react";
import { useProgress } from "@bprogress/next";
type NotificationCardProps = {
  title: string;
  description: string;
  Icon: ElementType;
  IconClass?: string;
  date: Date;
  tag: string;
  item: any;
};
export default function NotificationsCard({
  description,
  title,
  Icon,
  IconClass,
  date,
  item,
}: NotificationCardProps) {
  const router = useRouter();
  const { locale } = useParams();
  const session = useSession();
  const { setNotifications } = useNotification();
  const { start } = useProgress();

  const redirectPage = (item: any) => {
    if (item.notification_type == "announcement") {
      start();
      router.push(`/${locale}/announcements`);
    } else if (item.notification_type == "shipping_rate") {
      if (session.data?.profile?.companies?.show_shipping_rate) start();
      router.push(`/${locale}/shipping_rates`);
    } else if (item.notification_type == "mix_shipping_rate") {
      start();
      router.push(`/${locale}/mix-shipping-rates`);
    } else if (item.notification_type == "transaction") {
      start();
      router.push(`/${locale}/payments/all`);
    } else if (item.notification_type == "arrival_notice") {
      const containerIds = item?.data?.containers
        .map((item: any) => item.id)
        .join("&con=");
      start();
      router.push(`/${locale}/shipments/all?con=${containerIds}`);
    }
    setNotifications(false);
  };
  return (
    <Card
      className="grid grid-cols-[auto_1fr] mb-2 mr-2 cursor-pointer"
      dir={locale === "ar" ? "rtl" : "ltr"}
      onClick={() => redirectPage(item)}
    >
      <div className="relative px-3">
        <Icon className={IconClass + " absolute top-5"} />
      </div>
      <div className="pt-2">
        <CardHeader className="py-2">
          <CardTitle className="flex gap-x-2 text-sm font-semibold m-0 p-0">
            {" "}
            {title}
          </CardTitle>
          <FormatDate date={new Date(date)} />
        </CardHeader>
        <CardContent className=" text-sm text-justify ">
          <CardDescription
            dangerouslySetInnerHTML={{ __html: description }}
          ></CardDescription>
        </CardContent>
      </div>
    </Card>
  );
}
