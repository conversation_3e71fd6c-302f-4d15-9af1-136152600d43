import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";

import CustomTabs from "../Common_UI/custom-tabs";
import NotificationAnnouncement from "./notification-announcements";
import ArrivalNotice from "./arrival-notice";
import Payments from "./payments";
import { useNotification } from "@/context/notification-context";
import NotificationBadge from "../Common_UI/notification-badge";
import { useParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { Button } from "../ui/button";
import { CheckCheck } from "lucide-react";
import { useMarkAllReadNotification } from "./services/useMarkAllReadNotification";

export default function NotificationWindow() {
  const { markAllRead } = useMarkAllReadNotification();
  const { notifications, setNotifications, setTabValue, totalNotification } =
    useNotification();
  const total =
    (totalNotification.announcement || 0) +
    (totalNotification.arrival_notice || 0) +
    (totalNotification.transaction || 0);
  const t = useTranslations("notification");
  const { locale } = useParams();
  const isRTL = locale === "ar";

  return (
    <Dialog open={notifications} onOpenChange={setNotifications}>
      <CustomTabs
        defaultValue="announcement"
        tabsClassName="w-full max-w-[600px]"
      >
        <DialogContent
          className="sm:max-w-[600px] h-[500px] shadow-lg rounded-lg border-0"
          dir={isRTL ? "rtl" : "ltr"}
        >
          <DialogHeader className="pb-2 border-b">
            <div className="flex items-center justify-between mb-2">
              <DialogTitle
                className="text-xl font-semibold flex items-center gap-2"
                dir={isRTL ? "rtl" : "ltr"}
              >
                {t("title")}
                <div className="flex items-center gap-2">
                  <NotificationBadge
                    count={total}
                    containerClassName="flex items-center"
                  />

                  {total > 0 && (
                    <Button
                      variant="outline"
                      size="sm"
                      className={`flex items-center gap-1 px-2 text-xs h-6  text-primary bg-primary/10 hover:text-primary rtl:ml-8 `}
                      onClick={() => markAllRead()}
                    >
                      <CheckCheck className="w-5 h-5" />
                      {/* <span>{t("mark-all-read")}</span> */}
                    </Button>
                  )}
                </div>
              </DialogTitle>
            </div>

            <CustomTabs.TabList
              tabsTriggers={[
                {
                  value: "announcement",
                  label: t(`announcements`),
                  Badge: (
                    <NotificationBadge
                      count={totalNotification.announcement || 0}
                      containerClassName="inline-flex"
                    />
                  ),
                  click: () => setTabValue("announcement"),
                },
                {
                  value: "arrival_notice",
                  label: t(`arrival-notices`),
                  Badge: (
                    <NotificationBadge
                      count={totalNotification.arrival_notice || 0}
                      containerClassName="inline-flex"
                    />
                  ),
                  click: () => setTabValue("arrival_notice"),
                },
                {
                  value: "transaction",
                  label: t(`payments`),
                  Badge: (
                    <NotificationBadge
                      count={totalNotification.transaction || 0}
                      containerClassName="inline-flex"
                    />
                  ),
                  click: () => setTabValue("transaction"),
                },
              ]}
              tabsListClassName="grid w-full grid-cols-3 gap-2"
            />
          </DialogHeader>

          <div className="max-h-[380px] overflow-y-auto py-2 px-1">
            <CustomTabs.TabContent
              tabsContent={[
                {
                  children: <NotificationAnnouncement />,
                  value: "announcement",
                },
                { children: <ArrivalNotice />, value: "arrival_notice" },
                { children: <Payments />, value: "transaction" },
              ]}
            />
          </div>
        </DialogContent>
      </CustomTabs>
    </Dialog>
  );
}
