"use server";

import axios from "@/utils/axios-server";
type tabValue =
  | "announcement"
  | "shipping_rate"
  | "mix_shipping_rate"
  | "arrival_notice"
  | "transaction";
export type NotificationType = {
  page: number;
  tabValue: tabValue;
};

export const getNotifications = async ({
  page,
  tabValue,
}: NotificationType) => {
  try {
    const res = await axios.get("/v2/notifications", {
      params: {
        page,
        notification_types: JSON.stringify(
          tabValue == "announcement"
            ? ["announcement", "shipping_rate", "mix_shipping_rate"]
            : [tabValue]
        ),
      },
    });
    return res.data ?? [];
  } catch (error: any) {
    console.error('Notification fetch error:', error);
    // Return empty data structure instead of throwing error
    return {
      data: [],
      total: {},
      unreadTotal: {},
      page: 1,
      per_page: 10
    };
  }
};
export const markAllAsReadNotifications = async ({
  tabValue,
}: {
  tabValue: tabValue;
}) => {
  try {
    const res = await axios.patch("/v2/notifications", {
      notification_types: JSON.stringify(
        tabValue == "announcement"
          ? ["announcement", "shipping_rate", "mix_shipping_rate"]
          : [tabValue]
      ),
    });
    return res.data ?? [];
  } catch (error: any) {
    console.error('Mark all read error:', error);
    return [];
  }
};
