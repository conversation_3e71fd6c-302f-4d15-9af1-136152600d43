import { useProgress } from "@bprogress/next";
import { useRouter } from "next/navigation";

export function useRedirectNotification() {
  const router = useRouter();
  const { start } = useProgress();
  const redirectPage = (item: any) => {
    if (item.notification_type == "announcement") {
      start();
      router.push("/announcements");
    } else if (item.notification_type == "shipping_rate") {
      // if (profile?.data?.companies?.show_shipping_rate)
      //   router.push('/shipping_rates');
    } else if (item.notification_type == "mix_shipping_rate") {
      start();
      router.push("/mix_shipping_rates");
    } else if (item.notification_type == "arrival_notice") {
    } else if (item.notification_type == "transaction") {
      start();
      router.push("/payments/all");
    } else if (item.notification_type == "arrival_notice") {
      const containerIds = item?.data?.containers?.length
        ? item.data.containers.map((c: any) => c.id).join(",")
        : "";

      const url = `/shipment/all${
        containerIds ? `?containerIds=${encodeURIComponent(containerIds)}` : ""
      }`;
      start();
      router.push(url);
    }
  };

  return { redirectPage };
}
