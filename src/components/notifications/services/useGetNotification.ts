"use client";
import { useInfiniteQuery } from "@tanstack/react-query";
import { getNotifications } from "./notification-service"; // Import your API function
import { useNotification } from "@/context/notification-context";

interface responseDataType {
  data: any[];
  page: any;
  total: any;
  unreadTotal: any;
  per_page: number;
}
export function useGetNotification() {
  const { tabValue, setTotalNotification } = useNotification()

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
  } = useInfiniteQuery({
    queryKey: ["notification", tabValue],
    queryFn: async ({ pageParam }) => {
      const response = await getNotifications({
        page: pageParam,
        tabValue,
      });

      // Dynamically adjust 'announcement' total
      if (tabValue === "announcement") {
        response.unreadTotal.announcement =
          (response.unreadTotal.announcement || 0) +
          (response.unreadTotal.shipping_rate || 0) +
          (response.unreadTotal.mix_shipping_rate || 0);
      }
      setTotalNotification({ ...response.unreadTotal });

      return {
        data: response.data,
        total: response.total,
        unreadTotal: response.unreadTotal,
        page: response.page,
        per_page: response.per_page,
      };
    },
    getNextPageParam: (lastPage) => {
      const currentPage = Number(lastPage.page);
      const totalPages = Math.ceil(lastPage.total[tabValue] / lastPage.per_page);
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    initialPageParam: 1,
  });

  const notification = data?.pages.flatMap((page) => page) || []

  const newData: responseDataType = notification.length > 0
    ? notification.reduce((acc, item) => ({
      data: [...acc.data, ...item.data],
      page: item.page,
      total: { ...item.total },
      unreadTotal: { ...item.unreadTotal },
      per_page: 10
    }), { data: [], page: '1', total: {}, unreadTotal: {}, per_page: 10 })
    : { data: [], page: '1', total: {}, unreadTotal: {}, per_page: 10 };

  return {
    notificationsData: newData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
  };
}

