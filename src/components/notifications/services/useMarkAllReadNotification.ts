import { useMutation, useQueryClient } from "@tanstack/react-query";
import { markAllAsReadNotifications } from "./notification-service";
import { useNotification } from "@/context/notification-context";

export function useMarkAllReadNotification() {
  const qyeryClient = useQueryClient();
  const { tabValue, setTotalNotification, totalNotification } =
    useNotification();
  const { mutate: markAllRead,  } = useMutation({
    mutationFn: () => markAllAsReadNotifications({ tabValue }),
    onSuccess: () => {
      qyeryClient.invalidateQueries({ queryKey: ["notification", tabValue] });
      if (
        tabValue === "announcement" ||
        tabValue === "shipping_rate" ||
        tabValue === "mix_shipping_rate"
      ) {
        setTotalNotification({ ...totalNotification, announcement: 0 });
      } else if (tabValue === "arrival_notice") {
        setTotalNotification({ ...totalNotification, arrival_notice: 0 });
      } else if (tabValue === "transaction") {
        setTotalNotification({ ...totalNotification, transaction: 0 });
      }
    },
    onError: (error) => {
      throw error;
    },
  });
  return { markAllRead };
}
