import React, { useEffect } from 'react'
import NotificationsCard from './notification-card'
import { BusIcon } from 'lucide-react'
import NotificationSkeleton from './notification-skeleton'
import { useGetNotification } from './services/useGetNotification';
import { useInView } from 'react-intersection-observer';
export default function ArrivalNotice() {
  const { notificationsData,fetchNextPage,hasNextPage,isLoading} = useGetNotification();
  const arrival_notice = notificationsData?.data?.filter((item: any) => item.notification_type) || []
  const { ref, inView, } = useInView({
    threshold: 0,
  });
  useEffect(() => {
    if (inView) {
      fetchNextPage();
    }
  }, [fetchNextPage, inView]);
  if(isLoading) return  <NotificationSkeleton />
  return (
    <>
      {arrival_notice.map((item: any) => (
        <NotificationsCard
          key={item.id}
          title={item.title}
          tag='Value Customers!'
          date={new Date()}
          description={item.description}
          Icon={BusIcon} IconClass="w-8 h-8"
          item={item}
           />))}
        {hasNextPage? <NotificationSkeleton ref={ref} /> : <p className='text-center font-semibold py-4'>No more data</p>}
    </>
  )
}
