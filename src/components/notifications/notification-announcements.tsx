"use client";
import { Bell, Receipt } from "lucide-react";
import React, { useEffect } from "react";
import NotificationsCard from "./notification-card";
import NotificationSkeleton from "./notification-skeleton";
import { useGetNotification } from "./services/useGetNotification";
import { useInView } from "react-intersection-observer";
const announcementNofification = [
  "announcement",
  "shipping_rate",
  "mix_shipping_rate",
];
const notificationTypes: any = {
  announcement: Bell,
  shipping_rate: Receipt,
  mix_shipping_rate: Receipt,
};
export default function NotificationAnnouncement() {
  const { notificationsData, fetchNextPage, hasNextPage, isLoading } =
    useGetNotification();
  const announcements =
    notificationsData?.data?.filter((item: any) =>
      announcementNofification.includes(item.notification_type)
    ) || [];
  const { ref, inView } = useInView({
    threshold: 0,
  });
  useEffect(() => {
    if (inView) {
      fetchNextPage();
    }
  }, [fetchNextPage, inView]);
  if (isLoading) return <NotificationSkeleton />;
  return (
    <>
      {announcements.map((item: any) => (
        <NotificationsCard
          key={item.id}
          title={item.title}
          tag="Value Customers!"
          date={new Date()}
          description={item.description}
          Icon={notificationTypes[item.notification_type]}
          IconClass="w-8 h-8"
          item={item}
        />
      ))}
      {hasNextPage ? (
        <NotificationSkeleton ref={ref} />
      ) : (
        <p className="text-center font-semibold py-4">No more data</p>
      )}
    </>
  );
}
