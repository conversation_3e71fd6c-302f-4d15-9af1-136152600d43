import { useState, useEffect, forwardRef, useImperative<PERSON><PERSON>le } from "react";
import { Button } from "@/components/ui/button";
import { Download, Upload, FileText, CheckCircle2, X } from "lucide-react";
import { toast } from "sonner";
import { useRouter, useParams } from "next/navigation";
import { StepHeader } from "./StepHeader";
import { useTranslations } from "next-intl";
import { useDirection } from "@/hooks/useDirection";
import { useFetchClient } from "@/utils/axios";

interface ContractStepProps {
  formData: {
    contract?: {
      uploadedFile?: File | null;
      hasDownloadedContract: boolean;
      registrationData?: {
        id: string;
        customers: { lang: string }[];
      };
    };
  };
  updateFormData: (data: any) => void;
  setHideFooter: (hide: boolean) => void;
}

export interface ContractStepRef {
  handleUploadSuccess: () => void;
}

export const ContractStep = forwardRef<ContractStepRef, ContractStepProps>(
  ({ formData, updateFormData, setHideFooter }, ref) => {
    const [isDownloading, setIsDownloading] = useState(false);
    const [registrationComplete, setRegistrationComplete] = useState(false);
    const router = useRouter();
    const params = useParams();
    const locale = params?.locale || "en";
    const t = useTranslations("Register.Contract");
    const buttonsT = useTranslations("Register.buttons");
    const { isRTL } = useDirection();
    const fetchClient = useFetchClient();

    useImperativeHandle(ref, () => ({
      handleUploadSuccess: () => {
        setRegistrationComplete(true);
      },
    }));

    // Hide footer when registration is complete
    useEffect(() => {
      setHideFooter(registrationComplete);
    }, [registrationComplete, setHideFooter]);

    const handleDownloadContract = async () => {
      try {
        setIsDownloading(true);

        const companyId = formData.contract?.registrationData?.id;
        const lang =
          formData.contract?.registrationData?.customers[0]?.lang || "en";

        if (!companyId) {
          toast.error(t("registrationDataNotFound"));
          return;
        }

        // Get the contract file from the backend
        const response = await fetchClient(
          `/v2/customer-register-v2/downloadContract/${companyId}?companyId=${companyId}&lang=${lang}`,
          { responseType: "blob" }
        );

        // Create a download link
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const a = document.createElement("a");
        a.href = url;
        a.download = "customer_contract.pdf";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        updateFormData({
          contract: {
            ...formData.contract,
            hasDownloadedContract: true,
          },
        });
        toast.success(t("contractDownloaded"));
      } catch (error) {
        toast.error(t("downloadError"));
        console.error(error);
      } finally {
        setIsDownloading(false);
      }
    };

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      if (file.type !== "application/pdf") {
        toast.error(t("pdfOnly"));
        return;
      }

      updateFormData({
        contract: {
          ...formData.contract,
          uploadedFile: file,
          hasDownloadedContract: true,
        },
      });
    };

    const handleFileRemove = () => {
      updateFormData({
        contract: {
          ...formData.contract,
          uploadedFile: null,
        },
      });
    };

    if (registrationComplete) {
      return (
        <div
          className={`flex flex-col items-center justify-center space-y-6 py-20 animate-in fade-in duration-700 slide-in-from-bottom-4 ${
            isRTL ? "text-right" : ""
          }`}
        >
          <CheckCircle2 className="h-16 w-16 text-green-500 animate-bounce stroke-[1.5]" />
          <h2 className="text-2xl font-bold text-center">
            {t("registrationComplete")}
          </h2>
          <p
            className={`text-gray-600 text-center max-w-md ${
              isRTL ? "text-right" : ""
            }`}
          >
            {t("registrationCompleteDescription")}
          </p>
          <div className={`flex gap-4 mt-6 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Button
              onClick={() =>
                (window.location.href = `/${locale}/auth/register`)
              }
              variant="outline"
              className="min-w-[200px] transform transition-all duration-500 ease-in-out hover:scale-100 hover:shadow-xl"
            >
              {t("registerNew")}
            </Button>
            <Button
              onClick={() => router.push(`/${locale}/auth/signin`)}
              className="min-w-[200px] transform transition-all duration-500 ease-in-out hover:scale-100 hover:shadow-xl"
            >
              {t("goToLogin")}
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-8">
        <div className="">
          <StepHeader title={t("title")} />
        </div>

        <div className={`space-y-6 ${isRTL ? "text-right" : ""}`}>
          <p className="text-gray-500">{t("instructions")}</p>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{t("downloadStep")}</h3>
            <Button
              onClick={handleDownloadContract}
              disabled={isDownloading}
              className={`w-full sm:w-auto transform transition-all ease-in-out hover:scale-105 hover:shadow-md ${
                isRTL ? "flex-row-reverse" : ""
              }`}
            >
              <Download className={isRTL ? "ml-2 h-4 w-4" : "mr-2 h-4 w-4"} />
              {isDownloading ? buttonsT("submitting") : t("downloadContract")}
            </Button>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">{t("uploadStep")}</h3>
            <div
              className={`flex items-center space-x-4 ${
                isRTL ? "flex-row-reverse space-x-reverse" : ""
              }`}
            >
              <Button
                onClick={() =>
                  document.getElementById("contract-upload")?.click()
                }
                disabled={!formData.contract?.hasDownloadedContract}
                className={`w-full sm:w-auto transform transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-md ${
                  isRTL ? "flex-row-reverse" : ""
                }`}
              >
                <Upload className={isRTL ? "ml-2 h-4 w-4" : "mr-2 h-4 w-4"} />
                {t("uploadContract")}
              </Button>
              <input
                type="file"
                id="contract-upload"
                className="hidden"
                accept=".pdf"
                onChange={handleFileSelect}
                disabled={!formData.contract?.hasDownloadedContract}
              />
            </div>
            {formData.contract?.uploadedFile && (
              <div
                className={`flex items-center justify-between text-sm bg-green-50 p-2 rounded-md ${
                  isRTL ? "flex-row-reverse" : ""
                }`}
              >
                <div
                  className={`flex items-center space-x-2 text-green-600 ${
                    isRTL ? "flex-row-reverse space-x-reverse" : ""
                  }`}
                >
                  <FileText className="h-4 w-4" />
                  <span>
                    {t("selectedFile")}: {formData.contract.uploadedFile.name}
                  </span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleFileRemove}
                  className="text-red-500 hover:text-red-700 hover:bg-red-50 transform transition-all duration-300 ease-in-out"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            )}
            {!formData.contract?.hasDownloadedContract && (
              <p
                className={`text-sm text-amber-600 ${
                  isRTL ? "text-right" : ""
                }`}
              >
                {t("downloadFirst")}
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }
);
ContractStep.displayName = "ContractStep";
