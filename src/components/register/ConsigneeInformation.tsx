import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { ConsigneeInformationData } from "@/types/form"
import { StepHeader } from "./StepHeader"
import { useTranslations } from 'next-intl'
import { useDirection } from '@/hooks/useDirection'

interface ConsigneeInformationProps {
  data: ConsigneeInformationData;
  onUpdate: (data: ConsigneeInformationData) => void;
  errors?: { [key: string]: string };
}

interface InputFieldProps {
  id: string;
  label: string;
  value: string;
  type?: string;
  required?: boolean;
  onChange: (value: string) => void;
  error?: string;
  isRTL?: boolean;
}

const InputField = ({ id, label, value, type = "text", required = true, onChange, error, isRTL }: InputFieldProps) => (
  <div className="space-y-2">
    <Label htmlFor={id} className={isRTL ? 'text-right w-full block' : ''}>
      {label}
      {required && <span className="text-red-500 ml-1">*</span>}
    </Label>
    <Input 
      id={id}
      type={type}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      required={required}
      className={`${error ? "border-red-500" : ""} ${isRTL ? "text-right" : ""}`}
      dir={isRTL ? "rtl" : "ltr"}
    />
    {error && <p className={`text-sm text-red-500 ${isRTL ? "text-right" : ""}`}>{error}</p>}
  </div>
)

export function ConsigneeInformation({ data, onUpdate, errors = {} }: ConsigneeInformationProps) {
  const handleChange = (field: keyof ConsigneeInformationData, value: string) => {
    onUpdate({ ...data, [field]: value })
  }
  const t = useTranslations('Register.ConsigneeInformation')
  const { isRTL } = useDirection()

  return (
    <div className="space-y-6">
      <StepHeader 
        title={t('title')} 
      />

      <div className="grid grid-cols-2 gap-4 pt-2">
        <InputField
          id="consigneeFullName"
          label={t('fullName')}
          value={data.consigneeFullName}
          onChange={(value) => handleChange('consigneeFullName', value)}
          error={errors.consigneeFullName}
          required={false}
          isRTL={isRTL}
        />
        <InputField
          id="consigneeCompanyName"
          label={t('companyName')}
          value={data.consigneeCompanyName}
          onChange={(value) => handleChange('consigneeCompanyName', value)}
          error={errors.consigneeCompanyName}
          required={false}
          isRTL={isRTL}
        />
        <InputField
          id="consigneeEmail"
          label={t('email')}
          type="email"
          value={data.consigneeEmail}
          onChange={(value) => handleChange('consigneeEmail', value)}
          error={errors.consigneeEmail}
          required={false}
          isRTL={isRTL}
        />
        <InputField
          id="consigneePhone"
          label={t('phoneNumber')}
          type="tel"
          value={data.consigneePhone}
          onChange={(value) => handleChange('consigneePhone', value)}
          error={errors.consigneePhone}
          required={false}
          isRTL={isRTL}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="consigneeAddress" className={isRTL ? 'text-right w-full block' : ''}>{t('address')}</Label>
        <Textarea 
          id="consigneeAddress"
          value={data.consigneeAddress}
          onChange={(e) => handleChange('consigneeAddress', e.target.value)}
          className={`${errors.consigneeAddress ? "border-red-500" : ""} ${isRTL ? "text-right" : ""}`}
          dir={isRTL ? "rtl" : "ltr"}
          required={false}
        />
        {errors.consigneeAddress && <p className={`text-sm text-red-500 ${isRTL ? "text-right" : ""}`}>{errors.consigneeAddress}</p>}
      </div>
    </div>
  )
}

