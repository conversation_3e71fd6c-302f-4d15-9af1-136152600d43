import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { LoadVolumeData } from "@/types/form"
import { StepHeader } from "./StepHeader"
import { useTranslations } from 'next-intl'
import { useDirection } from '@/hooks/useDirection'

interface LoadVolumeProps {
  data: LoadVolumeData;
  onUpdate: (data: LoadVolumeData) => void;
  errors?: { [key: string]: string };
}

interface InputFieldProps {
  id: string;
  label: string;
  value: string;
  type?: string;
  required?: boolean;
  onChange: (value: string) => void;
  error?: string;
  isRTL?: boolean;
}

const InputField = ({ id, label, value, type = "text", required = true, onChange, error, isRTL }: InputFieldProps) => (
  <div className="space-y-2">
    <Label htmlFor={id} className={isRTL ? 'text-right w-full block' : ''}>{label}</Label>
    <Input 
      id={id}
      type={type}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      required={required}
      className={`${error ? "border-red-500" : ""} ${isRTL ? "text-right" : ""}`}
      dir={isRTL ? "rtl" : "ltr"}
    />
    {error && <p className={`text-sm text-red-500 ${isRTL ? "text-right" : ""}`}>{error}</p>}
  </div>
)

export function LoadVolume({ data, onUpdate, errors = {} }: LoadVolumeProps) {
  const handleChange = (field: keyof LoadVolumeData, value: string) => {
    onUpdate({ ...data, [field]: value })
  }
  const t = useTranslations('Register.LoadVolume')
  const { isRTL } = useDirection()

  return (
    <div className="space-y-6">
      <StepHeader title={t('title')} />
      <div className="grid grid-cols-2 gap-4 pt-2">
        <InputField
          id="numberOfVehicles"
          label={t('numberOfVehicles')}
          type="number"
          value={data.numberOfVehicles}
          onChange={(value) => handleChange('numberOfVehicles', value)}
          error={errors.numberOfVehicles}
          isRTL={isRTL}
        />
        <InputField
          id="numberOfContainers"
          label={t('numberOfContainers')}
          type="number"
          value={data.numberOfContainers}
          onChange={(value) => handleChange('numberOfContainers', value)}
          error={errors.numberOfContainers}
          isRTL={isRTL}
        />
      </div>
    </div>
  )
}

