import { Button } from "@/components/ui/button"
import { useTranslations } from 'next-intl'
import { useDirection } from '@/hooks/useDirection'

interface StepFooterProps {
  currentStep: number
  totalSteps: number
  onNext: () => void
  onPrev: () => void
  isLastStep?: boolean
  isSubmitting?: boolean
  isRegistered?: boolean
  disableNext?: boolean
  nextButtonText?: string
}

export function StepFooter({ 
  currentStep, 
  totalSteps, 
  onNext, 
  onPrev, 
  isSubmitting = false,
  isRegistered = false,
  disableNext = false,
  nextButtonText
}: StepFooterProps) {
  const t = useTranslations('Register.buttons')
  const { isRTL } = useDirection()
  const isContractStep = currentStep === 6 // Contract is step 6 (0-based index)
  const disablePrevious = currentStep === 0 || isSubmitting || (isContractStep && isRegistered)

  const PreviousButton = (
    <Button
      variant="outline"
      onClick={onPrev}
      disabled={disablePrevious}
    >
      {t('previous')}
    </Button>
  )

  const NextButton = (
    <Button
      onClick={onNext}
      disabled={disableNext || isSubmitting}
    >
      {isSubmitting ? t('submitting') : nextButtonText || (currentStep === totalSteps - 1 ? t('submit') : t('next'))}
    </Button>
  )

  return (
    <div className="p-6 border-t flex justify-between">
      {isRTL ? (
        <>
          {NextButton}
          {PreviousButton}
        </>
      ) : (
        <>
          {PreviousButton}
          {NextButton}
        </>
      )}
    </div>
  )
} 