'use client'

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/register/Stepper"
import { BasicInformation } from "@/components/register/BasicInformation"
import { AdditionalInformation } from "@/components/register/AdditionalInformation"
import { ConsigneeInformation } from "@/components/register/ConsigneeInformation"
import { NotifyParty } from "@/components/register/NotifyParty"
import { LoadVolume } from "@/components/register/LoadVolume"
import { Registration } from "@/components/register/Registration"
import { ContractStep, ContractStepRef } from "@/components/register/ContractStep"
import type { FormData as RegisterFormData } from "@/types/form"
import { toast } from "sonner"
import axios from "axios"
import { StepFooter } from "@/components/register/StepFooter"
import {
  validateBasicInformation,
  validateAdditionalInformation,
  validateConsigneeInformation,
  validateNotifyParty,
  validateLoadVolume
} from "@/lib/validations/form"
import Language from "@/components/localization/langauge"
import { useDirection } from "@/hooks/useDirection"
import { useTranslations } from 'next-intl'
import { useParams } from "next/navigation"
import { API_CONFIG } from "@/config/api"
import { Card } from "../ui/card"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"

interface RegisterPageProps {
  initialSteps: Array<{
    title: string
    icon: string
  }>
}

const initialFormData: RegisterFormData = {
  basicInformation: {
    fullName: "",
    companyName: "",
    email: "",
    phone: "",
    address: "",
    destination: "",
  },
  additionalInformation: {
    secondaryEmail: "",
    secondaryPhone: "",
    joinDate: "",
    usedCar: false,
    completeCars: {
      container: false,
      consolidation: false,
    },
    halfcutCars: {
      container: false,
      consolidation: false,
    },
    vehicleTypes: {
      suv: false,
      sedan: false,
    },
  },
  consigneeInformation: {
    consigneeFullName: "",
    consigneeCompanyName: "",
    consigneeEmail: "",
    consigneePhone: "",
    consigneeAddress: "",
  },
  notifyParty: {
    notifyFullName: "",
    notifyCompanyName: "",
    notifyEmail: "",
    notifyPhone: "",
    notifyAddress: "",
  },
  loadVolume: {
    numberOfVehicles: "",
    numberOfContainers: "",
  },
  contract: {
    uploadedFile: null,
    hasDownloadedContract: false,
  },
}

export default function RegisterStepperForm({ initialSteps }: RegisterPageProps) {
  const params = useParams()
  const router = useRouter()
  let locale = (params?.locale as string) || 'en'
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState<RegisterFormData>(initialFormData)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<{ [key: string]: any }>({})
  const [isRegistered, setIsRegistered] = useState(false)
  const [hideFooter, setHideFooter] = useState(false)
  const { isRTL } = useDirection()
  const contractStepRef = useRef<ContractStepRef>(null)
  const t = useTranslations('Register')

  const validateCurrentStep = () => {
    let currentErrors = {}

    switch (currentStep) {
      case 0:
        currentErrors = validateBasicInformation(formData.basicInformation)
        break
      case 1:
        currentErrors = validateAdditionalInformation(formData.additionalInformation)
        break
      case 2:
        currentErrors = validateConsigneeInformation(formData.consigneeInformation)
        break
      case 3:
        currentErrors = validateNotifyParty(formData.notifyParty)
        break
      case 4:
        currentErrors = validateLoadVolume(formData.loadVolume)
        break
      case 5:
        // No validation needed for confirmation step
        return true
      case 6:
        // Validate contract step
        if (!formData.contract.hasDownloadedContract) {
          currentErrors = { contract: "Please download and review the contract" }
        }
        if (!formData.contract.uploadedFile) {
          currentErrors = { ...currentErrors, upload: "Please upload the signed contract" }
        }
        break
    }

    setErrors(currentErrors)
    return Object.keys(currentErrors).length === 0
  }

  const handleStepClick = (stepIndex: number) => {
    // Prevent clicking on contract step if not ready
    if (stepIndex === 6 && !isRegistered) {
      return
    }
    
    if (completedSteps.includes(stepIndex) || stepIndex === currentStep || stepIndex === Math.min(Math.max(...completedSteps) + 1, initialSteps.length - 1)) {
      setErrors({})
      setCurrentStep(stepIndex)
    }
  }

  const nextStep = () => {
    if (validateCurrentStep()) {
      setCurrentStep((prev) => {
        const nextStepIndex = Math.min(prev + 1, initialSteps.length - 1)
        // Mark all steps as completed except the contract step
        if (!completedSteps.includes(prev)) {
          setCompletedSteps(prevCompleted => [...prevCompleted, prev])
        }
        return nextStepIndex
      })
    } else {
      toast.error('Please fix the validation errors before proceeding')
    }
  }

  const prevStep = () => {
    if (isRegistered && currentStep === 6) {
      // Allow going back to review registration details even after registration
      setErrors({})
      setCurrentStep((prev) => Math.max(prev - 1, 0))
      return
    }
    if (isRegistered) {
      return
    }
    setErrors({})
    setCurrentStep((prev) => Math.max(prev - 1, 0))
  }

  const updateFormData = (step: keyof RegisterFormData, data: any) => {
    setFormData((prev) => ({
      ...prev,
      [step]: data,
    }))
    // Clear errors when user updates data
    setErrors({})
  }

  const handleSubmit = async () => {
    if (!validateCurrentStep()) {
      toast.error('Please fix the validation errors before submitting')
      return
    }

    try {
      setIsSubmitting(true)
     

      switch(locale){
        case 'ar':
          locale = 'ara'
          break
        case 'ka':
          locale = 'geo'
          break
        case 'ru':
          locale = 'rus'
          break
      }

      const transformedData = {
        name: formData.basicInformation.fullName,
        company_name: formData.basicInformation.companyName,
        phone: formData.basicInformation.phone,
        email: formData.basicInformation.email,
        secondary_phone: formData.additionalInformation.secondaryPhone,
        secondary_email: formData.additionalInformation.secondaryEmail,
        address: formData.basicInformation.address,
        destination_id: parseInt(formData.basicInformation.destination),

        consignee_poc: formData.consigneeInformation.consigneeFullName,
        consignee: formData.consigneeInformation.consigneeCompanyName,
        consignee_phone: formData.consigneeInformation.consigneePhone,
        consignee_email: formData.consigneeInformation.consigneeEmail,
        consignee_street: formData.consigneeInformation.consigneeAddress,

        notify_poc: formData.notifyParty.notifyFullName,
        notify_party: formData.notifyParty.notifyCompanyName,
        notify_phone: formData.notifyParty.notifyPhone,
        notify_email: formData.notifyParty.notifyEmail,
        notify_street: formData.notifyParty.notifyAddress,

        vehicles_number: formData.loadVolume.numberOfVehicles.toString(),
        containers_number: formData.loadVolume.numberOfContainers.toString(),

        join_date: formData.additionalInformation.joinDate,
        is_belong_to_used_car: Boolean(formData.additionalInformation.usedCar),

        mix: Boolean(formData.additionalInformation.completeCars.consolidation),
        complete: Boolean(formData.additionalInformation.completeCars.container),
        mix_halfcut: Boolean(formData.additionalInformation.halfcutCars.consolidation),
        complete_halfcut: Boolean(formData.additionalInformation.halfcutCars.container),

        suv: Boolean(formData.additionalInformation.vehicleTypes.suv),
        sedan: Boolean(formData.additionalInformation.vehicleTypes.sedan),

        lang: locale
      }

      const response = await axios.post(`${API_CONFIG.URL}/v2/customer-register-v2/register_customer`, transformedData)

      if (response.data?.result) {
        // Store the registration data
        const registrationData = {
          id: response.data.data.id.toString(),
          customers: response.data.data.customers.map((customer: any) => ({
            id: customer.id,
            lang: customer.lang,
            loginable: customer.loginable
          }))
        }

        // Update form data with registration response
        setFormData(prev => ({
          ...prev,
          contract: {
            ...prev.contract,
            registrationData
          }
        }))

        toast.success('Registration successful! Please proceed to download your contract.')
        setIsRegistered(true)
        // Mark the registration step as completed when moving to contract step
        setCompletedSteps(prev => [...prev, 5])
        nextStep()
      } else {
        toast.error('Registration failed. Please try again.')
      }
    } catch (error: any) {
      console.error('Registration failed:', error)

      // Handle different types of error responses
      if (error.response?.data?.message) {
        // If message is an array, show each message
        if (Array.isArray(error.response.data.message)) {
          error.response.data.message.forEach((msg: string) => {
            toast.error(msg)
          })
        } else {
          // Show single message
          toast.error(error.response.data.message)
        }
      } else if (error.response?.data?.error) {
        // If error is an array, show each error
        if (Array.isArray(error.response.data.error)) {
          error.response.data.error.forEach((err: string) => {
            toast.error(err)
          })
        } else {
          toast.error(error.response.data.error)
        }
      } else if (error.response?.data?.errors) {
        // Handle validation errors from server
        const serverErrors = error.response.data.errors
        if (typeof serverErrors === 'object' && !Array.isArray(serverErrors)) {
          // If it's an object of field errors, show each error
          Object.entries(serverErrors).forEach(([field, message]) => {
            if (Array.isArray(message)) {
              // If the field has multiple errors
              message.forEach((msg: string) => {
                toast.error(`${field}: ${msg}`)
              })
            } else {
              toast.error(`${field}: ${message}`)
            }
          })
        } else if (Array.isArray(serverErrors)) {
          // If it's an array of errors, show each one
          serverErrors.forEach((error: string) => {
            toast.error(error)
          })
        } else {
          // If it's a single error message
          toast.error(serverErrors)
        }
      } else if (error.message) {
        // Show axios error message
        toast.error(error.message)
      } else {
        // Fallback error message
        toast.error('Registration failed. Please try again.')
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleContractUpload = async () => {
    try {
      setIsSubmitting(true)
      
      const companyId = formData.contract?.registrationData?.id
      const file = formData.contract?.uploadedFile
      
      if (!companyId || !file) {
        toast.error("Missing required information")
        return
      }

      const lang = formData.contract?.registrationData?.customers[0]?.lang || 'en' 
      const formDataToSend = new FormData()
      formDataToSend.append('file', file)
      formDataToSend.append('lang', lang)
   
      // Upload the signed contract
      await axios.post(
        `${API_CONFIG.URL}/v2/customer-register-v2/uploadContract/${companyId}`,
        formDataToSend,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      )
      
      // Only mark the contract step as completed after successful upload
      contractStepRef.current?.handleUploadSuccess()
      setHideFooter(true)
      setCompletedSteps(prev => [...prev, 6]) // Only mark contract step as completed
      toast.success("Contract uploaded successfully!")
      
    } catch (error: any) {
      if (axios.isAxiosError(error) && error.response?.data) {
        toast.error(`Upload failed: ${error.response.data.message || JSON.stringify(error.response.data)}`)
      } else {
        toast.error("Failed to upload contract")
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen  flex items-center justify-center p-4">
      <div className={`absolute md:top-4 md:right-10 top-4 ${isRTL ? 'right-auto left-4' : 'left-auto right-4'}`}>
        <Language />
      </div>
      <div className={`absolute md:top-4 ${isRTL ? 'md:right-10 right-4' : 'md:left-10 left-4'}`}>
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.push(`/${locale}/auth/signin`)}
          className="flex items-center gap-2"
        >
          {t('buttons.signIn')}
        </Button>
      </div>
      <Card className=" rounded-lg shadow-lg flex w-[1000px] h-[600px]">
        {!isRTL && (
          <Stepper
            steps={initialSteps}
            currentStep={currentStep}
            onStepClick={handleStepClick}
            completedSteps={completedSteps}
            isRegistered={isRegistered}
          />
        )}
        <div className="flex-1 flex flex-col">
          <div className="px-8 pb-8 mt-8  flex-1 overflow-y-auto">
            {currentStep === 0 && (
              <BasicInformation
                data={formData.basicInformation}
                onUpdate={(data) => updateFormData('basicInformation', data)}
                errors={errors}
              />
            )}
            {currentStep === 1 && (
              <AdditionalInformation
                data={formData.additionalInformation}
                onUpdate={(data) => updateFormData('additionalInformation', data)}
                errors={errors}
                onNext={nextStep}
                onPrev={prevStep}
              />
            )}
            {currentStep === 2 && (
              <ConsigneeInformation
                data={formData.consigneeInformation}
                onUpdate={(data) => updateFormData('consigneeInformation', data)}
                errors={errors}
              />
            )}
            {currentStep === 3 && (
              <NotifyParty
                data={formData.notifyParty}
                onUpdate={(data) => updateFormData('notifyParty', data)}
                errors={errors}
              />
            )}
            {currentStep === 4 && (
              <LoadVolume
                data={formData.loadVolume}
                onUpdate={(data) => updateFormData('loadVolume', data)}
                errors={errors}
              />
            )}
            {currentStep === 5 && (
              <Registration
                formData={formData}
                onPrev={prevStep}
                onSubmit={handleSubmit}
                isSubmitting={isSubmitting}
              />
            )}
            {currentStep === 6 && (
              <ContractStep
                ref={contractStepRef}
                formData={formData}
                updateFormData={(data:any) => updateFormData("contract", data.contract)}
                setHideFooter={setHideFooter}
              />
            )}
          </div>
          {!hideFooter && (
            <StepFooter
              currentStep={currentStep}
              totalSteps={initialSteps.length}
              onNext={currentStep === 6 ? handleContractUpload : currentStep === 5 ? handleSubmit : nextStep}
              onPrev={prevStep}
              isLastStep={currentStep === initialSteps.length - 1}
              isSubmitting={isSubmitting}
              isRegistered={isRegistered}
              disableNext={currentStep === 6 && !formData.contract.uploadedFile}
              nextButtonText={currentStep === 6 ? t('Contract.uploadContract') : undefined}
            />
          )}
        </div>
        {isRTL && (
          <Stepper
            steps={initialSteps}
            currentStep={currentStep}
            onStepClick={handleStepClick}
            completedSteps={completedSteps}
            isRegistered={isRegistered}
          />
        )}
      </Card>
    </div>
  )
} 