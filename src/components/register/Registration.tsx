import { FormData } from "@/types/form"
import { StepHeader } from "./StepHeader"
import { useTranslations } from 'next-intl'
import { useDirection } from '@/hooks/useDirection'

interface RegistrationProps {
  onPrev: () => void;
  onSubmit: () => Promise<void>;
  isSubmitting: boolean;
  formData: FormData;
}

interface SectionProps {
  title: string;
  children: React.ReactNode;
  isRTL?: boolean;
}

const Section = ({ title, children, isRTL }: SectionProps) => (
  <div className={` p-4 rounded-md border shadow-sm ${isRTL ? "text-right" : ""}`}>
    <h3 className="text-base font-semibold mb-3  pb-2 border-b">{title}</h3>
    {children}
  </div>
)

interface InfoFieldProps {
  label: string;
  value: string | number | boolean;
  fullWidth?: boolean;
  isRTL?: boolean;
}

const InfoField = ({ label, value, fullWidth = false, isRTL }: InfoFieldProps) => (
  <div className={`${fullWidth ? "col-span-2" : ""} p-2 rounded-md  ${isRTL ? "text-right" : ""}`}>
    <p className="text-xs font-medium  mb-0.5">{label}</p>
    <p className="text-sm ">
      {typeof value === 'boolean' 
        ? (value ? '✓ Yes' : '✗ No') 
        : value || 'Not provided'}
    </p>
  </div>
)

interface CarTypesSectionProps {
  title: string;
  data: {
    container: boolean;
    consolidation: boolean;
  };
  isRTL?: boolean;
}

const CarTypesSection = ({ title, data, isRTL }: CarTypesSectionProps) => (
  <div className={`mt-3 ${isRTL ? "text-right" : ""}`}>
    <p className="text-xs font-medium  mb-1.5">{title}</p>
    <div className="grid grid-cols-2 gap-2 ml-3">
      <InfoField label="Container" value={data.container} isRTL={isRTL} />
      <InfoField label="Consolidation" value={data.consolidation} isRTL={isRTL} />
    </div>
  </div>
)

export function Registration({ formData }: RegistrationProps) {
  const t = useTranslations('Register')
  const basicT = useTranslations('Register.BasicInformation')
  const additionalT = useTranslations('Register.AdditionalInformation')
  const consigneeT = useTranslations('Register.ConsigneeInformation')
  const notifyT = useTranslations('Register.NotifyParty')
  const loadT = useTranslations('Register.LoadVolume')
  const { isRTL } = useDirection()

  return (
    <div className="space-y-4 max-w-3xl mx-auto">
      <StepHeader 
        title={t('Registration.title')} 
      />
      
      <div className="space-y-4 pt-2 max-h-[calc(580px-200px)] overflow-y-auto">
        <Section title={basicT('title')} isRTL={isRTL}>
          <div className="grid grid-cols-2 gap-4">
            <InfoField label={basicT('fullName')} value={formData.basicInformation.fullName} isRTL={isRTL} />
            <InfoField label={basicT('companyName')} value={formData.basicInformation.companyName} isRTL={isRTL} />
            <InfoField label={basicT('email')} value={formData.basicInformation.email} isRTL={isRTL} />
            <InfoField label={basicT('phoneNumber')} value={formData.basicInformation.phone} isRTL={isRTL} />
            <InfoField label={basicT('address')} value={formData.basicInformation.address} fullWidth isRTL={isRTL} />
          </div>
        </Section>

        <Section title={additionalT('title')} isRTL={isRTL}>
          <div className="grid grid-cols-2 gap-4">
            <InfoField label={additionalT('secondaryEmail')} value={formData.additionalInformation.secondaryEmail} isRTL={isRTL} />
            <InfoField label={additionalT('secondaryPhone')} value={formData.additionalInformation.secondaryPhone} isRTL={isRTL} />
            <InfoField label={additionalT('joinDate')} value={formData.additionalInformation.joinDate} isRTL={isRTL} />
            <InfoField label={additionalT('usedCar')} value={formData.additionalInformation.usedCar} isRTL={isRTL} />
          </div>

          <CarTypesSection 
            title={additionalT('completeCars')} 
            data={formData.additionalInformation.completeCars} 
            isRTL={isRTL}
          />
          <CarTypesSection 
            title={additionalT('halfcutCars')} 
            data={formData.additionalInformation.halfcutCars} 
            isRTL={isRTL}
          />

          <div className={`mt-4 ${isRTL ? "text-right" : ""}`}>
            <p className="text-sm text-gray-500 font-medium mb-2">{additionalT('vehicleTypes')}</p>
            <div className="grid grid-cols-2 gap-2 ml-4">
              <InfoField label={additionalT('suv')} value={formData.additionalInformation.vehicleTypes.suv} isRTL={isRTL} />
              <InfoField label={additionalT('sedan')} value={formData.additionalInformation.vehicleTypes.sedan} isRTL={isRTL} />
            </div>
          </div>
        </Section>

        <Section title={consigneeT('title')} isRTL={isRTL}>
          <div className="grid grid-cols-2 gap-4">
            <InfoField label={consigneeT('fullName')} value={formData.consigneeInformation.consigneeFullName} isRTL={isRTL} />
            <InfoField label={consigneeT('companyName')} value={formData.consigneeInformation.consigneeCompanyName} isRTL={isRTL} />
            <InfoField label={consigneeT('email')} value={formData.consigneeInformation.consigneeEmail} isRTL={isRTL} />
            <InfoField label={consigneeT('phoneNumber')} value={formData.consigneeInformation.consigneePhone} isRTL={isRTL} />
            <InfoField label={consigneeT('address')} value={formData.consigneeInformation.consigneeAddress} fullWidth isRTL={isRTL} />
          </div>
        </Section>

        <Section title={notifyT('title')} isRTL={isRTL}>
          <div className="grid grid-cols-2 gap-4">
            <InfoField label={notifyT('fullName')} value={formData.notifyParty.notifyFullName} isRTL={isRTL} />
            <InfoField label={notifyT('companyName')} value={formData.notifyParty.notifyCompanyName} isRTL={isRTL} />
            <InfoField label={notifyT('email')} value={formData.notifyParty.notifyEmail} isRTL={isRTL} />
            <InfoField label={notifyT('phoneNumber')} value={formData.notifyParty.notifyPhone} isRTL={isRTL} />
            <InfoField label={notifyT('address')} value={formData.notifyParty.notifyAddress} fullWidth isRTL={isRTL} />
          </div>
        </Section>

        <Section title={loadT('title')} isRTL={isRTL}>
          <div className="grid grid-cols-2 gap-4">
            <InfoField label={loadT('numberOfVehicles')} value={formData.loadVolume.numberOfVehicles} isRTL={isRTL} />
            <InfoField label={loadT('numberOfContainers')} value={formData.loadVolume.numberOfContainers} isRTL={isRTL} />
          </div>
        </Section>
      </div>
    </div>
  )
}

