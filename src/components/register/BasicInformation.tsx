import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useEffect, useState } from "react";
import { BasicInformationData } from "@/types/form";
import { StepHeader } from "./StepHeader";
import { useTranslations } from "next-intl";
import { useDirection } from "@/hooks/useDirection";
import { useParams } from "next/navigation";
import { useFetchClient } from "@/utils/axios";

interface Destination {
  id: string;
  name: string;
}

interface BasicInformationProps {
  data: BasicInformationData;
  onUpdate: (data: BasicInformationData) => void;
  errors?: { [key: string]: string };
}

interface InputFieldProps {
  id: string;
  label: string;
  value: string;
  type?: string;
  required?: boolean;
  onChange: (value: string) => void;
  error?: string;
  onBlur?: () => void;
  isRTL?: boolean;
}

const InputField = ({
  id,
  label,
  value,
  type = "text",
  required = true,
  onChange,
  error,
  onBlur,
  isRTL,
}: InputFieldProps) => (
  <div className="space-y-2">
    <Label htmlFor={id} className={isRTL ? "text-right w-full block" : ""}>
      {label}
      {required && <span className="text-red-500 ml-1">*</span>}
    </Label>
    <Input
      id={id}
      type={type}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      onBlur={onBlur}
      required={required}
      className={`${error ? "border-red-500" : ""} ${
        isRTL ? "text-right" : ""
      }`}
      dir={isRTL ? "rtl" : "ltr"}
    />
    {error && (
      <p className={`text-sm text-red-500 ${isRTL ? "text-right" : ""}`}>
        {error}
      </p>
    )}
  </div>
);

export function BasicInformation({
  data,
  onUpdate,
  errors = {},
}: BasicInformationProps) {
  const params = useParams();
  let locale = (params?.locale as string) || "en";
  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [formData, setFormData] = useState<BasicInformationData>(data);
  const [localErrors, setLocalErrors] = useState<{ [key: string]: string }>(
    errors
  );
  const t = useTranslations("Register.BasicInformation");
  const { isRTL } = useDirection();

  useEffect(() => {
    const fetchDestinations = async () => {
      switch (locale) {
        case "ar":
          locale = "ara";
          break;
        case "ka":
          locale = "geo";
          break;
        case "ru":
          locale = "rus";
          break;
      }
      const res = await fetchClient(
        `/v2/customer-register-v2/destinations/${locale}`
      );
      setDestinations(res.data.data || []); // Access data property from response and fallback to empty array
    };
    fetchDestinations();
  }, []);

  const handleChange = (field: keyof BasicInformationData, value: string) => {
    const updatedData = { ...formData, [field]: value };
    setFormData(updatedData);

    // Clear error when user starts typing
    if (localErrors[field]) {
      const updatedErrors = { ...localErrors };
      delete updatedErrors[field];
      setLocalErrors(updatedErrors);
    }

    onUpdate(updatedData);
  };

  const fetchClient = useFetchClient();

  const checkEmailExists = async () => {
    if (!formData.email) return;

    try {
      const response = await fetchClient(
        `/v2/customer-register-v2/checkCustomerEmail`,
        {
          params: { email: formData.email },
        }
      );

      if (response.data.email_exist) {
        setLocalErrors((prev) => ({
          ...prev,
          email: "This email is already registered",
        }));
        onUpdate({ ...formData, email: formData.email }); // Trigger parent update to show error
      }
    } catch (error) {
      throw error;
    }
  };

  // Combine local and prop errors
  const combinedErrors = { ...errors, ...localErrors };

  return (
    <div className="space-y-6">
      <StepHeader title={t("title")} />

      <div className="space-y-4 pt-2">
        <div className={`grid grid-cols-2 gap-4 ${isRTL ? "dir-rtl" : ""}`}>
          <InputField
            id="fullName"
            label={t("fullName")}
            value={formData.fullName}
            onChange={(value) => handleChange("fullName", value)}
            error={combinedErrors.fullName}
            isRTL={isRTL}
          />
          <InputField
            id="companyName"
            label={t("companyName")}
            value={formData.companyName}
            onChange={(value) => handleChange("companyName", value)}
            error={combinedErrors.companyName}
            required={false}
            isRTL={isRTL}
          />
          <InputField
            id="email"
            label={t("email")}
            type="email"
            value={formData.email}
            onChange={(value) => handleChange("email", value)}
            onBlur={checkEmailExists}
            error={combinedErrors.email}
            isRTL={isRTL}
          />
          <InputField
            id="phone"
            label={t("phoneNumber")}
            type="tel"
            value={formData.phone}
            onChange={(value) => handleChange("phone", value)}
            error={combinedErrors.phone}
            isRTL={isRTL}
          />
          <div className="space-y-2 col-span-2">
            <Label
              htmlFor="destination"
              className={isRTL ? "text-right w-full block" : ""}
            >
              {t("destination")}
            </Label>
            <Select
              value={formData.destination}
              onValueChange={(value) => handleChange("destination", value)}
              dir={isRTL ? "rtl" : "ltr"}
            >
              <SelectTrigger
                id="destination"
                className={`${
                  combinedErrors.destination ? "border-destructive" : ""
                } ${isRTL ? "text-right" : ""}`}
              >
                <SelectValue placeholder={t("selectDestination")} />
              </SelectTrigger>
              <SelectContent>
                {destinations.map((destination) => (
                  <SelectItem
                    key={destination.id}
                    value={destination.id}
                    className={isRTL ? "text-right" : ""}
                  >
                    {destination.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {combinedErrors.destination && (
              <p
                className={`text-sm text-red-500 ${isRTL ? "text-right" : ""}`}
              >
                {combinedErrors.destination}
              </p>
            )}
          </div>
        </div>

        <div className="col-span-2 space-y-2">
          <Label
            htmlFor="address"
            className={isRTL ? "text-right w-full block" : ""}
          >
            {t("address")}
          </Label>
          <Textarea
            id="address"
            value={formData.address}
            onChange={(e) => handleChange("address", e.target.value)}
            required={false}
            className={`${combinedErrors.address ? "border-destructive" : ""} ${
              isRTL ? "text-right" : ""
            }`}
            dir={isRTL ? "rtl" : "ltr"}
          />
          {combinedErrors.address && (
            <p className={`text-sm text-red-500 ${isRTL ? "text-right" : ""}`}>
              {combinedErrors.address}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
