import { Check, type LucideIcon } from "lucide-react"
import * as LucideIcons from "lucide-react"
import { useDirection } from '@/hooks/useDirection'

interface Step {
  title: string
  icon: string
}

interface StepperProps {
  steps: Step[]
  currentStep: number
  completedSteps: number[]
  onStepClick: (stepIndex: number) => void
  isRegistered?: boolean
}

export function Stepper({ steps, currentStep, completedSteps, onStepClick, isRegistered }: StepperProps) {
  const { isRTL } = useDirection()

  return (
    <div className={` p-6 pt-24 ${isRTL ? "rounded-r-lg" : "rounded-l-lg"} w-64`}>
      <ol className="relative space-y-6">
        {steps.map((step, index) => {
          const Icon = LucideIcons[step.icon as keyof typeof LucideIcons] as LucideIcon
          const isLastStep = index === steps.length - 1
          const isCompleted = completedSteps.includes(index)
          const isActive = index === currentStep
          const nextAvailableStep = Math.min(Math.max(...completedSteps, -1) + 1, steps.length - 1)
          const registrationStepIndex = 5 // Registration is step 5 (0-based index)
          const isClickable = (!isRegistered || index >= currentStep) && 
            (isCompleted || index === nextAvailableStep) && 
            !(currentStep > registrationStepIndex && index < currentStep) // Prevent backward navigation after registration

          return (
            <li 
              key={step.title} 
              className={`flex items-center ${isRTL ? "flex-row-reverse" : ""} group ${
                isClickable ? 'cursor-pointer' : 'cursor-not-allowed'
              }`}
              onClick={() => {
                if (isClickable) {
                  onStepClick(index)
                }
              }}
            >
              <div className="relative">
                {/* Hover effect for completed steps */}
                {isCompleted && (
                  <div className="absolute inset-0 rounded-full bg-secondary scale-150 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                )}
                <div
                  className={`relative w-9 h-9 flex items-center justify-center rounded-full 
                    transition-all duration-300 border-[2.5px]
                    ${
                      isCompleted
                        ? "bg-green-500 border-green-500 text-white group-hover:bg-green-600 group-hover:border-green-600"
                        : isActive
                          ? "bg-transparent border-green-500 text-white"
                          : "bg-gray-300 border-gray-300 text-gray-500"
                    }
                    ${isClickable && !isCompleted ? "group-hover:border-secondary group-hover:text-green-600" : ""}
                  `}
                >
                  {isCompleted ? (
                    <Check className="w-[18px] h-[18px] transition-transform group-hover:scale-110" />
                  ) : (
                    <Icon className={`w-[18px] h-[18px] transition-transform group-hover:scale-110 ${isActive ? "text-green-500" : ""}`} />
                  )}
                </div>
                {!isLastStep && (
                  <div
                    className={`absolute ${isRTL ? "right-[1.125rem]" : "left-[1.125rem]"} top-9 w-[2px] h-[1.5rem] ${isRTL ? "translate-x-1/2" : "-translate-x-1/2"}
                      transition-all duration-300 
                      ${index < currentStep || isCompleted ? "bg-green-500" : "bg-gray-300"}
                      ${isCompleted ? "group-hover:scale-y-105" : ""}
                    `}
                  />
                )}
              </div>
              <span
                className={`text-sm font-medium transition-all duration-300 
                  ${isRTL ? "ml-auto mr-3" : "ml-3"}
                  ${
                    isCompleted || isActive
                      ? "text-green-500 group-hover:text-green-600" 
                      : "text-gray-500"
                  }
                  ${isRTL ? "group-hover:-translate-x-1" : "group-hover:translate-x-1"}
                `}
              >
                {step.title}
              </span>
            </li>
          )
        })}
      </ol>
    </div>
  )
}

