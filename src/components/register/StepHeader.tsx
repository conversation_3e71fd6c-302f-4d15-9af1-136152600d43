import React from 'react'
import { useDirection } from '@/hooks/useDirection'

interface StepHeaderProps {
  title: string;
}

export function StepHeader({ title }: StepHeaderProps) {
  const { isRTL } = useDirection()

  return (
    <div className="sticky top-0 pb-4 z-50 border-b ">
      <h2 className={`text-2xl font-semibold tracking-tight ${isRTL ? 'text-right' : 'text-left'}`}>
        {title}
      </h2>
    </div>
  )
} 