import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useState } from "react"
import { AdditionalInformationData, CarTypes } from "@/types/form"
import { DatePicker } from "../ui/datapicker"
import { StepHeader } from "./StepHeader"
import { useTranslations } from 'next-intl'
import { useDirection } from '@/hooks/useDirection'

interface AdditionalInformationProps {
  onNext: () => void;
  onPrev: () => void;
  data: AdditionalInformationData;
  onUpdate: (data: AdditionalInformationData) => void;
  errors?: { [key: string]: string };
}

interface CarSectionProps {
  title: string;
  containerLabel: string;
  consolidationLabel: string;
  data: CarTypes;
  onChange: (field: keyof CarTypes, value: boolean) => void;
  isRTL?: boolean;
}

interface InputFieldProps {
  id: string;
  label: string;
  value: string;
  type?: string;
  required?: boolean;
  onChange: (value: string) => void;
  error?: string;
  isRTL?: boolean;
}

const InputField = ({ id, label, value, type = "text", required = true, onChange, error, isRTL }: InputFieldProps) => (
  <div className="space-y-2">
    <Label htmlFor={id} className={isRTL ? 'text-right w-full block' : ''}>{label}</Label>
    <Input 
      id={id}
      type={type}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      required={required}
      className={`${error ? "border-red-500" : ""} ${isRTL ? "text-right" : ""}`}
      dir={isRTL ? "rtl" : "ltr"}
    />
    {error && <p className={`text-sm text-red-500 ${isRTL ? "text-right" : ""}`}>{error}</p>}
  </div>
)

const CarSection = ({ title, containerLabel, consolidationLabel, data, onChange, isRTL }: CarSectionProps) => (
  <div className={`space-y-3 ${isRTL ? "text-right" : ""}`}>
    <Label className={`text-sm font-medium ${isRTL ? "text-right w-full block" : ""}`}>{title}</Label>
    <div className={`flex gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className={`flex items-center rounded-lg border border-gray-200 px-4 py-2 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}>
        <Checkbox 
          id={`${title.toLowerCase()}-container`}
          checked={data.container}
          onCheckedChange={(checked) => onChange('container', checked as boolean)}
          className={isRTL ? "ml-2" : "mr-2"}
        />
        <Label 
          htmlFor={`${title.toLowerCase()}-container`}
          className={`text-sm cursor-pointer select-none ${isRTL ? "text-right" : ""}`}
        >
          {containerLabel}
        </Label>
      </div>
      <div className={`flex items-center rounded-lg border border-gray-200  px-4 py-2 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}>
        <Checkbox 
          id={`${title.toLowerCase()}-consolidation`}
          checked={data.consolidation}
          onCheckedChange={(checked) => onChange('consolidation', checked as boolean)}
          className={isRTL ? "ml-2" : "mr-2"}
        />
        <Label 
          htmlFor={`${title.toLowerCase()}-consolidation`}
          className={`text-sm cursor-pointer select-none ${isRTL ? "text-right" : ""}`}
        >
          {consolidationLabel}
        </Label>
      </div>
    </div>
  </div>
)

export function AdditionalInformation({ onNext,  data, onUpdate, errors = {} }: AdditionalInformationProps) {
  const [formData, setFormData] = useState<AdditionalInformationData>(data)
  const t = useTranslations('Register.AdditionalInformation')
  const { isRTL } = useDirection()

  const handleChange = (field: keyof AdditionalInformationData, value: any) => {
    const updatedData = { ...formData, [field]: value }
    setFormData(updatedData)
    onUpdate(updatedData)
  }

  const handleNestedChange = (
    category: 'completeCars' | 'halfcutCars' | 'vehicleTypes',
    field: string,
    value: boolean
  ) => {
    const updatedData = {
      ...formData,
      [category]: {
        ...formData[category],
        [field]: value,
      },
    }
    setFormData(updatedData)
    onUpdate(updatedData)
  }

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        onNext()
      }}
      className={`space-y-6 ${isRTL ? "text-right" : ""}`}
    >
      <StepHeader 
        title={t('title')}
        
      />
      

      <div className="space-y-3 pt-2">
        <div className={`grid grid-cols-2 gap-4 ${isRTL ? "dir-rtl" : ""}`}>
          <InputField
            id="secondaryEmail"
            label={t('secondaryEmail')}
            type="email"
            value={formData.secondaryEmail}
            required={false}
            onChange={(value) => handleChange('secondaryEmail', value)}
            error={errors.secondaryEmail}
            isRTL={isRTL}
          />
          <InputField
            id="secondaryPhone"
            label={t('secondaryPhone')}
            type="tel"
            value={formData.secondaryPhone}
            required={false}
            onChange={(value) => handleChange('secondaryPhone', value)}
            error={errors.secondaryPhone}
            isRTL={isRTL}
          />
        </div>

        <div className={`space-y-2 ${isRTL ? "text-right" : ""}`}>
          <Label htmlFor="joinDate" className={`block w-full ${isRTL ? "text-right" : ""}`}>{t('joinDate')}</Label>
          <div className="w-full">
            <DatePicker
              date={formData.joinDate ? new Date(formData.joinDate) : undefined}
              onSelect={(date) => handleChange('joinDate', date ? date.toISOString() : '')}
            />
          </div>
          {errors.joinDate && <p className={`text-sm text-red-500 mt-1 ${isRTL ? "text-right" : ""}`}>{errors.joinDate}</p>}
        </div>

        <div className={`flex ${isRTL ? "justify-end" : "justify-start"}`}>
          <div className={`inline-flex items-center rounded-lg border border-gray-200  px-4 py-2 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}>
            <Checkbox
              id="usedCar"
              checked={formData.usedCar}
              onCheckedChange={(checked) => handleChange('usedCar', checked)}
              className={isRTL ? "ml-2" : "mr-2"}
            />
            <Label 
              htmlFor="usedCar"
              className={`text-sm cursor-pointer select-none ${isRTL ? "text-right" : ""}`}
            >
              {t('usedCar')}
            </Label>
          </div>
        </div>

        <div className={`flex gap-12 ${isRTL ? "flex-row-reverse" : ""}`}>
          <CarSection 
            title={t('completeCars')}
            containerLabel={t('container')}
            consolidationLabel={t('consolidation')}
            data={formData.completeCars}
            onChange={(field, value) => handleNestedChange('completeCars', field, value)}
            isRTL={isRTL}
          />

          <CarSection 
            title={t('halfcutCars')}
            containerLabel={t('container')}
            consolidationLabel={t('consolidation')}
            data={formData.halfcutCars}
            onChange={(field, value) => handleNestedChange('halfcutCars', field, value)}
            isRTL={isRTL}
          />
        </div>

        <div className={`space-y-3 ${isRTL ? "text-right" : ""}`}>
          <Label className={`text-sm font-medium ${isRTL ? "text-right w-full block" : ""}`}>{t('vehicleTypes')}</Label>
          <div className={`flex gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`flex items-center rounded-lg border border-gray-200 px-4 py-2 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}>
              <Checkbox
                id="suv"
                checked={formData.vehicleTypes.suv}
                onCheckedChange={(checked) => handleNestedChange('vehicleTypes', 'suv', checked as boolean)}
                className={isRTL ? "ml-2" : "mr-2"}
              />
              <Label 
                htmlFor="suv"
                className={`text-sm cursor-pointer select-none ${isRTL ? "text-right" : ""}`}
              >
                {t('suv')}
              </Label>
            </div>
            <div className={`flex items-center rounded-lg border border-gray-200  px-4 py-2 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors ${isRTL ? "flex-row-reverse" : ""}`}>
              <Checkbox
                id="sedan"
                checked={formData.vehicleTypes.sedan}
                onCheckedChange={(checked) => handleNestedChange('vehicleTypes', 'sedan', checked as boolean)}
                className={isRTL ? "ml-2" : "mr-2"}
              />
              <Label 
                htmlFor="sedan"
                className={`text-sm cursor-pointer select-none ${isRTL ? "text-right" : ""}`}
              >
                {t('sedan')}
              </Label>
            </div>
          </div>
        </div>
      </div>
    </form>
  )
}

