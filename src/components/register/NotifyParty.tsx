import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { NotifyPartyData } from "@/types/form"
import { StepHeader } from "./StepHeader"
import { useTranslations } from 'next-intl'
import { useDirection } from '@/hooks/useDirection'

interface NotifyPartyProps {
  data: NotifyPartyData;
  onUpdate: (data: NotifyPartyData) => void;
  errors?: { [key: string]: string };
}

interface InputFieldProps {
  id: string;
  label: string;
  value: string;
  type?: string;
  required?: boolean;
  onChange: (value: string) => void;
  error?: string;
  isRTL?: boolean;
}

const InputField = ({ id, label, value, type = "text", required = true, onChange, error, isRTL }: InputFieldProps) => (
  <div className="space-y-2">
    <Label htmlFor={id} className={isRTL ? 'text-right w-full block' : ''}>
      {label}
      {required && <span className="text-red-500 ml-1">*</span>}
    </Label>
    <Input 
      id={id}
      type={type}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      required={required}
      className={`${error ? "border-red-500" : ""} ${isRTL ? "text-right" : ""}`}
      dir={isRTL ? "rtl" : "ltr"}
    />
    {error && <p className={`text-sm text-red-500 ${isRTL ? "text-right" : ""}`}>{error}</p>}
  </div>
)

export function NotifyParty({ data, onUpdate, errors = {} }: NotifyPartyProps) {
  const handleChange = (field: keyof NotifyPartyData, value: string) => {
    onUpdate({ ...data, [field]: value })
  }
  const t = useTranslations('Register.NotifyParty')
  const { isRTL } = useDirection()

  return (
    <div className="space-y-6">
      <StepHeader 
        title={t('title')} 
      />

      <div className="grid grid-cols-2 gap-4 pt-2">
        <InputField
          id="notifyFullName"
          label={t('fullName')}
          value={data.notifyFullName}
          onChange={(value) => handleChange('notifyFullName', value)}
          error={errors.notifyFullName}
          required={false}
          isRTL={isRTL}
        />
        <InputField
          id="notifyCompanyName"
          label={t('companyName')}
          value={data.notifyCompanyName}
          onChange={(value) => handleChange('notifyCompanyName', value)}
          error={errors.notifyCompanyName}
          required={false}
          isRTL={isRTL}
        />
        <InputField
          id="notifyEmail"
          label={t('email')}
          type="email"
          value={data.notifyEmail}
          onChange={(value) => handleChange('notifyEmail', value)}
          error={errors.notifyEmail}
          required={false}
          isRTL={isRTL}
        />
        <InputField
          id="notifyPhone"
          label={t('phoneNumber')}
          type="tel"
          value={data.notifyPhone}
          onChange={(value) => handleChange('notifyPhone', value)}
          error={errors.notifyPhone}
          required={false}
          isRTL={isRTL}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="notifyAddress" className={isRTL ? 'text-right w-full block' : ''}>{t('address')}</Label>
        <Textarea 
          id="notifyAddress"
          value={data.notifyAddress}
          onChange={(e) => handleChange('notifyAddress', e.target.value)}
          className={`${errors.notifyAddress ? "border-red-500" : ""} ${isRTL ? "text-right" : ""}`}
          dir={isRTL ? "rtl" : "ltr"}
          required={false}
        />
        {errors.notifyAddress && <p className={`text-sm text-red-500 ${isRTL ? "text-right" : ""}`}>{errors.notifyAddress}</p>}
      </div>
    </div>
  )
}

