import { Separator } from "@/components/ui/separator";
import { formatOnlyDate } from "@/utils/helper-function";
import type { CustomCellRendererProps } from "ag-grid-react";
import { useTranslations } from "next-intl";
import { type FunctionComponent } from "react";

export const CenterCell: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("invoice-datatable.body");
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap ">
        <div className="min-w-12">{t("issue_date")}:</div> {data?.invoice_date && formatOnlyDate(new Date(data?.invoice_date))}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap ">
        <div className="min-w-12">{t("due_date")}:</div> {data?.invoice_due_date && formatOnlyDate(new Date(data?.invoice_due_date))}
      </div>
    </div>
  );
};
