"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";

import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import { RefObject, useMemo, useRef, useState } from "react";

import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";

import { BalanceRenderer } from "./BalanceRenderer";
import { CenterCell } from "./CenterCell";
import { StatusRenderer } from "./StatusRenderer";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import { InvoicesRenderer } from "./InvoicesRenderer";
import { PurposeRenderer } from "./PurposeRenderer";
import { InvoiceAmountRenderer } from "./InvoiceAmountRenderer";
import useSidebarConfig from "../sidebarConfig";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);
export type InvoiceType = {
  id: number;
  invoice_number: string;
  purpose: string;
  container_number: string;
  status: string;
  invoice_date: string;
  invoice_due_date: string;
  created_at: string;
  created_date: string;
  payment_date: string;
  payment_received: number;
  type: string;
  invoice_amount: number;
};

interface Props {
  gridTheme?: string;
  isDarkMode?: boolean;
  records: {
    page: number;
    per_page: number;
    total: number;
    data: InvoiceType[];
  } | null;
  gridRefProps?: RefObject<AgGridReact | null>;
  exportColDefs?: ColDef[];
}

export const InovicesDataTable = ({
  records,
  gridRefProps,
  exportColDefs,
}: Props) => {
  const gridRef = useRef<AgGridReact>(null);
  const t = useTranslations("invoice-datatable");
  const sidebarConfig = useSidebarConfig();
  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: "#",
        cellDataType: "text",
        minWidth: 50,
        maxWidth: 50,
        cellStyle: {
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        },
        valueGetter: (params) => {
          if (params.node) {
            return params.node.rowIndex ? params.node.rowIndex + 1 : 1;
          }
        },
      },
      {
        field: "invoice",
        headerName: t("header.invoice"),
        cellDataType: "text",
        cellRenderer: InvoicesRenderer,
        minWidth: 220,
      },
      {
        field: "status",
        headerName: t("header.status"),
        cellRenderer: StatusRenderer,
        minWidth: 110,
      },
      {
        field: "invoice_amount",
        headerName: t("header.invoice_amount"),
        minWidth: 170,
        cellRenderer: InvoiceAmountRenderer,
      },
      {
        field: "balance",
        headerName: t("header.balance"),
        minWidth: 250,
        cellRenderer: BalanceRenderer,
      },
      {
        field: "date",
        headerName: t("header.dates"),
        minWidth: 170,
        cellRenderer: CenterCell,
      },


      {
        field: "purpose",
        headerName: t("header.purpose"),
        cellRenderer: PurposeRenderer,
        minWidth: 295,
      },
    ],
    [t]
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  const [quickFilterText] = useState<string>();



  const selectionColumnDef = useMemo(() => {
    return {
      minWidth: 44,
    };
  }, []);
  const { isRTL } = useDirection();

  return (
    <AgGridDataTable
      enableRtl={isRTL ? true : false}
      ref={gridRefProps || gridRef}
      selectionColumnDef={selectionColumnDef}
      columnDefs={exportColDefs || colDefs}
      rowData={records?.data || []}
      autoSizeStrategy={autoSizeStrategy}
      masterDetail
      detailCellRendererParams={{ t }}
      quickFilterText={quickFilterText}
      rowHeight={72}
      colResizeDefault="shift"
      headerHeight={60}
      sideBar={sidebarConfig}
      rowClassRules={{
        "row-even": (params) => {
          return params.node.rowIndex
            ? params.node.rowIndex % 2 === 0
              ? false
              : true
            : true;
        },
        "row-odd": (params) => {
          return params.node.rowIndex
            ? params.node.rowIndex % 2 !== 0
              ? false
              : true
            : true;
        },
      }}
    />
  );
};
