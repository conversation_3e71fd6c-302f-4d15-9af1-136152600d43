import { Separator } from "@/components/ui/separator";
import type { CustomCellRendererProps } from "ag-grid-react";

import { type FunctionComponent } from "react";
import { useTranslations } from "next-intl";
import { differenceInDays } from "date-fns";
import { formatOnlyDate } from "@/utils/helper-function";

export const BalanceRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("invoice-datatable.body");
  
  if (data?.status === "paid") {
    return (
      <div className="flex flex-col justify-center h-full select-text leading-5">
        <div className="flex w-full items-center overflow-hidden text-ellipsis whitespace-nowrap">
          <div className="min-w-[94px] pr-2">{t("received_date")}:</div>
          <div className="flex flex-col">
            {data?.payment_date ? (
              data.payment_date.includes("|") ? (
                data.payment_date
                  .split("|")
                  .map((date: string, index: number) => (
                    <span key={index} className="px-2">
                      {formatOnlyDate(date.trim())}
                    </span>
                  ))
              ) : (
                <span className="px-2">{formatOnlyDate(data.payment_date)}</span>
              )
            ) : null}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full items-center overflow-hidden text-ellipsis whitespace-nowrap">
        <div className="min-w-[94px] pr-2">{t("received_date")}:</div>
        <div className="flex flex-col">
          {data?.payment_date ? (
            data.payment_date.includes("|") ? (
              data.payment_date
                .split("|")
                .map((date: string, index: number) => (
                  <span key={index} className="px-2">
                    {formatOnlyDate(date.trim())}
                  </span>
                ))
            ) : (
              <span className="px-2">{formatOnlyDate(data.payment_date)}</span>
            )
          ) : null}
        </div>
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap">
        <div className="min-w-[94px] pr-2">{t("past_due_days")}:</div>
        {data?.status !== "paid" && (
          <span>
            {data?.invoice_due_date &&
              data?.invoice_date &&
              differenceInDays(
                new Date(Date.now()),
                new Date(data?.invoice_due_date)
              )}
          </span>
        )}
      </div>
    </div>
  );
};
