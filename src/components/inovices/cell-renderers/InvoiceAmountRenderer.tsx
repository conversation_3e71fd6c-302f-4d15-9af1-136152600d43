import { Separator } from "@/components/ui/separator";
import type { CustomCellRendererProps } from "ag-grid-react";
import { type FunctionComponent } from "react";

export const InvoiceAmountRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data }) => {
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 0,
  });
  const unpaid = data.invoice_amount - data.payment_received;

  return (
    <div className="flex flex-col justify-center items-center w-full h-full leading-5">
      <div className=" rounded-md">{formatter.format(data.invoice_amount)}</div>

      <Separator className="my-1" />
      <div className="rounded-md  flex leading-[22px] text-xs font-semibold overflow-hidden">
        <div className="px-2 bg-blue-600/10 text-blue-500 dark:text-blue-300">
          {formatter.format(data.payment_received)}
        </div>
        <div
          className={`
            px-2 ${
              unpaid > 0
                ? "bg-red-600/10 text-red-400"
                : "bg-green-600/10 text-green-500 dark:text-green-300"
            }`}
        >
          {formatter.format(unpaid)}
        </div>
      </div>
    </div>
  );
};
