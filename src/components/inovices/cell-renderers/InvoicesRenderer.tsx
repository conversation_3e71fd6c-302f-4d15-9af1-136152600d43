import type { CustomCellRendererProps } from "ag-grid-react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>h, FileText } from "lucide-react";
import { type FunctionComponent, useState } from "react";
import { useDownloadInvoice } from "../services/use-download-invoice";
import { LoadingSpinner } from "@/components/Common_UI/loading";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useResponsive } from "@/hooks/use-mobile";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { colorSystem, ColorSystemKey } from "@/lib/constant";
import { removeUnderScore } from "@/utils/commons";
import { formatDate } from "date-fns";

export const InvoicesRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const { downloadInvoice, isPending } = useDownloadInvoice();
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const { isMobile } = useResponsive();
  const colors = colorSystem[data?.carstate as ColorSystemKey] || {
    bg: "bg-green-500/10",
    txt: "text-green-500",
  };
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 2,
  });
  const unpaid = data.invoice_amount - data.payment_received;

  const handleClick = () => {
    if (isMobile) {
      setIsSheetOpen(true);
    }
  };

  const safeFormatDate = (
    dateString: string | null | undefined,
    formatStr = "yyyy-MMM-dd"
  ) => {
    if (!dateString) return "N/A";

    try {
      const date = new Date(dateString);
      return isNaN(date.getTime())
        ? "Invalid Date"
        : formatDate(date, formatStr);
    } catch {
      return "Invalid Date";
    }
  };

  const detailRows = [
    {
      label: "Status",
      value: (
        <Badge
          className={`${colors.bg} ${colors.txt} border-none text-[10px]`}
          variant="outline"
        >
          {data?.status && removeUnderScore(data?.status)?.toUpperCase()}
        </Badge>
      ),
    },
    {
      label: "Invoice Number",
      value: (
        <span className="text-xs font-medium">{data?.invoice_number}</span>
      ),
    },
    {
      label: "Invoice Amount",
      value: (
        <span className="text-xs">
          {formatter.format(Number(data?.invoice_amount || 0))}
        </span>
      ),
    },
    {
      label: "Payment Received",
      value: (
        <span className="text-xs">
          {formatter.format(Number(data?.payment_received || 0))}
        </span>
      ),
    },
    {
      label: "Due Balance",
      value: (
        <Badge
          className={`
            px-2 ${
              unpaid > 0
                ? "bg-red-600/10 text-red-400"
                : "bg-green-600/10 text-green-500 dark:text-green-300"
            }`}
          variant="outline"
        >
          {formatter.format(unpaid)}
        </Badge>
      ),
    },
    {
      label: "Container Number",
      value: <span className="text-xs">{data?.container_number}</span>,
    },
    {
      label: "Received Date",
      value: (
        <span className="text-xs">
          {data?.invoice_date &&
            safeFormatDate(data.payment_date, "yyyy-MMM-dd")}
        </span>
      ),
    },
    {
      label: "Issue Date",
      value: (
        <span className="text-xs">
          {data?.invoice_date &&
            safeFormatDate(data.invoice_date, "yyyy-MMM-dd")}
        </span>
      ),
    },
    {
      label: "Due Date",
      value: (
        <span className="text-xs">
          {data?.invoice_due_date &&
            safeFormatDate(data.invoice_due_date, "yyyy-MMM-dd")}
        </span>
      ),
    },
    {
      label: "Purpose",
      value: <span className="text-xs">{data?.purpose || "N/A"}</span>,
    },
    {
      label: "Download",
      value: <span className="text-xs">{data?.purpose || "N/A"}</span>,
    },
  ];

  return (
    <>
      <div
        className="flex items-center h-full gap-2 hover:cursor-pointer md:hover:cursor-default lg:hover:cursor-default group"
        onClick={handleClick}
      >
        <div className="pt-1">
          <Button
            size={"icon"}
            variant={"outline"}
            onClick={(e) => {
              e.stopPropagation();
              downloadInvoice(data);
            }}
            className="bg-green-500/10 dark:bg-green-500/10 text-green-500 dark:text-green-500"
          >
            {isPending ? (
              <LoadingSpinner className="h-5 w-5 p-0" />
            ) : (
              <PrinterCheck />
            )}
          </Button>
        </div>
        <div className="flex flex-col justify-center h-full select-text leading-5 flex-1">
          <div className="flex items-center">
            <div className="w-full overflow-hidden text-ellipsis whitespace-nowrap font-normal px-2">
              {data.invoice_number}
            </div>
          </div>
          <div className="w-full overflow-hidden text-ellipsis whitespace-nowrap font-normal px-2 text-primary/70">
            {data.container_number}
          </div>
        </div>
      </div>

      <Drawer open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <DrawerContent className="h-[80vh] rounded-t-3xl border-t-2 border-primary px-4">
          <DrawerHeader className="sr-only">
            <DrawerTitle>Invoice Details</DrawerTitle>
            <DrawerDescription>
              Detailed information about invoice {data?.invoice_number}
            </DrawerDescription>
          </DrawerHeader>
          <div className="flex flex-col h-full">
            <div className="px-1 py-2">
              <div className="flex items-center gap-2">
                <span className="font-semibold text-xs">
                  Invoice: {data?.invoice_number}
                </span>
              </div>
              <div className="flex items-center gap-2 justify-between py-2">
                <span className="flex items-center text-xs text-primary">
                  <Hash className="w-4 h-4" />
                  {data?.container_number}
                </span>
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4 text-primary" />
                  <span className="text-xs">
                    {formatter.format(Number(data?.total_amount || 0))}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex-1 overflow-y-auto">
              <div className="rounded-lg overflow-hidden border">
                <Table>
                  <TableBody>
                    {detailRows.map((row, index) => (
                      <TableRow
                        key={row.label}
                        className={`${
                          index % 2 === 0 ? "bg-primary/5" : "bg-primary/10"
                        }`}
                      >
                        <TableCell className="py-1 px-2 text-xs">
                          {row.label}
                        </TableCell>
                        <TableCell className="py-1 px-2 text-right text-xs">
                          {row.value}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
};
