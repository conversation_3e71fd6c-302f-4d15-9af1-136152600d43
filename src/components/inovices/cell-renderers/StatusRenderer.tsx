import { Badge } from "@/components/ui/badge";
import { colorSystem, ColorSystemKey } from "@/lib/constant";
import { removeUnderScore } from "@/utils/commons";
import type { CustomCellRendererProps } from "ag-grid-react";
import { type FunctionComponent } from "react";

export const StatusRenderer: FunctionComponent<CustomCellRendererProps> = ({
  value,
}) => {
  const colors = colorSystem[value as ColorSystemKey] || {
    bg: "bg-green-500/10",
    txt: "text-green-500",
  };
  return (
    <div className="flex items-center h-full select-text ">
      <Badge
        className={`rounded-md hover:bg-inherit  font-semibold uppercase ${colors.bg}  ${colors.txt}`}
      > {removeUnderScore(value === "final_review" ? "open" : value)}
      </Badge>
    </div>
  );
};
