"use client";
import PaginationComponent from "@/components/Common_UI/use-paginition";
import { transformObject } from "@/lib/transferObject";
import React from "react";
import { useResponsive } from "@/hooks/use-mobile";
import { loadInvoicesData } from "./services/invoices-action";
import { InvoicesDataTableMobile } from "./invoices-datatable-mobile";
import { InovicesDataTable } from "./cell-renderers/invoices-data-table";

const allowedKeys = [
  "con",
  "inv",
  "inv_from",
  "inv_to",
  "payment_from",
  "payment_to",
  "v_status",
  "from",
  "to",
];

// Define dynamic mapping (Customize as needed)
const mapping = {
  con: "container_id",
  inv: "invoice_number",
  inv_from: "invoice_amount.min",
  inv_to: "invoice_amount.max",
  payment_from: "payment_received.min",
  payment_to: "payment_received.max",
  v_status: "v_status",
  from: "invoice_date.from",
  to: "invoice_date.to",
};

interface VehiclePageClientProps {
  initialRecords: any;
  baseState: string;
  searchParams: any;
}

const InvoicesClient: React.FC<VehiclePageClientProps> = ({
  initialRecords,
  baseState,
  searchParams,
}) => {
  const boundLoadMoreData = loadInvoicesData.bind(
    null,
    baseState,
    searchParams
  );
  const { isMobile } = useResponsive();

  const initialParams = {
    search: searchParams?.search || "",
    filterData:
      Object.keys(transformObject(searchParams, allowedKeys, mapping))
        .length !== 0
        ? JSON.stringify(transformObject(searchParams, allowedKeys, mapping))
        : "",
  };
  const componentKey = [
    searchParams?.search || "no-search",
    baseState || "no-status",
    initialParams.filterData || "no-filters",
    searchParams?.page || "1",
  ].join("-");

  return (
    <>
      {isMobile ? (
        <div className="h-[calc(100vh-160px)] ">
          <InvoicesDataTableMobile
            key={componentKey}
            records={initialRecords}
            onLoadMoreData={boundLoadMoreData}
            initialParams={initialParams}
          />
        </div>
      ) : (
        <>
          <div className="">
            <div className="h-[calc(100vh-188px)] ">
              <InovicesDataTable records={initialRecords} />
              <PaginationComponent
                count={initialRecords?.total || 0}
                pageSize={initialRecords?.per_page || 0}
              />
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default InvoicesClient;
