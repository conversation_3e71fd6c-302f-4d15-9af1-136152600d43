"use client";
import React, { useMemo } from "react";
import { InvoiceType } from "./cell-renderers/invoices-data-table";
import { ColDef } from "ag-grid-community";
import { format } from "date-fns";
import { getInvoice } from "./services/invoices-service";
import { formatter } from "@/utils/helper-function";
import { useTranslations } from "next-intl";
import { ExportModal } from "../Common_UI/export-modal";
import { toast } from "sonner";

type Props = {
  records: {
    page: number;
    per_page: number;
    total: number;
    data: InvoiceType[];
  };
};

// Helper function to validate dates
const isValidDate = (date: any): boolean => {
  return date && !isNaN(new Date(date).getTime());
};

export default function ExportDate({ records }: Props) {
  const t = useTranslations("export-modal");

  const colDefs = useMemo<ColDef[]>(
    () => [
      { field: "invoice_number", headerName: "Invoice Number" },

      { field: "container_number", headerName: "Container Number" },

      { field: "purpose", headerName: "Purpose" },

      {
        headerName: "Invoice Amount",
        valueGetter: (params) =>
          formatter("USD").format(params.data.invoice_amount),
      },

      {
        headerName: "Payment Received",
        valueGetter: (params) =>
          formatter("USD").format(params.data.payment_received),
      },

      {
        field: "payment_date",
        headerName: "Payment Date",
        valueGetter: (params) =>
          params.data?.payment_date && isValidDate(params.data.payment_date)
            ? format(new Date(params.data.payment_date), "yyyy-MM-dd")
            : "",
      },

      {
        field: "invoice_date",
        headerName: "Invoice Date",
        valueGetter: (params) =>
          params.data?.invoice_date && isValidDate(params.data.invoice_date)
            ? format(new Date(params.data.invoice_date), "yyyy-MM-dd")
            : "",
      },

      {
        field: "invoice_due_date",
        headerName: "Invoice Due Date",
        valueGetter: (params) =>
          params.data?.invoice_due_date &&
          isValidDate(params.data.invoice_due_date)
            ? format(new Date(params.data.invoice_due_date), "yyyy-MM-dd")
            : "",
      },
    ],
    []
  );
  const fetchAllData = async (): Promise<InvoiceType[]> => {
    const response = await getInvoice({
      params: {
        status: "",
        page: 1,
        per_page: records.total,
        search: "",
        exactMatch: false,
        filterData: "",
      },
    });
    return response.data;
  };

  return (
    <ExportModal
      columnDefs={colDefs}
      currentData={records.data}
      exportFileName="Invoices Data"
      fetchAllData={fetchAllData}
      totalItems={records.total}
      translations={{
        title: t("title"),
        exportData: t("sub-title"),
        subTitle: t("sub-title"),
        currentData: t("current-data"),
        allData: t("all-data"),
        cancel: t("cancel"),
        export: t("export"),
      }}
      onExportSuccess={() => toast("Invoices Export Completed")}
    />
  );
}
