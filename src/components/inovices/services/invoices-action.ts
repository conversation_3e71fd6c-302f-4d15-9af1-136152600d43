"use server";
import { transformObject } from "@/lib/transferObject";
import { getInvoice } from "./invoices-service";
// Define allowed keys (Only these fields will be processed)
const allowedKeys = [
  "con",
  "inv",
  "inv_from",
  "inv_to",
  "payment_from",
  "payment_to",
  "v_status",
  "from",
  "to",
];

// Define dynamic mapping (Customize as needed)
const mapping = {
  con: "container_id",
  inv: "invoice_number",
  inv_from: "invoice_amount.min",
  inv_to: "invoice_amount.max",
  payment_from: "payment_received.min",
  payment_to: "payment_received.max",
  v_status: "v_status",
  from: "invoice_date.from",
  to: "invoice_date.to",
};

export async function loadInvoicesData(
  baseState: string,
  searchParams: any,
  params: {
    page: number;
    per_page: number;
    search?: string;
    filterData?: string;
    status?: string;
  }
) {
  try {
    const transformedFilters = transformObject(
      searchParams,
      allowedKeys,
      mapping
    );

    const result = await getInvoice({
      params: {
        status: baseState,
        page: params.page,
        per_page: params.per_page,
        search: params.search || "",
        exactMatch: false,
        filterData:
          Object.keys(transformedFilters).length !== 0
            ? JSON.stringify(transformedFilters)
            : "",
      },
    });

    const response = {
      data: Array.isArray(result.data) ? result.data : [],
      total: result.total || 0,
      page: params.page,
      per_page: params.per_page,
      success: true,
    };

    return response;
  } catch (error) {
    console.error(error);
    return {
      data: [],
      total: 0,
      page: params.page,
      per_page: params.per_page,
      success: false,
    };
  }
}
