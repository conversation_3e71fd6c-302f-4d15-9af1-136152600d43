"use client";
import { useMutation } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";

export const useDownloadInvoice = () => {
  const fetchClient = useFetchClient();

  const { mutate: downloadInvoice, isPending } = useMutation({
    mutationFn: async (item: any) => {
      const res = await fetchClient(
        item.type === "invoice" ? `/v2/invoices-v2/generatepdf/invoice/${item?.id}` :
          `/v2/mix-shippings/generatepdf/mix-full-invoice/${item?.id}`,
        { responseType: "blob" }
      );
      const name = item.type === 'invoice' ? 'invoice' : 'mix-full-invoice';
      const filename = `${name} invoice ${item?.invoice_number}.pdf`;
      const url = window.URL.createObjectURL(new Blob([res.data]));

      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
  });
  return { downloadInvoice, isPending };
};
