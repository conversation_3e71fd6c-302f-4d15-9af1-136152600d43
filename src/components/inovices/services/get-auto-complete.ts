"use server";

import axios from "@/utils/axios-server";

type InvoiceParamType = {
  status: any;
  page: number;
  per_page: number;
  search: string;
  exactMatch: boolean;
  filterData: string;
};

export async function getInvoice({ params }: { params: InvoiceParamType }) {
  try {
    const response = await axios.get(`/v2/invoices-v2/v1`, {
      params: { ...params },
    });
    return response.data;
  } catch (error:any) {
    throw new Error(error?.response?.data?.message);
  }
}

