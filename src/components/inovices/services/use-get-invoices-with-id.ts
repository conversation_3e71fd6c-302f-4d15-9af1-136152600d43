"use client";

import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { getAutoComplete } from "@/utils/autoComplete/autoComplete-server";

export function useGetInvoicesWithId() {
  const search = useSearchParams();
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["invoice-with-id"],
    queryFn: () =>
      getAutoComplete({ column: "invoice_number", model: "invoices" }),
    enabled: false,
  });
  useEffect(() => {
    if (search.get("con")) {
      refetch();
    }
  }, [search, refetch]);
  return {
    data,
    isLoading,
    refetch,
  };
}
