"use client";

import * as React from "react";
import { Sidebar } from "@/components/ui/sidebar";
import { RotateCcw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import FilterCollapse from "../vehicles/filter-collapse";
import CustomSlider from "../Common_UI/custom-slider";
import { ContainerSelector } from "../Common_UI/auto-complete";
import { useGetInvoicesWithId } from "./services/use-get-invoices-with-id";
import { addDays } from "date-fns";
import { DateRange } from "react-day-picker";
import { CustomDateRangePicker } from "../Common_UI/customer-range-date";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { formatNumberByLocale } from "@/utils/helper-function";
import { localesTypes } from "@/i18n/routing";
import { useGetAutoComplete } from "@/utils/use-get-autocomplete";

type optionType = { label: string; value: string };
const ToggleOptionsData: optionType[] = [
  { value: "open", label: "Open" },
  { value: "past_due", label: "Past Due" },
  { value: "paid", label: "Paid" },
];
const MAXIMUM_SIZE = 100000;
const MINMUM_SIZE = 200;
export function FilterModel({}: React.ComponentProps<typeof Sidebar> & {}) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const t = useTranslations("filter-modal");
  const urlParam = useParams();
  const params = new URLSearchParams(Array.from(searchParams.entries()));
  // State to control collapsible open/close behavior
  const [openCollapse, toggleCollapse] = React.useState<string[]>([]);
  const clearSelectionsRef = React.useRef<(() => void) | null>(null);
  const [date, setDate] = React.useState<DateRange>(function () {
    const from = params.get("from");
    const to = params.get("to");
    return {
      from: from ? new Date(from) : new Date(2023, 0, 20),
      to: to ? new Date(to) : addDays(new Date(2023, 0, 20), 20),
    };
  });

  const [paymentRecived, setPaymentRecived] = React.useState([20000, 90000]);
  const [invoiceAmount, setInvoiceAmount] = React.useState([20000, 90000]);

  const {
    data,
    isLoading,
    refetch,
    handleSearch: handleContainerSearch,
  } = useGetAutoComplete({
    column: "container_number",
    model: "containers",
    key: "con",
  });
  const {
    data: invoicesData,
    isLoading: isLoadingInvoices,
    refetch: refetchInvoices,
  } = useGetInvoicesWithId();

  const handleSelectionContainer = (selected: any[]) => {
    params.delete("con");
    selected?.forEach((select) => params.append("con", select.id));
    router.push(`?${params}`);
  };
  const handleSelectionInoviceId = (selected: any[]) => {
    params.delete("inv");
    selected?.forEach((select) => params.append("inv", select.id));
    router.push(`?${params}`);
  };

  const handleSelectionVehicleStatus = (selected: any[]) => {
    params.delete("v_status");
    selected?.forEach((select) => params.append("v_status", select.value));
    router.push(`?${params}`);
  };

  const handleInvoiceAmount = (selected: number[]) => {
    params.delete("inv_from");
    params.delete("inv_to");
    params.append("inv_from", `${selected[0]}`);
    params.append("inv_to", `${selected[1]}`);
    router.push(`?${params}`);
    setInvoiceAmount(selected);
  };
  const handlePaymentRecived = (selected: number[]) => {
    params.delete("payment_from");
    params.delete("payment_to");
    params.append("payment_from", `${selected[0]}`);
    params.append("payment_to", `${selected[1]}`);
    router.push(`?${params}`);
    setPaymentRecived(selected);
  };

  const handleDate = (selected: DateRange) => {
    setDate(selected);
    if (selected.from && selected.to) {
      params.delete("from");
      params.delete("to");
      params.append("from", selected.from.toISOString().split("T")[0]);
      params.append("to", selected.to.toISOString().split("T")[0]);
      router.push(`?${params}`);
    }
  };
  const handleClearFilters = () => {
    params.delete("inv");
    params.delete("con");
    params.delete("v_status");
    params.delete("from");
    params.delete("to");
    params.delete("payment_from");
    params.delete("payment_to");
    params.delete("inv_from");
    params.delete("inv_to");
    router.push(`?${params}`);
    setInvoiceAmount([20000, 90000]);
    setDate({
      from: new Date(2023, 0, 20),
      to: addDays(new Date(2023, 0, 20), 20),
    });
    setPaymentRecived([20000, 90000]);
    toggleCollapse([]);
    clearSelectionsRef.current?.();
  };
  return (
    <div className="mt-1 p-2 w-72">
      <div className="flex flex-row pl-3 pt-2 justify-between py-2">
        <h2 className="text-lg font-medium">{t("filters")}</h2>
        <Button
          onClick={() => {
            clearSelectionsRef.current?.();
            handleClearFilters();
          }}
          variant={"link"}
          size={"icon"}
          className="clear-filters"
        >
          <RotateCcw />
        </Button>
      </div>
      <div className="flex flex-col p-2 gap-2">
        <FilterCollapse
          label={t("invoice-filter-modal.container")}
          className="p-2"
          isOpen={openCollapse?.includes("containers")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("containers")
                ? prev.filter((item) => item !== "containers")
                : [...prev, "containers"]
            )
          }
        >
          <ContainerSelector
            data={
              data?.data?.map((item: any) => ({ ...item, checked: false })) ||
              []
            }
            isLoading={isLoading}
            onFetch={refetch}
            onSearch={handleContainerSearch}
            onSelectionChange={handleSelectionContainer}
            selectedLabel={(count) =>
              `${t("checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            unselectedLabel={(count) =>
              `${t("not-checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            labelKey="container_number"
            valueKey="id"
            clearSelectionsRef={clearSelectionsRef}
            searchParamsKey="con"
          />
        </FilterCollapse>

        <FilterCollapse
          label={t("invoice-filter-modal.invoice-number")}
          className="p-2"
          isOpen={openCollapse?.includes("invoice")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("invoice")
                ? prev.filter((item) => item !== "invoice")
                : [...prev, "invoice"]
            )
          }
        >
          <ContainerSelector
            data={
              invoicesData?.data?.map((item: any) => ({
                ...item,
                checked: false,
              })) || []
            }
            isLoading={isLoadingInvoices}
            onFetch={refetchInvoices}
            onSelectionChange={handleSelectionInoviceId}
            selectedLabel={(count) =>
              `${t("checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            unselectedLabel={(count) =>
              `${t("not-checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            labelKey="invoice_number"
            valueKey="id"
            clearSelectionsRef={clearSelectionsRef}
            searchParamsKey="inv"
          />
        </FilterCollapse>

        <FilterCollapse
          label={t("invoice-filter-modal.status")}
          isOpen={openCollapse?.includes("vehicle")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("vehicle")
                ? prev.filter((item) => item !== "vehicle")
                : [...prev, "vehicle"]
            )
          }
        >
          <ContainerSelector
            data={
              ToggleOptionsData?.map((item: any) => ({
                ...item,
                checked: false,
              })) || []
            }
            onSelectionChange={handleSelectionVehicleStatus}
            selectedLabel={(count) =>
              `${t("checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            unselectedLabel={(count) =>
              `${t("not-checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            labelKey="label"
            valueKey="value"
            searchParamsKey="v_status"
            clearSelectionsRef={clearSelectionsRef}
          />
        </FilterCollapse>
        <Separator />
        <CustomSlider
          label={t("invoice-filter-modal.invoice-amount")}
          max={MAXIMUM_SIZE}
          min={MINMUM_SIZE}
          onCommit={handleInvoiceAmount}
          onChange={setInvoiceAmount}
          value={invoiceAmount}
        />
        <Separator />
        <Separator />
        <CustomSlider
          label={t("invoice-filter-modal.payment-received")}
          max={MAXIMUM_SIZE}
          min={MINMUM_SIZE}
          onCommit={handlePaymentRecived}
          onChange={setPaymentRecived}
          value={paymentRecived}
        />
        <Separator />
        <FilterCollapse
          label={t("invoice-filter-modal.date-range.label")}
          isOpen={openCollapse?.includes("Date Range")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("Date Range")
                ? prev.filter((item) => item !== "Date Range")
                : [...prev, "Date Range"]
            )
          }
        >
          <div className="flex flex-col gap-4 pt-3">
            <Label htmlFor="loading_date">
              {t("invoice-filter-modal.date-range.issue-date")}
            </Label>
            <CustomDateRangePicker
              id="loading_date"
              date={date}
              onChange={(selected) => handleDate(selected as DateRange)}
            />
          </div>
        </FilterCollapse>
      </div>
    </div>
  );
}
