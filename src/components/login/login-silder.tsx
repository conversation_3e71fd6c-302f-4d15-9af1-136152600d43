"use client";

import Image from "next/image";
import useEmblaCarousel from "embla-carousel-react";
import { useEffect, useRef } from "react";
import { useDirection } from "@/hooks/useDirection";

const LoginSlider = ({ images }: { images: string[] }) => {
  const { dir } = useDirection();
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    direction: dir,
  });

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const startAutoPlay = () => {
    timeoutRef.current = setTimeout(() => {
      if (emblaApi) {
        if (emblaApi.canScrollNext()) {
          emblaApi.scrollNext();
        } else {
          emblaApi.scrollTo(0);
        }
        startAutoPlay();
      }
    }, 5000);
  };

  useEffect(() => {
    if (emblaApi) {
      startAutoPlay();
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [emblaApi]);

  return (
    <div className="overflow-hidden min-w-[calc(100%-40%)] min-h-full" ref={emblaRef}>
      <div className="flex h-full">
        {images.map((img, index) => (
          <div
            key={index}
            className="flex-[0_0_100%] p-1 relative bg-transparent"
          >
            <div className="outline outline-1 m-1 outline-border size-full flex items-center justify-center rounded-xl bg-background">
              <Image
                src={img}
                alt="International shipping and shopping"
                fill
                className="absolute inset-0 h-full object-cover"
                priority
                sizes="(max-width: 640px) 100vw, 50vw"
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LoginSlider;
