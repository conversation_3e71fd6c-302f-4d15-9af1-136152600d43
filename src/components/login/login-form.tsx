"use client";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { useDirection } from "@/hooks/useDirection";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { toast } from "sonner";
import { Eye, EyeOff, Loader2, LogIn } from "lucide-react";
import { ForgotPasswordModal } from "./forget-password-modal";
import Image from "next/image";
import { signIn } from "next-auth/react";
import { useProgress } from "@bprogress/next";

// Define form validation schema
const loginSchema = z.object({
  email_username: z.string().min(1, "Email or username is required"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export function LoginForm({
  className,
  ...props
}: React.ComponentProps<"div"> & { callbackUrl?: string }) {
  const t = useTranslations("login");
  const { locale } = useParams();
  const { dir } = useDirection();
  const router = useRouter();
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const { start } = useProgress();

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email_username: "",
      password: "",
    },
  });

  async function onSubmit(data: LoginFormValues) {
    try {
      const result = await signIn("credentials", {
        username: data.email_username,
        password: data.password,
        redirect: false,
      });
      if (result && (!result.ok || result.error)) {
        const loginError =
          result.error === "CredentialsSignin"
            ? "Wrong password or username"
            : result.error;
        toast.error(loginError as string);
        return;
      }
      start();
      router.push(props.callbackUrl ? props.callbackUrl : `/${locale}`);
    } catch (error) {
      toast.error(String(error));
    }
  }

  return (
    <div
      className={cn("flex items-center justify-center min-h-screen p-4", className)}
      {...props}
      dir={dir}
    >
      <Card className="w-full  overflow-hidden rounded-2xl shadow-xl border-0 dark:bg-gray-800/80 dark:backdrop-blur-sm">
        <CardContent className="p-0 flex flex-col md:flex-row">
          {/* Left side - Login form */}
          <div className="w-full xl:w-1/2 p-3 flex flex-col justify-center">
            <div className="mb-6 text-center">
              <div className="inline-flex p-3 rounded-full bg-primary/10">
                <Image
                  src="/favicons/default.ico"
                  alt="App Logo"
                  width={48}
                  height={48}
                  className="mx-auto rounded-full"
                />
              </div>
              <h1 className="text-xl md:text-2xl font-bold mb-1 text-primary">
                {t("left-side.title")}
              </h1>
              <p className="text-xs md:text-sm text-muted-foreground max-w-xs mx-auto">
                {t("left-side.description")}
              </p>
            </div>

            <h2 className="text-xl md:text-2xl font-semibold mb-3 text-center">
              {t("title")}
            </h2>

            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="space-y-4 w-full max-w-md mx-auto"
            >
              <div className="space-y-1">
                <Label htmlFor="email_username" className="font-medium text-sm">
                  {t("email")}
                </Label>
                <div className="relative">
                  <Input
                    id="email_username"
                    type="text"
                    placeholder={t("email")}
                    disabled={form.formState.isSubmitting}
                    autoComplete="email"
                    className="pl-4 h-12 rounded-lg focus-visible:ring-2 focus-visible:ring-primary/50 focus-visible:ring-offset-0"
                    {...form.register("email_username")}
                  />
                </div>
                {form.formState.errors.email_username && (
                  <p className="text-xs text-red-500 mt-1">
                    {form.formState.errors.email_username.message}
                  </p>
                )}
              </div>

              <div className="space-y-1">
                <Label htmlFor="password" className="font-medium text-sm">
                  {t("password")}
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={isVisible ? "text" : "password"}
                    placeholder={t("password")}
                    disabled={form.formState.isSubmitting}
                    autoComplete="current-password"
                    className="pl-4 h-12 rounded-lg pr-12 focus-visible:ring-2 focus-visible:ring-primary/50 focus-visible:ring-offset-0"
                    {...form.register("password")}
                  />
                  <Button
                    type="button"
                    onClick={() => setIsVisible(!isVisible)}
                    variant="ghost"
                    size="icon"
                    className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                    aria-label={isVisible ? "Hide password" : "Show password"}
                  >
                    {isVisible ? (
                      <EyeOff
                        size={16}
                        className="text-muted-foreground"
                        aria-hidden="true"
                      />
                    ) : (
                      <Eye
                        size={16}
                        className="text-muted-foreground"
                        aria-hidden="true"
                      />
                    )}
                  </Button>
                </div>
                {form.formState.errors.password && (
                  <p className="text-xs text-red-500 mt-1">
                    {form.formState.errors.password.message}
                  </p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full h-12 bg-primary hover:bg-primary/90 text-white rounded-lg flex items-center justify-center gap-2 font-medium text-base transition-colors"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting ? (
                  <Loader2 className="animate-spin" size={18} />
                ) : (
                  <LogIn size={18} />
                )}
                {t("label")}
              </Button>

              <div className="flex flex-col sm:flex-row justify-between items-center pt-2 text-sm gap-2">
                <ForgotPasswordModal />
                <Link
                  href={`/${locale}/auth/register`}
                  className="text-primary font-medium hover:underline"
                >
                  {t("register")}
                </Link>
              </div>
            </form>
          </div>

          {/* Right side - Image */}
          <div className="relative  xl:w-3/4 h-64 md:h-auto hidden xl:block">
            <Image
              src="/login_page_image.png"
              alt="International shipping and shopping"
              fill
              className="absolute  inset-0 h-full w-full object-cover "
              priority
              sizes="(max-width: 640px) 100vw, 50vw"
            />
          </div>
        </CardContent>
      </Card>
    </div>)
}
