"use client";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { useDirection } from "@/hooks/useDirection";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { LockKeyhole, Loader2, CheckCircle } from "lucide-react";

import { useFetchClient } from "@/utils/axios";
import { useParams } from "next/navigation";

// Email regex pattern
const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

// Define form validation schema
const forgotPasswordSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .refine((value) => emailRegex.test(value), {
      message: "Please enter a valid email address",
    }),
});

type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

export function ForgotPasswordModal() {
  const t = useTranslations("forgot-password");
  const { dir } = useDirection();
  const [open, setOpen] = useState(false);
  const [isValidEmail, setIsValidEmail] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const fetchClient = useFetchClient();
  const { locale } = useParams();

  const form = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  const checkEmailValidity = (email: string) => {
    setIsValidEmail(emailRegex.test(email));
  };

  async function onSubmit(data: ForgotPasswordFormValues) {
    setIsSubmitting(true);
    try {
      await fetchClient(`/v2/auth/forgot-password`, {
        method: "POST",
        data: {
          email: data.email,
          locale: locale,
        },
      });

      toast.success("If the email exists, a reset link has been sent.");
      setOpen(false);
      form.reset();
    } catch (error) {
      toast.error("An error occurred. Please try again." + error);
    } finally {
      setIsSubmitting(false);
    }
  }

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    form.handleSubmit(onSubmit)(e);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="link"
          className="text-primary font-medium hover:underline p-0 h-auto"
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setOpen(true);
          }}
        >
          {t("linkText")}
        </Button>
      </DialogTrigger>
      <DialogContent
        className="sm:max-w-[425px] rounded-xl shadow-lg border-0 dark:bg-gray-800/95 dark:backdrop-blur-sm"
        dir={dir}
        style={{ borderTop: "4px solid var(--primary)" }}
        onPointerDownOutside={(e) => e.preventDefault()}
      >
        <DialogHeader className="space-y-4 text-center pt-2">
          <div className="mx-auto bg-primary/10 p-4 rounded-full">
            <LockKeyhole className="h-8 w-8 text-primary" />
          </div>
          <DialogTitle className="text-xl md:text-2xl font-semibold text-primary text-center">
            {t("title")}
          </DialogTitle>
          <DialogDescription className="text-sm text-muted-foreground">
            {t("description")}
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={handleFormSubmit}
          className="space-y-5 pt-3"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium">
              {t("emailLabel") || "Email Address"}
            </Label>
            <div className="relative">
              <Input
                id="email"
                type="email"
                placeholder={t("emailPlaceholder") || "<EMAIL>"}
                autoComplete="email"
                autoFocus
                className="pl-4 h-12 rounded-lg pr-10 focus-visible:ring-2 focus-visible:ring-primary/50 focus-visible:ring-offset-0"
                {...form.register("email", {
                  onChange: (e) => checkEmailValidity(e.target.value),
                })}
                disabled={isSubmitting}
              />
              {isValidEmail && (
                <div className="absolute right-3 top-1/2 -translate-y-1/2 transition-opacity">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                </div>
              )}
            </div>
            {form.formState.errors.email && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.email.message}
              </p>
            )}
          </div>
          <DialogFooter className="flex flex-col pt-2 gap-3">
            <Button
              type="submit"
              className="w-full h-12 bg-primary hover:bg-primary/90 text-white rounded-lg font-medium text-base transition-colors"
              disabled={isSubmitting || !isValidEmail}
              onClick={(e) => e.stopPropagation()}
            >
              {isSubmitting ? (
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              ) : null}
              {t("submitButton") || "Send Reset Link"}
            </Button>
            <Button
              type="button"
              variant="ghost"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setOpen(false);
              }}
              className="text-muted-foreground hover:bg-transparent hover:underline"
              disabled={isSubmitting}
            >
              {t("cancelButton") || "Cancel"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
