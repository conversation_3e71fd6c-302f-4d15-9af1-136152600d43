"use client";
import type {
  Col<PERSON>ef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";

import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  ServerSideRowModelModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import { RefObject, useMemo, useRef, useState } from "react";
import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import { CirclePlus } from "lucide-react";
import { Button } from "@/components/ui/button";
import InputSearch from "@/components/Common_UI/InputSearch";
import CustomerOfCustomerForm from "./customer-form";
import { useServerSideDatasource } from "@/hooks/use-infinite-scroll";
import { FullNameRenderer } from "./cell-renderers/FullNameRenderer";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ServerSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);
export type customerType = {
  id: number;
  fullname: string;
  phone: string;
  photo: string;
  status: string;
  company: {
    name: string;
    company_city: string;
  };
  loginable: {
    status: "active" | "deactive";
    email: string;
    username: string;
  };
  created_at: string;
  updated_at: string;
};

interface Props {
  gridTheme?: string;
  isDarkMode?: boolean;
  records: {
    page: number;
    per_page: number;
    total: number;
    data: customerType[];
  } | null;
  gridRefProps?: RefObject<AgGridReact | null>;
  exportColDefs?: ColDef[];
  onLoadMoreData: (params: {
    page: number;
    per_page: number;
    search?: string;
    state?: string;
  }) => Promise<any>;
  initialParams?: {
    search?: string;
    filterData?: string;
    state?: string;
  };
}

export const CustomerDataTableMobile = ({
  gridRefProps,
  exportColDefs,
  onLoadMoreData,
  initialParams,
  records,
}: Props) => {
  const gridRef = useRef<AgGridReact>(null);
  const t = useTranslations("customer-of-customer");
  const t2 = useTranslations("datatable");
  const [open, setOpen] = useState(false);

  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        field: "fullname",
        headerName: t("header.fullname"),
        cellRenderer: FullNameRenderer,
        minWidth: 100,
      },
    ],
    [t]
  );

  const { onGridReady } = useServerSideDatasource({
    onLoadMoreData,
    initialParams,
    pageSize: 20,
    initialRecords: records,
  });

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  const [quickFilterText] = useState<string>();

  const { isRTL } = useDirection();

  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
      flex: 1,
    }),
    []
  );

  return (
    <>
      <div className="flex flex-col md:flex-row items-center px-2 justify-between mb-4">
        <h2 className="hidden md:text-3xl font-bold tracking-tight md:block">
          {t("body.customers")}
        </h2>
        <div className="flex items-center gap-1">
          <InputSearch
            type="text"
            fieldName="search"
            placeholder={t2("sidebar.search")}
          />

          <Button
            onClick={() => setOpen(true)}
            variant={"outline"}
            size={"icon"}
          >
            <CirclePlus className="w-6 h-6 cursor-pointer" />
          </Button>
        </div>
      </div>

      <AgGridDataTable
        enableRtl={isRTL}
        ref={gridRefProps || gridRef}
        columnDefs={exportColDefs || colDefs}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        rowModelType="serverSide"
        onGridReady={onGridReady}
        rowBuffer={0}
        cacheBlockSize={20}
        maxBlocksInCache={5}
        pagination={false}
        masterDetail={false}
        quickFilterText={quickFilterText}
        rowHeight={180}
        colResizeDefault="shift"
        headerHeight={50}
        rowClassRules={{
          "row-even": (params) =>
            params.node.rowIndex ? params.node.rowIndex % 2 !== 0 : true,
          "row-odd": (params) =>
            params.node.rowIndex ? params.node.rowIndex % 2 === 0 : true,
        }}
        suppressHorizontalScroll={true}
        suppressColumnVirtualisation={true}
      />

      <CustomerOfCustomerForm open={open} setOpen={setOpen} />
    </>
  );
};
