"use client";
import { useMutation } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
export const useAddCustomerOfCustomer = () => {
  const fetchClient = useFetchClient();

  const { mutate: addCustomer, isPending } = useMutation({
    mutationFn: async (data: any) =>
      await fetchClient(`/v2/customer-of-customer`, {
        data: data,
        method: "POST",
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }),
  });
  return { addCustomer, isPending };
};
