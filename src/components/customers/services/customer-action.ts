"use server";

import { getCusotmer } from "./get-customer";

export async function loadCustomerData(
  baseState: string,
  params: {
    page: number;
    per_page: number;
    search?: string;
    status?: string;
  }
) {
  try {
    const result = await getCusotmer({
      params: {
        status: baseState,
        page: params.page,
        per_page: params.per_page,
        search: params.search || "",
      },
    });

    const response = {
      data: Array.isArray(result.data) ? result.data : [],
      total: result.total || 0,
      page: params.page,
      per_page: params.per_page,
      success: true,
    };

    return response;
  } catch (error) {
    console.error(error);
    return {
      data: [],
      total: 0,
      page: params.page,
      per_page: params.per_page,
      success: false,
    };
  }
}
