"use client";
import { useMutation } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
export const useDeleteCustomerOfCustomer = () => {
  const fetchClient = useFetchClient();

  const { mutate: deleteCustomer, isPending } = useMutation({
    mutationFn: async (data: number[]) =>
      await fetchClient(`/v2/customer-of-customer/${data}`, {
        method: "DELETE",
      }),
  });
  return { deleteCustomer, isPending };
};
