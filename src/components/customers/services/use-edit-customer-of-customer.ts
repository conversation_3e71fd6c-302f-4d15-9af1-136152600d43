"use client";
import { useMutation } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
export const useEditCustomerOfCustomer = () => {
  const fetchClient = useFetchClient();

  const { mutate: editCustomer, isPending } = useMutation({
    mutationFn: async (data: any) =>
      await fetchClient(
        `/v2/customer-of-customer/${data.customer_of_customer_id}`,
        {
          data: data,
          method: "PATCH",
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      ),
  });
  return { editCustomer, isPending };
};
