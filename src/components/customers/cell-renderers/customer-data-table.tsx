"use client";
import type {
  ColDef,
  RowSelectionOptions,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";

import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import { RefObject, useMemo, useRef, useState } from "react";
import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import { StatusCell } from "./status-cell";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { CirclePlus, Trash2Icon } from "lucide-react";
import { format } from "date-fns";
import { OperationCell } from "./operation-cell";
import CustomerOfCustomerForm from "../customer-form";
import { Button } from "@/components/ui/button";
import { useDeleteCustomerOfCustomer } from "../services/use-delete-customer-of-customer";
import { toast } from "sonner";
import { LoadingSpinner } from "@/components/Common_UI/loading";
import { useRouter } from "next/navigation";
import InputSearch from "@/components/Common_UI/InputSearch";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);
export type customerType = {
  id: number;
  fullname: string;
  phone: string;
  photo: string;
  status: string;
  company: {
    name: string;
    company_city: string;
  };
  loginable: {
    status: "active" | "deactive";
    email: string;
    username: string;
  };
  created_at: string;
  updated_at: string;
};

interface Props {
  gridTheme?: string;
  isDarkMode?: boolean;
  records: {
    page: number;
    per_page: number;
    total: number;
    data: customerType[];
  } | null;
  gridRefProps?: RefObject<AgGridReact | null>;
  exportColDefs?: ColDef[];
}

export const CustomerDataTable = ({
  records,
  gridRefProps,
  exportColDefs,
}: Props) => {
  const gridRef = useRef<AgGridReact>(null);
  const t = useTranslations("customer-of-customer");
  const t2 = useTranslations("datatable");
  const [open, setOpen] = useState(false);
  const router = useRouter();
  const [deleteIds, setDeleteIds] = useState<number[]>([]);
  const { deleteCustomer, isPending } = useDeleteCustomerOfCustomer();
  const [isDelete, setIsDelete] = useState(false);
  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: "#",
        cellRenderer: (params: any) => {
          return !params.node?.rowIndex ? 1 : params.node?.rowIndex + 1;
        },
        minWidth: 40,
        maxWidth: 40,
      },
      {
        field: "id",
        headerName: t("header.id"),
        minWidth: 40,
        maxWidth: 40,
      },
      {
        field: "fullname",
        headerName: t("header.fullname"),
        minWidth: 120,
      },
      {
        headerName: t("header.username"),
        cellRenderer: (params: any) => (
          <div>{params.data?.loginable?.username}</div>
        ),
        minWidth: 120,
      },
      {
        headerName: t("header.email"),
        cellRenderer: (params: any) => (
          <div>{params.data?.loginable?.email}</div>
        ),
        minWidth: 250,
      },
      { field: "password", headerName: t("header.password"), minWidth: 100 },
      {
        field: "phone",
        headerName: t("header.phone"),
        minWidth: 120,
      },

      {
        headerName: t("header.status"),
        cellRenderer: StatusCell,
        minWidth: 100,
      },
      {
        headerName: t("header.created-at"),
        cellRenderer: (params: any) => (
          <div>
            {params.data?.created_at &&
              format(new Date(params.data?.created_at), "yyyy-MM-dd")}
          </div>
        ),
        minWidth: 120,
      },
      {
        headerName: t("header.updated-at"),
        cellRenderer: (params: any) => (
          <div>
            {params.data?.updated_at &&
              format(new Date(params.data?.updated_at), "yyyy-MM-dd")}
          </div>
        ),
        minWidth: 120,
      },
      {
        cellRenderer: OperationCell,
        minWidth: 48,
        maxWidth: 48,
      },
    ],
    [t]
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };
  const rowSelection: "single" | "multiple" | RowSelectionOptions<any, any> = {
    mode: "multiRow",
    selectAll: "filtered",
  };
  const onRowSelected = (event: any) => {
    if (event?.data?.deleted_at) return;
    setDeleteIds((item) =>
      item.includes(event.data.id)
        ? item.filter((id) => id !== event.data.id)
        : [...item, event.data.id]
    );
  };
  const [quickFilterText] = useState<string>();
  const handleDelete = () => {
    deleteCustomer(deleteIds, {
      onSuccess: () => {
        router.refresh();
        toast.success("Customer of customer deleted successfully!");
        setDeleteIds([]);
        setIsDelete(false);
        gridRef.current?.api.deselectAll();
      },
      onError: () => {
        toast.error("Deletion failed. Please try again.");
      },
    });
  };
  const { isRTL, dir } = useDirection();
  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
      flex: 1,
    }),
    []
  );
  return (
    <>
      <div className="flex flex-col md:flex-row items-center px-2 justify-between mb-4">
        <h2 className="text-3xl font-bold tracking-tight">
          {t("body.customers")}
        </h2>
        <div className="flex items-center gap-1">
          <InputSearch
            type="text"
            fieldName="search"
            placeholder={t2("sidebar.search")}
          />
          {deleteIds.length === 0 ? (
            <Button
              onClick={() => setOpen(true)}
              variant={"outline"}
              size={"icon"}
            >
              <CirclePlus className="w-6 h-6 cursor-pointer" />
            </Button>
          ) : (
            <Button
              onClick={() => setIsDelete(true)}
              variant={"outline"}
              size={"icon"}
            >
              <Trash2Icon className="w-6 h-6 cursor-pointer" />
            </Button>
          )}
        </div>
      </div>
      <AgGridDataTable
        enableRtl={isRTL ? true : false}
        ref={gridRefProps || gridRef}
        columnDefs={exportColDefs || colDefs}
        rowData={records?.data || []}
        autoSizeStrategy={autoSizeStrategy}
        defaultColDef={defaultColDef}
        rowSelection={rowSelection}
        onRowSelected={onRowSelected}
        masterDetail
        detailCellRendererParams={{ t }}
        quickFilterText={quickFilterText}
        enableCellTextSelection
        colResizeDefault="shift"
        headerHeight={50}
        rowClassRules={{
          "row-even": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 === 0
                ? false
                : true
              : true;
          },
          "row-odd": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 !== 0
                ? false
                : true
              : true;
          },
        }}
      />
      <CustomerOfCustomerForm open={open} setOpen={setOpen} />

      <AlertDialog open={isDelete} onOpenChange={setIsDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className={`${isRTL ? "text-right" : ""}`}>
              {t("body.delete")}
            </AlertDialogTitle>
            <AlertDialogDescription className={`${isRTL ? "text-right" : ""}`}>
              {t("body.delete-description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter dir={dir} className={`${isRTL ? "gap-2" : ""}`}>
            <AlertDialogCancel>{t("body.cancel")}</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>
              {isPending ? (
                <LoadingSpinner className="h-5 w-5" />
              ) : (
                t("body.delete")
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
