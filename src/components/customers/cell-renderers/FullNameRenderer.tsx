import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { CustomCellRendererProps } from "ag-grid-react";
import { useState, type FunctionComponent } from "react";
import {
  Edit2Icon,
  MoreVertical,
  Trash,
  UserCheck,
  UserX,
  Mail,
  Phone,
  User,
  Key,
  Copy,
  Eye,
  EyeOff,
} from "lucide-react";
import { useEditCustomerOfCustomer } from "../services/use-edit-customer-of-customer";
import { useDeleteCustomerOfCustomer } from "../services/use-delete-customer-of-customer";
import { toast } from "sonner";
import { useDirection } from "@/hooks/useDirection";
import { LoadingSpinner } from "@/components/Common_UI/loading";
import { useTranslations } from "next-intl";
import CustomerOfCustomerForm from "../customer-form";

type AlertType = "delete" | "enable" | "disable" | null;

export const FullNameRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
  api,
  node,
}) => {
  const { editCustomer, isPending: isEditPending } =
    useEditCustomerOfCustomer();
  const { deleteCustomer, isPending: isDeletePending } =
    useDeleteCustomerOfCustomer();
  const t = useTranslations("customer-of-customer");
  const [editFormOpen, setEditFormOpen] = useState(false);
  const [alertType, setAlertType] = useState<AlertType>(null);
  const [showPassword, setShowPassword] = useState(false);
  const { dir, isRTL } = useDirection();

  const isActive = data?.loginable?.status === "active";
  const isDeleted = !!data?.deleted_at;
  const newStatus = isActive ? "deactive" : "active";
  const isPending = isEditPending || isDeletePending;

  const handleStatusChange = () => {
    editCustomer(
      {
        email: data?.loginable?.email,
        phone: data?.phone,
        fullname: data?.fullname,
        username: data?.loginable?.username,
        password: data?.password,
        status: newStatus,
        customer_of_customer_id: data?.id,
      },
      {
        onSuccess: () => {
          toast.success(
            `Customer ${
              newStatus === "active" ? "enabled" : "disabled"
            } successfully!`
          );

          const updatedRowData = {
            ...data,
            loginable: {
              ...data.loginable,
              status: newStatus,
            },
          };

          node.setData(updatedRowData);
          api.refreshCells({ rowNodes: [node] });
          setAlertType(null);
        },
        onError: () => {
          toast.error("Status update failed. Please try again.");
        },
      }
    );
  };

  const updateUIAfterDeletion = () => {
    const updatedRowData = {
      ...data,
      deleted_at: new Date().toISOString(),
      _isDeleted: true,
    };

    node.setData(updatedRowData);
    api.refreshCells({ rowNodes: [node] });

    try {
      node.setRowHeight(0);
      api.onRowHeightChanged();
    } catch (error) {
      console.warn("Could not hide row:", error);
    }
  };

  const handleDelete = () => {
    deleteCustomer([data.id], {
      onSuccess: () => {
        toast.success("🗑️ Customer deleted successfully!", {
          duration: 3000,
          position: "top-right",
        });

        updateUIAfterDeletion();
        setAlertType(null);
      },
      onError: (error: any) => {
        toast.error("Deletion failed. Please try again.", {
          duration: 4000,
          position: "top-right",
        });
        console.error("Delete error:", error);
      },
    });
  };

  const handleEditSuccess = (updatedData: any) => {
    const updatedRowData = {
      ...data,
      fullname: updatedData.fullname,
      phone: updatedData.phone,
      loginable: {
        ...data.loginable,
        email: updatedData.email,
        username: updatedData.username,
        status: updatedData.status,
      },
    };

    node.setData(updatedRowData);
    api.refreshCells({ rowNodes: [node] });
  };

  const getAlertContent = () => {
    switch (alertType) {
      case "delete":
        return {
          title: t("body.delete"),
          description: t("body.delete-description"),
          action: handleDelete,
          actionText: t("body.delete"),
          className:
            "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        };
      case "enable":
        return {
          title: `${t("body.enable")} ${t("body.customer")}`,
          description: t("body.active-description"),
          action: handleStatusChange,
          actionText: t("body.enable"),
          className: "",
        };
      case "disable":
        return {
          title: `${t("body.disable")} ${t("body.customer")}`,
          description: t("body.deactive-description"),
          action: handleStatusChange,
          actionText: t("body.disable"),
          className:
            "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        };
      default:
        return null;
    }
  };

  const alertContent = getAlertContent();

  const copyToClipboard = async (text: string, label: string) => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(text);
        toast.success(`${label} copied to clipboard!`);
      } else {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
          document.execCommand("copy");
          toast.success(`${label} copied to clipboard!`);
        } catch (err) {
          console.error("Fallback copy failed:", err);
          toast.error("Failed to copy to clipboard");
        } finally {
          textArea.remove();
        }
      }
    } catch (err) {
      console.error("Copy to clipboard failed:", err);
      toast.error("Failed to copy to clipboard");
    }
  };

  const getInitials = (name: string) => {
    return (
      name
        ?.split(" ")
        .map((word) => word[0])
        .join("")
        .toUpperCase()
        .slice(0, 2) || "?"
    );
  };

  const maskPassword = (password: string) => {
    return "*".repeat(password?.length || 8);
  };

  return (
    <div className="w-full h-full">
      <Card
        className={`transition-all duration-200 hover:shadow-md ${
          isDeleted
            ? "opacity-50 bg-muted/30"
            : isActive
            ? "border-primary-200 bg-primary-50/30"
            : "border-red-200 bg-red-50/30 dark:border-red-500/30 dark:bg-red-500/10"
        } w-full h-full`}
      >
        <CardHeader className="pb-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div
                className={`w-5 h-5 rounded-full flex items-center justify-center text-white text-[10px] ${
                  isActive
                    ? "bg-gradient-to-br from-green-400 to-green-600"
                    : "bg-gradient-to-br from-red-400 to-red-600"
                }`}
              >
                {getInitials(data?.fullname)}
              </div>

              {/* <div className="flex-1"> */}
              <div className="flex items-center justify-between gap-2">
                <h3
                  className={`font-semibold text-xs ${
                    isDeleted
                      ? "line-through text-muted-foreground"
                      : "text-foreground"
                  }`}
                >
                  {data?.fullname?.charAt(0).toUpperCase() +
                    data?.fullname?.slice(1)}
                </h3>
                <Badge
                  variant={isActive ? "default" : "destructive"}
                  className={`text-xs ${
                    isDeleted ? "opacity-30" : ""
                  } px-1 py-0`}
                >
                  {isActive ? (
                    <UserCheck className="w-2 h-2 mr-1" />
                  ) : (
                    <UserX className="w-2 h-2 mr-1" />
                  )}
                  <span className="text-[10px]">
                    {isDeleted
                      ? "Deleted"
                      : isActive
                      ? t("body.active")
                      : t("body.disable")}
                  </span>
                </Badge>
              </div>
              {/* </div> */}
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  disabled={isDeleted || isPending}
                >
                  {isPending ? (
                    <LoadingSpinner className="h-4 w-4" />
                  ) : (
                    <MoreVertical className="h-4 w-4" />
                  )}
                </Button>
              </DropdownMenuTrigger>

              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuGroup>
                  <DropdownMenuItem onClick={() => setEditFormOpen(true)}>
                    {t("body.edit")}
                    <DropdownMenuShortcut
                      className={isRTL ? "mr-auto ml-0" : ""}
                    >
                      <Edit2Icon className="w-4 h-4" />
                    </DropdownMenuShortcut>
                  </DropdownMenuItem>
                </DropdownMenuGroup>

                <DropdownMenuSeparator />

                <DropdownMenuGroup>
                  <DropdownMenuItem
                    onClick={() =>
                      setAlertType(isActive ? "disable" : "enable")
                    }
                  >
                    {isActive ? t("body.disable") : t("body.enable")}
                    <DropdownMenuShortcut
                      className={isRTL ? "mr-auto ml-0" : ""}
                    >
                      {isActive ? (
                        <UserX className="w-4 h-4" />
                      ) : (
                        <UserCheck className="w-4 h-4" />
                      )}
                    </DropdownMenuShortcut>
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    onClick={() => setAlertType("delete")}
                    className="text-destructive focus:text-destructive"
                  >
                    {t("body.delete")}
                    <DropdownMenuShortcut
                      className={isRTL ? "mr-auto ml-0" : ""}
                    >
                      <Trash className="w-4 h-4" />
                    </DropdownMenuShortcut>
                  </DropdownMenuItem>
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-1 text-sm">
            {/* Email */}
            {data?.loginable?.email && (
              <div className="flex items-center gap-2 p-1 rounded-lg bg-muted/30 text-xs">
                <Mail className="w-3 h-3 text-muted-foreground flex-shrink-0" />
                <span
                  className={`flex-1 truncate ${
                    isDeleted ? "line-through opacity-50" : ""
                  }`}
                >
                  {data.loginable.email}
                </span>

                <Button
                  variant="ghost"
                  size="sm"
                  className="h-2 w-2 p-0"
                  onClick={() => copyToClipboard(data.loginable.email, "Email")}
                >
                  <Copy className="w-1 h-1" />
                </Button>
              </div>
            )}

            {/* Phone */}
            {data?.phone && (
              <div className="flex items-center gap-2 p-1 rounded-lg bg-muted/30 text-xs">
                <Phone className="w-3 h-3 text-muted-foreground flex-shrink-0" />
                <span
                  className={`flex-1 ${
                    isDeleted ? "line-through opacity-50" : ""
                  }`}
                >
                  {data.phone}
                </span>

                <Button
                  variant="ghost"
                  size="sm"
                  className="h-2 w-2 p-0"
                  onClick={() => copyToClipboard(data.phone, "Phone")}
                >
                  <Copy className="w-1 h-1" />
                </Button>
              </div>
            )}

            {/* Username */}
            {data?.loginable?.username && (
              <div className="flex items-center gap-2 p-1 rounded-lg bg-muted/30 text-xs">
                <User className="w-3 h-3 text-muted-foreground flex-shrink-0" />
                <span
                  className={`flex-1 truncate ${
                    isDeleted ? "line-through opacity-50" : ""
                  }`}
                >
                  {data.loginable.username}
                </span>

                <Button
                  variant="ghost"
                  size="sm"
                  className="h-2 w-2 p-0"
                  onClick={() =>
                    copyToClipboard(data.loginable.username, "Username")
                  }
                >
                  <Copy className="w-1 h-1" />
                </Button>
              </div>
            )}

            {/* Password */}
            {data?.password && (
              <div className="flex items-center gap-2 p-1 rounded-lg bg-muted/30 text-xs">
                <Key className="w-3 h-3 text-muted-foreground flex-shrink-0" />
                <span
                  className={`flex-1 font-mono text-xs ${
                    isDeleted ? "line-through opacity-50" : ""
                  }`}
                >
                  {showPassword ? data.password : maskPassword(data.password)}
                </span>
                <div className="flex gap-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-3 w-3 p-0"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="w-2 h-2" />
                    ) : (
                      <Eye className="w-2 h-2" />
                    )}
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-2 w-2 p-0"
                    onClick={() => copyToClipboard(data.password, "Password")}
                  >
                    <Copy className="w-1 h-1" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Edit Form Modal */}
      <CustomerOfCustomerForm
        open={editFormOpen}
        setOpen={setEditFormOpen}
        formData={{
          email: data?.loginable?.email,
          phone: data?.phone,
          fullname: data?.fullname,
          username: data?.loginable?.username,
          password: data?.password,
          status: data?.loginable?.status,
          customer_of_customer_id: data?.id,
        }}
        onEditSuccess={handleEditSuccess}
      />

      {/* Confirmation Alert Dialog */}
      <AlertDialog open={!!alertType} onOpenChange={() => setAlertType(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className={isRTL ? "text-right" : ""}>
              {alertContent?.title}
            </AlertDialogTitle>
            <AlertDialogDescription className={isRTL ? "text-right" : ""}>
              {alertContent?.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter dir={dir} className={isRTL ? "gap-2" : ""}>
            <AlertDialogCancel>{t("body.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={alertContent?.action}
              className={alertContent?.className}
            >
              {isPending ? (
                <LoadingSpinner className="h-4 w-4" />
              ) : (
                alertContent?.actionText
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
