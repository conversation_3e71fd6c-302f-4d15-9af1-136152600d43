import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { CustomCellRendererProps } from "ag-grid-react";
import { useState, type FunctionComponent } from "react";
import { useEditCustomerOfCustomer } from "../services/use-edit-customer-of-customer";
import { toast } from "sonner";
import { useDirection } from "@/hooks/useDirection";
import { LoadingSpinner } from "@/components/Common_UI/loading";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

export const StatusCell: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const { editCustomer, isPending } = useEditCustomerOfCustomer();
  const t = useTranslations("customer-of-customer");
  const [isOpen, setIsOpen] = useState(false);
  const { dir, isRTL } = useDirection();
  const router = useRouter();
  const status = data?.loginable?.status === "deactive" ? "active" : "deactive";

  const onLogout = (status: "active" | "deactive") => {
    editCustomer(
      {
        email: data?.loginable?.email,
        phone: data?.phone,
        fullname: data?.fullname,
        username: data?.loginable?.username,
        password: data?.password,
        status: status,
        customer_of_customer_id: data?.id,
      },
      {
        onSuccess: () => {
          toast.success(
            `Customer is ${
              status === "active" ? "Enable" : "Disable"
            } successfully!`
          );
          router.refresh();
          setIsOpen(false);
        },
        onError: () => {
          toast.error("Edition failed. Please try again.");
        },
      }
    );
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild disabled={data?.deleted_at}>
          <Badge
            className="capitalize cursor-pointer"
            variant={status === "deactive" ? "default" : "destructive"}
          >
            {status === "deactive" ? t("body.enable") : t("body.disable")}
          </Badge>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-24">
          <DropdownMenuGroup>
            {data?.loginable?.status === "deactive" ? (
              <DropdownMenuItem onClick={() => setIsOpen(true)}>
                {t("body.enable")}
              </DropdownMenuItem>
            ) : (
              <DropdownMenuItem onClick={() => setIsOpen(true)}>
                {t("body.disable")}
              </DropdownMenuItem>
            )}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className={`${isRTL ? "text-right" : ""}`}>
              {status === "deactive" ? t("body.disable") : t("body.enable")}{" "}
              {t("body.customer")}
            </AlertDialogTitle>
            <AlertDialogDescription className={`${isRTL ? "text-right" : ""}`}>
              {t(`body.${status}-description`)}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter dir={dir} className={`${isRTL ? "gap-2" : ""}`}>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={() => onLogout(status)}>
              {isPending ? (
                <LoadingSpinner className="h-5 w-5" />
              ) : status === "deactive" ? (
                t("body.disable")
              ) : (
                t("body.enable")
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
