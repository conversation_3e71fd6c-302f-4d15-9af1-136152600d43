import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuShortcut, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { CustomCellRendererProps } from 'ag-grid-react'
import { Edit2Icon, EllipsisVertical, Trash } from 'lucide-react'
import React, { FunctionComponent, useState } from 'react'
import CustomerOfCustomerForm from '../customer-form'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'
import { useDirection } from '@/hooks/useDirection'
import { useDeleteCustomerOfCustomer } from '../services/use-delete-customer-of-customer'
import { toast } from 'sonner'
import { LoadingSpinner } from '@/components/Common_UI/loading'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl'

export const OperationCell: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const [open, setOpen] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const { dir, isRTL } = useDirection();
  const t = useTranslations("customer-of-customer");
  const router = useRouter()
  const { deleteCustomer, isPending } = useDeleteCustomerOfCustomer();
  const onLogout = () => {
    deleteCustomer([data.id], {
      onSuccess: () => {
        router.refresh()
        toast.success('Customer of customer deleted successfully!');
        setIsOpen(false);
      },
      onError: () => {
        toast.error('Deletion failed. Please try again.');
      }
    })
  }
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild disabled={data?.deleted_at}>
          <Button variant="link" size={'icon'}><EllipsisVertical className="w-16 h-16" /></Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-24">
          <DropdownMenuGroup>
            <DropdownMenuItem onClick={() => setOpen(true)}>
              {t('body.edit')}
              <DropdownMenuShortcut className={`${isRTL ? "mr-auto ml-0" : ""}`}><Edit2Icon className="w-4 h-4" /></DropdownMenuShortcut>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setIsOpen(true)}>
              {t('body.delete')}
              <DropdownMenuShortcut className={`${isRTL ? "mr-auto ml-0" : ""}`}><Trash className="w-4 h-4" /></DropdownMenuShortcut>
            </DropdownMenuItem>
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
      <CustomerOfCustomerForm open={open} setOpen={setOpen}
        formData={{
          email: data?.loginable?.email,
          phone: data?.phone,
          fullname: data?.fullname,
          username: data?.loginable?.username,
          password: data?.password,
          status: data?.loginable?.status,
          customer_of_customer_id: data?.id
        }} />
      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className={`${isRTL ? "text-right" : ""}`}>
              {t('body.delete')}
            </AlertDialogTitle>
            <AlertDialogDescription className={`${isRTL ? "text-right" : ""}`}>
              {t('body.delete-description')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter dir={dir} className={`${isRTL ? "gap-2" : ""}`}>
            <AlertDialogCancel>{t('body.cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={onLogout} >
              {isPending ? <LoadingSpinner className="h-5 w-5" /> : t('body.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
