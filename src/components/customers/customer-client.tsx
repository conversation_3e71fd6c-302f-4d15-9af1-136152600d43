"use client";
import PaginationComponent from "@/components/Common_UI/use-paginition";
import React from "react";
import { useResponsive } from "@/hooks/use-mobile";
import { loadCustomerData } from "./services/customer-action";
import { CustomerDataTableMobile } from "./customer-data-table-mobile";
import { CustomerDataTable } from "./cell-renderers/customer-data-table";

interface CustomerPageClientProps {
  initialRecords: any;
  baseState: string;
  searchParams: any;
}

const CustomerPageClient: React.FC<CustomerPageClientProps> = ({
  initialRecords,
  baseState,
  searchParams,
}) => {
  const boundLoadMoreData = loadCustomerData.bind(
    null,
    baseState,
    searchParams
  );
  const { isMobile } = useResponsive();

  const initialParams = {
    search: searchParams?.search || "",
  };

  const componentKey = `${searchParams?.search || "no-search"}-${baseState}`;

  return (
    <>
      {isMobile ? (
        <div className="h-[calc(100vh-160px)] ">
          <CustomerDataTableMobile
            key={componentKey}
            records={initialRecords}
            onLoadMoreData={boundLoadMoreData}
            initialParams={initialParams}
          />
        </div>
      ) : (
        <>
          <div className="">
            <div className="h-[calc(100vh-188px)] ">
              <CustomerDataTable records={initialRecords} />
              <PaginationComponent
                count={initialRecords?.total || 0}
                pageSize={initialRecords?.per_page || 0}
              />
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default CustomerPageClient;
