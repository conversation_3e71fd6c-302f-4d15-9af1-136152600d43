// app/components/CustomerOfCustomerForm.tsx
"use client";
import React, { SetStateAction, useState } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { Label } from "../ui/label";
import { Input } from "../ui/input";
import Photo from "@/components/Common_UI/photo";

import { Card } from "../ui/card";
import { Button } from "../ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAddCustomerOfCustomer } from "./services/use-add-customer-of-customer";
import { z } from "zod";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { LoadingSpinner } from "../Common_UI/loading";
import { generateImageUrl } from "@/utils/generate-image-url";
import { useEditCustomerOfCustomer } from "./services/use-edit-customer-of-customer";
import { useRouter } from "next/navigation";
import { Eye, EyeOff } from "lucide-react";
import { useTranslations } from "next-intl";
import { useDirection } from "@/hooks/useDirection";

const FormFieldSchema = z.object({
  email: z.string().email("Invalid email").min(1, "Required"),
  phone: z.string().min(1, "Required"),
  photo: z.instanceof(File).optional(),
  fullname: z.string().min(1, "Required"),
  username: z.string().min(1, "Required"),
  password: z.string(),
  status: z.enum(["active", "deactive"]),
});
export type FormFields = z.infer<typeof FormFieldSchema>;

export default function CustomerOfCustomerForm({
  open,
  setOpen,
  formData,
  onEditSuccess,
  onAddSuccess,
}: {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  formData?: FormFields & { customer_of_customer_id?: number };
  onEditSuccess?: (updatedData: any) => void;
  onAddSuccess?: (newData: any) => void;
}) {
  const router = useRouter();
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const { addCustomer, isPending } = useAddCustomerOfCustomer();
  const { editCustomer, isPending: isPendingEdit } =
    useEditCustomerOfCustomer();
  const { customer_of_customer_id: customer_id, ...defultValues } =
    formData || ({} as FormFields & { customer_of_customer_id?: number });
  const t = useTranslations("customer-of-customer");
  const isEidtSession = Boolean(customer_id);
  const { isRTL } = useDirection();
  const form = useForm<FormFields>({
    resolver: zodResolver(FormFieldSchema),
    defaultValues: isEidtSession ? defultValues : {},
  });

  const handleImageCropped = (
    croppedImage: string,
    setCloseDialog: React.Dispatch<SetStateAction<boolean>>
  ) => {
    form.setValue("photo", generateImageUrl(croppedImage));
    setCloseDialog(false);
  };

  const onSubmit = async (
    data: FormFields & { customer_of_customer_id?: number }
  ) => {
    try {
      if (!isEidtSession) {
        addCustomer(data, {
          onSuccess: (newCustomerData) => {
            toast.success("Customer of customer added successfully!");
            setOpen(false);
            form.reset();

            // Call the callback to update AG-Grid
            if (onAddSuccess) {
              onAddSuccess(newCustomerData);
            }

            // Only refresh as fallback
            router.refresh();
          },
          onError: () => {
            toast.error("Addition failed. Please try again.");
          },
        });
      } else {
        data.customer_of_customer_id = customer_id;
        editCustomer(data, {
          onSuccess: () => {
            toast.success("Customer of customer edited successfully!");
            setOpen(false);
            form.reset();

            // Call the callback to update AG-Grid immediately
            if (onEditSuccess) {
              onEditSuccess(data); // Pass the form data since it contains the updated values
            }

            // Only refresh as fallback
            router.refresh();
          },
          onError: () => {
            toast.error("Edition failed. Please try again.");
          },
        });
      }
    } catch (error: any) {
      toast.error(
        "Operation failed. Please try again.",
        error?.response?.data?.message
      );
    }
  };

  const handleError = (error: any) => {
    toast.error("Operation failed. Please try again." + error);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen} key={"customer-form"}>
      <DialogContent className="className=w-full max-w-[768px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEidtSession ? t("body.edit-customer") : t("body.add-customer")}
          </DialogTitle>
        </DialogHeader>
        <Card className="shadow-none border-none p-2">
          <form
            onSubmit={form.handleSubmit(onSubmit, handleError)}
            className="flex flex-col gap-4"
          >
            <div className="flex justify-center relative">
              <Photo
                onImageCropped={handleImageCropped}
                defaultImage=" "
                isUpdating={false}
                isAsync={false}
              />
            </div>
            <div
              className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4"
              dir={isRTL ? "rtl" : "ltr"}
            >
              <div className="space-y-2">
                <Label htmlFor={"full_name"}>{t("body.fullname")}</Label>
                <Input
                  id={"full_name"}
                  type={"text"}
                  {...form.register("fullname")}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor={"email"}>{t("body.email")}</Label>
                <Input
                  id={"email"}
                  type={"text"}
                  {...form.register("email")}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor={"username"}>{t("body.username")}</Label>
                <Input
                  id={"username"}
                  type={"text"}
                  {...form.register("username")}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor={"password"}>{t("body.password")}</Label>
                <div className="relative">
                  <Input
                    id={"password"}
                    type={isVisible ? "text" : "password"}
                    autoComplete="current-password"
                    {...form.register("password")}
                  />
                  <Button
                    className="absolute inset-y-0 end-0 flex h-full w-9 text-foreground/80 hover:text-foreground/100"
                    type="button"
                    onClick={() => setIsVisible(!isVisible)}
                    aria-label={isVisible ? "Hide password" : "Show password"}
                    aria-pressed={isVisible}
                    aria-controls="password"
                    variant={"link"}
                  >
                    {isVisible ? (
                      <EyeOff size={16} strokeWidth={2} aria-hidden="true" />
                    ) : (
                      <Eye size={16} strokeWidth={2} aria-hidden="true" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor={"phone_number"}>{t("body.phone-number")}</Label>
                <Input
                  id={"phone_number"}
                  type={"text"}
                  {...form.register("phone")}
                />
              </div>
              <div className="space-y-2">
                <Controller
                  control={form.control}
                  name="status"
                  render={({ field: { onChange, value } }) => (
                    <>
                      <Label htmlFor={"status"}>{t("body.status")}</Label>
                      <Select
                        onValueChange={onChange}
                        defaultValue={value}
                        value={value}
                      >
                        <SelectTrigger>
                          <SelectValue
                            placeholder={t("body.status-placeholder")}
                          />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="active">
                            {t("body.active")}
                          </SelectItem>
                          <SelectItem value="deactive">
                            {t("body.deactive")}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </>
                  )}
                />
              </div>
            </div>
            <div
              className={`flex gap-2 justify-end ${
                isRTL ? "flex-row-reverse" : "flex-row"
              }`}
            >
              <Button
                type="button"
                onClick={() => {
                  setOpen(false);
                  form.reset();
                }}
                variant={"outline"}
              >
                {t("body.cancel")}
              </Button>
              <Button type="submit">
                {isPending || isPendingEdit ? (
                  <LoadingSpinner className="h-5 w-5" />
                ) : (
                  t("body.submit")
                )}
              </Button>
            </div>
          </form>
        </Card>
      </DialogContent>
    </Dialog>
  );
}
