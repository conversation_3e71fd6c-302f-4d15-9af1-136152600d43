"use client";
import React from "react";
import {
  Package,
  Truck,
  CheckCircle,
  Clock,
  Calendar,
  Search,
  ArrowRight,
  Building,
  Hash,
  Container,
  Eye,
  Ship,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import InputSearch from "@/components/Common_UI/InputSearch";
import { useSearchParams, useRouter } from "next/navigation";
import Image from "next/image";
import { BadgeVariant, CarState, Stage, StageInfo, VehicleData } from "./types";
import { useTranslations } from "next-intl";
import { Photos } from "../vehicles/custom-carousel";
import Link from "next/link";

interface VehicleTrackingPageProps {
  initialData: VehicleData | null;
  initialSearch: string;
  initialError: string;
  domainData: any;
}

const VehicleTrackingPage = ({
  initialData,
  initialSearch,
  initialError,
  domainData,
}: VehicleTrackingPageProps) => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const searchValue = searchParams.get("search") || "";
  const t = useTranslations("vehicle-tracking");

  const isGoogleImages =
    initialData?.auction_photos_link?.includes("drive.google.com") ||
    initialData?.photo_link?.includes("drive.google.com")
      ? true
      : false;
  const trackingData = searchValue === initialSearch ? initialData : null;
  const isError = !!initialError;

  const handleTracking = (
    shippingLine: string | undefined,
    containerNumber: string | undefined
  ) => {
    switch (shippingLine) {
      case "MAERSK":
        return `https://www.maersk.com/tracking/${containerNumber}`;
      case "ONE":
        return "https://ecomm.one-line.com/one-ecom/manage-shipment/cargo-tracking";
      case "MSC":
        return "https://www.msc.com/en/track-a-shipment?agencyPath=mwi";
      case "HMM":
        return "https://www.hmm21.com/e-service/general/trackNTrace/TrackNTrace.do";
      case "CMA CGM":
        return "https://www.cma-cgm.com/ebusiness/tracking";
      case "Hapag-Lloyd":
        return `https://www.hapag-lloyd.com/en/online-business/track/track-by-container-solution.html?container=${containerNumber}`;
      case "Yang Ming":
        return `https://e-solution.yangming.com/e-service/track_trace/track_trace_cargo_tracking.aspx`;
      case "Evergreen":
        return `https://ct.shipmentlink.com/servlet/TDB1_CargoTracking.do`;
      default:
        return "";
    }
  };

  const trackingUrl = handleTracking(
    initialData?.containers?.bookings?.vessels?.steamshiplines?.name,
    initialData?.containers?.container_number
  );

  const hasValidTracking = [
    "MAERSK",
    "ONE",
    "MSC",
    "HMM",
    "CMA CGM",
    "Hapag-Lloyd",
    "Yang Ming",
    "Evergreen",
  ].includes(
    initialData?.containers?.bookings?.vessels?.steamshiplines?.name || ""
  );

  const getTrackingStage = (vehicle: VehicleData): StageInfo => {
    const carState = vehicle.carstate;

    if (carState === "shipped") {
      if (vehicle.containers?.bookings?.eta_status === "actual") {
        return {
          stage: "delivered",
          substage: "arrived",
          label: "Delivered",
          variant: "destructive",
        };
      }
      return {
        stage: "shipped",
        substage: "shipped",
        label: "Shipped",
        variant: "outline",
      };
    }

    const stages: Record<CarState, StageInfo> = {
      auction_unpaid: {
        stage: "ordered",
        substage: "auction_unpaid",
        label: t("vehicle-progress.auction-unpaid"),
        variant: "secondary",
      },
      auction_paid: {
        stage: "ordered",
        substage: "auction_paid",
        label: t("vehicle-progress.auction-paid"),
        variant: "secondary",
      },
      pending_auction: {
        stage: "ordered",
        substage: "pending_auction",
        label: t("vehicle-progress.pending-auction"),
        variant: "secondary",
      },
      added_by_customer: {
        stage: "ordered",
        substage: "added_by_customer",
        label: t("vehicle-progress.added_by_customer"),
        variant: "secondary",
      },
      on_the_way: {
        stage: "process",
        substage: "on_the_way",
        label: t("vehicle-progress.on-the-way"),
        variant: "default",
      },
      on_hand_with_title: {
        stage: "process",
        substage: "on_hand_with_title",
        label: t("vehicle-progress.on-hand-with-title"),
        variant: "default",
      },
      on_hand_no_title: {
        stage: "process",
        substage: "on_hand_no_title",
        label: t("vehicle-progress.on-hand-no-title"),
        variant: "default",
      },
      pending_on_the_way: {
        stage: "process",
        substage: "pending_on_the_way",
        label: t("vehicle-progress.pending-picked"),
        variant: "default",
      },
      shipped: {
        stage: "shipped",
        substage: "shipped",
        label: t("vehicle-progress.shipped"),
        variant: "outline",
      },
      at_port: {
        stage: "shipped",
        substage: "at_port",
        label: t("vehicle-progress.at-port"),
        variant: "outline",
      },
      arrived: {
        stage: "delivered",
        substage: "arrived",
        label: t("vehicle-progress.delivered"),
        variant: "destructive",
      },
      archived: {
        stage: "delivered",
        substage: "archived",
        label: t("vehicle-progress.completed"),
        variant: "destructive",
      },
      pending: {
        stage: "process",
        substage: "pending",
        label: t("vehicle-progress.pending"),
        variant: "default",
      },
      cost_analysis: {
        stage: "process",
        substage: "cost_analysis",
        label: t("vehicle-progress.cost-analysis"),
        variant: "default",
      },
      datelines: {
        stage: "process",
        substage: "datelines",
        label: t("vehicle-progress.datelines"),
        variant: "default",
      },
    };

    return (
      stages[carState] || {
        stage: "ordered" as Stage,
        substage: carState,
        label: carState
          .replace(/_/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase()),
        variant: "secondary" as BadgeVariant,
      }
    );
  };

  const TrackingProgress = ({ currentStage }: { currentStage: Stage }) => {
    const stages = [
      {
        key: "ordered",
        label: t("vehicle-progress.ordered"),
        icon: Package,
        description: t("vehicle-progress.order-placed"),
      },
      {
        key: "process",
        label: t("vehicle-progress.processing"),
        icon: Clock,
        description: t("vehicle-progress.being-prepared"),
      },
      {
        key: "shipped",
        label: t("vehicle-progress.shipped"),
        icon: Truck,
        description: t("vehicle-progress.on-the-way"),
      },
      {
        key: "delivered",
        label: t("vehicle-progress.delivered"),
        icon: CheckCircle,
        description: t("vehicle-progress.completed"),
      },
    ];

    const currentIndex = stages.findIndex(
      (stage) => stage.key === currentStage
    );
    const activeIndex = currentIndex !== -1 ? currentIndex : 0;

    return (
      <Card className="w-full max-w-6xl mx-auto shadow-lg border-0 bg-gradient-to-br from-background to-background/80 backdrop-blur-sm">
        <CardHeader className="text-center px-4 sm:px-6">
          <CardTitle className="text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
            {t("vehicle-progress.title")}
          </CardTitle>
          <CardDescription className="text-base sm:text-lg mt-2">
            {t("vehicle-progress.description")}
          </CardDescription>
        </CardHeader>
        <CardContent className="pb-6 sm:pb-10 px-4 sm:px-6">
          <div className="relative">
            {/* Progress Line - Hidden on mobile, visible on tablet+ */}
            <div className="hidden sm:block absolute top-8 left-0 right-0 h-1 bg-gradient-to-r from-muted via-muted to-muted mx-8 lg:mx-16">
              <div
                className="h-full bg-gradient-to-r from-primary to-primary/80 transition-all duration-1000 ease-out rounded-full shadow-sm"
                style={{
                  width:
                    stages.length > 1
                      ? `${(activeIndex / (stages.length - 1)) * 100}%`
                      : "0%",
                }}
              />
            </div>

            {/* Mobile: Vertical Layout, Desktop: Horizontal Layout */}
            <div className="sm:hidden space-y-4">
              {stages.map((stage, index) => {
                const Icon = stage.icon;
                const isActive = index <= activeIndex;
                const isCurrent = index === activeIndex;
                const isCompleted = index < activeIndex;

                return (
                  <div
                    key={stage.key}
                    className="flex items-center gap-4 p-4 rounded-lg border border-border/30 bg-background/50"
                  >
                    <div
                      className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-500 border-2 ${
                        isCompleted
                          ? "bg-primary text-primary-foreground border-primary"
                          : isCurrent
                          ? "bg-primary text-primary-foreground border-primary animate-pulse"
                          : isActive
                          ? "bg-primary text-primary-foreground border-primary"
                          : "bg-background text-muted-foreground border-border"
                      }`}
                    >
                      <Icon size={18} />
                    </div>
                    <div className="flex-1">
                      <div className="font-semibold text-sm">{stage.label}</div>
                      <div className="text-xs text-muted-foreground mt-1">
                        {stage.description}
                      </div>
                    </div>
                    {isCurrent && (
                      <div className="w-2 h-2 bg-primary rounded-full animate-ping"></div>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Desktop: Horizontal Layout */}
            <div className="hidden sm:flex justify-between items-start px-4 lg:px-8">
              {stages.map((stage, index) => {
                const Icon = stage.icon;
                const isActive = index <= activeIndex;
                const isCurrent = index === activeIndex;
                const isCompleted = index < activeIndex;

                return (
                  <div
                    key={stage.key}
                    className="flex flex-col items-center relative z-10 group max-w-[200px]"
                  >
                    <div
                      className={`w-12 h-12 sm:w-16 sm:h-16 rounded-full flex items-center justify-center transition-all duration-500 border-4 shadow-lg ${
                        isCompleted
                          ? "bg-primary text-primary-foreground border-primary shadow-primary/20 transform scale-105"
                          : isCurrent
                          ? "bg-primary text-primary-foreground border-primary shadow-primary/30 transform scale-110 animate-pulse"
                          : isActive
                          ? "bg-primary text-primary-foreground border-primary shadow-primary/20"
                          : "bg-background text-muted-foreground border-border hover:border-primary/50 hover:shadow-md"
                      }`}
                    >
                      <Icon
                        size={16}
                        className={`sm:w-6 sm:h-6 ${
                          isCompleted ? "animate-bounce" : ""
                        }`}
                      />
                    </div>

                    <div className="mt-3 sm:mt-4 text-center space-y-1">
                      <span
                        className={`block text-xs sm:text-sm lg:text-base font-semibold ${
                          isActive ? "text-foreground" : "text-muted-foreground"
                        }`}
                      >
                        {stage.label}
                      </span>
                      <span
                        className={`hidden lg:block text-xs text-muted-foreground ${
                          isActive ? "opacity-90" : "opacity-60"
                        }`}
                      >
                        {stage.description}
                      </span>
                    </div>

                    {isCurrent && (
                      <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2">
                        <div className="w-2 h-2 bg-primary rounded-full animate-ping"></div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const VehicleDetails = ({ vehicle }: { vehicle: VehicleData }) => {
    const InfoCard = ({
      icon: Icon,
      title,
      value,
      date,
    }: {
      icon: any;
      title: string;
      value?: string | null;
      date?: string | null;
    }) =>
      value || date ? (
        <div className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg bg-muted/10 border border-border/30">
          <div className="flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-primary/10 flex items-center justify-center">
            <Icon size={14} className="sm:w-4 sm:h-4 text-primary" />
          </div>
          <div className="min-w-0 flex-1">
            <p className="text-xs text-muted-foreground font-medium truncate">
              {title}
            </p>
            <p className="text-xs sm:text-sm font-medium truncate">
              {date ? new Date(date).toLocaleDateString() : value}
            </p>
          </div>
        </div>
      ) : null;

    return (
      <Card className="w-full max-w-6xl mx-auto shadow-xl border-0 overflow-hidden">
        <CardContent className="p-0">
          <div className="grid lg:grid-cols-5 min-h-[300px] lg:min-h-[400px]">
            {/* Image Section */}
            <div className="lg:col-span-2 relative">
              <div className="h-64 sm:h-80 lg:h-full flex items-center justify-center relative overflow-hidden p-4">
                {!isGoogleImages ? (
                  <Photos
                    url="/v2/vehicles/publicVehicleImages"
                    vehicleId={vehicle.id}
                    vin={vehicle.vin}
                    width="100%"
                    height="100%"
                    carouselHeight="100%"
                    maxWidth="395px"
                  />
                ) : (
                  <div className="flex flex-col gap-4 w-full max-w-xs">
                    <Link
                      href={`${
                        initialData?.photo_link ??
                        initialData?.auction_photos_link
                      }`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-full px-3 py-2 sm:px-4 sm:py-3 bg-primary hover:bg-primary/90 rounded-xl flex items-center justify-center gap-2 sm:gap-3 font-semibold text-sm sm:text-base transition-all shadow-md hover:shadow-lg"
                    >
                      <Eye size={18} className="sm:w-5 sm:h-5 stroke-[2]" />
                      <span className="text-center">
                        View Images on Google Drive
                      </span>
                    </Link>
                  </div>
                )}
              </div>
            </div>

            {/* Details Section */}
            <div className="lg:col-span-3 p-4 sm:p-6 space-y-4 sm:space-y-5">
              <div className="space-y-3">
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
                  <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-foreground leading-tight">
                    {vehicle.year} {vehicle.make} {vehicle.model}{" "}
                    {vehicle.color}
                  </h2>

                  {(() => {
                    const hasContainer =
                      vehicle?.containers?.container_number != null &&
                      String(vehicle.containers?.container_number).trim() !==
                        "";

                    const hasValidUrl =
                      trackingUrl != null && String(trackingUrl).trim() !== "";

                    const showTrackingLink =
                      hasContainer && hasValidUrl && hasValidTracking;

                    return (
                      <div className="flex-shrink-0">
                        {showTrackingLink ? (
                          <Link href={trackingUrl} target="_blank">
                            <Button
                              variant="outline"
                              size="sm"
                              className="bg-blue-500/10 dark:bg-blue-500/10 text-blue-500 dark:text-blue-500 h-8 w-8 sm:h-10 sm:w-10"
                            >
                              <Ship className="w-3 h-3 sm:w-4 sm:h-4" />
                            </Button>
                          </Link>
                        ) : (
                          <Button
                            variant="outline"
                            size="sm"
                            className="bg-red-500/10 dark:bg-red-500/10 text-red-500 dark:text-red-500 h-8 w-8 sm:h-10 sm:w-10"
                            disabled
                          >
                            <Ship className="w-3 h-3 sm:w-4 sm:h-4 opacity-50" />
                          </Button>
                        )}
                      </div>
                    );
                  })()}
                </div>

                <div className="flex flex-wrap gap-2 text-xs sm:text-sm text-muted-foreground">
                  <div className="flex items-center gap-1 sm:gap-2 bg-muted/20 px-2 py-1 sm:px-3 sm:py-1.5 rounded-full">
                    <Hash size={12} className="sm:w-3.5 sm:h-3.5" />
                    <span className="truncate">VIN: {vehicle.vin}</span>
                  </div>
                  <div className="flex items-center gap-1 sm:gap-2 bg-muted/20 px-2 py-1 sm:px-3 sm:py-1.5 rounded-full">
                    <Package size={12} className="sm:w-3.5 sm:h-3.5" />
                    <span>Lot: {vehicle.lot_number}</span>
                  </div>
                  {vehicle?.containers?.container_number && (
                    <div className="flex items-center gap-1 sm:gap-2 bg-muted/20 px-2 py-1 sm:px-3 sm:py-1.5 rounded-full">
                      <Container size={12} className="sm:w-3.5 sm:h-3.5" />
                      <span className="truncate">
                        CN: {vehicle.containers.container_number}
                      </span>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-3 sm:space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                  <InfoCard
                    icon={Building}
                    title={t("vehicle-info.found.auction-name")}
                    value={vehicle?.auction_name?.toUpperCase()}
                  />
                  <InfoCard
                    icon={Building}
                    title={t("vehicle-info.found.auction-city")}
                    value={
                      vehicle?.auction_city ||
                      vehicle?.auction_cities?.city_name
                    }
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-3">
                  <InfoCard
                    icon={Calendar}
                    title={t("vehicle-info.found.purchased")}
                    date={vehicle?.purchased_at}
                  />
                  <InfoCard
                    icon={Truck}
                    title={t("vehicle-info.found.pickup")}
                    date={vehicle?.pickup_date}
                  />
                  <InfoCard
                    icon={CheckCircle}
                    title={t("vehicle-info.found.delivery")}
                    date={vehicle?.deliver_date}
                  />
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-3">
                  <InfoCard
                    icon={Calendar}
                    title={t("vehicle-info.found.arrival-date")}
                    date={vehicle?.containers?.bookings?.eta}
                  />
                  <InfoCard
                    icon={Calendar}
                    title={t("vehicle-info.found.loading-date")}
                    date={vehicle?.containers?.loading_date}
                  />
                  <InfoCard
                    icon={Calendar}
                    title={t("vehicle-info.found.departure-date")}
                    date={vehicle?.containers?.bookings?.vessels?.etd}
                  />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="min-h-screen dark:bg-gray-800/80">
      <div className="container mx-auto px-4 py-6 sm:py-10 lg:py-16">
        {/* Header Card */}
        <Card className="w-full max-w-6xl mx-auto mb-6 sm:mb-8 shadow-xl border-0 bg-background">
          <CardHeader className="text-center px-4 sm:px-6">
            <div className="flex justify-center">
              <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-primary/10 flex items-center justify-center">
                <Image
                  src={domainData?.logo || "/alargan.jpg"}
                  alt="Logo"
                  width={40}
                  height={40}
                  className="sm:w-[50px] sm:h-[50px] rounded-full"
                />
              </div>
            </div>
            <CardTitle className="text-xl sm:text-2xl mt-3 sm:mt-4">
              {t("title")}
            </CardTitle>
            <CardDescription className="text-sm sm:text-base">
              {t("description")}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 sm:space-y-6 pt-2 sm:pt-4 px-4 sm:px-6">
            <div className="max-w-2xl mx-auto">
              <InputSearch
                type="text"
                fieldName="search"
                placeholder={t("search.placeholder")}
                isClose={true}
                debounceMs={500}
              />
            </div>

            {isError && (
              <Alert
                variant="destructive"
                className="border-l-4 border-l-destructive"
              >
                <AlertDescription className="text-sm sm:text-base">
                  {initialError}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Main Content */}
        {trackingData ? (
          <div className="space-y-6 sm:space-y-8 animate-in fade-in duration-500">
            <TrackingProgress
              currentStage={getTrackingStage(trackingData).stage}
            />
            <VehicleDetails vehicle={trackingData} />
          </div>
        ) : searchValue ? (
          <div className="w-full max-w-6xl mx-auto animate-in fade-in duration-500 mt-6 sm:mt-10">
            <Card className="text-center border-2 border-dashed border-border bg-muted/10">
              <CardContent className="py-8 sm:py-12 lg:py-16 px-4 sm:px-6">
                <div className="max-w-md mx-auto space-y-4 sm:space-y-5">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-muted rounded-full flex items-center justify-center">
                    <Search
                      size={24}
                      className="sm:w-8 sm:h-8 text-muted-foreground"
                    />
                  </div>
                  <h3 className="text-lg sm:text-xl lg:text-2xl font-semibold">
                    {t("vehicle-info.not-found-data.title")}
                  </h3>
                  <p className="text-sm sm:text-base text-muted-foreground px-2 sm:px-4">
                    {t("vehicle-info.not-found-data.description")}
                  </p>
                  <Button
                    variant="outline"
                    className="mt-4 sm:mt-6"
                    onClick={() => router.push("?search=")}
                  >
                    <ArrowRight size={14} className="sm:w-4 sm:h-4 mr-2" />
                    {t("vehicle-info.not-found-data.btn")}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="w-full max-w-6xl mx-auto animate-in fade-in duration-500 mt-6 sm:mt-10">
            <Card className="text-center border-2 border-dashed border-border bg-muted/10">
              <CardContent className="py-8 sm:py-12 lg:py-16 px-4 sm:px-6">
                <div className="max-w-md mx-auto space-y-4 sm:space-y-5">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                    <Package size={24} className="sm:w-8 sm:h-8 text-primary" />
                  </div>
                  <h3 className="text-lg sm:text-xl lg:text-2xl font-semibold">
                    {t("vehicle-progress.title")}
                  </h3>
                  <p className="text-sm sm:text-base text-muted-foreground">
                    {t("vehicle-progress.description")}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default VehicleTrackingPage;
