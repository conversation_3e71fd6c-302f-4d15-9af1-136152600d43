"use server";
import axios from "@/utils/axios-server";
import { VehicleData } from "./types";

export interface ApiResponse {
  result: boolean;
  data: VehicleData[];
}

export default async function getTrackingData(
  searchValue: string,
  customer_id: string
): Promise<ApiResponse> {
  try {
    const params = new URLSearchParams();
    params.append("search", searchValue);
    params.append("customer_id", customer_id);

    const response = await axios.get(
      `/v2/vehicles/tracking?${params.toString()}`
    );

    if (response.status === 404) {
      throw new Error("Vehicle not found. Please check your tracking number.");
    } else if (response.status === 401) {
      throw new Error("Unauthorized. Please log in again.");
    }

    return response.data;
  } catch (err: any) {
    console.error("API Error:", err);
    throw new Error(
      err.response?.data?.message ||
        err.message ||
        "An error occurred while searching for the vehicle."
    );
  }
}
