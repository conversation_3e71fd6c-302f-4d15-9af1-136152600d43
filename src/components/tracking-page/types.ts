export type CarState =
  | "auction_unpaid"
  | "auction_paid"
  | "pending_auction"
  | "added_by_customer"
  | "on_the_way"
  | "on_hand_with_title"
  | "on_hand_no_title"
  | "pending_on_the_way"
  | "shipped"
  | "at_port"
  | "arrived"
  | "archived"
  | "pending"
  | "cost_analysis"
  | "datelines";

export type Stage = "ordered" | "process" | "shipped" | "delivered";

export type BadgeVariant = "default" | "secondary" | "destructive" | "outline";

export interface VehicleData {
  id: number;
  vin: string;
  lot_number: string;
  year: string;
  make: string;
  model: string;
  color: string;
  carstate: CarState;
  auction_name: string;
  auction_city: string;
  auction_city_id: number;
  auction_cities: {
    id: number;
    city_name: string;
  };
  purchased_at: string;
  pickup_date: string;
  deliver_date: string;
  yard_location: string;
  customer_remark: string;
  status_changed_at: string;
  cover_photo: string | null;
  price: number;
  containers: {
    container_number: string;
    loading_date: string;
    bookings: {
      eta_status: string;
      eta: string;
      vessels: {
        etd: string;
        steamshiplines: {
          name: string;
        };
      };
    };
  };
  auction_photos_link: string;
  photo_link: string;
}

export interface StageInfo {
  stage: Stage;
  substage: string;
  label: string;
  variant: BadgeVariant;
}
