"use client";
import * as React from "react";
import { Sidebar } from "@/components/ui/sidebar";
import { RotateCcw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import FilterCollapse from "@/components/vehicles/filter-collapse";
import { addDays } from "date-fns";
import { DateRange } from "react-day-picker";
import { CustomDateRangePicker } from "@/components/Common_UI/customer-range-date";
import { useRouter, useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { useProgress } from "@bprogress/next";

export function FilterModel({}: React.ComponentProps<typeof Sidebar> & {}) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { start } = useProgress();
  const params = new URLSearchParams(Array.from(searchParams.entries()));
  const t = useTranslations("filter-modal");
  const [openCollapse, toggleCollapse] = React.useState<string[]>([]);
  const clearSelectionsRef = React.useRef<(() => void) | null>(null);
  const [date, setDate] = React.useState<DateRange>(function () {
    const from = params.get("from");
    const to = params.get("to");
    return {
      from: from ? new Date(from) : new Date(2023, 0, 20),
      to: to ? new Date(to) : addDays(new Date(2023, 0, 20), 20),
    };
  });
  const handleCreatedAt = (selected: DateRange) => {
    setDate(selected);
    if (selected.from && selected.to) {
      params.delete("from_created_at");
      params.delete("to_created_at");
      params.append(
        "from_created_at",
        selected.from.toISOString().split("T")[0]
      );
      params.append("to_created_at", selected.to.toISOString().split("T")[0]);
      start();
      router.push(`?${params}`);
    }
  };
  const handleUpdatedAt = (selected: DateRange) => {
    setDate(selected);
    if (selected.from && selected.to) {
      params.delete("from_updated_at");
      params.delete("to_updated_at");
      params.append(
        "from_updated_at",
        selected.from.toISOString().split("T")[0]
      );
      params.append("to_updated_at", selected.to.toISOString().split("T")[0]);
      start();
      router.push(`?${params}`);
    }
  };
  const handleClearFilters = () => {
    params.delete("from_created_at");
    params.delete("to_created_at");
    params.delete("from_updated_at");
    params.delete("to_updated_at");
    start();
    router.push(`?${params}`);
  };
  return (
    <div className="mt-1 p-2 w-72">
      <div className="flex flex-row pl-3 pt-2 justify-between py-2">
        <h2 className="text-lg font-medium">{t("filters")}</h2>
        <Button
          onClick={() => {
            clearSelectionsRef.current?.();
            handleClearFilters();
          }}
          variant={"link"}
          size={"icon"}
          className="clear-filters"
        >
          <RotateCcw />
        </Button>
      </div>
      <div className="flex flex-col p-2 gap-2">
        <FilterCollapse
          label={t("payment-filter-modal.date-range.label")}
          isOpen={openCollapse?.includes("Date Range")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("Date Range")
                ? prev.filter((item) => item !== "Date Range")
                : [...prev, "Date Range"]
            )
          }
        >
          <div className="flex flex-col gap-4 pt-3">
            <Label htmlFor="created_at">
              {t("payment-filter-modal.date-range.created-at")}
            </Label>
            <CustomDateRangePicker
              id="created_at"
              date={date}
              onChange={(selected) => handleCreatedAt(selected as DateRange)}
            />
            <Label htmlFor="updated_at">
              {t("payment-filter-modal.date-range.updated-at")}
            </Label>
            <CustomDateRangePicker
              id="updated_at"
              date={date}
              onChange={(selected) => handleUpdatedAt(selected as DateRange)}
            />
          </div>
        </FilterCollapse>
      </div>
    </div>
  );
}
