"use server";

import axios from "@/utils/axios-server";

type PaymentParamType = {
  page: number;
  per_page: number;
  search: string;
  exactMatch: boolean;
  filterData: string;
};

export async function getBuyerNumber({ params }: { params: PaymentParamType }) {
  try {
    const response = await axios(`/v2/buyer-numbers-v2`, {
      params: { ...params },
    });

    return response.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message);
  }
}
