"use client";
import React, { useMemo } from "react";
import { ColDef } from "ag-grid-community";
import { useTranslations } from "next-intl";
import { getBuyerNumber } from "./buyer-number-service";
import { BuyerNumberType } from "../cell-renderers/buyer-number-data-table";
import { ExportModal } from "@/components/Common_UI/export-modal";
import { toast } from "sonner";

type BuyerNumberExportProps = {
  records: {
    page: number;
    per_page: number;
    total: number;
    data: BuyerNumberType[];
  };
};

export default function BuyerNumberExport({ records }: BuyerNumberExportProps) {
  const t = useTranslations("export-modal");

  // Column definitions for AG Grid
  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        field: `buyer_number`,
        headerName: "Buyer Number",
      },
    ],
    []
  );

  // Function to fetch all auction payment data
  const fetchAllBuyerNumber = async (): Promise<BuyerNumberType[]> => {
    const response = await getBuyerNumber({
      params: {
        page: 1,
        per_page: records.total,
        search: "",
        exactMatch: false,
        filterData: "",
      },
    });
    return response.data;
  };
  // Translations for the export modal
  const exportTranslations = {
    title: t("title"),
    exportData: t("sub-title"),
    subTitle: t("sub-title"),
    currentData: t("current-data"),
    allData: t("all-data"),
    cancel: t("cancel"),
    export: t("export"),
  };

  return (
    <ExportModal
      columnDefs={colDefs}
      currentData={records.data}
      exportFileName="Buyer Number Data"
      fetchAllData={fetchAllBuyerNumber}
      totalItems={records.total}
      translations={exportTranslations}
      triggerButtonPosition="relative"
      onExportSuccess={() => toast("Buyer Number Export Completed")}
    />
  );
}
