"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";

import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import {
  type FunctionComponent,
  RefObject,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import { BuyerNumberRenderer } from "./BuyerNumberRenderer";
import useSidebarConfig from "../sidebarConfig";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);
export type BuyerNumberType = {
  id: number;
  buyer_number: number;
};

type Props = {
  gridTheme?: string;
  isDarkMode?: boolean;
  records: {
    page: number;
    per_page: number;
    total: number;
    data: BuyerNumberType[];
  } | null;
  gridRefProps?: RefObject<AgGridReact | null>;
  exportColDefs?: ColDef[];
};

const paginationPageSizeSelector = [5, 10, 20];

export const BuyerNumberDataTable: FunctionComponent<Props> = ({
  records,
  gridRefProps,
  exportColDefs,
}: Props) => {
  const gridRef = useRef<AgGridReact>(null);
  const t = useTranslations("buyer-number-datatable");
  const sidebarConfig = useSidebarConfig();
  const [colDefs] = useState<ColDef[]>([
    {
      headerName: "#",
      cellRenderer: (params: any) => {
        return !params.node?.rowIndex ? 1 : params.node?.rowIndex + 1;
      },
      minWidth: 40,
      maxWidth: 40,
    },
    {
      field: "buyer_number",
      headerName: t("header.buyer-number"),
      cellDataType: "text",
      cellRenderer: BuyerNumberRenderer,
      minWidth: 170,
    },
  ]);

  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
    }),
    []
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  const [quickFilterText] = useState<string>();

  const selectionColumnDef = useMemo(() => {
    return {
      minWidth: 44,
    };
  }, []);
  const { isRTL } = useDirection();

  return (
    <>
      <AgGridDataTable
        enableRtl={isRTL ? true : false}
        ref={gridRefProps || gridRef}
        selectionColumnDef={selectionColumnDef}
        columnDefs={exportColDefs || colDefs}
        rowData={records?.data || []}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        pagination={false}
        paginationPageSize={records?.per_page || 20}
        paginationPageSizeSelector={paginationPageSizeSelector}
        masterDetail
        detailCellRendererParams={{ t }}
        quickFilterText={quickFilterText}
        colResizeDefault="shift"
        headerHeight={60}
        sideBar={sidebarConfig}
        detailRowHeight={328}
      />
    </>
  );
};
