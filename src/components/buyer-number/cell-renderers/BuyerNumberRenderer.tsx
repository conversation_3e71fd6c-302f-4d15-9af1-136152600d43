import type { CustomCellRendererProps } from "ag-grid-react";
import { type FunctionComponent } from "react";

export const BuyerNumberRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data }) => {
  return (
    <div className="flex items-center h-full gap-2 group">
      <div className="flex flex-col justify-center h-full select-text leading-5 flex-1">
        <div className="flex items-center gap-1.5">
          <div className="w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
            {data?.buyer_number}
          </div>
        </div>
      </div>
    </div>
  );
};
