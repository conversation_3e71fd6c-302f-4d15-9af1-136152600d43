"use client";
import { useMemo, useRef } from "react";
import { formatData } from "./fomat-data";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  ColDef,
  ColGroupDef,
  CsvExportModule,
  ModuleRegistry,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import CenterHeader from "./CenterHeader";
import useSidebarConfig from "../sidebarConfig";
import { AgGridReact } from "ag-grid-react";
import { useTranslations } from "next-intl";
import DownloadButton from "./get-pdf-download";
import { useDirection } from "@/hooks/useDirection";
ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);
const RATE_LABELS: Record<string, string> = {
  rate_20ft: "20ft",
  rate_40hc: "40HC",
  rate_45hc: "45HC",
  rate_sedan: "Sedan",
  rate_suv: "SUV",
  rate_suv_dismantle: "SUV Dismantle",
  rate_sedan_dismantle: "Sedan Dismantle",
  rate_mix: "Mix",
};

export const ShippingRateTable = ({ data }: { data?: any[] }) => {
  const gridRef = useRef<AgGridReact>(null);
  const sidebarConfig = useSidebarConfig();
  const t = useTranslations("shipping-rate-datatable");
  const {
    destinations,
    locationMap,
    effectiveDate,
    isEffectiveDateTheSame,
    rate_apply_Type,
  } = formatData(data);
  const { isRTL } = useDirection();
  const currencyFormatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 0,
  });

  const columnDefs = useMemo<ColDef[] | ColGroupDef[]>(() => {
    const polColumn: ColGroupDef = {
      headerName: t("destination"),
      marryChildren: true,
      children: [
        {
          headerName: t("Shiplines"),
          marryChildren: true,
          children: [
            {
              headerName: t("Equipment"),
              field: "location",
              pinned: "left",
              minWidth: 160,
            },
          ],
        },
      ],
    };

    const cols: any[] = [polColumn];

    const destinationColumns: ColGroupDef[] = destinations.map((dest: any) => ({
      headerName: dest.destination.name,
      marryChildren: true,
      children: [] as ColGroupDef[],
      minWidth: 160,
      headerGroupComponent: CenterHeader,
    }));

    destinations.forEach((dest: any, destIndex: number) => {
      const shiplineColumns = dest.shiplines.map((ship: any) => ({
        headerName: ship.shipline.name,
        marryChildren: true,
        children: [] as any[],
      }));

      dest.shiplines.forEach((ship: any, shipIndex: number) => {
        Object.keys(ship.rates).forEach((rateKey) => {
          const rateLabel = RATE_LABELS[rateKey as keyof typeof RATE_LABELS];
          shiplineColumns[shipIndex].children.push({
            headerName: rateLabel,
            field: `dest_${dest.destination.id}_ship_${ship.shipline.id}_rate_${rateKey}`,
            minWidth: 100,
            valueFormatter: (params: any) => {
              return params.value ? currencyFormatter.format(params.value) : "";
            },
          });
        });
      });

      destinationColumns[destIndex].children = shiplineColumns;
    });

    return [...cols, ...destinationColumns];
  }, [destinations]);

  const rowData = useMemo(() => {
    const rows: any[] = [];

    Array.from(locationMap, (arr) => arr[1]).forEach((location) => {
      const row: any = { location: location.name };

      destinations.forEach((dest: any) => {
        dest.shiplines.forEach((ship: any) => {
          Object.keys(ship.rates).forEach((rateKey) => {
            const value =
              location.rates[
                `dest(${dest.destination.id}).loc(${location.id}).ship(${ship.shipline.id}).rate(${rateKey})`
              ]?.current;

            row[
              `dest_${dest.destination.id}_ship_${ship.shipline.id}_rate_${rateKey}`
            ] = value;
          });
        });
      });

      rows.push(row);
    });

    return rows;
  }, [locationMap, destinations]);

  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
      autoHeight: true,
      flex: 1,
      filter: true,
    }),
    []
  );
  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  return (
    <>
      <div className="flex flex-col md:flex-row items-center justify-between mb-2">
        <h2 className="hidden md:text-2xl lg:text-3xl font-bold tracking-tight md:block">
          {t("title")}
        </h2>
        <div className="flex items-center space-x-2 pr-3">
          {isEffectiveDateTheSame && (
            <div className=" text-[#22c55e] inline-block">
              Eff. Date <span className="text-xs ">({rate_apply_Type})</span>:
              <div className="px-1 inline-block">{effectiveDate}</div>
            </div>
          )}
          <DownloadButton />
        </div>
      </div>
      <AgGridDataTable
        enableRtl={isRTL ? true : false}
        columnDefs={columnDefs}
        rowData={rowData}
        ref={gridRef}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        sideBar={sidebarConfig}
        headerHeight={40}
        rowHeight={40}
        rowClassRules={{
          "row-even": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 === 0
                ? false
                : true
              : true;
          },
          "row-odd": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 !== 0
                ? false
                : true
              : true;
          },
        }}
      />
    </>
  );
};
