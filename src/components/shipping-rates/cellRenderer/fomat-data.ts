import { format, isSameDay, parseISO } from "date-fns";
const RATE_APPLY_DATE_LABLES: Record<string, string> = {
  purchase_date: "Purchase Date",
  loading_date: "Loading Date",
  etd_date: "ETD Date",
  ingate_date: "Ingate Date",
};

export function formatData(Data?: any) {
  const locationMap = new Map();
  const destinations: any = [];
  const isEffectiveDateTheSame = Data?.every(
    (rate: any, _: any, array: any) =>
      rate?.effective_date &&
      array[0]?.effective_date &&
      isSameDay(
        parseISO(rate?.effective_date),
        parseISO(array[0]?.effective_date)
      )
  );

  const rate_apply_Type = Data[0]?.company?.rate_apply_date
    ? RATE_APPLY_DATE_LABLES[Data[0].company.rate_apply_date] || "ETD"
    : "ETD";

  const effectiveDate = Data[0]?.effective_date
    ? format(parseISO(Data[0]?.effective_date), "MMM do yyyy")
    : "";

  Data?.map((shipping_rate: any) => {
    const dest = shipping_rate?.shipping_rate_destinations[0];
    const shiplines: any[] = [];

    dest.shipping_rate_destination_locations.map((loc: any) => {
      if (!loc.archived) {
        const locRates: { [key: string]: any } = {};
        loc?.shipping_rate_destination_location_shiplines.map((ship: any) => {
          if (!ship.archived) {
            const newRates: { [key: string]: any } = {};
            Object.entries(ship.rates).map(([key, value]: [string, any]) => {
              if (value?.current && !value?.archived) {
                // filter approved rates and not archived
                newRates[key] = value;
                locRates[
                  `dest(${dest.destination.id}).loc(${loc.location_id}).ship(${
                    ship.shipline ? ship.shipline.id : -1
                  }).rate(${key})`
                ] = value;
              }
            });
            if (Object.keys(newRates).length)
              shiplines.push({
                ...ship,
                shipline: ship.shipline ?? { id: -1, name: "N/A" },
              });
          }
        });
        if (!locationMap.has(`loc(${loc.location_id})`)) {
          if (Object.keys(locRates).length)
            locationMap.set(`loc(${loc.location_id})`, {
              ...loc.location,
              rates: locRates,
            });
        } else {
          const locItem = locationMap.get(`loc(${loc.location_id})`);
          locItem.rates = { ...locItem.rates, ...locRates };
        }
      }
    });
    const shiplinesRemoveDuplicates = shiplines.filter(
      (shipline, index, self) =>
        index === self.findIndex((t) => t.shipline.id === shipline.shipline.id)
    );
    const destinationObj = {
      destination: dest.destination,
      effective_date: shipping_rate.effective_date,
      colSpan: shiplinesRemoveDuplicates.reduce((acc, next) => {
        return acc + Object.keys(next.rates).length;
      }, 0),
      shiplines: shiplinesRemoveDuplicates.sort((a, b) =>
        a.shipline.name.localeCompare(b.shipline.name)
      ),
      TDS_amount: shipping_rate.TDS_amount,
    };
    destinations.push(destinationObj);
  });

  return {
    destinations,
    locationMap,
    rate_apply_Type,
    effectiveDate,
    isEffectiveDateTheSame,
  };
}
