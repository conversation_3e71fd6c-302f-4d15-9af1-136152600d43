"use client";

import { useMutation } from "@tanstack/react-query";
import { Download, Loader } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useFetchClient } from "@/utils/axios";

const downloadPdf = async (
  fetchClient: ReturnType<typeof useFetchClient>
) => {
  try {
    const response = await fetchClient(
      `/v2/shipping-rates/download-pdf`,
      {
        responseType: "blob",
        headers: {
          Accept: "application/pdf",
        },
      }
    );

    const url = window.URL.createObjectURL(new Blob([response.data]));
    const a = document.createElement("a");
    a.href = url;
    a.download = "shipping-rates.pdf";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  } catch (e: any) {
    if (e.message === "Network Error") toast.error("No Internet Connection");
  }
};

const DownloadButton = () => {
  const fetchClient = useFetchClient();
  const mutation = useMutation({
    mutationFn: () => downloadPdf(fetchClient),
    onSuccess: () => toast.success("Download Successful!"),
    onError: () => {
      toast.error("Download Failed!");
    },
  });

  return (
    <Button
      variant={"outline"}
      size={"icon"}
      onClick={() => mutation.mutate()}
      disabled={mutation.isPending}
    >
      {mutation.isPending ? <Loader className="animate-spin" /> : <Download />}
    </Button>
  );
};

export default DownloadButton;
