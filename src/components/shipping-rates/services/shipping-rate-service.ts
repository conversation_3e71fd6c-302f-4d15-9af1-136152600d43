"use server";
import api from "@/utils/axios-server";
import { removeMatchingValue } from "@/utils/helper-function";
type ShippingRateParamType = {
  state: string;
  page: number;
  per_page: number;
  search: string;
  exactMatch: boolean;
  filterData: string;
};

export async function getShippingRate({ params }: { params: ShippingRateParamType }) {
  try {
    removeMatchingValue(params, "state", ["1620278055.png", "undefined"]);
    const response = await api.get(`/v2/shipping-rates`, {
      params: { ...params },
    });

    return response.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message);
  }
}