

import { useTranslations } from "next-intl";
import { useMemo } from "react";

const useSidebarConfig = () => {
  const t = useTranslations("datatable");

  const sidebarConfig = useMemo(() => ({
    hiddenByDefault: false,
    toolPanels: [
      {
        id: "columns",
        labelDefault: t("sidebar.columns"),
        labelKey: "columns",
        iconKey: "columns",
        toolPanel: "agColumnsToolPanel",
        toolPanelParams: {
          suppressRowGroups: true,
          suppressValues: true,
          suppressPivots: true,
          suppressPivotMode: true,
          suppressColumnFilter: true,
          suppressColumnSelectAll: true,
          suppressColumnExpandAll: true,
        },
      }
    ],
  }), [t]);

  return sidebarConfig;
};

export default useSidebarConfig;