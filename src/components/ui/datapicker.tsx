"use client"

import * as React from "react"
import { format } from "date-fns"
import { CalendarIcon } from "@radix-ui/react-icons"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useDirection } from '@/hooks/useDirection'

export interface DatePickerProps {
  date?: Date
  onSelect: (date?: Date) => void
}

export function DatePicker({
  date,
  onSelect
}: DatePickerProps) {
  const { isRTL } = useDirection()
  const [open, setOpen] = React.useState(false)

  const handleSelect = (selectedDate?: Date) => {
    onSelect(selectedDate)
    setOpen(false)
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          className={cn(
            "w-full justify-start text-left font-normal",
            !date && "text-muted-foreground",
            isRTL && "text-right flex-row-reverse"
          )}
        >
          <CalendarIcon className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
          {date ? format(date, "PPP") : isRTL ? "اختر التاريخ" : "Pick a date"}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align={isRTL ? "end" : "start"}>
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleSelect}
          initialFocus
          className={isRTL ? "rtl" : ""}
        />
      </PopoverContent>
    </Popover>
  )
}
