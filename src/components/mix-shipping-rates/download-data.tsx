"use client"
import React from 'react'
import { useDownloadMixShippingRates } from './services/use-download-data';
import { Button } from '../ui/button';
import { Download } from 'lucide-react';
import { LoadingSpinner } from '../Common_UI/loading';

export default function DownloadData() {
  const { downloadMixShippingRates, isPending } = useDownloadMixShippingRates();
  const handleDownload = () => {
    downloadMixShippingRates();
  };
  return (
    <Button variant={'outline'} size={'icon'} onClick={handleDownload} disabled={isPending}>
      {isPending ? <LoadingSpinner className='w-5 h-5' /> : <Download   />}
    </Button>
  )
}
