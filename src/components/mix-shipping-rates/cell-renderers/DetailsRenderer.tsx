import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import type { CustomCellRendererProps } from "ag-grid-react";
import { type FunctionComponent } from "react";
import { useGetMixShippingRate } from "../services/use-get-mix-shipping-rate";
import { LoadingSpinner } from "@/components/Common_UI/loading";
import { useTranslations } from "next-intl";
export const DetailsRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
  node,
}) => {
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 0,
  });
  const t = useTranslations("mix-shipping-rates-datatable.details");
  const { data: mixShippingRate, isLoading } = useGetMixShippingRate(
    data?.location_id,
    data?.loading_cities?.loading_states?.parent?.name,
    data?.id
  );
  if (isLoading)
    return (
      <div className="flex h-80  justify-center items-center">
        <LoadingSpinner />
      </div>
    );
  const showAdditionalColumns = data?.destination_id !== 22;

  const columnHeaders = [
    t("number"),
    t("branch"),
    t("city"),
    t("towing"),
    t("shipping"),
    ...(showAdditionalColumns
      ? [t("clearance"), t("tdsCharges"), t("taxDuty")]
      : []),
    t("total"),
  ];
  const getShipping = (row: any) => {
    return (
      getTotal(row) -
      ((row.towing ? row.towing : 0) +
        (row.clearance ? row.clearance : 0) +
        (row.TDS_charges ? row.TDS_charges : 0))
    );
  };
  const getTotal = (row: any) => {
    return (
      (row.towing ?? 0) +
      (row.shipping ?? 0) +
      (row.clearance ?? 0) +
      (row.TDS_charges ?? 0)
    );
  };
  return (
    <div className={`${isLoading ? "h-64" : "h-auto"} p-2`}>
      <Table className="table-responsive">
        <TableHeader>
          <TableRow>
            {columnHeaders.map((header) => (
              <TableHead key={header} className="rtl:text-right">
                {header}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {mixShippingRate?.map((row: any, index: number) => (
            <TableRow
              key={index}
              className={`${index % 2 === 0 ? "row-even" : "row-odd"}`}
            >
              <TableCell>{`${node.rowIndex ? node.rowIndex : 1}.${
                index + 1
              }`}</TableCell>
              <TableCell>{row?.loading_cities?.loading_states.name}</TableCell>
              <TableCell>{row?.loading_cities?.city_name}</TableCell>
              <TableCell>{formatter.format(row?.towing || 0)}</TableCell>
              <TableCell>{formatter.format(getShipping(row) || 0)}</TableCell>
              {showAdditionalColumns && (
                <>
                  <TableCell>{formatter.format(row?.clearance || 0)}</TableCell>
                  <TableCell>
                    {formatter.format(row?.TDS_charges || 0)}
                  </TableCell>
                  <TableCell>5%+5%</TableCell>
                </>
              )}
              <TableCell>{formatter.format(getTotal(row) || 0)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
