"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";
import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import {
  type FunctionComponent,
  RefObject,
  useCallback,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import { TowingShippingCostRenderer } from "./DestinationRenderer";
import { DetailsRenderer } from "./DetailsRenderer";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import { StateRenderer } from "./StateRenderer";
import { LocationsRenderer } from "./LocationRenderer";
import useSidebarConfig from "../sidebarConfig";
import { useResponsive } from "@/hooks/use-mobile";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);

interface Props {
  gridTheme?: string;
  isDarkMode?: boolean;
  records: {
    page: number;
    per_page: number;
    total: number;
    data: any[];
  } | null;
  gridRefProps?: RefObject<AgGridReact | null>;
  exportColDefs?: ColDef[];
}

export const MixShippingRatesDataTable: FunctionComponent<Props> = ({
  records,
}) => {
  const gridRef = useRef<AgGridReact>(null);
  const t = useTranslations("mix-shipping-rates-datatable");
  const sidebarConfig = useSidebarConfig();
  const { isMobile } = useResponsive();

  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: "#",
        cellDataType: "text",
        valueGetter: (params) => {
          if (params.node) {
            return params.node.rowIndex ? params.node.rowIndex + 1 : 1;
          }
        },
        maxWidth: 100,
        hide: isMobile,
      },
      {
        field: "state",
        headerName: t("header.state"),
        cellDataType: "text",
        cellRenderer: StateRenderer,
        minWidth: isMobile ? 100 : 420,
        valueGetter: (params) => {
          return params.data?.loading_cities?.loading_states?.parent?.name;
        },
      },
      {
        field: "locations",
        headerName: t("header.locations"),
        minWidth: 300,
        cellRenderer: LocationsRenderer,
        valueGetter: (params) => {
          return params.data.locations?.name;
        },
        hide: isMobile,
      },
      {
        headerName: "Destination",
        minWidth: 300,
        cellRenderer: TowingShippingCostRenderer,
        flex: 2,
        cellStyle: {
          width: "100%",
        },
        hide: isMobile,
      },
    ],
    [isMobile, t]
  );

  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
      flex: 1,
    }),
    []
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  const [quickFilterText] = useState<string>();
  const detailCellRenderer = useCallback(DetailsRenderer, []);

  const { isRTL } = useDirection();

  return (
    <>
      <AgGridDataTable
        enableRtl={isRTL ? true : false}
        ref={gridRef}
        columnDefs={colDefs}
        rowData={records?.data || []}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        masterDetail
        detailCellRenderer={detailCellRenderer}
        detailCellRendererParams={{ t }}
        quickFilterText={quickFilterText}
        colResizeDefault="shift"
        headerHeight={60}
        sideBar={sidebarConfig}
        rowClassRules={{
          "row-even": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 === 0
                ? false
                : true
              : true;
          },
          "row-odd": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 !== 0
                ? false
                : true
              : true;
          },
        }}
        detailRowAutoHeight
        rowHeight={isMobile ? 80 : 40}
        suppressHorizontalScroll={isMobile}
      />
    </>
  );
};
