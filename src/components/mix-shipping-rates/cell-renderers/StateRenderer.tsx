import { useDirection } from "@/hooks/useDirection";
import type { CustomCellRendererProps } from "ag-grid-react";
import { ChevronLeft, ChevronRight, MapPin, Navigation } from "lucide-react";
import { useEffect, useState, type FunctionComponent } from "react";
import { useResponsive } from "@/hooks/use-mobile";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useGetMixShippingRate } from "../services/use-get-mix-shipping-rate";
import { LoadingSpinner } from "@/components/Common_UI/loading";
import { useTranslations } from "next-intl";
import { Ship } from "lucide-react";

export const StateRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
  api,
  node,
}) => {
  const { isRTL } = useDirection();
  const { isMobile } = useResponsive();
  const [isExpanded, setIsExpanded] = useState(node.expanded);
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 0,
  });

  const t = useTranslations("mix-shipping-rates-datatable.details");

  const { data: mixShippingRate, isLoading } = useGetMixShippingRate(
    data?.location_id,
    data?.loading_cities?.loading_states?.parent?.name,
    data?.id
  );

  useEffect(() => {
    const expandListener = () => setIsExpanded(node.expanded);
    node.addEventListener("expandedChanged", expandListener);
    return () => node.removeEventListener("expandedChanged", expandListener);
  }, []); // Remove node from dependency array to prevent infinite re-renders

  const handleClick = () => {
    if (isMobile) {
      setIsSheetOpen(true);
    } else {
      api.setRowNodeExpanded(node, !node.expanded);
    }
  };

  const getShipping = (row: any) => {
    return (
      getTotal(row) -
      ((row.towing ? row.towing : 0) +
        (row.clearance ? row.clearance : 0) +
        (row.TDS_charges ? row.TDS_charges : 0))
    );
  };

  const getTotal = (row: any) => {
    return (
      (row.towing ?? 0) +
      (row.shipping ?? 0) +
      (row.clearance ?? 0) +
      (row.TDS_charges ?? 0)
    );
  };

  return (
    <>
      <div
        className="flex justify-between items-center w-full h-full select-text leading-5 hover:cursor-pointer md:hover:cursor-default lg:hover:cursor-default group"
        onClick={handleClick}
      >
        <div className="flex-1  items-center gap-2">
          <div className="flex items-center gap-2">
            <p className="font-medium text-sm text-foreground/90 truncate">
              {data?.loading_cities?.loading_states?.parent?.name}
            </p>
          </div>

          {isMobile && (
            <>
              <div className="flex flex-col justify-center h-full select-text leading-5 px-2 min-w-0">
                <div className="flex items-center gap-1">
                  <MapPin className="w-3 h-3 text-primary/70 flex-shrink-0" />
                  <span className="text-xs text-foreground/80 truncate">
                    {data.locations?.name || "N/A"}
                  </span>
                </div>
              </div>
              <div className="flex flex-col justify-center h-full select-text leading-5 px-2 min-w-0">
                <div className="flex items-center gap-1">
                  <Navigation className="w-3 h-3 text-primary/70 flex-shrink-0" />
                  <span className="text-xs text-foreground/80 truncate">
                    {data.destinations?.name || "N/A"}
                  </span>
                </div>
              </div>
            </>
          )}
        </div>
        <div className="flex items-center gap-2">
          {!isMobile &&
            (!isRTL ? (
              <ChevronRight
                className={`ml-2 h-4 w-4 transition-all duration-200 ${
                  isExpanded ? "rotate-90" : ""
                }`}
              />
            ) : (
              <ChevronLeft
                className={`ml-2 h-4 w-4 transition-all duration-200 ${
                  isExpanded ? "-rotate-90" : ""
                }`}
              />
            ))}
        </div>
      </div>

      <Drawer open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <DrawerContent className="h-[80vh] rounded-t-3xl border-t-2 border-primary px-4">
          <DrawerHeader className="sr-only">
            <DrawerTitle>Location Details</DrawerTitle>
            <DrawerDescription>
              Detailed information about{" "}
              {data?.loading_cities?.loading_states?.parent?.name}
            </DrawerDescription>
          </DrawerHeader>
          <div className="flex flex-col h-full">
            <div className="px-1 py-2">
              <div className="flex items-center gap-2">
                <span className="font-semibold text-xs">
                  {data?.loading_cities?.loading_states?.parent?.name}
                </span>
              </div>
              <div className="flex items-center gap-2 justify-between py-2">
                <span className="flex items-center text-xs text-primary">
                  <MapPin className="w-4 h-4" />
                  {data.locations?.name}
                </span>
                <div className="flex items-center gap-2">
                  <Navigation className="w-4 h-4 text-primary" />
                  <span className="text-xs">
                    {data?.destinations?.name || "No destination"}
                  </span>
                </div>
              </div>
            </div>
            <ScrollArea className="flex-1">
              {isLoading ? (
                <div className="flex h-64 justify-center items-center">
                  <LoadingSpinner />
                </div>
              ) : (
                <div className="space-y-3 pb-4">
                  {mixShippingRate?.length > 0 ? (
                    mixShippingRate.map((row: any, index: number) => (
                      <Card
                        key={index}
                        className="border border-primary/50 bg-card/30 backdrop-blur-sm"
                      >
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <div className="w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center">
                                <span className="text-[10px] font-medium text-primary">
                                  {`${node.rowIndex ? node.rowIndex : 1}.${
                                    index + 1
                                  }`}
                                </span>
                              </div>
                              <div className="flex flex-col">
                                <span className="text-xs font-medium text-foreground">
                                  {row?.loading_cities?.loading_states.name}
                                </span>
                                <span className="text-[10px] text-muted-foreground">
                                  {row?.loading_cities?.city_name}
                                </span>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-sm font-bold text-primary">
                                {formatter.format(getTotal(row) || 0)}
                              </div>
                              <div className="text-[10px] text-muted-foreground">
                                Total Cost
                              </div>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="grid grid-cols-2 gap-3">
                            <div className="space-y-2">
                              <div className="flex justify-between items-center">
                                <span className="text-[10px] text-muted-foreground">
                                  {t("towing")}
                                </span>
                                <span className="text-xs font-medium">
                                  {formatter.format(row?.towing || 0)}
                                </span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-[10px] text-muted-foreground">
                                  {t("shipping")}
                                </span>
                                <span className="text-xs font-medium">
                                  {formatter.format(getShipping(row) || 0)}
                                </span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-[10px] text-muted-foreground">
                                  {t("clearance")}
                                </span>
                                <span className="text-xs font-medium">
                                  {formatter.format(row?.clearance || 0)}
                                </span>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <div className="flex justify-between items-center">
                                <span className="text-[10px] text-muted-foreground">
                                  {t("tdsCharges")}
                                </span>
                                <span className="text-xs font-medium">
                                  {formatter.format(row?.TDS_charges || 0)}
                                </span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-[10px] text-muted-foreground">
                                  {t("taxDuty")}
                                </span>
                                <span className="text-xs font-medium">
                                  5%+5%
                                </span>
                              </div>
                              <div className="pt-1 border-t border-border/30">
                                <div className="flex justify-between items-center">
                                  <span className="text-[10px] font-medium text-foreground">
                                    Total
                                  </span>
                                  <span className="text-xs font-bold text-primary">
                                    {formatter.format(getTotal(row) || 0)}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <div className="w-16 h-16 bg-muted/30 rounded-full flex items-center justify-center mb-4">
                        <Ship className="w-8 h-8 text-muted-foreground/50" />
                      </div>
                      <p className="text-sm text-muted-foreground mb-1">
                        No shipping rates available
                      </p>
                      <p className="text-xs text-muted-foreground/70">
                        Check back later for updated rates
                      </p>
                    </div>
                  )}
                </div>
              )}
            </ScrollArea>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
};
