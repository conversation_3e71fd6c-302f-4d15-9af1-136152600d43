"use server";
import axios from "@/utils/axios-server";

type ParamType = {
  status: any;
  page: number;
  per_page: number;
  search: string;
  exactMatch: boolean;
  filterData: string;
};

export async function getMixShippingRate({ params }: { params: ParamType }) {
  try {
    const response = await axios.get(`/v2/mix-shipping-rate`, {
      params: { ...params },
    });
    return response.data;
  } catch (error) {
    throw error;
  }
}

export async function pdfMixShippingRate({ params }: { params: ParamType }) {
  try {
    const response = await axios.get(`/v2/mix-shipping-rate`, {
      params: { ...params },
    });
    return response.data;
  } catch (error) {
    throw error;
  }
}

export async function getSingleMixShippingRate({
  cities,
  location_id,
}: {
  location_id: number;
  cities: number;
}) {
  try {
    const response = await axios.get(
      `/v2/mix-shipping-rate/single-state-shipment-rate`,
      {
        params: { cities, location_id },
      }
    );
    return response.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message);
  }
}
