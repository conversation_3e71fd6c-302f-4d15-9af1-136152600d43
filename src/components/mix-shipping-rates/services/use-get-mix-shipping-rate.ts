import { useQuery } from "@tanstack/react-query"
import { getSingleMixShippingRate } from "./mix-shipping-rates-service"

export const useGetMixShippingRate = (location_id: number, cities: number,id:number) => {
  const { data, isLoading, } = useQuery({
    queryKey: ['get-mix-shipping-rate',id],
    queryFn: async () => getSingleMixShippingRate({ cities:cities, location_id }),
  })
  return { data, isLoading }
}