"use client";
import { useMutation } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";

export const useDownloadMixShippingRates = () => {
  const fetchClient = useFetchClient();

  const { mutate: downloadMixShippingRates, isPending } = useMutation({
    mutationFn: async () => {
      const res = await fetchClient(`/v2/mix-shipping-rate/shipmentRatePdf`, {
        responseType: "blob",
      });
      const filename = `mix-shipping-rate.pdf`;
      const url = window.URL.createObjectURL(new Blob([res.data]));

      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
  });
  return { downloadMixShippingRates, isPending };
};
