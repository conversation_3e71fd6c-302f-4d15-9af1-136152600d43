import { LoadingSpinner } from "@/components/Common_UI/loading";
import { But<PERSON> } from "@/components/ui/button";
import type { CustomCellRendererProps } from "ag-grid-react";
import { PrinterCheck, Hash, Package } from "lucide-react";
import { type FunctionComponent, useState } from "react";
import { useDownloadMixInvoice } from "../services/use-download-mix-shipping";
import { colorSystem, ColorSystemKey } from "@/lib/constant";
import { Badge } from "@/components/ui/badge";
import { removeUnderScore } from "@/utils/commons";
import { useResponsive } from "@/hooks/use-mobile";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import {
  countTotal,
  formatOnlyDate,
  getInvoicePayments,
} from "@/utils/helper-function";
import { CustomFormatCurrency } from "@/lib/helper";
import { differenceInDays } from "date-fns";

export const InvoicesRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
  node,
}) => {
  const { downloadMixInvoice, isPending } = useDownloadMixInvoice();
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const { isMobile } = useResponsive();
  let paidTotal = 0;
  let amountTotal = 0;
  paidTotal = getInvoicePayments(data, "Mix");
  amountTotal = countTotal(data?.mix_shipping_vehicles);
  const balance = +amountTotal * +data?.exchange_rate - paidTotal;
  const total =
    countTotal(data?.mix_shipping_vehicles) * Number(data?.exchange_rate);
  const due =
    countTotal(data?.mix_shipping_vehicles) * Number(data?.exchange_rate) -
    balance;

  const colors = colorSystem[data?.status as ColorSystemKey] || {
    bg: "bg-green-500/10",
    txt: "text-green-500",
  };

  // const formatter = new Intl.NumberFormat("en-US", {
  //   style: "currency",
  //   currency: "USD",
  //   maximumFractionDigits: 2,
  // });

  const handleClick = () => {
    if (isMobile) {
      setIsSheetOpen(true);
    }
  };

  if (node.rowPinned === "bottom") {
    return <div className="uppercase"> {data?.invoice}</div>; // or any custom display
  }

  const detailRows = [
    {
      label: "Status",
      value: (
        <Badge
          className={`${colors.bg} ${colors.txt} border-none text-[10px] uppercase`}
          variant="outline"
        >
          {removeUnderScore(data?.status)}
        </Badge>
      ),
    },
    {
      label: "Invoice",
      value: (
        <span className="px-2 bg-blue-600/10 rounded-md text-blue-400 text-xs">
          {CustomFormatCurrency(total, data?.currency)}
        </span>
      ),
    },
    {
      label: "Paid",
      value: (
        <span className="px-2 bg-green-600/10 rounded-md text-green-500 dark:text-green-300 text-xs">
          {CustomFormatCurrency(balance, data?.currency)}
        </span>
      ),
    },
    {
      label: "Due",
      value: (
        <span className="px-2 bg-red-600/10 rounded-md text-red-500 dark:text-red-300 text-xs">
          {CustomFormatCurrency(due, data?.currency)}
        </span>
      ),
    },
    {
      label: "VINs",
      value: (
        <span className="leading-normal text-xs">
          {data?.mix_shipping_vehicles?.map((vehicle: any, index: number) => {
            return <p key={index}>{vehicle?.vehicles?.vin}</p>;
          })}
        </span>
      ),
    },
    {
      label: "Lot Numbers",
      value: (
        <span className="leading-normal text-xs">
          {data?.mix_shipping_vehicles?.map((vehicle: any, index: number) => {
            return <p key={index}>{vehicle?.vehicles?.lot_number}</p>;
          })}
        </span>
      ),
    },
    {
      label: "Issue Date",
      value: (
        <span className="text-xs">
          {data?.inv_date && formatOnlyDate(new Date(data?.inv_date))}
        </span>
      ),
    },
    {
      label: "Due Date",
      value: (
        <span className="text-xs">
          {data?.inv_due_date && formatOnlyDate(new Date(data?.inv_due_date))}
        </span>
      ),
    },
    {
      label: "Past Due Days",
      value: (
        <span className="text-xs">
          {data?.status !== "paid" && (
            <span>
              {data?.inv_due_date &&
                data?.inv_date &&
                differenceInDays(
                  new Date(Date.now()),
                  new Date(data?.inv_due_date)
                )}
            </span>
          )}
        </span>
      ),
    },
  ];

  return (
    <>
      <div
        className="flex items-center h-full gap-2 hover:cursor-pointer md:hover:cursor-default lg:hover:cursor-default group"
        onClick={handleClick}
      >
        <div className="pt-1">
          <Button
            size={"icon"}
            variant={"outline"}
            onClick={(e) => {
              e.stopPropagation();
              downloadMixInvoice(data);
            }}
            className="bg-green-500/10 dark:bg-green-500/10 text-green-500 dark:text-green-500"
          >
            {isPending ? (
              <LoadingSpinner className="h-5 w-5 p-0" />
            ) : (
              <PrinterCheck />
            )}
          </Button>
        </div>
        <div className="flex flex-col justify-center h-full select-text leading-5 flex-1">
          <div className="flex items-center">
            <div className="w-full overflow-hidden text-ellipsis whitespace-nowrap font-normal px-2">
              {data?.containers?.invoice_number}
            </div>
          </div>

          <div className="w-32 overflow-hidden text-ellipsis whitespace-nowrap font-normal px-2 text-primary/70">
            {data?.containers?.container_number}
          </div>
        </div>
      </div>

      <Drawer open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <DrawerContent className="h-[80vh] rounded-t-3xl border-t-2 border-primary px-4">
          <DrawerHeader className="sr-only">
            <DrawerTitle>Shipping Invoice Details</DrawerTitle>
            <DrawerDescription>
              Detailed information about container{" "}
              {data?.containers?.container_number}
            </DrawerDescription>
          </DrawerHeader>
          <div className="flex flex-col h-full">
            <div className="px-1 py-2">
              <div className="flex items-center gap-2">
                <span className="font-semibold text-xs">
                  Container: {data?.containers?.container_number}
                </span>
              </div>
              <div className="flex items-center gap-2 justify-between py-2">
                <span className="flex items-center text-xs text-primary">
                  <Hash className="w-4 h-4" />
                  {data?.containers?.invoice_number}
                </span>
                <div className="flex items-center gap-2">
                  <Package className="w-4 h-4 text-primary" />
                </div>
              </div>
            </div>
            <div className="flex-1 overflow-y-auto">
              <div className="rounded-lg overflow-hidden border">
                <Table>
                  <TableBody>
                    {detailRows.map((row, index) => (
                      <TableRow
                        key={row.label}
                        className={`${
                          index % 2 === 0 ? "bg-primary/5" : "bg-primary/10"
                        }`}
                      >
                        <TableCell className="py-1 px-2 text-xs">
                          {row.label}
                        </TableCell>
                        <TableCell className="py-1 px-2 text-right text-xs">
                          {row.value}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
};
