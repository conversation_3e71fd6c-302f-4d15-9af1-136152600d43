import { Separator } from "@/components/ui/separator";
import { formatOnlyDate } from "@/utils/helper-function";
import type { CustomCellRendererProps } from "ag-grid-react";
import { differenceInDays } from "date-fns";
import { useTranslations } from "next-intl";
import { type FunctionComponent } from "react";

export const CenterCell: FunctionComponent<CustomCellRendererProps> = ({
  data,
  node
}) => {
  const t = useTranslations("mix-shipping-datatable.body");
  if (node.rowPinned === 'bottom') {
    return <div>
      {t('due')} : <span className="px-2 bg-red-600/10 rounded-md text-red-500 dark:text-red-300 leading-[22px] text-xs ">
        {data?.date}
      </span>
    </div>
  }
  return (
    <div className="flex flex-col  py-1 select-text leading-5">
      <div className="flex  overflow-hidden text-ellipsis whitespace-nowrap ">
        <div className="min-w-[74px]">{t("issue_date")}:</div>{data?.inv_date && formatOnlyDate(new Date(data?.inv_date))}
      </div>
      <Separator className="my-1" />
      <div className="flex overflow-hidden text-ellipsis whitespace-nowrap ">
        <div className="min-w-[74px]">{t("due_date")}:</div>{data?.inv_due_date && formatOnlyDate(new Date(data?.inv_due_date))}
      </div>
      <Separator className="my-1" />
      <div className="rounded-md flex text-ellipsis overflow-hidden">
        <div className="min-w-[100px]">{t("past_due_days")}:</div>
        {data?.status !== "paid" && <span>{(data?.inv_due_date
          && data?.inv_date
        ) && differenceInDays(new Date(Date.now()), new Date(data?.inv_due_date))}</span>}
      </div>
    </div>
  );
};
