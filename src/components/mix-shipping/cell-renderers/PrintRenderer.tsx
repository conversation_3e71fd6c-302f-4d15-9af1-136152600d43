import type { CustomCellRendererProps } from "ag-grid-react";

import { type FunctionComponent } from "react";
import { PrinterCheck } from "lucide-react";
import { useDownloadMixInvoice } from "../services/use-download-mix-shipping";
import { LoadingSpinner } from "@/components/Common_UI/loading";
import { Button } from "@/components/ui/button";

export const PrintRenderer: FunctionComponent<CustomCellRendererProps> = ({ data }) => {
  const { downloadMixInvoice, isPending } = useDownloadMixInvoice();
  return (
    <div className="flex justify-start h-full w-full whitespace-nowrap ">
      <Button variant="outline" size={'icon'}
        onClick={() => downloadMixInvoice(data)}
      >
        {isPending ? <LoadingSpinner className="h-4 w-4 p-0" /> : <PrinterCheck />}
      </Button>
    </div>
  );
};
