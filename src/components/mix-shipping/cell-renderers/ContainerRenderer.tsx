import type { CustomCellRendererProps } from "ag-grid-react";
import { useTranslations } from "next-intl";
import { type FunctionComponent } from "react";

export const ContainerRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
  node
}) => {
  const t = useTranslations("mix-shipping-datatable.body");
  if (node.rowPinned === 'bottom') {
    return <div className="uppercase">
      {t('invoice')} : <span className="px-2 bg-blue-600/10 rounded-md text-blue-400 leading-[22px] text-xs ">
        {data?.container}
      </span>

    </div>; // or any custom display
  }
  return (
    <div className=" text-start leading-normal select-text py-1 items-center">
      {data?.mix_shipping_vehicles?.map((vehicle: any, index: number) => {
        return <p key={index}>{vehicle?.vehicles?.lot_number}</p>
      })}
    </div>
  );
};
