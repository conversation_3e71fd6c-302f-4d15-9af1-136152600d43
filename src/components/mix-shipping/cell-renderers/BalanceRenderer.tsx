import { Separator } from "@/components/ui/separator";
import { CustomFormatCurrency } from "@/lib/helper";
import { countTotal, getInvoicePayments } from "@/utils/helper-function";
import type { CustomCellRendererProps } from "ag-grid-react";
import { useTranslations } from "next-intl";
import { type FunctionComponent } from "react";

export const BalanceRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
  node,
}) => {
  let paidTotal = 0;
  let amountTotal = 0;
  paidTotal = getInvoicePayments(data, "Mix");
  amountTotal = countTotal(data?.mix_shipping_vehicles);
  const discount = data?.mix_shipping_vehicles?.[0]?.discount;

  const balance =
    +amountTotal * +data?.exchange_rate - paidTotal - Number(discount) < 1
      ? 0
      : +amountTotal * +data?.exchange_rate - paidTotal - Number(discount);

  const total =
    countTotal(data?.mix_shipping_vehicles) * Number(data?.exchange_rate);

  const t = useTranslations("mix-shipping-datatable.body");
  if (node.rowPinned === "bottom") {
    return (
      <div className="uppercase">
        {t("paid")} :{" "}
        <span className="px-2 bg-green-600/10 rounded-md text-green-500 dark:text-green-300 leading-[22px] text-xs ">
          {data?.balance}
        </span>
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-start select-text h-full leading-5 ">
      <div>
        {t("invoice")} :{" "}
        <span className="px-2 bg-blue-600/10 rounded-md text-blue-400 leading-[22px] text-xs ">
          {CustomFormatCurrency(total, data?.currency)}
        </span>
      </div>
      <Separator />
      {discount > 0 && (
        <>
          <div>
            {t("discount")} :
            <span className="px-2 bg-green-600/10 rounded-md text-green-500 dark:text-green-300 leading-[22px] text-xs ">
              {CustomFormatCurrency(discount, data?.currency)}
            </span>
          </div>
          <Separator />
        </>
      )}
      <div>
        {t("paid")} :
        <span className="px-2 bg-green-600/10 rounded-md text-green-500 dark:text-green-300 leading-[22px] text-xs ">
          {CustomFormatCurrency(paidTotal, data?.currency)}
        </span>
      </div>
      <Separator />
      <div>
        {t("due")} :{" "}
        <span className="px-2 bg-red-600/10 rounded-md text-red-500 dark:text-red-300 leading-[22px] text-xs ">
          {CustomFormatCurrency(balance, data?.currency)}
        </span>
      </div>
      <div className="rounded-md  flex leading-[22px] text-xs font-semibold overflow-hidden"></div>
    </div>
  );
};
