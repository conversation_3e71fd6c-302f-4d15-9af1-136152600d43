"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
  PinnedRowModule,
} from "ag-grid-community";

import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import {
  type FunctionComponent,
  RefObject,
  useMemo,
  useRef,
  useState,
} from "react";

import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";

import { CenterCell } from "./CenterCell";
import { StatusRenderer } from "./StatusRenderer";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import { InvoicesRenderer } from "./InvoicesRenderer";
import { ContainerRenderer } from "./ContainerRenderer";
import { BalanceRenderer } from "./BalanceRenderer";
import useSidebarConfig from "../sidebarConfig";
import { countTotal, getInvoicePayments } from "@/utils/helper-function";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
  PinnedRowModule,
]);

interface Props {
  gridTheme?: string;
  isDarkMode?: boolean;
  records: {
    page: number;
    per_page: number;
    total: number;
    data: any[];
  } | null;
  gridRefProps?: RefObject<AgGridReact | null>;
  exportColDefs?: ColDef[];
}

export const MisShippingDataTable: FunctionComponent<Props> = ({
  records,
  gridRefProps,
  exportColDefs,
}) => {
  const gridRef = useRef<AgGridReact>(null);
  const t = useTranslations("mix-shipping-datatable");
  const sidebarConfig = useSidebarConfig();
  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: "#",
        cellDataType: "text",
        minWidth: 50,
        cellStyle: {
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        },
        valueGetter: (params) => {
          if (params.node && params.node.rowPinned !== "bottom") {
            return params.node.rowIndex ? params.node.rowIndex + 1 : 1;
          }
        },
      },
      {
        field: "invoice",
        headerName: t("header.invoice"),
        cellDataType: "text",
        cellRenderer: InvoicesRenderer,
        minWidth: 250,
        cellClass: "flex items-center",
      },

      {
        headerName: t("header.vin"),
        cellRenderer: StatusRenderer,
        minWidth: 220,
        cellClass: "flex items-center",
      },
      {
        field: "container",
        headerName: t("header.lot"),
        cellRenderer: ContainerRenderer,
        minWidth: 160,
        cellClass: "flex items-center",
      },
      {
        field: "balance",
        headerName: t("header.balance"),
        minWidth: 180,
        cellRenderer: BalanceRenderer,
        cellClass: "flex items-center",
      },
      {
        field: "date",
        headerName: t("header.dates"),
        minWidth: 220,
        cellRenderer: CenterCell,
        cellClass: "flex items-center",
      },
    ],
    []
  );

  const stistics = records?.data?.reduce(
    (prev, cur) => {
      let paidTotal = 0;
      let amountTotal = 0;
      paidTotal = getInvoicePayments(cur, "Mix");
      amountTotal = countTotal(cur?.mix_shipping_vehicles);
      const balance = +amountTotal * +cur?.exchange_rate - paidTotal;
      const discount = cur?.mix_shipping_vehicles?.reduce(
        (accumulator: number, p: any) => {
          return accumulator + +p?.discount;
        },
        0
      );

      return {
        total:
          prev.total +
          Math.round(
            countTotal(cur?.mix_shipping_vehicles) * Number(cur?.exchange_rate)
          ),
        balance: prev.balance + Math.round(balance) - Math.round(discount),
        dueBalance:
          prev.dueBalance +
          Math.round(
            countTotal(cur?.mix_shipping_vehicles) * Number(cur?.exchange_rate)
          ) -
          Math.round(balance),
      };
    },
    { total: 0, balance: 0, dueBalance: 0 }
  );
  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
      autoHeight: true,
      flex: 1,
    }),
    []
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  const [quickFilterText] = useState<string>();

  const pinnedBottomRowData = useMemo<any[]>(() => {
    return [
      {
        invoice: t("body.total_footer"),
        container: (stistics.total || 0).toLocaleString(),
        balance: (stistics.dueBalance || 0).toLocaleString(),
        date: (stistics.balance || 0).toLocaleString(),
      },
    ];
  }, [stistics, t]);

  const selectionColumnDef = useMemo(() => {
    return {
      minWidth: 44,
    };
  }, []);
  const { isRTL } = useDirection();

  return (
    <>
      <AgGridDataTable
        enableRtl={isRTL ? true : false}
        ref={gridRefProps || gridRef}
        selectionColumnDef={selectionColumnDef}
        columnDefs={exportColDefs || colDefs}
        rowData={records?.data || []}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        detailCellRendererParams={{ t }}
        quickFilterText={quickFilterText}
        colResizeDefault="shift"
        headerHeight={60}
        rowHeight={40}
        sideBar={sidebarConfig}
        pinnedBottomRowData={pinnedBottomRowData}
        rowClassRules={{
          "row-even": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 === 0
                ? false
                : true
              : true;
          },
          "row-odd": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 !== 0
                ? false
                : true
              : true;
          },
        }}
        detailRowAutoHeight={true}
      />
    </>
  );
};
