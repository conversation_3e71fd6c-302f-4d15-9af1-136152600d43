import type { CustomCellRendererProps } from "ag-grid-react";
import { type FunctionComponent } from "react";

export const StatusRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {

  return (
    <div className="flex flex-col min-h-full select-text justify-center text-start group leading-normal">
      {data?.mix_shipping_vehicles?.map((vehicle: any, index: number) => {
        return <p key={index}>{vehicle?.vehicles?.vin}</p>;
      })}
    </div>

  );
};
