"use client";
import React, { useMemo } from "react";

import { ColDef } from "ag-grid-community";
import { differenceInDays, format } from "date-fns";
import { getMixShipping } from "./services/mix-shipping-service";
import {
  countTotal,
  formatter,
  getInvoicePayments,
} from "@/utils/helper-function";
import { useTranslations } from "next-intl";
import { ExportModal } from "../Common_UI/export-modal";
import { toast } from "sonner";

type Props = {
  records: {
    page: number;
    per_page: number;
    total: number;
    data: any[];
  };
  baseState: string;
};

// Helper function to validate dates
const isValidDate = (date: any): boolean => {
  return date && !isNaN(new Date(date).getTime());
};

export default function ExportMixShippingData({ records, baseState }: Props) {
  const t = useTranslations("export-modal");
  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: "Invoice Number",
        valueGetter: (params) => params.data?.containers?.invoice_number,
      },

      {
        headerName: "Container Number",
        valueGetter: (params) => params.data?.containers?.container_number,
      },

      {
        headerName: "Vin",
        valueGetter: (params) =>
          params.data?.mix_shipping_vehicles?.map((vehicle: any) => {
            return vehicle?.vehicles?.vin;
          }),
      },

      {
        headerName: "Lot Number",
        valueGetter: (params) =>
          params.data?.mix_shipping_vehicles?.map((vehicle: any) => {
            return vehicle?.vehicles?.lot_number;
          }),
      },

      {
        headerName: "Issue Date",
        valueGetter: (params) =>
          params.data?.inv_date && isValidDate(params.data.inv_date)
            ? format(new Date(params.data.inv_date), "yyyy-MM-dd")
            : "",
      },

      {
        headerName: "Due Date",
        valueGetter: (params) =>
          params.data?.inv_due_date && isValidDate(params.data.inv_due_date)
            ? format(new Date(params.data.inv_due_date), "yyyy-MM-dd")
            : "",
      },

      {
        headerName: "Invoice Amount ($)",
        valueGetter: (params) =>
          formatter("USD").format(
            countTotal(params.data?.mix_shipping_vehicles)
          ),
      },

      {
        headerName: "Invoice Amount (AED)",
        valueGetter: (params) =>
          countTotal(params.data?.mix_shipping_vehicles) *
          Number(params.data?.exchange_rate),
      },

      {
        headerName: "Payment Received",
        valueGetter: (params) => {
          let total = 0;
          total = getInvoicePayments(params.data, "Mix");
          return `${Math.round(total).toFixed(2)} DH`;
        },
      },

      {
        headerName: "Balance Due",
        valueGetter: (params) => {
          let paidTotal = 0;
          let amountTotal = 0;
          paidTotal = getInvoicePayments(params.data, "Mix");
          amountTotal = countTotal(params.data?.mix_shipping_vehicles);
          const balance =
            +amountTotal * +params.data?.exchange_rate - paidTotal;
          return `${Math.round(balance).toFixed(2)} DH`;
        },
      },

      {
        headerName: "Past Due Days",
        valueGetter: (params) => {
          if (
            params?.data?.status !== "paid" &&
            params?.data?.inv_due_date &&
            isValidDate(params.data.inv_due_date)
          ) {
            return differenceInDays(
              new Date(Date.now()),
              new Date(params.data.inv_due_date)
            );
          }
          return "";
        },
      },
    ],
    []
  );

  const fetchAllData = async (): Promise<any[]> => {
    const response = await getMixShipping({
      params: {
        status: baseState,
        page: 1,
        per_page: records.total,
        search: "",
        exactMatch: false,
        filterData: "",
      },
    });
    return response.data;
  };

  return (
    <ExportModal
      columnDefs={colDefs}
      currentData={records.data}
      exportFileName="Mix Shipment Data"
      fetchAllData={fetchAllData}
      totalItems={records.total}
      translations={{
        title: t("title"),
        exportData: t("sub-title"),
        subTitle: t("sub-title"),
        currentData: t("current-data"),
        allData: t("all-data"),
        cancel: t("cancel"),
        export: t("export"),
      }}
      onExportSuccess={() => toast("Mix Shipment Export Completed")}
    />
  );
}
