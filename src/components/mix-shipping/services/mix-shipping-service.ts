"use server"
import axios from "@/utils/axios-server";
import { removeMatchingValue } from "@/utils/helper-function";

type ParamType = {
  status: any;
  page: number;
  per_page: number;
  search: string;
  exactMatch: boolean;
  filterData: string;
};

export async function getMixShipping({ params }: { params: ParamType }) {
  try {
    // removing undefined and 1620278055.png from status
    removeMatchingValue(params, "status", ["1620278055.png", "undefined"]);
    const response = await axios.get(`/v2/mix-shippings/mixShipping-v1`, {
      params: { ...params }
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message);
  }
}
