"use server";
import { transformObject } from "@/lib/transferObject";
import { getMixShipping } from "./mix-shipping-service";
// Define allowed keys (Only these fields will be processed)
const allowedKeys = [
  "con",
  "inv",
  "inv_from",
  "inv_to",
  "payment_from",
  "payment_to",
  "v_status",
  "from",
  "to",
];

// Define dynamic mapping (Customize as needed)
const mapping = {
  con: "container_id",
  inv: "container_id",
  inv_from: "invoice_amount.min",
  inv_to: "invoice_amount.max",
  payment_from: "payment_received.min",
  payment_to: "payment_received.max",
  v_status: "status",
  from: "inv_due_date.from",
  to: "inv_due_date.to",
};

export async function loadMixShippingData(
  baseState: string,
  searchParams: any,
  params: {
    page: number;
    per_page: number;
    search?: string;
    filterData?: string;
    status?: string;
  }
) {
  try {
    const transformedFilters = transformObject(
      searchParams,
      allowedKeys,
      mapping
    );

    const result = await getMixShipping({
      params: {
        status: baseState,
        page: params.page,
        per_page: params.per_page,
        search: params.search || "",
        exactMatch: false,
        filterData:
          Object.keys(transformedFilters).length !== 0
            ? JSON.stringify(transformedFilters)
            : "",
      },
    });

    const response = {
      data: Array.isArray(result.data) ? result.data : [],
      total: result.total || 0,
      page: params.page,
      per_page: params.per_page,
      success: true,
    };

    return response;
  } catch (error) {
    console.error(error);
    return {
      data: [],
      total: 0,
      page: params.page,
      per_page: params.per_page,
      success: false,
    };
  }
}
