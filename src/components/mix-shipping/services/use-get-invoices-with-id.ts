"use client";

import { useQuery } from "@tanstack/react-query";
import { getAutoComplete } from "../../../utils/autoComplete/autoComplete-server";
import { useEffect } from "react";
import { useSearchParams } from "next/navigation";

export function useGetInvoicesWithId() {
  const search = useSearchParams();
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["invoice-with-id"],
    queryFn:()=> getAutoComplete({column:"invoice_number",model:"invoices",}),
    enabled: false,
  });
   useEffect(() => {
      if (search.get("con")) {
        refetch();
      }
    }, [search]);
  return {
    data,
    isLoading,
    refetch,
  };
}