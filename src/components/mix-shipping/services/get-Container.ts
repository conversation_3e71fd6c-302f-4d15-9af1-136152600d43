"use client";

import { useQuery } from "@tanstack/react-query";
import { getAutoComplete } from "../../../utils/autoComplete/autoComplete-server";
import { useSearchParams } from "next/navigation";
import { useEffect } from "react";

export function useGetContainers() {
  const search = useSearchParams();
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["containers-mix-shipping"],
    queryFn: () =>
      getAutoComplete({ column: "container_number", model: "containers" }),
    enabled: false,
  });
  useEffect(() => {
    if (search.get("con")) {
      refetch();
    }
  }, [search, refetch]);
  return {
    data,
    isLoading,
    refetch,
  };
}
