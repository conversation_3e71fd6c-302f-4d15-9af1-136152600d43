"use client";
import { useMutation } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";

export const useDownloadMixInvoice = () => {
  const fetchClient = useFetchClient();

  const { mutate: downloadMixInvoice, isPending } = useMutation({
    mutationFn: async (item: any) => {
      const res = await fetchClient(
        `/v2/mix-shippings/generatepdf/mix-invoice/${item?.id}`,
        { responseType: "blob" }
      );
      const filename = `mix-full-invoice.pdf`;
      const url = window.URL.createObjectURL(new Blob([res.data]));

      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    },
  });
  return { downloadMixInvoice, isPending };
};
