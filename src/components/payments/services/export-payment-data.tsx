"use client";
import React, { useMemo } from "react";
import { ColDef } from "ag-grid-community";
import { getPayment } from "./payment-service";
import { PaymentType } from "./config";
import { useTranslations } from "next-intl";
import { ExportModal } from "@/components/Common_UI/export-modal";
import { toast } from "sonner";

type Props = {
  records: {
    page: number;
    per_page: number;
    total: number;
    data: PaymentType[];
  };
};

export default function ExportPaymentData({ records }: Props) {
  const t = useTranslations("export-modal");
  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        field: `id`,
        headerName: "Payment Number",
        valueGetter: (params) => `PGLPN${params?.data?.id}`,
      },
      {
        field: "transaction_number",
        headerName: "Transaction Number",
      },
      {
        field: "payment_method",
        headerName: "Payment Method",
      },

      { field: "amount", headerName: "Amount" },
      { field: "amount_applied", headerName: "Amount Applied" },
      {
        field: "remaining_amount",
        headerName: "Remaining Amount",
        valueGetter: (params) =>
          params?.data?.amount - params?.data?.amount_applied,
      },
      { field: "currency", headerName: "Currency" },
      { field: "state", headerName: "Status" },
      {
        field: "exchange_rate",
        headerName: "Exchange Rate",
      },
    ],
    []
  );

  const fetchAllData = async (): Promise<any[]> => {
    const response = await getPayment({
      params: {
        state: "",
        page: 1,
        per_page: records.total,
        search: "",
        exactMatch: false,
        filterData: "",
      },
    });
    return response.data;
  };

  return (
    <ExportModal
      columnDefs={colDefs}
      currentData={records.data}
      exportFileName="Payment Data"
      fetchAllData={fetchAllData}
      totalItems={records.total}
      translations={{
        title: t("title"),
        exportData: t("sub-title"),
        subTitle: t("sub-title"),
        currentData: t("current-data"),
        allData: t("all-data"),
        cancel: t("cancel"),
        export: t("export"),
      }}
      onExportSuccess={() => toast("Payment Export Completed")}
    />
  );
}
