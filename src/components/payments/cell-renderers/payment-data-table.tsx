"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";

import {
  CellSelectionModule,
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import {
  type FunctionComponent,
  useCallback,
  useMemo,
  useRef,
  useState,
} from "react";

import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";

import { RemainAppliedAmountRenderer } from "./RemainAppliedAmountRenderer";
import { ExchangeRateRenderer } from "./ExchangeRateRenderer";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import useSidebarConfig from "../sidebarConfig";
import { PaymentRenderer } from "./PaymentRenderer";
import { PayementMethodRenderer } from "./PayementMethodRenderer";
import { AmountRenderer } from "./AmountRenderer";
import DetailCellRenderer from "./detailCellRenderer";
import { PaymentProps } from "../services/config";
import { TransactionRenderer } from "./TransactionRenderer";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
  CellSelectionModule,
]);

const paginationPageSizeSelector = [5, 10, 20];

export const PayementsDataTable: FunctionComponent<PaymentProps> = ({
  records,
  gridRefProps,
  exportColDefs,
}: PaymentProps) => {
  const gridRef = useRef<AgGridReact>(null);
  const t = useTranslations("payment-datatable");
  const detailCellRenderer = useCallback(DetailCellRenderer, []);
  const sidebarConfig = useSidebarConfig();
  const [colDefs] = useState<ColDef[]>([
    {
      headerName: "#",
      cellRenderer: (params: any) => {
        return (
          <div className="pt-3">
            {!params.node?.rowIndex ? 1 : params.node?.rowIndex + 1}
          </div>
        );
      },
      minWidth: 40,
      maxWidth: 40,
    },
    {
      field: "payment",
      headerName: t("header.payment"),
      cellDataType: "text",
      cellRenderer: PaymentRenderer,
      minWidth: 200,
    },
    {
      field: "payment_method",
      headerName: t("header.payment_method"),
      cellRenderer: PayementMethodRenderer,
      minWidth: 180,
    },
    {
      field: "amount",
      headerName: t("header.amount"),
      minWidth: 120,
      cellRenderer: AmountRenderer,
    },
    {
      field: "transaction_fee",
      headerName: t("header.transaction_fee"),
      minWidth: 150,
      cellRenderer: TransactionRenderer,
    },
    {
      field: "remain_applied_amount",
      headerName: t("header.remain_applied_amount"),
      minWidth: 240,
      cellRenderer: RemainAppliedAmountRenderer,
    },
    {
      field: "exchange_rate",
      headerName: t("header.exchange_rate"),
      cellRenderer: ExchangeRateRenderer,
      minWidth: 130,
    },
  ]);

  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
      flex: 1,
      minWidth: 100,
    }),
    []
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  const [quickFilterText] = useState<string>();

  const selectionColumnDef = useMemo(() => {
    return {
      minWidth: 44,
    };
  }, []);
  const { isRTL } = useDirection();

  const gridOptions = useMemo(
    () => ({
      enableRangeSelection: true,
      enableFillHandle: true,
      suppressRowClickSelection: true,
      rowHeight: 72,
      headerHeight: 60,
      detailRowHeight: 650,
      detailRowAutoHeight: true,
      suppressPropertyNamesCheck: true,
    }),
    []
  );

  return (
    <>
      <AgGridDataTable
        enableRtl={isRTL ? true : false}
        ref={gridRefProps || gridRef}
        selectionColumnDef={selectionColumnDef}
        columnDefs={exportColDefs || colDefs}
        rowData={records?.data || []}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        pagination={false}
        paginationPageSize={records?.per_page || 20}
        paginationPageSizeSelector={paginationPageSizeSelector}
        masterDetail
        detailCellRenderer={detailCellRenderer}
        detailCellRendererParams={{ t }}
        quickFilterText={quickFilterText}
        rowHeight={72}
        colResizeDefault="shift"
        headerHeight={60}
        sideBar={sidebarConfig}
        gridOptions={gridOptions}
      />
    </>
  );
};
