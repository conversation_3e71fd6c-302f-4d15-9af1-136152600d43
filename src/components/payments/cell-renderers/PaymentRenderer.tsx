import type { CustomCellRendererProps } from "ag-grid-react";
import { <PERSON>2, FileText, CircleDollarSign } from "lucide-react";
import Link from "next/link";
import { type FunctionComponent, useState } from "react";
import { useResponsive } from "@/hooks/use-mobile";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { ScrollArea } from "@/components/ui/scroll-area";
import { formatCurrency } from "@/lib/utils";
import { LoadingSpinner } from "@/components/Common_UI/loading";
import { usePaymentDetails } from "../services/payment-queries";
import { PaymentDetail } from "../services/config";
import clsx from "clsx";
import { removeUnderScore } from "@/utils/commons";
import { colorSystem, ColorSystemKey } from "@/lib/constant";

export const PaymentRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
  node,
  api,
}) => {
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const { isMobile } = useResponsive();

  const { data: paymentDetails = [], isLoading } = usePaymentDetails(data?.id);

  const colors = colorSystem[data?.status as ColorSystemKey] || {
    bg: "bg-green-500/10",
    txt: "text-green-500",
  };

  const handleClick = () => {
    if (isMobile) {
      setIsSheetOpen(true);
    } else {
      api.setRowNodeExpanded(node, !node.expanded);
    }
  };

  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: data?.currency,
    maximumFractionDigits: 2,
  });

  const getPaymentReference = (payment: PaymentDetail) => {
    switch (payment.type) {
      case "shipment":
        return payment?.shipmentInvoice?.invoice_number;
      case "clearance":
        return `PGLCB${payment?.clearanceInvoice?.id}`;
      case "exit_claim_charge":
        return `PGLE${payment?.exitClaimCharge?.id}`;
      case "detention_charge":
        return `PGLD${payment?.detentionCharge?.id}`;
      case "delivery_charge":
        return `PGLDO${payment?.deliveryChargeInvoice?.id}`;
      case "single_vcc":
        return `PGLV${payment?.singleVcc?.id}`;
      case "clear_log":
        return `PGLC${payment?.logInvoice?.id}`;
      default:
        return null;
    }
  };

  const getPaymentTypeColor = (type: string) => {
    const colors = {
      shipment: "bg-blue-500/10 text-blue-600",
      clearance: "bg-primary-500/10 text-primary-600",
      exit_claim_charge: "bg-red-500/10 text-red-600",
      detention_charge: "bg-purple-500/10 text-purple-600",
      delivery_charge: "bg-orange-500/10 text-orange-600",
      single_vcc: "bg-cyan-500/10 text-cyan-600",
      clear_log: "bg-pink-500/10 text-pink-600",
      mix: "bg-yellow-500/10 text-yellow-600",
      auction: "bg-indigo-500/10 text-indigo-600",
    };
    return (
      colors[type as keyof typeof colors] || "bg-gray-500/10 text-gray-600"
    );
  };
  const remainingAmount =
    Number(data?.amount) +
    Number(data?.transaction_fee) +
    Number(data?.inapplicable_amount) -
    Number(data?.amount_applied);

  const renderReferenceInfo = (payment: PaymentDetail) => {
    if (payment.type === "shipment") {
      return (
        <div className="flex items-center gap-2 text-[10px]">
          <div className="flex items-center gap-2 text-[10px]">
            <span className="font-medium select-text text-muted-foreground">
              INV:
            </span>
            <span className="font-medium select-text">
              {getPaymentReference(payment) || "-"}
            </span>
          </div>
          <div className="flex items-center gap-2 text-[10px]">
            <span className="font-medium select-text text-muted-foreground">
              CON:
            </span>
            <span className="font-medium select-text">
              {payment?.shipmentInvoice?.containers?.container_number || "-"}
            </span>
          </div>
        </div>
      );
    } else if (payment.type === "mix") {
      return (
        <div className="flex items-center gap-2 text-[10px]">
          <div className="flex items-center gap-2 text-[10px]">
            <span className="font-medium select-text text-muted-foreground">
              VIN:
            </span>
            <span className="font-medium select-text">
              {payment?.mixShippingVehicle?.vehicles?.vin || "-"}
            </span>
          </div>
          <div className="flex items-center gap-2 text-[10px]">
            <span className="font-medium select-text text-muted-foreground">
              Lot:
            </span>
            <span className="font-medium select-text">
              {payment?.mixShippingVehicle?.vehicles?.lot_number || "-"}
            </span>
          </div>
        </div>
      );
    } else if (payment.type === "auction") {
      return (
        <div className="flex items-center gap-2 text-[10px]">
          <div className="flex items-center gap-2 text-[10px]">
            <span className="font-medium select-text text-muted-foreground">
              VIN:
            </span>
            <span className="font-medium select-text">
              {payment?.vehicle?.vin || "-"}
            </span>
          </div>
          <div className="flex items-center gap-2 text-[10px]">
            <span className="font-medium select-text text-muted-foreground">
              Lot:
            </span>
            <span className="font-medium select-text">
              {payment?.vehicle?.lot_number || "-"}
            </span>
          </div>
        </div>
      );
    } else {
      return (
        <div className="flex items-center gap-2 text-[10px]">
          <div className="flex items-center gap-2 text-[10px]">
            <span className="font-medium select-text text-muted-foreground">
              INV:
            </span>
            <span className="font-medium select-text">
              {getPaymentReference(payment) || "-"}
            </span>
          </div>
        </div>
      );
    }
  };

  return (
    <>
      <div
        className="h-full w-full flex items-center justify-center gap-3 px-3 py-2 hover:bg-muted/50 transition rounded-lg cursor-pointer group md:cursor-default lg:cursor-default"
        onClick={handleClick}
      >
        <div className="flex flex-col flex-1 justify-center leading-tight select-text gap-2">
          <div className="flex items-center gap-1 text-sm font-medium">
            <span className="truncate px-2">{`PGLPN${data?.id}`} </span>
          </div>
          <div className="truncate px-2 text-xs text-primary/70">
            {data?.transaction_number}
          </div>

          {isMobile && (
            <div className="truncate px-2 text-xs text-primary/70">
              {formatter.format(Number(data?.amount))}
            </div>
          )}
        </div>

        <div className="flex flex-col items-center justify-center gap-1.5">
          {isMobile && (
            <div className="mt-1 flex flex-col items-center gap-1">
              <Badge
                variant="outline"
                className="text-xs text-primary/70 bg-primary/10 truncate max-w-[90px]"
              >
                {data?.payment_method}
              </Badge>
              <Badge
                variant="outline"
                className="text-xs text-primary/70 bg-primary/10 truncate max-w-[90px]"
              >
                {data?.currency}
              </Badge>
            </div>
          )}
          <Link
            href={data?.link ? data?.link : "#"}
            target="_blank"
            rel="noopener noreferrer"
          >
            <Link2
              className={clsx(
                "w-5 h-5 transition-transform group-hover:scale-110",
                data?.link ? "text-blue-500" : "text-red-500"
              )}
            />
          </Link>
        </div>
      </div>

      <Drawer open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <DrawerContent className="h-[85vh] rounded-t-3xl border-t-2 border-primary px-4 pb-2">
          <DrawerHeader className="sr-only">
            <DrawerTitle>Payment Details</DrawerTitle>
            <DrawerDescription>
              Detailed payment information for {`PGLPN${data.id}`}
            </DrawerDescription>
          </DrawerHeader>

          <div className="flex flex-col h-full">
            <div className="px-3 py-2 border-b border-border/50 bg-card/50">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-1">
                  <FileText className="w-3.5 h-3.5 text-primary" />
                  <span className="font-semibold text-[10px] text-foreground">
                    {`PGLPN${data.id}`}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <CircleDollarSign className="w-3.5 h-3.5 text-primary" />
                  <span className="font-medium text-[10px] text-foreground truncate">
                    {data?.transaction_number}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <Badge
                    className={`${colors.bg} ${colors.txt} border-none text-[9px] uppercase px-1.5 py-0.5`}
                    variant="outline"
                  >
                    {removeUnderScore(data?.payment_method)}
                  </Badge>
                  <Badge
                    className={`${colors.bg} ${colors.txt} border-none text-[9px] uppercase px-1.5 py-0.5`}
                    variant="outline"
                  >
                    {removeUnderScore(data?.currency)}
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground text-[10px]">
                      Amount:
                    </span>
                    <span className="text-blue-500 bg-blue-500/10 text-[10px] px-1.5 rounded-full">
                      {formatter.format(Number(data?.amount))}
                    </span>
                  </div>
                  <div className="flex justify-between text-[10px]">
                    <span className="text-muted-foreground">Applied:</span>
                    <span className="text-blue-500 bg-blue-500/10 px-1.5 rounded-full">
                      {formatter.format(Number(data?.amount_applied))}
                    </span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between text-[10px]">
                    <span className="text-muted-foreground">
                      Transaction Fee
                    </span>
                    <span className="text-blue-500 bg-blue-500/10 px-1.5 rounded-full">
                      {formatter.format(Number(data?.transaction_fee))}
                    </span>
                  </div>
                  <div className="flex justify-between text-[10px]">
                    <span className="text-muted-foreground">Rate:</span>
                    <span className="text-blue-500 bg-blue-500/10 px-1.5 rounded-full">
                      {data?.exchange_rate}
                    </span>
                  </div>
                </div>
              </div>

              <div className="mt-0.5 px-1 bg-primary/20 rounded border border-border/50">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-primary text-[10px]">Remaining:</span>
                  <span className="font-bold text-blue-500 bg-blue-500/10 text-[10px] px-1.5 py-0.5 rounded-full">
                    {formatter.format(
                      remainingAmount < 1 ? 0 : remainingAmount
                    )}
                  </span>
                </div>
              </div>
            </div>
            <ScrollArea className="flex-1">
              <div className="space-y-3 pb-4">
                {isLoading ? (
                  <div className="w-full h-64 space-y-3 flex justify-center items-center">
                    <LoadingSpinner className="h-10 w-10" />
                  </div>
                ) : paymentDetails.length > 0 ? (
                  paymentDetails.map((payment, index) => (
                    <Card
                      key={payment.id}
                      className="border border-primary/20 bg-card/50 backdrop-blur-sm mb-2"
                    >
                      <CardHeader className="pb-1 pt-3 px-3">
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 bg-primary/10 rounded-full flex items-center justify-center">
                              <span className="text-[8px] font-medium text-primary">
                                {index + 1}
                              </span>
                            </div>
                            <Badge
                              className={`${getPaymentTypeColor(
                                payment.type
                              )} border-none text-[9px] px-1.5 py-0.5`}
                              variant="outline"
                            >
                              {payment.type.replace(/_/g, " ").toUpperCase()}
                            </Badge>
                            <div className="p-1 rounded-lg bg-muted/20">
                              {renderReferenceInfo(payment)}
                            </div>
                            <div className="text-blue-500 bg-blue-500/10 text-[10px] px-1.5 py-0.5 rounded-full font-medium">
                              {formatCurrency(payment.amount_applied)}
                            </div>
                          </div>
                        </div>
                      </CardHeader>

                      <CardContent className="px-3 pb-2">
                        {payment.payment_allocations &&
                          payment.payment_allocations.length > 0 && (
                            <div className="mb-1">
                              <div className="text-[9px] font-medium text-muted-foreground mb-1">
                                PAYMENT BREAKDOWN
                              </div>
                              <div className="grid grid-cols-2 gap-1 text-xs">
                                {payment.payment_allocations
                                  .slice(0, 4)
                                  .map((allocation) => (
                                    <div
                                      key={allocation.id}
                                      className="flex justify-between text-[9px]"
                                    >
                                      <span className="text-muted-foreground truncate capitalize">
                                        {allocation.type.replace(/_/g, " ")}:
                                      </span>
                                      <span className="text-blue-500 bg-blue-500/10 px-1 rounded-full">
                                        {formatCurrency(allocation.amount)}
                                      </span>
                                    </div>
                                  ))}

                                {payment.payment_allocations.length > 4 && (
                                  <div className="col-span-2 flex justify-between items-center mt-1 pt-1 border-t border-border/30">
                                    <span className="text-[9px] text-primary">
                                      +{payment.payment_allocations.length - 4}{" "}
                                      more items
                                    </span>
                                    <span className="text-[9px] font-semibold text-primary">
                                      Total:{" "}
                                      {formatCurrency(
                                        payment.payment_allocations
                                          .reduce(
                                            (sum, allocation) =>
                                              sum +
                                              parseFloat(allocation.amount),
                                            0
                                          )
                                          .toString()
                                      )}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}

                        {payment.payment_remark && (
                          <div className="mb-1 p-1 bg-primary/20 rounded border border-border/50">
                            <div className="flex items-center justify-between text-xs">
                              <span className="text-[9px] text-muted-foreground truncate">
                                {payment.payment_remark.length > 60
                                  ? payment.payment_remark.substring(0, 65) +
                                    "..."
                                  : payment.payment_remark}
                              </span>
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <div className="w-16 h-16 bg-muted/30 rounded-full flex items-center justify-center mb-4">
                      <FileText className="w-8 h-8 text-muted-foreground/50" />
                    </div>
                    <p className="text-sm text-muted-foreground mb-1">
                      No payment details available
                    </p>
                    <p className="text-xs text-muted-foreground/70">
                      Check back later for updated payment information
                    </p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
};
