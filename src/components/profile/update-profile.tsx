"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { X, Upload, Pen, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useUpdateProfile } from "./service/profile-queries"
import { toast } from "sonner"
import { useTranslations } from "next-intl"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"

// Define the form schema with Zod
const formSchema = z.object({
  logo: z
    .instanceof(File)
    .optional(),
  profile_name: z.string().min(2, "Name must be at least 2 characters"),
});

export type updateProfileType = z.infer<typeof formSchema>

export default function UpdateProfile() {
  const t = useTranslations("profile");
  const session = useSession()
  const router = useRouter()
  // Initialize the form with react-hook-form
  const form = useForm<updateProfileType>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      profile_name: session.data?.profile.companies.profile_name || "",
      logo: undefined
    },
  })
  const { mutate, isPending } = useUpdateProfile()
  const defaultLogo = session.data?.profile.companies.logo || ''
  const [preview, setPreview] = useState<string>(defaultLogo)

  // Clean up object URLs when component unmounts
  useEffect(() => {
    return () => {
      if (preview) URL.revokeObjectURL(preview);
    };
  }, [preview]);

  // Handle form submission
  const onSubmit = (data: updateProfileType) => {
    mutate(data, {
      onSuccess: async (updateData) => {

        await session.update({
          profile: {
            ...session.data?.profile,
            companies: {
              ...session.data?.profile.companies,
              logo: updateData.data?.data?.logo,
              profile_name: updateData.data?.data?.profile_name
            },
          },
        })
        router.refresh()
        toast.success("Profile updated successfully")
        form.reset()
        setPreview("");
      },
      onError: (error) => {
        toast.error(" Profile updated successfully" + error.message)
      }
    })
  }

  // Generate image previews when files are selected
  const handleFileChange = (file: File | null) => {
    if (!file) {
      setPreview("");
      form.setValue("logo", undefined, { shouldValidate: true });
      return;
    }

    // Clean up previous preview
    if (preview) URL.revokeObjectURL(preview);

    // Create new preview
    const newPreview = URL.createObjectURL(file);
    setPreview(newPreview);

    // Update form value
    form.setValue("logo", file, {
      shouldValidate: true,
      shouldDirty: true,
      shouldTouch: true,
    });
  };

  // Remove a specific image from the selection
  const removeImage = () => {
    if (preview) URL.revokeObjectURL(preview);
    setPreview("");
    form.setValue("logo", undefined, { shouldValidate: true });
  };

  return (
    <Card className="w-full shadow-none border-none max-w-full mx-auto">
      <CardHeader>
        <CardTitle>{t('profile_tab.title')}</CardTitle>
        <CardDescription>
          {t('profile_tab.description')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-1">
            <Label htmlFor="current" className="text-sm font-medium">{t('profile_tab.new_name')}</Label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                <Pen className="h-4 w-4" />
              </div>
              <Input
                id="name"
                type={"text"}
                disabled={form.formState.isSubmitting}
                autoComplete="current-password"
                className="pl-10 focus:border-primary/50 focus:ring-1 focus:ring-primary/50"
                {...form.register("profile_name")}
              />
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex flex-col items-center justify-center border-2 border-dashed  rounded-lg p-4 transition-colors hover:border-primary">
              <Input
                type="file"
                accept="image/jpeg,image/png,image/webp,image/gif"
                className="hidden"
                id="image-upload"
                onChange={(e) => handleFileChange(e.target.files?.[0] || null)}
              />
              {preview ? (
                <div className="relative group aspect-square rounded-md overflow-hidden border border-gray-200 w-40 h-40 mx-auto">
                  <img
                    src={preview}
                    alt="Preview"
                    className="w-full h-full object-cover"
                  />
                  <button
                    type="button"
                    onClick={removeImage}
                    className="absolute top-1 right-1 bg-black/50 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                    aria-label="Remove image"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ) :
                <Label
                  htmlFor="image-upload"
                  className="flex flex-col items-center justify-center cursor-pointer"
                >
                  <Upload className="h-10 w-10 text-muted-foreground mb-2" />
                  <span className="text-sm font-medium text-muted-foreground">
                    {t('profile_tab.logo')}
                  </span>
                  <span className="text-xs text-muted-foreground mt-1">{t('profile_tab.size')}</span>
                </Label>
              }
            </div>



          </div>

          <div className="w-full  flex justify-end">
            <Button type="submit" >
              {isPending ? <Loader2 className="animate-spin" /> : t('password.button')}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card >
  )
}