"use client";
import { useTranslations } from "next-intl";
import CustomTabs from "../Common_UI/custom-tabs";
import AccountSetting from "./account-setting";
import PasswordSetting from "./password-setting";
import Photo from "@/components/Common_UI/photo";
import { SetStateAction } from "react";
import ManageDevices from "./manage-devices";
import { User, Lock, Laptop, UserRoundCogIcon } from "lucide-react";
import UpdateProfile from "./update-profile";
import { useSession } from "next-auth/react";
import { useUpdatePhoto } from "./service/profile-queries";
import { generateImageUrl } from "@/utils/generate-image-url";
import { toast } from "sonner";

export default function ProfileTabs() {
  const t = useTranslations("profile");
  const { mutate, isPending } = useUpdatePhoto();
  const session = useSession();
  const handleImageCropped = (
    croppedImage: string,
    setCloseDialog: React.Dispatch<SetStateAction<boolean>>
  ) => {
    const image = generateImageUrl(croppedImage);
    mutate(
      { profile: image },
      {
        onSuccess: async (data) => {
          await session.update({
            profile: {
              ...session.data?.profile,
              photo: data.data?.photo,
            },
          });
          setCloseDialog(false);
          toast.success("Profile updated successfully");
        },
      }
    );
  };
  return (
    <>
      <div className="relative min-h-[calc(100svh-66px)] py-8 grid grid-rows-[auto_1fr] w-full bg-gradient-to-b from-muted/50 to-background">
        <div className="flex justify-center relative">
          <Photo
            onImageCropped={handleImageCropped}
            defaultImage={session.data?.profile?.photo || " "}
            isUpdating={isPending}
            isAsync={true}
          />
        </div>
        <div className="py-4 flex justify-center px-4 md:px-0">
          <CustomTabs
            defaultValue="account"
            tabsClassName="w-full max-w-[700px] shadow-xl rounded-xl overflow-hidden bg-card border"
          >
            <CustomTabs.TabList
              tabsTriggers={[
                {
                  value: "account",
                  label: (
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      <span>{t("account.label")}</span>
                    </div>
                  ),
                },
                {
                  value: "password",
                  label: (
                    <div className="flex items-center gap-2">
                      <Lock className="h-4 w-4" />
                      <span>{t("password.label")}</span>
                    </div>
                  ),
                },
                {
                  value: "devices",
                  label: (
                    <div className="flex items-center gap-2">
                      <Laptop className="h-4 w-4" />
                      <span>{t("devices.label")}</span>
                    </div>
                  ),
                },
                {
                  value: "profile",
                  label: (
                    <div className="flex items-center gap-2">
                      <UserRoundCogIcon className="h-4 w-4" />
                      <span>{t("profile_tab.label")}</span>
                    </div>
                  ),
                },
              ].filter((el) => {
                if (
                  el.value === "profile" &&
                  session.data?.user_type === "customer_of_customer"
                ) {
                  return false;
                }
                if (
                  el.value === "profile" &&
                  !session.data?.profile.companies.has_customer === true
                ) {
                  return false;
                } else {
                  return true;
                }
              })}
              tabsListClassName={`grid w-full ${
                session.data?.user_type === "customer_of_customer" ||
                !session.data?.profile.companies.has_customer === true
                  ? "grid-cols-3"
                  : "grid-cols-4"
              } p-1 bg-muted/50 border-b`}
            />
            <CustomTabs.TabContent
              tabsContent={[
                {
                  children: (
                    <div
                      className="p-1 overflow-auto"
                      style={{ height: "550px" }}
                    >
                      <AccountSetting />
                    </div>
                  ),
                  value: "account",
                },
                {
                  children: (
                    <div
                      className="p-1 overflow-auto"
                      style={{ height: "550px" }}
                    >
                      <PasswordSetting />
                    </div>
                  ),
                  value: "password",
                },
                {
                  children: (
                    <div
                      className="p-1 overflow-auto"
                      style={{ height: "550px" }}
                    >
                      <ManageDevices />
                    </div>
                  ),
                  value: "devices",
                },
                {
                  children: (
                    <div
                      className="p-1 overflow-auto"
                      style={{ height: "550px" }}
                    >
                      <UpdateProfile />
                    </div>
                  ),
                  value: "profile",
                },
              ]}
            />
          </CustomTabs>
        </div>
      </div>
    </>
  );
}
