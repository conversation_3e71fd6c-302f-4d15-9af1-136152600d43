import { useFetchClient } from "@/utils/axios";
import { useMutation, useQuery } from "@tanstack/react-query";
import { UpdatePasswordType } from "../schemas/password-schema";
import { useSession } from "next-auth/react";
import { updateProfileType } from "../update-profile";

export function useUpdatePassword() {
  const fetchClient = useFetchClient();

  const { mutate: updatePassword, isPending } = useMutation({
    mutationFn: (data: UpdatePasswordType) =>
      fetchClient("/v2/auth/changeCredentials", {
        method: "PATCH",
        data: data,
      }),
  });
  return { updatePassword, isPending };
}

export function useGetDevices() {
  const session = useSession();
  const fetchClient = useFetchClient();
  const userId = session.data?.profile?.loginable.id;
  return useQuery({
    queryKey: ["getDevices"],
    queryFn: () => getDevices(fetchClient, userId),
    // staleTime: 24 * 60 * 60 * 1000, // Keep data in cache for 1 day
    // gcTime: 24 * 60 * 60 * 1000, // Keep data in cache for 1 day
  });
}

async function getDevices(
  fetchClient: ReturnType<typeof useFetchClient>,
  userId?: number
) {
  try {
    if (!userId) return [];
    const res = await fetchClient("/v2/auth/list-devices", {
      params: {
        userId: userId,
      },
    });

    if (Array.isArray(res.data)) {
      return res.data;
    } else {
      console.error("Unexpected response format:", res.data);
      return [];
    }
  } catch (error) {
    console.error("Error fetching devices:", error);
    throw new Error("Failed to fetch devices");
  }
}

export const useRevokeDevice = () => {
  const fetchClient = useFetchClient();

  return useMutation({
    mutationFn: (sessionId: number) =>
      fetchClient("/v2/auth/logout", {
        params: { sessionId },
        method: "POST",
      }),
  });
};

export const useRemoveDevice = () => {
  const fetchClient = useFetchClient();

  return useMutation({
    mutationFn: (sessionId: number) =>
      fetchClient("/v2/auth/delete-device", {
        params: { sessionId },
        method: "DELETE",
      }),
  });
};

export function useUpdateProfile() {
  const fetchClient = useFetchClient();

  return useMutation({
    mutationFn: (data: updateProfileType) =>
      fetchClient("/v2/auth/uploadProfile", {
        data: { ...data },
        method: "POST",
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }),
  });
}

export function useUpdatePhoto() {
  const fetchClient = useFetchClient();

  return useMutation({
    mutationFn: (data: { profile: File | undefined }) =>
      fetchClient("/v2/auth/updatePhoto", {
        data: { ...data },
        method: "POST",
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }),
  });
}
