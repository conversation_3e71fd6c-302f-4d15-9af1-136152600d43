import { z } from 'zod';

/**
 * Simple password validation function
 */
export function validatePassword(password: string, confirmPassword: string) {
    return {
        minLength: password.length >= 6,
        hasNumber: /\d/.test(password),
        hasSpecial: /[!@#$%^&*(),.?":{}|<>]/.test(password),
        passwordsMatch: password === confirmPassword && confirmPassword !== ""
    };
}

/**
 * Password schema for form validation
 */
export const passwordSchema = z.object({
    username: z.string().optional(),
    email: z.string().email().optional(),
    old_password: z.string().min(1, "Current password is required"),
    new_password: z.string().min(6, "Password must be at least 6 characters"),
    confirm_password: z.string().min(1, "Please confirm your password"),
}).refine((data) => data.new_password === data.confirm_password, {
    message: "Passwords don't match",
    path: ["confirm_password"],
});

export type UpdatePasswordType = Omit<z.infer<typeof passwordSchema>, 'confirm_password'>; 