import { useSession } from "next-auth/react";
import { useGetDevices, useRevokeDevice, useRemoveDevice } from "./service/profile-queries";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Laptop, Smartphone, Loader2, Trash2, LogOut, Clock, MapPin, Shield, CheckCircle2, RefreshCw } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { formatDistanceToNow, format } from "date-fns";
import { toast } from "sonner";
import { useState } from "react";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslations } from "next-intl";

export default function ManageDevices() {
    const t = useTranslations('profile.devices');
    const session = useSession();
    const currentSessionId = session.data?.sessionId;
    const [deviceToRevoke, setDeviceToRevoke] = useState<number | null>(null);
    const [deviceToRemove, setDeviceToRemove] = useState<number | null>(null);
    const [isAlertOpen, setIsAlertOpen] = useState(false);
    const [isRemoveAlertOpen, setIsRemoveAlertOpen] = useState(false);

    const { data: devices, isLoading, refetch } = useGetDevices();
    const { mutate: revokeDevice, isPending: isRevoking } = useRevokeDevice();
    const { mutate: removeDevice, isPending: isRemoving } = useRemoveDevice();

    const handleRevoke = (deviceId: number) => {
        setDeviceToRevoke(deviceId);
        setIsAlertOpen(true);
    };

    const handleRemove = (deviceId: number) => {
        setDeviceToRemove(deviceId);
        setIsRemoveAlertOpen(true);
    };

    const confirmRevoke = () => {
        if (deviceToRevoke === null) return;

        revokeDevice(deviceToRevoke, {
            onSuccess: () => {
                toast.success(t('logout_success'));
                refetch();
                setIsAlertOpen(false);
            },
            onError: (error) => {
                toast.error(t('logout_error'));
                console.error(error);
            }
        });
    };

    const confirmRemove = () => {
        if (deviceToRemove === null) return;

        removeDevice(deviceToRemove, {
            onSuccess: () => {
                toast.success(t('remove_success'));
                refetch();
                setIsRemoveAlertOpen(false);
            },
            onError: (error) => {
                toast.error(t('remove_error'));
                console.error(error);
            }
        });
    };

    const getDeviceIcon = (deviceInfo: string) => {
        try {
            const info = JSON.parse(deviceInfo);
            if (info.device.type === "mobile" || info.device.type === "tablet") {
                return <Smartphone className="h-6 w-6" />;
            }
            return <Laptop className="h-6 w-6" />;
        } catch (e: any) {
            console.error(e?.a);

            return <Laptop className="h-6 w-6" />;
        }
    };

    const getDeviceName = (deviceInfo: string) => {
        try {
            const info = JSON.parse(deviceInfo);
            const browser = info.browser.name !== "Unknown" ? info.browser.name : t('browser');
            const os = info.os.name !== "Unknown" ? `on ${info.os.name}` : "";
            const device = info.device.type !== "Unknown" ? info.device.type : t('device_type');

            return `${browser} ${os} (${device})`;
        } catch (e: any) {
            console.error(e?.a);
            return "Unknown Device";
        }
    };

    const getLocationInfo = (location: string) => {
        try {
            const locationData = JSON.parse(location);
            return `${locationData.city}, ${locationData.country}`;
        } catch (e: any) {
            console.error(e?.a);
            return t('location');
        }
    };

    const isCurrentDevice = (deviceId: number) => {
        return deviceId === currentSessionId;
    };

    if (isLoading) {
        return (
            <Card className="shadow-none border-0 p-6 relative">
                <CardHeader className="px-0 pb-6 sticky -top-1 z-10 bg-card pt-1 border-b border-transparent">
                    <div className="flex items-center gap-2 mb-2">
                        <Shield className="h-5 w-5 text-primary" />
                        <CardTitle className="text-2xl font-bold">{t('label')}</CardTitle>
                    </div>
                    <CardDescription>
                        {t('description')}
                    </CardDescription>
                </CardHeader>
                <CardContent className="p-0 pt-2">
                    <div className="space-y-4">
                        {[1, 2, 3].map((i) => (
                            <div
                                key={i}
                                className="border rounded-lg p-5 shadow-sm bg-card"
                            >
                                <div className="flex justify-between">
                                    <div className="flex items-center gap-3">
                                        <Skeleton className="h-12 w-12 " />
                                        <div>
                                            <Skeleton className="h-5 w-40" />
                                            <Skeleton className="h-4 w-24 mt-2" />
                                        </div>
                                    </div>
                                    <div>
                                        <Skeleton className="h-9 w-28 " />
                                    </div>
                                </div>
                                <div className="mt-4">
                                    <Skeleton className="h-4 w-full" />
                                </div>
                            </div>
                        ))}
                    </div>
                </CardContent>
            </Card>
        );
    }

    const sortedDevices = devices ? [...devices].sort((a, b) => {
        if (isCurrentDevice(a.id) && !isCurrentDevice(b.id)) return -1;
        if (!isCurrentDevice(a.id) && isCurrentDevice(b.id)) return 1;

        if (a.isActive && !b.isActive) return -1;
        if (!a.isActive && b.isActive) return 1;

        return new Date(b.lastActiveAt).getTime() - new Date(a.lastActiveAt).getTime();
    }) : [];

    return (
        <Card className="shadow-none border-0 p-6 relative">
            <CardHeader className="px-0 pb-6 sticky -top-1 z-10 bg-card pt-1 border-b border-transparent">
                <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                        <Shield className="h-5 w-5 text-primary" />
                        <CardTitle className="text-2xl font-bold">{t('label')}</CardTitle>
                    </div>

                </div>
                <CardDescription>
                    {t('description')}
                </CardDescription>
            </CardHeader>
            <CardContent className="p-0 pt-2">
                <div className="space-y-4">
                    {Array.isArray(sortedDevices) && sortedDevices.length > 0 ? (
                        sortedDevices.map((device: any) => {
                            const isCurrent = isCurrentDevice(device.id);
                            return (
                                <div
                                    key={device.id}
                                    className={`border rounded-lg p-5 shadow-sm hover:shadow-md transition-all duration-200 bg-card ${isCurrent ? 'border-primary/30 bg-primary/5' : device.isActive ? 'border-primary/20' : ''}`}
                                >
                                    <div className="flex justify-between">
                                        {/* Left side: Device info */}
                                        <div className="space-y-3">
                                            <div className="flex items-center gap-3">
                                                <div className={`p-2  ${device.isActive ? 'bg-primary/10 text-primary' : 'bg-muted'}`}>
                                                    {getDeviceIcon(device.deviceInfo)}
                                                </div>
                                                <div>
                                                    <div className="flex items-center gap-2">
                                                        <h3 className="font-medium text-base">{getDeviceName(device.deviceInfo)}</h3>
                                                        {isCurrent && (
                                                            <Badge variant="outline" className="bg-primary/20 text-primary border-primary/30 flex items-center gap-1">
                                                                <CheckCircle2 className="h-3 w-3" />
                                                                {t('current')}
                                                            </Badge>
                                                        )}
                                                        {device.isActive && !isCurrent && (
                                                            <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
                                                                {t('active')}
                                                            </Badge>
                                                        )}
                                                        {!device.isActive && (
                                                            <Badge variant="outline" className="bg-muted text-muted-foreground border-muted">
                                                                {t('logout')}
                                                            </Badge>
                                                        )}
                                                    </div>
                                                    <div className="flex items-center gap-4 mt-1">
                                                        <div className="flex items-center text-sm text-muted-foreground">
                                                            <MapPin className="h-3.5 w-3.5 mr-1" />
                                                            {getLocationInfo(device.location)}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex items-center text-sm text-muted-foreground">
                                                <Clock className="h-3.5 w-3.5 mr-1" />
                                                {t('last_active')}: {formatDistanceToNow(new Date(device.lastActiveAt), { addSuffix: true })}
                                                <span className="mx-1">•</span>
                                                <span className="text-xs">
                                                    {format(new Date(device.lastActiveAt), "MMM d, yyyy 'at' h:mm a")}
                                                </span>
                                            </div>
                                        </div>

                                        {/* Right side: Action buttons */}
                                        <div className="flex flex-col gap-2 justify-center">
                                            {device.isActive && !isCurrent && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="w-28   text-amber-600 border-amber-500 hover:bg-amber-50 hover:text-amber-700  dark:text-amber-500  "
                                                    onClick={() => handleRevoke(device.id)}
                                                    disabled={isRevoking && deviceToRevoke === device.id}
                                                >
                                                    {isRevoking && deviceToRevoke === device.id ? (
                                                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                                    ) : (
                                                        <LogOut className="h-4 w-4 mr-2" />
                                                    )}
                                                    {t('logout')}
                                                </Button>
                                            )}
                                            {!isCurrent && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="w-28 text-red-500 border-red-500 hover:bg-red-200 hover:text-red-800 "
                                                    onClick={() => handleRemove(device.id)}
                                                    disabled={isRemoving && deviceToRemove === device.id}
                                                >
                                                    {isRemoving && deviceToRemove === device.id ? (
                                                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                                    ) : (
                                                        <Trash2 className="h-4 w-4 mr-2" />
                                                    )}
                                                    {t('remove')}
                                                </Button>
                                            )}
                                            {isCurrent && (
                                                <div className="flex flex-col items-center gap-2">
                                                    <div className="flex items-center gap-2 bg-primary/10 text-primary px-3 py-1.5 rounded-sm ">
                                                        <CheckCircle2 className="h-4 w-4" />
                                                        <span className="text-sm font-medium">{t('current_device')}</span>
                                                    </div>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        className="w-28 text-primary border-primary/20 hover:bg-primary/5 "
                                                        onClick={() => refetch()}
                                                    >
                                                        <RefreshCw className="h-3.5 w-3.5 mr-1.5" />
                                                        {t('refresh')}
                                                    </Button>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            );
                        })
                    ) : (
                        <div className="text-center py-6 text-muted-foreground">
                            {t('no_devices')}
                        </div>
                    )}
                </div>
            </CardContent>

            {/* Logout Dialog */}
            <AlertDialog open={isAlertOpen} onOpenChange={setIsAlertOpen}>
                <AlertDialogContent className="max-w-md">
                    <AlertDialogHeader>
                        <AlertDialogTitle className="flex items-center gap-2">
                            <LogOut className="h-5 w-5 text-amber-600" />
                            {t('logout_title')}
                        </AlertDialogTitle>
                        <Separator className="my-2" />
                        <AlertDialogDescription>
                            {t('logout_description')}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel className="">{t('cancel')}</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={confirmRevoke}
                            className="bg-amber-600 hover:bg-amber-700 text-white "
                        >
                            {isRevoking ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                            {t('confirm_logout')}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Remove Dialog */}
            <AlertDialog open={isRemoveAlertOpen} onOpenChange={setIsRemoveAlertOpen}>
                <AlertDialogContent className="max-w-md">
                    <AlertDialogHeader>
                        <AlertDialogTitle className="flex items-center gap-2">
                            <Trash2 className="h-5 w-5 text-destructive" />
                            {t('remove_title')}
                        </AlertDialogTitle>
                        <Separator className="my-2" />
                        <AlertDialogDescription>
                            {t('remove_description')}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel className="">{t('cancel')}</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={confirmRemove}
                            className="bg-destructive hover:bg-destructive/90 "
                        >
                            {isRemoving ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                            {t('confirm_remove')}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </Card>
    );
}