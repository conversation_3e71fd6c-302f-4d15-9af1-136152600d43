import React from 'react'
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useTranslations } from 'next-intl'
import { useSession } from 'next-auth/react'
import { User, Mail, AtSign, BadgeCheck, Shield } from 'lucide-react'

export default function AccountSetting() {
    const t = useTranslations('profile.account');
    const session = useSession()
    return (
        <div className='p-6'>
            <Card className="border-0 shadow-none">
                <CardHeader className="px-0 sticky -top-1 z-10 bg-card pt-1 border-b border-transparent">
                    <div className="flex items-center gap-2 mb-2">
                        <Shield className="h-5 w-5 text-primary" />
                        <CardTitle className="text-2xl font-bold">{t('label')}</CardTitle>
                    </div>
                    <CardDescription>
                        {t('description')}
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 px-0 pt-4">
                    <div className="space-y-2">
                        <Label htmlFor="name" className="text-sm font-medium flex items-center gap-1">
                            {t('name')}
                            <BadgeCheck className="h-3.5 w-3.5 text-primary" />
                        </Label>
                        <div className="relative">
                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                                <User className="h-4 w-4" />
                            </div>
                            <Input
                                id="name"
                                defaultValue={session.data?.profile.fullname}
                                readOnly
                                placeholder={t('name')}
                                className="pl-10 bg-muted/50 focus:border-primary/50 focus:ring-1 focus:ring-primary/50"
                            />
                        </div>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="username" className="text-sm font-medium flex items-center gap-1">
                            {t('username')}
                            <BadgeCheck className="h-3.5 w-3.5 text-primary" />
                        </Label>
                        <div className="relative">
                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                                <AtSign className="h-4 w-4" />
                            </div>
                            <Input
                                id="username"
                                defaultValue={session.data?.profile.loginable.username}
                                readOnly
                                placeholder={t('username')}
                                className="pl-10 bg-muted/50 focus:border-primary/50 focus:ring-1 focus:ring-primary/50"
                            />
                        </div>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="email" className="text-sm font-medium flex items-center gap-1">
                            {t('email')}
                            <BadgeCheck className="h-3.5 w-3.5 text-primary" />
                        </Label>
                        <div className="relative">
                            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                                <Mail className="h-4 w-4" />
                            </div>
                            <Input
                                id="email"
                                defaultValue={session.data?.profile.loginable.email}
                                readOnly
                                placeholder={t('email')}
                                className="pl-10 bg-muted/50 focus:border-primary/50 focus:ring-1 focus:ring-primary/50"
                            />
                        </div>
                    </div>
                    <div className="bg-muted/30 p-4 rounded-lg border border-muted mt-4">
                        <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                            <Shield className="h-4 w-4 text-primary" />
                            {t('security.title')}
                        </h4>
                        <p className="text-xs text-muted-foreground">
                            {t('security.description')}
                        </p>
                    </div>
                </CardContent>

            </Card>
        </div>
    )
}
