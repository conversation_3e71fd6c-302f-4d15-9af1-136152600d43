import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "../ui/button";
import { useTranslations } from "next-intl";
import { useSession } from "next-auth/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useUpdatePassword } from "./service/profile-queries";
import { toast } from "sonner";
import {
  Eye,
  EyeOff,
  Lock,
  KeyRound,
  ShieldCheck,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import { LoadingSpinner } from "../Common_UI/loading";
import { passwordSchema, validatePassword } from "./schemas/password-schema";

export default function PasswordSetting() {
  const t = useTranslations("profile.password");
  const [isVisible1, setIsVisible1] = useState(false);
  const [isVisible2, setIsVisible2] = useState(false);
  const [isVisible3, setIsVisible3] = useState(false);
  const session = useSession();
  const { isPending, updatePassword } = useUpdatePassword();

  // Password validation states
  const [validations, setValidations] = useState({
    minLength: false,
    hasNumber: false,
    hasSpecial: false,
    passwordsMatch: false,
  });

  const form = useForm({
    defaultValues: {
      username: session.data?.profile.loginable.username,
      email: session.data?.profile.loginable.email,
      old_password: "",
      new_password: "",
      confirm_password: "",
    },
    resolver: zodResolver(passwordSchema),
    mode: "onChange",
  });

  // Watch password fields for real-time validation
  const newPassword = form.watch("new_password");
  const confirmPassword = form.watch("confirm_password");

  // Update validation state when passwords change
  useEffect(() => {
    setValidations(validatePassword(newPassword, confirmPassword));
  }, [newPassword, confirmPassword]);

  const handleSubmit = (data: any) => {
    // Remove confirm_password before sending to API
    const { confirm_password, ...submitData } = data;

    // Check if passwords match
    if (data.new_password !== data.confirm_password) {
      toast.error(t("passwords_dont_match"));
      return;
    }

    updatePassword(submitData, {
      onSuccess: () => {
        toast.success(t("update_success"));
        form.reset({
          username: session.data?.profile.loginable.username,
          email: session.data?.profile.loginable.email,
          old_password: "",
          new_password: "",
          confirm_password: "",
        });
      },
      onError: (error: any) => {
        console.error(error);
        toast.error(error?.response?.data?.message || t("update_error"));
      },
    });
  };

  return (
    <div className="p-6">
      <Card className="border-0 shadow-none">
        <form onSubmit={form.handleSubmit(handleSubmit)}>
          <CardHeader className="px-0 sticky -top-1 z-10 bg-card pt-1 border-b border-transparent">
            <div className="flex items-center gap-2 mb-2">
              <ShieldCheck className="h-5 w-5 text-primary" />
              <CardTitle className="text-2xl font-bold">{t("label")}</CardTitle>
            </div>
            <CardDescription className="text-muted-foreground">
              {t("description")}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3 px-0 pt-3">
            <div className="space-y-1">
              <Label htmlFor="current" className="text-sm font-medium">
                {t("current_password")}
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                  <Lock className="h-4 w-4" />
                </div>
                <Input
                  id="password"
                  type={isVisible1 ? "text" : "password"}
                  disabled={form.formState.isSubmitting}
                  autoComplete="current-password"
                  className="pl-10 focus:border-primary/50 focus:ring-1 focus:ring-primary/50"
                  {...form.register("old_password")}
                />
                <Button
                  className="absolute inset-y-0 end-0 flex h-full w-9 text-foreground/80 hover:text-foreground/100"
                  type="button"
                  onClick={() => setIsVisible1(!isVisible1)}
                  aria-label={
                    isVisible1 ? t("hide_password") : t("show_password")
                  }
                  aria-pressed={isVisible1}
                  aria-controls="password"
                  variant={"link"}
                >
                  {isVisible1 ? (
                    <EyeOff size={16} strokeWidth={2} aria-hidden="true" />
                  ) : (
                    <Eye size={16} strokeWidth={2} aria-hidden="true" />
                  )}
                </Button>
              </div>
              {form.formState.errors.old_password && (
                <p className="text-xs text-red-500 mt-1 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {form.formState.errors.old_password.message}
                </p>
              )}
            </div>
            <div className="space-y-1">
              <Label htmlFor="new" className="text-sm font-medium">
                {t("new_password")}
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                  <KeyRound className="h-4 w-4" />
                </div>
                <Input
                  id="new"
                  type={isVisible2 ? "text" : "password"}
                  className={`pl-10 focus:ring-1 ${
                    newPassword
                      ? validations.minLength
                        ? "border-green-500/50 focus:ring-green-500/50"
                        : "border-orange-500/50 focus:ring-orange-500/50"
                      : "focus:border-primary/50 focus:ring-primary/50"
                  }`}
                  {...form.register("new_password")}
                />
                <Button
                  className="absolute inset-y-0 end-0 flex h-full w-9 text-foreground/80 hover:text-foreground/100"
                  type="button"
                  onClick={() => setIsVisible2(!isVisible2)}
                  aria-label={
                    isVisible2 ? t("hide_password") : t("show_password")
                  }
                  aria-pressed={isVisible2}
                  aria-controls="new"
                  variant={"link"}
                >
                  {isVisible2 ? (
                    <EyeOff size={16} strokeWidth={2} aria-hidden="true" />
                  ) : (
                    <Eye size={16} strokeWidth={2} aria-hidden="true" />
                  )}
                </Button>
              </div>
              {form.formState.errors.new_password && (
                <p className="text-xs text-red-500 mt-1 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {form.formState.errors.new_password.message}
                </p>
              )}
            </div>

            {/* Confirm password field */}
            <div className="space-y-1">
              <Label htmlFor="confirm" className="text-sm font-medium">
                {t("confirm_password")}
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-muted-foreground">
                  <KeyRound className="h-4 w-4" />
                </div>
                <Input
                  id="confirm"
                  type={isVisible3 ? "text" : "password"}
                  className={`pl-10 focus:ring-1 ${
                    confirmPassword
                      ? validations.passwordsMatch
                        ? "border-green-500/50 focus:ring-green-500/50"
                        : "border-orange-500/50 focus:ring-orange-500/50"
                      : "focus:border-primary/50 focus:ring-primary/50"
                  }`}
                  {...form.register("confirm_password")}
                />
                <Button
                  className="absolute inset-y-0 end-0 flex h-full w-9 text-foreground/80 hover:text-foreground/100"
                  type="button"
                  onClick={() => setIsVisible3(!isVisible3)}
                  aria-label={
                    isVisible3 ? t("hide_password") : t("show_password")
                  }
                  aria-pressed={isVisible3}
                  aria-controls="confirm"
                  variant={"link"}
                >
                  {isVisible3 ? (
                    <EyeOff size={16} strokeWidth={2} aria-hidden="true" />
                  ) : (
                    <Eye size={16} strokeWidth={2} aria-hidden="true" />
                  )}
                </Button>
              </div>
              {form.formState.errors.confirm_password && (
                <p className="text-xs text-red-500 mt-1 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {form.formState.errors.confirm_password.message}
                </p>
              )}
            </div>

            {/* Dynamic password requirements */}
            <div className="bg-muted/30 p-3 rounded-lg border border-muted mt-2">
              <h4 className="text-sm font-medium mb-1.5 flex items-center gap-2">
                <Lock className="h-4 w-4 text-primary" />
                {t("requirements")}
              </h4>
              <ul className="text-xs space-y-1 text-muted-foreground">
                <li className="flex items-center gap-1.5">
                  {validations.minLength ? (
                    <CheckCircle className="h-3.5 w-3.5 text-green-500" />
                  ) : (
                    <div
                      className={`h-1.5 w-1.5 rounded-full ${
                        newPassword ? "bg-orange-500" : "bg-primary"
                      }`}
                    ></div>
                  )}
                  {t("min_length")}
                </li>
                <li className="flex items-center gap-1.5">
                  {validations.hasNumber ? (
                    <CheckCircle className="h-3.5 w-3.5 text-green-500" />
                  ) : (
                    <div className="h-1.5 w-1.5 rounded-full bg-muted-foreground"></div>
                  )}
                  {t("include_number")}
                </li>
              </ul>
            </div>
          </CardContent>
          <CardFooter className="w-full flex justify-end px-0 pt-1">
            <Button
              type="submit"
              className="px-6"
              disabled={
                isPending ||
                !form.formState.isDirty ||
                !validations.minLength ||
                !validations.passwordsMatch ||
                !!form.formState.errors.old_password
              }
            >
              {isPending ? (
                <LoadingSpinner className="animate-spin h-5 w-5 mr-2" />
              ) : (
                <ShieldCheck className="h-4 w-4 mr-2" />
              )}
              {t("button")}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
