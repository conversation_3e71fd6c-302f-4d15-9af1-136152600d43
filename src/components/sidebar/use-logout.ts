"use client";
import { useMutation } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
export const useLogout = () => {
  const fetchClient = useFetchClient();

  const { mutate: logoutUser, isPending } = useMutation({
    mutationFn: async (id?: number) =>
      await fetchClient("/v2/auth/logout", {
        params: {
          sessionId: id,
        },
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      }),
  });
  return { logoutUser, isPending };
};
