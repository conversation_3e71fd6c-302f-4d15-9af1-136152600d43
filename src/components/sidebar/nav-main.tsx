"use client";
import { Ch<PERSON>ronLeft, ChevronRight, type LucideIcon } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import { Link, usePathname } from "@/i18n/routing";
import { useDirection } from "@/hooks/useDirection";
import { useEffect, useState } from "react";
import { useSidebarCountContext } from "@/context/sidebar-count-context";

interface SidebarItem {
  title: string;
  url: string;
  icon?: LucideIcon;
  items?: SidebarItem[];
  key?: string;
}
export function NavMain({
  items,
  sidebarCounts,
}: {
  items: (SidebarItem | null)[];
  sidebarCounts: { [key: string]: number | string };
}) {
  const pathname = usePathname();
  const { dir } = useDirection();
  const { getSidebarCounts, fetchableCount } = useSidebarCountContext();

  const [expandedItems, setExpandedItems] = useState<string[]>(() => {
    const initialExpanded: string[] = [];
    items.forEach((item) => {
      if (item && pathname.includes(item.url)) {
        initialExpanded.push(item.url);
      }
    });
    return initialExpanded;
  });

  const filteredItems = items.filter(
    (item): item is SidebarItem => item !== null
  );

  // Badge component to display counts
  const Badge = ({ count }: { count?: number | string }) =>
    count ? (
      <span className="ms-auto rounded-full bg-primary/70 px-2 py-0.4 text-[10px] font-bold text-foreground text-white fade-in">
        {count}
      </span>
    ) : null;

  const MenuWithChild = ({ item }: { item: SidebarItem }) => {
    const ChevronIcon = dir === "ltr" ? ChevronRight : ChevronLeft;

    const isOpen = expandedItems.includes(item.url);

    const handleOpenChange = (open: boolean) => {
      setExpandedItems((prev) =>
        open ? [...prev, item.url] : prev.filter((url) => url !== item.url)
      );
    };

    useEffect(() => {
      if (item.key && isOpen && fetchableCount.includes(item.key)) {
        getSidebarCounts({ key: item.key });
      }
    }, [isOpen]);
    return (
      <Collapsible
        key={item.title}
        open={isOpen}
        onOpenChange={handleOpenChange}
        className="group/collapsible"
        asChild
      >
        <SidebarMenuItem>
          <CollapsibleTrigger asChild>
            <SidebarMenuButton
              tooltip={item.title}
              isActive={pathname === item.url}
              dir={dir}
            >
              <div className="flex items-center flex-1 gap-2">
                {item.icon && <item.icon className="flex-shrink-0 w-4 h-4" />}
                <span className="truncate">{item.title}</span>
              </div>

              <div className="ml-auto flex items-center gap-1">
                <Badge count={sidebarCounts[item.key!]} />
                <ChevronIcon
                  size={16}
                  className={`transition-transform duration-200 ${
                    isOpen ? (dir === "ltr" ? "rotate-90" : "-rotate-90") : ""
                  }`}
                />
              </div>
            </SidebarMenuButton>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <SidebarMenuSub>
              {item.items?.map((subItem) =>
                subItem.items ? (
                  <MenuWithChild key={subItem.title} item={subItem} />
                ) : (
                  <SidebarMenuSubItem key={subItem.title}>
                    <SidebarMenuSubButton
                      asChild
                      isActive={pathname === subItem.url}
                      onClick={() =>
                        subItem.key && getSidebarCounts({ key: subItem.key })
                      }
                    >
                      <Link href={subItem.url} className="flex justify-between">
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                          {subItem.icon && <subItem.icon />}
                          <span className="truncate">{subItem.title}</span>
                        </div>
                        <Badge count={sidebarCounts[subItem.key!]} />
                      </Link>
                    </SidebarMenuSubButton>
                  </SidebarMenuSubItem>
                )
              )}
            </SidebarMenuSub>
          </CollapsibleContent>
        </SidebarMenuItem>
      </Collapsible>
    );
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel></SidebarGroupLabel>
      <SidebarMenu>
        {filteredItems.map((item) =>
          item.items ? (
            <MenuWithChild key={item.title} item={item} />
          ) : (
            <SidebarMenuItem key={item.title}>
              <Link href={item.url}>
                <SidebarMenuButton
                  tooltip={item.title}
                  isActive={pathname === item.url}
                  onClick={() =>
                    item.key && getSidebarCounts({ key: item.key })
                  }
                >
                  {item.icon && <item.icon />}
                  <span className="truncate">{item.title}</span>
                  <Badge count={sidebarCounts[item.key!]} />
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          )
        )}
      </SidebarMenu>
    </SidebarGroup>
  );
}
