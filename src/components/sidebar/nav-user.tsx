"use client";
import {
  BadgeCheck,
  ChevronRight,
  ChevronLeft,
  ChevronsUpDown,
  Loader2,
  LogOut,
  Moon,
  Sun,
} from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useTheme } from "next-themes";
import { useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useDirection } from "@/hooks/useDirection";
import { signOut, useSession } from "next-auth/react";
import { useLogout } from "./use-logout";
import { toast } from "sonner";
import { Button } from "../ui/button";
import { useProgress } from "@bprogress/next";
export function NavUser({
  user,
}: {
  user: {
    name: string;
    email: string;
    avatar: string;
  };
}) {
  const { isPending, logoutUser } = useLogout();
  const [isOpen, setIsOpen] = useState(false);
  const { isAppSidebar } = useSidebar();
  const { theme, setTheme } = useTheme();
  const { dir, isRTL } = useDirection();
  const session = useSession();
  const locale = useLocale();
  const t = useTranslations("sidebar");
  const router = useRouter();
  const { start } = useProgress();
  async function onLogout() {
    logoutUser(session?.data?.sessionId, {
      onSuccess: async () => {
        await signOut({
          redirect: false,
        });
        start();

        router.push(`/api/auth/logout`);
        setIsOpen(false);
      },
      onError: (error: any) => {
        console.log(error);
        toast.error("error", error?.message);
      },
    });
  }

  return (
    <>
      <SidebarMenu dir={dir}>
        <SidebarMenuItem dir={dir}>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton
                size="lg"
                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground relative flex items-center gap-2 overflow-visible"
                dir={dir}
              >
                <Avatar className="h-8 w-8 rounded-lg overflow-visible">
                  <AvatarImage
                    src={user.avatar}
                    alt={user.name}
                    className="rounded-lg"
                  />
                  <AvatarFallback className="rounded-lg">
                    {user?.name
                      ?.split(" ")
                      .slice(0, 2)
                      .map((word) => word[0])
                      .join("")
                      .toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                {/* <span className={`absolute top-0 font-extrabold bg- text-green-400 text-foreground rounded-full p-[1px] ${!isRTL ? "left-1" : "right-1"} `}>.</span> */}
                <div
                  className={`grid flex-1 text-${isRTL ? "right" : "left"
                    } text-sm leading-tight`}
                >
                  <span className="truncate font-semibold">{user.name}</span>
                  <span className="truncate text-xs">{user.email}</span>
                </div>
                <ChevronsUpDown
                  className={`size-4 group-data-[state=collapsed]:hidden`}
                />
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
              side={isAppSidebar ? "bottom" : isRTL ? "left" : "right"}
              align="end"
              sideOffset={4}
            >
              <DropdownMenuLabel className="p-0 font-normal">
                <div
                  className={`flex items-center gap-2 px-1 py-1.5 text-left text-sm `}
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={user.avatar} alt={user.name} />
                    <AvatarFallback className="rounded-lg">
                      {" "}
                      {user?.name
                        ?.split(" ")
                        .slice(0, 2)
                        .map((word) => word[0])
                        .join("")
                        .toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div
                    className={`grid flex-1 text-${isRTL ? "right" : "left"
                      } text-sm leading-tight`}
                  >
                    <span className="truncate font-semibold">{user.name}</span>
                    <span className="truncate text-xs">{user.email}</span>
                  </div>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem
                  onClick={() => {
                    start();
                    router.push(`/${locale}/profile`);
                  }}
                >
                  <BadgeCheck className="h-4 w-4" />
                  {t("account.label")}
                </DropdownMenuItem>
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger
                    Icon={isRTL ? ChevronLeft : ChevronRight}
                  >
                    <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
                    <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
                    {t("account.mode.label")}
                  </DropdownMenuSubTrigger>
                  <DropdownMenuPortal>
                    <DropdownMenuSubContent>
                      <DropdownMenuCheckboxItem
                        checked={theme === "system"}
                        onCheckedChange={() => setTheme("system")}
                      >
                        {t("account.mode.system")}
                      </DropdownMenuCheckboxItem>
                      <DropdownMenuCheckboxItem
                        checked={theme === "light"}
                        onCheckedChange={() => setTheme("light")}
                      >
                        {t("account.mode.light")}
                      </DropdownMenuCheckboxItem>
                      <DropdownMenuCheckboxItem
                        checked={theme === "dark"}
                        onCheckedChange={() => setTheme("dark")}
                      >
                        {t("account.mode.dark")}
                      </DropdownMenuCheckboxItem>
                    </DropdownMenuSubContent>
                  </DropdownMenuPortal>
                </DropdownMenuSub>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setIsOpen(true)}>
                <LogOut className={`${isRTL ? "rotate-180" : ""}`} />
                {t("account.logout")}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuItem>
      </SidebarMenu>
      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className={`${isRTL ? "text-right" : ""}`}>
              {" "}
              {t("account.popup.title")}
            </AlertDialogTitle>
            <AlertDialogDescription className={`${isRTL ? "text-right" : ""}`}>
              {t("account.popup.description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter dir={dir} className={`${isRTL ? "gap-2" : ""}`}>
            <AlertDialogCancel>{t("account.popup.cancel")}</AlertDialogCancel>
            <Button onClick={onLogout} disabled={isPending}>
              {isPending ? (
                <Loader2 className="animate-spin" />
              ) : (
                t("account.popup.continue")
              )}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
