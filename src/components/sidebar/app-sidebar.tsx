"use client";
import * as React from "react";
import {
  BellRingIcon,
  BookTextIcon,
  Bus,
  CarFront,
  ChartNoAxesGantt,
  DollarSign,
  Hash,
  Loader2,
  ReceiptSwissFranc,
  RefreshCw,
  Ship,
  SquareTerminal,
  TrendingUp,
  User,
} from "lucide-react";
import { NavMain } from "@/components/sidebar/nav-main";
import { NavUser } from "@/components/sidebar/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { useDirection } from "@/hooks/useDirection";
import { useQuery } from "@tanstack/react-query";
import {
  getAllLocations,
  getDestinations,
} from "../vehicles/services/vehicle-service";
import { useSession } from "next-auth/react";
import { useSidebarCountContext } from "@/context/sidebar-count-context";
import { Button } from "../ui/button";
import { useDomain } from "@/hooks/get-domain";

export const AppSidebar = React.memo((props: any) => {
  const t = useTranslations("sidebar");
  const session = useSession();
  const { isRTL, dir } = useDirection();
  const domain = useDomain();

  const isNotPglDomain = !(
    domain?.includes("localhost") || domain?.includes("pglsystem.com")
  );

  const profile = session.data?.profile;

  const companies = profile?.companies;
  const companyFromCompanies = Array.isArray(companies)
    ? companies[0]
    : companies;

  const company = companyFromCompanies || profile?.company;

  const profileImage =
    isNotPglDomain && company?.logo ? `${company.logo}` : "/logo.png";

  const profile_name =
    isNotPglDomain && company?.profile_name ? company.profile_name : undefined;
  const { sidebarCounts, revalidateLoading, revalidateSidebarCounts } =
    useSidebarCountContext();

  const { data: locations = [] } = useQuery({
    queryKey: ["sidebar-locations"],
    queryFn: getAllLocations,
  });

  const { data: destinations = [] } = useQuery({
    queryKey: ["sidebar-destinations"],
    queryFn: getDestinations,
  });

  // Add keys to locations and destinations
  const locationsItems = React.useMemo(
    () =>
      locations?.map((location: any) => ({
        title: location.name,
        url: `/vehicles/inventory-pol/${location.name}`,
        key: `inventory_pol${
          location.id || location.name.toLowerCase().replace(/\s+/g, "_")
        }`,
      })) ?? [],
    [locations]
  );

  const destinationsItems = React.useMemo(
    () =>
      destinations?.map((destination: any) => ({
        title: destination.name,
        url: `/vehicles/inventory-pod/${destination.name}`,
        key: `inventory_pod${
          destination.id || destination.name.toLowerCase().replace(/\s+/g, "_")
        }`,
      })) ?? [],
    [destinations]
  );

  const customerNavItem = [
    {
      title: t("nav.dashboard"),
      url: "/",
      icon: SquareTerminal,
      key: "dashboard",
    },
    {
      title: t("nav.vechicles.label"),
      url: "/c_vehicles",
      icon: CarFront,
      key: "c_vehicles",
    },
  ];

  const towingItems = React.useMemo(() => {
    const items = [];
    if (session.data?.profile?.companies?.complete) {
      items.push({
        title: t("nav.towing-rates.complete"),
        url: "/towing-rates/complete",
        key: "towing_complete",
      });
    }
    if (session.data?.profile?.companies?.complete_halfcut) {
      items.push({
        title: t("nav.towing-rates.half-cut"),
        url: "/towing-rates/halfcut",
        key: "towing_halfcut",
      });
    }
    return items;
  }, [session.data?.profile, t]);

  const towingNavItem = towingItems.length
    ? {
        title: t("nav.towing-rates.label"),
        url: "/towing_rates",
        icon: TrendingUp,
        key: "towing_rates",
        items: towingItems,
      }
    : null;

  const items = React.useMemo(() => {
    return [
      {
        title: t("nav.dashboard"),
        url: "/",
        icon: SquareTerminal,
        key: "dashboard",
      },
      {
        title: t("nav.vechicles.label"),
        url: "/vehicles",
        icon: CarFront,
        key: "vehicles",
        items: [
          {
            title: t("nav.vechicles.all"),
            url: "/vehicles/all",
            key: "vehicles",
          },
          {
            title: t("nav.vechicles.auction-unpaid"),
            url: "/vehicles/auction_unpaid",
            key: "vehicles_auction_unpaid",
          },
          {
            title: t("nav.vechicles.auction-paid"),
            url: "/vehicles/auction_paid",
            key: "vehicles_auction_paid",
          },
          {
            title: t("nav.vechicles.on-the-way"),
            url: "/vehicles/on_the_way",
            key: "vehicles_on_the_way",
          },
          {
            title: t("nav.vechicles.on-the-hand-no"),
            url: "/vehicles/on_hand_no_title",
            key: "vehicles_on_hand_no_title",
          },
          {
            title: t("nav.vechicles.on-the-hand-with"),
            url: "/vehicles/on_hand_with_title",
            key: "vehicles_on_hand_with_title",
          },
          {
            title: t("nav.vechicles.on-the-hand-with-load"),
            url: "/vehicles/on_hand_with_load",
            key: "vehicles_on_hand_with_load",
          },
          {
            title: t("nav.vechicles.inventory-poD.label"),
            url: "/vehicles/inventory-pod",
            key: "inventory_pod",
            items: [
              {
                title: t("nav.vechicles.inventory-poD.all"),
                url: "/vehicles/inventory-pod/all",
                key: "inventory_pod",
              },
              {
                title: t("nav.vechicles.inventory-poD.without-pod"),
                url: "/vehicles/inventory-pod/none",
                key: "without_pod",
              },
              ...destinationsItems,
            ],
          },
          {
            title: t("nav.vechicles.inventory-pol.label"),
            url: "/vehicles/inventory-pol",
            key: "inventory_pol",
            items: [
              {
                title: t("nav.vechicles.inventory-pol.all"),
                url: "/vehicles/inventory-pol/all",
                key: "inventory_pol",
              },
              ...locationsItems,
            ],
          },
          {
            title: t("nav.vechicles.shipped"),
            url: "/vehicles/shipped",
            key: "vehicles_shipped",
          },
          {
            title: t("nav.vechicles.cost-analysis"),
            url: "/vehicles/cost-analysis",
            key: "vehicles_cost_analysis",
          },
          {
            title: t("nav.vechicles.dateline"),
            url: "/vehicles/datelines",
            key: "vehicles_datelines",
          },
        ],
      },
      {
        title: t("nav.shipments.label"),
        url: "/shipments",
        icon: Ship,
        key: "containers",
        items: [
          {
            title: t("nav.shipments.all"),
            url: "/shipments/all",
            key: "containers",
          },
          {
            title: t("nav.shipments.at-loading"),
            url: "/shipments/at_loading",
            key: "at_loading",
          },
          {
            title: t("nav.shipments.on-the-way"),
            url: "/shipments/on_the_way",
            key: "on_the_way",
          },
          {
            title: t("nav.shipments.arrived"),
            url: "/shipments/arrived",
            key: "arrived",
          },
        ],
      },
      {
        title: t("nav.invoice.label"),
        url: "/invoices",
        icon: BookTextIcon,
        key: "invoices",
        items: [
          {
            title: t("nav.invoice.all"),
            url: "/invoices/all",
            key: "invoices",
          },
          {
            title: t("nav.invoice.open"),
            url: "/invoices/open",
            key: "invoice_open",
          },
          {
            title: t("nav.invoice.past-due"),
            url: "/invoices/past_due",
            key: "invoice_past_due",
          },
          {
            title: t("nav.invoice.paid"),
            url: "/invoices/paid",
            key: "invoice_paid",
          },
        ],
      },
      {
        title: t("nav.mix-shipping.label"),
        url: "/mix-shipping",
        icon: Bus,
        key: "mix_shipping",
        items: [
          {
            title: t("nav.mix-shipping.all"),
            url: "/mix-shipping/all",
            key: "mix_shipping",
          },
          {
            title: t("nav.mix-shipping.open"),
            url: "/mix-shipping/open",
            key: "mix_shipping_open",
          },
          {
            title: t("nav.mix-shipping.past-due"),
            url: "/mix-shipping/past_due",
            key: "mix_shipping_past_due",
          },
          {
            title: t("nav.mix-shipping.paid"),
            url: "/mix-shipping/paid",
            key: "mix_shipping_paid",
          },
        ],
      },
      {
        title: t("nav.mix-shipping-rates"),
        url: "/mix-shipping-rates",
        icon: DollarSign,
        key: "mix_shipping_rates",
      },
      {
        title: t("nav.shipping-rates"),
        url: "/shipping-rates",
        icon: ChartNoAxesGantt,
        key: "shipping_rates",
      },
      towingNavItem,
      {
        title: t("nav.payments.label"),
        url: "/payments",
        icon: DollarSign,
        key: "payments",
        items: [
          {
            title: t("nav.payments.freight-payments.label"),
            url: "/payments/all",
            key: "customer_payment_transactions",
            items: [
              {
                title: t("nav.payments.freight-payments.all"),
                url: "/payments/all",
                key: "customer_payment_transactions",
              },
              {
                title: t("nav.payments.freight-payments.approved"),
                url: "/payments/approved",
                key: "customer_payment_transactions_approved",
              },
              {
                title: t("nav.payments.freight-payments.pending"),
                url: "/payments/pending",
                key: "customer_payment_transactions_pending",
              },
            ],
          },
          {
            title: t("nav.payments.auction-payments.label"),
            url: "/auction-payments",
            key: "customer_payment_transactions_auction",
            items: [
              {
                title: t("nav.payments.auction-payments.all"),
                url: "/auction-payments/all",
                key: "customer_payment_transactions_auction",
              },
              {
                title: t("nav.payments.auction-payments.approved"),
                url: "/auction-payments/approved",
                key: "customer_payment_transactions_auction_approved",
              },
              {
                title: t("nav.payments.auction-payments.pending"),
                url: "/auction-payments/pending",
                key: "customer_payment_transactions_auction_pending",
              },
            ],
          },
        ],
      },
      {
        title: t("nav.announcements"),
        url: "/announcements",
        icon: BellRingIcon,
        key: "announcements",
      },
      {
        title: t("nav.customer.label"),
        url: "/customer/all",
        icon: User,
        key: "customer_all",
      },
      {
        title: t("nav.customer-vehicles"),
        url: "/buyer-number",
        icon: Hash,
        key: "buyer_number",
      },
      {
        title: t("nav.customer-invoice"),
        url: "/customer_invoice",
        icon: ReceiptSwissFranc,
        key: "customer_invoice",
      },
    ]
      .filter(Boolean)
      .filter((item) => {
        if (
          !session.data?.profile?.mix_shipping_status &&
          item?.url === "/mix-shipping"
        ) {
          return false;
        }
        if (
          (!session.data?.profile?.companies?.mix ||
            session.data?.profile?.companies?.mix_halfcut) &&
          item?.url === "/mix-shipping-rates"
        ) {
          return false;
        }

        if (
          (!session.data?.profile?.companies?.complete ||
            session.data?.profile?.companies?.complete_halfcut) &&
          item?.url === "/shipping-rates"
        ) {
          return false;
        }

        if (
          !session.data?.profile?.companies?.has_customer &&
          item?.url === "/customer/all"
        ) {
          return false;
        }
        if (!session.data?.profile?.has_invoices && item?.url === "/invoices") {
          return false;
        }
        if (
          !session.data?.profile?.companies?._count?.buyer_numbers &&
          item?.url === "/buyer-number"
        ) {
          return false;
        }
        if (
          !session.data?.profile?.companies?.has_customer_invoice &&
          item?.url === "/customer_invoice"
        ) {
          return false;
        }
        return true;
      });
  }, [
    t,
    session.data?.profile,
    destinationsItems,
    locationsItems,
    towingNavItem,
  ]);

  return (
    <Sidebar
      variant="floating"
      collapsible="icon"
      {...props}
      side={isRTL ? "right" : "left"}
      dir={dir}
    >
      <SidebarHeader dir={dir}>
        <div className="flex items-center gap-2">
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg- text-sidebar-primary-foreground group-data-[state=expanded]:size-10 transition-all">
            <Image
              src={profileImage === `null` ? `/logo.png` : profileImage}
              height={40}
              width={40}
              alt="Logo"
              className="rounded-lg"
            />
          </div>
          <div className="grid flex-1 ltr:text-left text-sm leading-tight">
            {profile_name ? (
              <span className="truncate font-semibold">{profile_name}</span>
            ) : (
              <>
                <span className="truncate font-semibold">{t("title")}</span>
                <span className="truncate text-xs" dir={dir}>
                  {t("description")}
                </span>
              </>
            )}
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => revalidateSidebarCounts()}
            disabled={revalidateLoading}
          >
            {revalidateLoading ? (
              <Loader2 className="animate-spin" size={16} />
            ) : (
              <RefreshCw />
            )}
          </Button>
        </div>
      </SidebarHeader>

      {session.data?.user_type &&
      session.data?.user_type === "customer_of_customer" ? (
        <SidebarContent>
          <NavMain items={customerNavItem} sidebarCounts={sidebarCounts} />
        </SidebarContent>
      ) : session.data?.user_type && session.data?.user_type === "customer" ? (
        <SidebarContent>
          <NavMain items={items} sidebarCounts={sidebarCounts} />
        </SidebarContent>
      ) : (
        <SidebarContent>
          <></>
        </SidebarContent>
      )}
      <SidebarFooter>
        <NavUser
          user={{
            name: session.data?.profile?.fullname || "",
            email: session.data?.profile?.loginable?.email || "",
            avatar: session.data?.profile?.photo || "",
          }}
        />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
});

AppSidebar.displayName = "AppSidebar";
