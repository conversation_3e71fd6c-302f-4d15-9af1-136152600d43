import { useDirection } from "@/hooks/useDirection";
import { cn } from "@/lib/utils";
import React from "react";
type NotificationBadgeProps = {
  className?: string;
  containerClassName?: string;
};
export function NotificationBadge({
  className = "bg-primary text-white",
  containerClassName,
}: NotificationBadgeProps) {
  const { isRTL } = useDirection();
  return (
    <div
      className={cn(
        `absolute -top-0.5 ${isRTL ? "-right-1" : " -left-1"} `,
        containerClassName
      )}
    >
      <p
        className={cn(
          `flex items-center w-2 h-2 leading-[18px] justify-center rounded-full  !text-[10px] font-bold`,
          className
        )}
      ></p>
    </div>
  );
}

type NotificationBadgeWithCountProps = {
  className?: string;
  count: number;
  containerClassName?: string;
};
export default function NotificationBadgeWithCount({
  count,
  className,
  containerClassName,
}: NotificationBadgeWithCountProps) {
  return (
    <div className={cn(containerClassName)}>
      {count > 0 && (
        <p
          className={cn(
            "flex  items-center justify-center rounded-full leading-4 bg-primary px-1 !text-[10px] text-white",
            className
          )}
        >
          {count}
        </p>
      )}
    </div>
  );
}
