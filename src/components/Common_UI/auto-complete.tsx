"use client";
import {
  Command,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandSeparator,
} from "@/components/ui/command";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2 } from "lucide-react";
import { useEffect, useState, useRef } from "react";
import { useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";

interface OptionType {
  value: string;
  label: string;
  [key: string]: any;
}

interface ContainerSelectorProps {
  data?: OptionType[];
  isLoading?: boolean;
  onFetch?: () => void;
  onSearch?: (searchTerm: string) => void;
  onSelectionChange?: (selected: OptionType[]) => void;
  selectedLabel?: (count: number) => string;
  unselectedLabel?: (count: number) => string;
  clearSelectionsRef?: React.RefObject<(() => void) | null>;
  labelKey: string;
  valueKey: string;
  searchParamsKey?: string;
}

export function ContainerSelector({
  data = [],
  isLoading,
  onFetch,
  onSearch,
  onSelectionChange,
  selectedLabel = (count) => `Selected (${count})`,
  unselectedLabel = (count) => `Unselected (${count})`,
  labelKey,
  valueKey,
  clearSelectionsRef,
  searchParamsKey,
}: ContainerSelectorProps) {
  const searchParams = useSearchParams();
  const t = useTranslations("autocomplete");
  const [allContainers, setAllContainers] = useState<OptionType[]>(data);
  const [selectedContainers, setSelectedContainers] = useState<OptionType[]>(
    []
  );
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [filteredUnselectedContainers, setFilteredUnselectedContainers] =
    useState<OptionType[]>([]);
  const forceUpdateRef = useRef(0);

  useEffect(() => {
    if (data) {
      const selected = data
        .filter((item: any) =>
          searchParams
            .getAll(searchParamsKey || "")
            .includes(String(item[valueKey]))
        )
        .map((item: any) => ({ ...item, checked: true }));

      setAllContainers(data);
      setSelectedContainers(selected);
      forceUpdateRef.current += 1;

      updateFilteredUnselectedContainers(data, selected, searchTerm);
    }
  }, [data, searchParams, searchParamsKey, valueKey]);

  const updateFilteredUnselectedContainers = (
    allItems: OptionType[],
    selectedItems: OptionType[],
    term: string
  ) => {
    const unselectedItems = allItems.filter(
      (item: any) =>
        !selectedItems.some((selected) => selected[valueKey] === item[valueKey])
    );

    if (term) {
      const filtered = unselectedItems.filter((item) =>
        String(item[labelKey]).toLowerCase().includes(term.toLowerCase())
      );
      setFilteredUnselectedContainers(filtered);
    } else {
      setFilteredUnselectedContainers(unselectedItems);
    }
  };

  const handleSearchInput = (value: string) => {
    setSearchTerm(value);

    updateFilteredUnselectedContainers(
      allContainers,
      selectedContainers,
      value
    );

    if (onSearch) {
      onSearch(value);
    }
  };

  const handleCheckboxChange = (option: OptionType) => {
    let updatedSelected: OptionType[];

    if (
      selectedContainers.some(
        (item: any) => item[valueKey] === option[valueKey]
      )
    ) {
      updatedSelected = selectedContainers.filter(
        (item: any) => item[valueKey] !== option[valueKey]
      );
    } else {
      updatedSelected = [...selectedContainers, option];
    }

    setSelectedContainers(updatedSelected);

    updateFilteredUnselectedContainers(
      allContainers,
      updatedSelected,
      searchTerm
    );

    onSelectionChange?.(updatedSelected);
  };

  if (clearSelectionsRef) {
    clearSelectionsRef.current = () => {
      setSelectedContainers([]);
      updateFilteredUnselectedContainers(allContainers, [], "");
      setSearchTerm("");
    };
  }

  const commandKey = `command-${forceUpdateRef.current}`;

  return (
    <Command key={commandKey}>
      <CommandInput
        placeholder={t("label")}
        value={searchTerm}
        onValueChange={handleSearchInput}
        onFocus={() => {
          if (onFetch) onFetch();
        }}
        autoFocus
      />
      <CommandList className="overflow-auto max-h-44">
        {isLoading ? (
          <div className="flex justify-center items-center py-4">
            <Loader2 className="animate-spin h-6 w-6 text-gray-500" />
          </div>
        ) : (
          <>
            <CommandEmpty>{t("option-label")}</CommandEmpty>

            <CommandGroup heading={selectedLabel(selectedContainers.length)}>
              {selectedContainers.length > 0 ? (
                selectedContainers.map((option) => (
                  <CommandItem
                    key={`selected-${option[valueKey]}`}
                    onSelect={() => handleCheckboxChange(option)}
                  >
                    <div className="flex items-center space-x-2 gap-2">
                      <Checkbox
                        checked
                        value={option[valueKey]}
                        onCheckedChange={() => handleCheckboxChange(option)}
                        onClick={(e) => e.stopPropagation()}
                      />
                      <label className="text-sm font-medium leading-none">
                        {option[labelKey]}
                      </label>
                    </div>
                  </CommandItem>
                ))
              ) : (
                <p className="text-gray-500 px-2">{t("selected")}</p>
              )}
            </CommandGroup>

            <CommandSeparator className="w-full" />

            <CommandGroup
              heading={unselectedLabel(filteredUnselectedContainers.length)}
            >
              {filteredUnselectedContainers.length > 0 ? (
                filteredUnselectedContainers.map((option) => (
                  <CommandItem
                    key={`unselected-${option[valueKey]}`}
                    onSelect={() => handleCheckboxChange(option)}
                  >
                    <div className="flex items-center space-x-2 gap-2">
                      <Checkbox
                        checked={false}
                        value={option[valueKey]}
                        onCheckedChange={() => handleCheckboxChange(option)}
                        onClick={(e) => e.stopPropagation()}
                      />
                      <label className="text-sm font-medium leading-none">
                        {option[labelKey]}
                      </label>
                    </div>
                  </CommandItem>
                ))
              ) : (
                <p className="text-gray-500 px-2">{t("unselected")}</p>
              )}
            </CommandGroup>
          </>
        )}
      </CommandList>
    </Command>
  );
}
