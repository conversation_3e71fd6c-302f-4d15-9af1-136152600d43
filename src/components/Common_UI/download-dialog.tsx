"use client";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import React, { cloneElement, useState } from "react";
import { useResponsive } from "@/hooks/use-mobile";
import {
  Drawer,
  Drawer<PERSON>ontent,
  Drawer<PERSON>eader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { Download } from "lucide-react";
interface ChildProps {
  close: () => void;
}
function DownloadDialog({
  children,
  title,
  buttonSize = "default",
  buttonVariant = "default",
  buttonClassName,
}: {
  children: React.ReactNode;
  title: string;
  buttonSize?: "lg" | "default" | "icon" | "sm";
  buttonVariant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  buttonClassName?: string;
}) {
  const [openModel, setOpenModel] = useState(false);
  const Close = () => setOpenModel(false);

  const childWithProps = React.isValidElement<ChildProps>(children)
    ? cloneElement(children, { close: Close })
    : children;
  const isDesktop = useResponsive();
  if (!isDesktop) {
    return (
      <Dialog open={openModel} onOpenChange={setOpenModel}>
        <DialogTrigger asChild>
          <Button
            variant={buttonVariant}
            size={buttonSize}
            className={buttonClassName}
          >
            <Download />
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-fit w-[100vw]  ">
          <DialogHeader>
            <DialogTitle className="uppercase">{title}</DialogTitle>
          </DialogHeader>
          {childWithProps}
          <DialogDescription> </DialogDescription>
        </DialogContent>
      </Dialog>
    );
  }
  return (
    <Drawer open={openModel} onOpenChange={setOpenModel}>
      <DrawerTrigger asChild>
        <Button
          variant={buttonVariant}
          size={buttonSize}
          className={buttonClassName}
        >
          <Download />
        </Button>
      </DrawerTrigger>
      <DrawerContent>
        <DrawerHeader className="text-left ">
          <DrawerTitle>{title}</DrawerTitle>
        </DrawerHeader>
        <div role="div" className="w-[100wv] overflow-y-scroll max-h-[85vh]">
          {childWithProps}
        </div>
      </DrawerContent>
    </Drawer>
  );
}

export default DownloadDialog;
