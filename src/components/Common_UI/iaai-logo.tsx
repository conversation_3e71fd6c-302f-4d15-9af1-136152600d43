import React from "react";

const IAAILogo = ({
  width = "40px",
  height = "30px",
}: {
  width?: string;
  height?: string;
}) => {
  return (
    <svg
      version="1.1"
      id="Logo"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      viewBox="5 15 62.93 42.53"
      //    viewBox="10 20 140 53"
      enableBackground="new 0 0 62.93 42.53"
      xmlSpace="preserve"
      width={width}
      height={height}
    >
      <style type="text/css">{`.st0{fill:#C90107;}`}</style>
      <g>
        <g>
          <g id="Icon__x26__Registration_2_">
            <g id="Icon_2_">
              <polygon
                className="st0"
                points="11.27,41.26 16.78,41.26 32.12,11.27 26.62,11.27"
              />
              <path
                className="st0"
                d="M53.02,41.26h5.45L43.39,11.27h-0.74v0h-5.42v29.99h5.48V35.1h7.16L53.02,41.26z M47.65,30.71h-4.93v-9.67
                L47.65,30.71z"
              />
              <polygon
                className="st0"
                points="32.51,21.05 27.59,30.74 32.51,30.74"
              />
              <polygon
                className="st0"
                points="22.25,41.27 32.53,41.27 32.53,35.21 25.32,35.21"
              />
            </g>
            <path
              id="Registration_2_"
              className="st0"
              d="M60.09,37c0.87,0,1.56,0.71,1.56,1.59c0,0.9-0.69,1.6-1.57,1.6
              c-0.87,0-1.58-0.7-1.58-1.6C58.51,37.7,59.21,37,60.09,37L60.09,37z M60.09,37.24c-0.7,0-1.28,0.6-1.28,1.35
              c0,0.76,0.57,1.35,1.28,1.35c0.71,0.01,1.28-0.6,1.28-1.34C61.37,37.85,60.8,37.24,60.09,37.24L60.09,37.24z M59.78,39.52H59.5
              v-1.78c0.15-0.02,0.29-0.04,0.5-0.04c0.27,0,0.45,0.06,0.55,0.14c0.1,0.08,0.16,0.2,0.16,0.37c0,0.23-0.16,0.38-0.35,0.43v0.01
              c0.16,0.03,0.26,0.17,0.3,0.43c0.04,0.27,0.09,0.38,0.11,0.44h-0.3c-0.04-0.06-0.08-0.22-0.12-0.45
              c-0.04-0.23-0.15-0.31-0.38-0.31h-0.2V39.52z M59.78,38.53h0.21c0.23,0,0.43-0.08,0.43-0.3c0-0.16-0.11-0.31-0.43-0.31
              c-0.09,0-0.16,0.01-0.21,0.01V38.53z"
            />
          </g>
        </g>
      </g>
    </svg>
  );
};

export default IAAILogo;
