"use client";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import React, {
  cloneElement,
  forwardRef,
  ReactElement,
  SetStateAction,
} from "react";

import { Button } from "../ui/button";
import { Copy, Download, X } from "lucide-react";
import useImageDownloader from "@/hooks/use-download-image";
import { LoadingSpinner } from "./loading";

interface CustomDialogProps {
  children: React.ReactElement<{ downloadLink?: string }>;
  setOpenModal: React.Dispatch<SetStateAction<boolean>>;
  openModal: boolean;
  title: string;
  isGoogleDrive?: boolean;
  handleCopyClick?: () => void;
  downloadLink?: string;
}

const CustomDialog = forwardRef<HTMLDivElement, CustomDialogProps>(
  (
    {
      children,
      openModal,
      setOpenModal,
      title,
      isGoogleDrive = false,
      handleCopyClick,
      downloadLink,
    }: CustomDialogProps,
    ref
  ) => {
    const handleCopy = () => {
      handleCopyClick?.();
    };

    const { handleDownload, isLoading } = useImageDownloader(
      downloadLink || "#"
    );
    const childWithProps = React.isValidElement(children)
      ? cloneElement(children as ReactElement)
      : children;

    return (
      <Dialog open={openModal} onOpenChange={() => setOpenModal(false)}>
        <DialogContent
          className="bg-transparent backdrop-blur-md  p-0 gap-0"
          style={{ minWidth: "100vw", minHeight: "100vh" }}
          close="hidden"
          ref={ref}
        >
          <DialogHeader className="w-full bg-background justify-between h-[45px]">
            <div className="flex flex-row items-center py-1 justify-between ">
              <DialogTitle className="pl-5 text-start">{title}</DialogTitle>
              <div className="flex flex-row items-center justify-between gap-3 pr-3">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleDownload}
                  // disabled={isLoading || isGoogleDrive}
                  // className={`${isGoogleDrive ? "hidden" : ""}`}
                >
                  {isLoading ? (
                    <LoadingSpinner className="w-5 h-5 p-0" />
                  ) : (
                    <Download />
                  )}
                </Button>
                <Button
                  variant={"outline"}
                  size={"icon"}
                  onClick={handleCopy}
                  disabled={isGoogleDrive}
                  className={`${isGoogleDrive ? "hidden" : ""}`}
                >
                  <Copy />
                </Button>
                <Button
                  variant={"outline"}
                  size={"icon"}
                  onClick={() => setOpenModal(false)}
                >
                  <X />
                </Button>
              </div>
            </div>
          </DialogHeader>
          <div className="min-h-[calc(100vh-45px)] max-h-[calc(100vh-45px)] w-[100vw] p-0">
            {childWithProps}
          </div>
        </DialogContent>
      </Dialog>
    );
  }
);
CustomDialog.displayName = "custom-dialog";
export default CustomDialog;
