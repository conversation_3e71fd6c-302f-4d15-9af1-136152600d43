import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  Too<PERSON>ipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export function CustomTooltip({
  children,
  tooltip,
}: {
  children: React.ReactNode;
  tooltip: string;
}) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>{children}</TooltipTrigger>
        <TooltipContent>
          <p>{tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
