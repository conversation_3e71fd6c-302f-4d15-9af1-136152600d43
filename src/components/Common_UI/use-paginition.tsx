"use client";
import {
  Pa<PERSON><PERSON>,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useSearchParams, useRouter, useParams } from "next/navigation";
import React from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import { formatNumberByLocale } from "@/utils/helper-function";
import { localesTypes } from "@/i18n/routing";
import { useProgress } from "@bprogress/next";

const pageSizes = [20, 50, 100];

type RenderPageProps = {
  activePages: number[];
  pageNumbers: number[];
  currentPage: number;
  setCurrentPage: (page: number) => void;
  locale: localesTypes;
};

// Function to render page numbers with ellipsis
const RenderPages = ({
  activePages,
  currentPage,
  pageNumbers,
  setCurrentPage,
  locale,
}: RenderPageProps) => {
  const renderedPages = activePages.map((page, idx) => (
    <PaginationItem
      key={idx}
      className={
        currentPage === page ? "bg-stone-200 dark:bg-stone-800 rounded-md" : ""
      }
    >
      <PaginationLink
        className="cursor-pointer  rounded-md"
        onClick={() => setCurrentPage(page)}
        size={"sm"}
      >
        {formatNumberByLocale(page, locale)}
      </PaginationLink>
    </PaginationItem>
  ));

  if (activePages[0] > 1) {
    renderedPages.unshift(
      <PaginationEllipsis
        key="ellipsis-start"
        className="cursor-pointer  rounded-md"
        onClick={() => setCurrentPage(activePages[0] - 1)}
      />
    );
  }

  if (activePages[activePages.length - 1] < pageNumbers.length) {
    renderedPages.push(
      <PaginationEllipsis
        key="ellipsis-end"
        className="cursor-pointer  rounded-md"
        onClick={() => setCurrentPage(activePages[activePages.length - 1] + 1)}
      />
    );
  }

  return renderedPages;
};

export default function PaginationComponent({
  count,
  pageSize,
}: {
  count: number;
  pageSize: number;
}) {
  const searchParams = useSearchParams();
  const urlParams = useParams();
  const router = useRouter();
  const currentPage = searchParams.get("page")
    ? Number(searchParams.get("page"))
    : 1;
  const per_page = searchParams.get("per_page")
    ? Number(searchParams.get("per_page"))
    : 20;
  const t = useTranslations("pagination");
  const { isRTL } = useDirection();
  const pageCount = Math.ceil(count / pageSize);
  const pageNumbers = Array.from({ length: pageCount }, (_, i) => i + 1);
  const maxPageNum = 5; // Maximum pages to show
  const pageNumLimit = Math.floor(maxPageNum / 2);
  const { start } = useProgress();

  const activePages = pageNumbers.slice(
    Math.max(0, currentPage - 1 - pageNumLimit),
    Math.min(currentPage - 1 + pageNumLimit + 1, pageNumbers.length)
  );

  const params = new URLSearchParams(Array.from(searchParams.entries()));

  const setCurrentPage = (page: number) => {
    params.set("page", page.toString());
    start();
    router.push(`?${params}`);
  };
  const setPerPage = (page: number) => {
    params.delete("page");
    params.set("per_page", page.toString());
    start();
    router.push(`?${params}`);
  };

  const nextPage = () => setCurrentPage(Math.min(currentPage + 1, pageCount));
  const previousPage = () => setCurrentPage(Math.max(currentPage - 1, 1));

  return (
    <div className="flex md:justify-between pt-2 sm:flex-row flex-col justify-start min-w-full px-1">
      <div className="w-full flex justify-center sm:justify-start sm:py-0 py-2">
        <p>
          {t("displaying")}{" "}
          <span className="font-medium">
            {formatNumberByLocale(
              (currentPage - 1) * pageSize + 1,
              urlParams.locale as localesTypes
            )}
          </span>{" "}
          {t("to")}{" "}
          <span className="font-medium">
            {formatNumberByLocale(
              currentPage === pageCount ? count : currentPage * pageSize,
              urlParams.locale as localesTypes
            )}
          </span>
          {"  "}
          {t("of")}
          <span className="font-medium">
            {" "}
            {formatNumberByLocale(count, urlParams.locale as localesTypes)}
          </span>{" "}
          {t("results")}
        </p>
      </div>
      <Pagination className="py-0 w-full  flex justify-end px-3">
        <Select
          defaultValue={`${per_page}`}
          onValueChange={(value) => setPerPage(Number(value))}
          dir={isRTL ? "rtl" : "ltr"}
        >
          <SelectTrigger className="w-[80px] mx-2">
            <SelectValue placeholder="Select a fruit" />
          </SelectTrigger>
          <SelectContent className="min-w-[20px]">
            <SelectGroup>
              {pageSizes?.map((pageSize) => (
                <SelectItem
                  value={`${pageSize}`}
                  key={pageSize}
                  dir={isRTL ? "rtl" : "ltr"}
                >
                  {formatNumberByLocale(
                    pageSize,
                    urlParams.locale as localesTypes
                  )}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              className="cursor-pointer  rounded-md"
              onClick={previousPage}
              size={"sm"}
              rlt={isRTL}
              label={t("previous")}
            />
          </PaginationItem>
          <RenderPages
            activePages={activePages}
            currentPage={currentPage}
            pageNumbers={pageNumbers}
            setCurrentPage={setCurrentPage}
            locale={urlParams.locale as localesTypes}
          />

          <PaginationItem>
            <PaginationNext
              className={`${
                currentPage === pageCount ? " cursor-not-allowed " : ""
              } cursor-pointer  rounded-md`}
              onClick={nextPage}
              size={"sm"}
              rlt={isRTL}
              label={t("next")}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}
