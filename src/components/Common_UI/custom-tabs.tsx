"use client";
import React from "react";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import clsx from "clsx";
import { useParams } from "next/navigation";

type customTabs = {
  children: React.ReactNode;
  defaultValue: string;
  tabsClassName?: string;
  value?: string; // Add controlled value prop
  onValueChange?: (value: string) => void; // Add change handler
};

type CustomTabListType = {
  tabsTriggers: {
    value: string;
    label: string | any;
    Badge?: React.ReactNode;
    click?: () => void;
    className?: string;
    activeClassName?: string;
  }[];
  tabsListClassName?: string;
  tabsTriggersClassName?: string;
};

type CustomTabContentType = {
  tabsContent: { children: React.ReactNode; value: string }[];
  tabsContentClassName?: string;
};

function CustomTabs({
  defaultValue,
  tabsClassName,
  children,
  value,
  onValueChange,
}: customTabs) {
  const { locale } = useParams();

  return (
    <Tabs
      defaultValue={defaultValue}
      value={value}
      onValueChange={onValueChange}
      className={clsx(tabsClassName)}
      dir={locale === "ar" ? "rtl" : "ltr"}
    >
      {children}
    </Tabs>
  );
}

function CustomTabList({
  tabsTriggers,
  tabsListClassName,
  tabsTriggersClassName,
}: CustomTabListType) {
  const { locale } = useParams();

  return (
    <TabsList className={clsx(tabsListClassName)}>
      {tabsTriggers.map((trigger, index) => (
        <TabsTrigger
          key={index}
          value={trigger.value}
          className={clsx(
            tabsTriggersClassName + " relative",
            trigger.className
          )}
          data-state={trigger.activeClassName ? "active" : undefined}
          dir={locale === "ar" ? "rtl" : "ltr"}
          onClick={() => trigger.click?.()}
        >
          {trigger.label}
          {trigger.Badge && (
            <span
              className={`absolute top-[2px] ${
                locale === "ar" ? "left-1" : "right-1"
              } text-xs`}
            >
              {trigger.Badge}
            </span>
          )}
        </TabsTrigger>
      ))}
    </TabsList>
  );
}

function CustomTabContent({
  tabsContent,
  tabsContentClassName,
}: CustomTabContentType) {
  return (
    <>
      {tabsContent.map((content) => (
        <TabsContent
          key={content.value}
          value={content.value}
          className={clsx(tabsContentClassName)}
        >
          {content.children}
        </TabsContent>
      ))}
    </>
  );
}

CustomTabs.TabList = CustomTabList;
CustomTabs.TabContent = CustomTabContent;
export default CustomTabs;
