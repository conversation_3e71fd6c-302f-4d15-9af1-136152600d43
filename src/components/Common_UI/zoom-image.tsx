"use client";
import { useEffect, useRef, useState } from "react";
import {
  TransformWrapper,
  TransformComponent,
  ReactZoomPanPinchRef,
} from "react-zoom-pan-pinch";
import Image from "next/image";
import { getGoogleDriveImageSizeUrl, getImageSizeUrl } from "@/utils/imageURL";

interface ZoomImageProps {
  file: { name?: string; id: string; url: string };
  isGoogleDrive?: boolean;
}

const ZoomImage = ({ file, isGoogleDrive = false }: ZoomImageProps) => {
  const [isInteracting, setIsInteracting] = useState(false);
  const transformRef = useRef<ReactZoomPanPinchRef | null>(null);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!transformRef.current) return;
      if (event.key === "ArrowUp") transformRef.current?.zoomIn();
      if (event.key === "ArrowDown") transformRef.current?.zoomOut();
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  return (
    <TransformWrapper
      ref={transformRef}
      initialScale={1}
      minScale={0.5}
      initialPositionX={100}
      initialPositionY={100}
      maxScale={5}
      wheel={{ step: 0.1 }}
      pinch={{ step: 0.1 }}
      onPanningStart={() => setIsInteracting(true)}
      onPanningStop={() => setIsInteracting(false)}
      onZoomStart={() => setIsInteracting(true)}
      onZoomStop={() => setIsInteracting(false)}
      centerOnInit
    >
      <TransformComponent contentClass="min-h-[calc(100vh-45px)] max-h-[calc(100vh-45px)] w-[100vw]">
        <div className="relative flex justify-center items-center min-h-[calc(100vh-45px)] max-h-[calc(100vh-45px)] w-[100vw]">
          {isGoogleDrive ? (
            <img
              src={getGoogleDriveImageSizeUrl({
                url: file.url,
                size: 1024,
              })}
              alt={`Vehicle Image ${file.id}`}
              className={`object-contain rounded-md h-full overflow-hidden ${
                isInteracting ? "cursor-pointer" : ""
              }`}
            />
          ) : (
            <Image
              src={getImageSizeUrl({
                url: file.url,
                size: 1024,
              })}
              fill
              blurDataURL="/placeholder.svg"
              placeholder="blur"
              alt={`Vehicle Image ${file.id}`}
              className={`object-contain rounded-md h-full overflow-hidden ${
                isInteracting ? "cursor-pointer" : ""
              }`}
            />
          )}
        </div>
      </TransformComponent>
    </TransformWrapper>
  );
};
ZoomImage.displayName = "ZoomImage";
export default ZoomImage;
