"use client";
import {
  Command,
  CommandInput,
  CommandItem,
  CommandList,
  CommandEmpty,
  CommandGroup,
} from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { useQuery } from '@tanstack/react-query';
import { useFetchClient } from '@/utils/axios';

interface SArrayInput {
  id?: string | number;
  [key: string]: any;
}

interface SArrayOption {
  id: string | number | undefined;
  label: string;
}

const sArray = (
  data: Record<string, SArrayInput> | SArrayInput[],
  fieldNames: string | string[]
): SArrayOption[] =>
  Object.values(data).map((value: SArrayInput) => ({
    id: value?.id,
    label: Array.isArray(fieldNames)
      ? fieldNames
        .map((fieldName) =>
          fieldName
            .split('.')
            .reduce<any>((obj, key) => obj?.[key], value) || ''
        )
        .join(' ')
      : value?.[fieldNames] || '',
  }));

export const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debouncedValue;
};

interface AutoCompleteProps {
  url?: string;
  label?: string;
  field: any;
  fieldName: string | string[];
  error?: { message?: string };
  staticOptions?: any[];
  column?: any;
  modal?: any;
  defualtValue?: any;
  customeName?: string | null;
  disableAutoComplete?: boolean;
  isSpecialRate?: any;
  showIndex?: boolean;
  emitOptions?: (item: any) => void;
  handleOnChangeFromStepper?: boolean;
  stepperHandleOnChange?: ((value: any) => void) | null;
  parentHierarchyCheck?: boolean;
  pol?: any;
  [key: string]: any;
}

function AutoComplete({
  url,
  label,
  field,
  fieldName,
  error,
  staticOptions,
  column,
  modal,
  defualtValue = null,
  customeName = null,
  isSpecialRate = null,
  showIndex = false,
  emitOptions = (_item) => { },
  handleOnChangeFromStepper = false,
  stepperHandleOnChange = null,
  parentHierarchyCheck = false,
  pol,
}: AutoCompleteProps) {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearch = useDebounce(searchValue, 300);
  const fetchClient = useFetchClient();

  const buildQueryParams = (search?: string) => {
    return {
      column_name: Array.isArray(column) ? { ...column } : column,
      modal,
      isSpecialRate,
      parentHierarchyCheck,
      ...(search
        ? { [customeName || 'name']: search, pol: pol || null }
        : { id: field?.value }),
    };
  };

  const fetchOptions = async (_key: string, params: any) => {
    const res = await fetchClient(url!, { params });
    if (res.status !== 200) throw new Error('Fetch failed');
    return res.data?.data || [];
  };

  const {
    data: optionsData = [],
    isFetching,
  } = useQuery({
    queryKey: ['autocomplete-options', url, debouncedSearch, field.value],
    queryFn: () => fetchOptions(url!, buildQueryParams(debouncedSearch)),
    enabled: !!url && !staticOptions,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const options = staticOptions || sArray(optionsData, fieldName);

  useEffect(() => {
    emitOptions(optionsData);
  }, [optionsData]);

  useEffect(() => {
    if (defualtValue && staticOptions) {
      const selected = staticOptions.find((opt) => opt.id === field.value);
      field.onChange((selected || defualtValue).id);
    }
  }, [staticOptions]);

  const selectedItem =
    options.find((opt) =>
      typeof opt === 'object' ? opt?.id === field?.value : opt === field?.value
    ) || null;

  return (
    <div className="w-full">
      {label && (
        <Label className="mb-1 block capitalize text-sm font-medium">
          {label}
        </Label>
      )}
      <Popover open={open} onOpenChange={setOpen} modal>
        <PopoverTrigger asChild>
          <Input
            aria-readonly
            readOnly
            placeholder="Select an option"
            value={selectedItem?.label || ''}
            onClick={() => setOpen(!open)}
            className={cn(error && 'border-red-500')}
          />
        </PopoverTrigger>
        <PopoverContent
          className="z-50 w-[--radix-popover-trigger-width] p-0 pointer-events-auto"
          side="bottom"
          align="start"
        >
          <Command shouldFilter={false}>
            <CommandInput
              placeholder="Search..."
              value={searchValue}
              onValueChange={(val) => setSearchValue(val)}
              className="h-9"
            />
            <CommandList className="max-h-60 overflow-y-auto p-2">
              {isFetching && (
                <div className="py-2 px-4 text-sm text-muted-foreground">
                  Loading...
                </div>
              )}
              <CommandEmpty>No available option.</CommandEmpty>
              <CommandGroup>
                {options.map((option, index) => (
                  <CommandItem
                    key={option.id}
                    onSelect={() => {
                      const selectedId = typeof option === 'object' ? option.id : option;
                      (handleOnChangeFromStepper
                        ? stepperHandleOnChange
                        : field.onChange)(selectedId);
                      setOpen(false);
                    }}
                  >
                    {showIndex && `${index + 1}. `}
                    {typeof option === 'object' ? option.label : option}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      {error?.message && (
        <p className="text-sm text-red-500 mt-1">{error.message}</p>
      )}
    </div>
  );
}

export default AutoComplete;
