"use client";
import * as React from "react";
import { cn } from "@/lib/utils";
import { Loader } from "lucide-react";
interface LoadingSpinnerProps extends React.SVGAttributes<SVGSVGElement> {
  className?: string;
}

const LoadingSpinner = React.forwardRef<SVGSVGElement, LoadingSpinnerProps>(
  (props, ref) => {
    const { className, ...rest } = props;
    return (
      <Loader
        ref={ref}
        className={cn("w-12 h-12 animate-spin text-gray-600", className)}
        {...rest}
      />
    );
  }
);

LoadingSpinner.displayName = "LoadingSpinner";

export { LoadingSpinner };
