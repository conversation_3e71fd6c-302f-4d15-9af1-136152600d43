"use client";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import React, { cloneElement, ElementType, forwardRef, useState } from "react";

interface CustomPopoverProps {
  children: React.ReactElement<any>;
  ButtonLabel: ElementType | string;
  buttonSize?: "lg" | "default" | "icon" | "sm";
  buttonVariant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  buttonClassName?: string;
  popoverClassName?: string;
  title?: string;
}

const CustomPopover = forwardRef<
  { close: () => void | any },
  CustomPopoverProps
>(
  (
    {
      children,
      ButtonLabel,
      buttonSize = "default",
      buttonVariant = "default",
      buttonClassName,
      title,
    }: CustomPopoverProps,
    ref
  ) => {
    const [openPopover, setOpenPopover] = useState(false);

    const close = () => setOpenPopover(false);

    React.useImperativeHandle(ref, () => ({
      close,
    }));

    const childWithProps = React.isValidElement(children)
      ? cloneElement(children)
      : children;

    return (
      <Popover open={openPopover} onOpenChange={setOpenPopover}>
        <PopoverTrigger asChild>
          <Button
            variant={buttonVariant}
            size={buttonSize}
            className={buttonClassName}
          >
            {typeof ButtonLabel === "string" ? ButtonLabel : <ButtonLabel />}
          </Button>
        </PopoverTrigger>

        <PopoverContent className={`w-80 py-3 `}>
          <h1 className="font-bold pb-4">{title}</h1>
          {childWithProps}
        </PopoverContent>
      </Popover>
    );
  }
);
CustomPopover.displayName = "Custom-Popover";
export default CustomPopover;
