"use client";

import * as React from "react";
import { Check, CircleChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { LoadingSpinner } from "./loading";
import { useDirection } from "@/hooks/useDirection";

type Option = {
  name: string;
  id: string | number;
};

type ComboboxDemoProps = {
  options: Option[];
  value?: string | number | null;
  onChange?: (value: string | number | null) => void;
  label: string;
  btnClass?: string;
  contentClass?: string;
  isloading?: boolean;
  onClick?: () => void;
  placeholder?: string;
  labelOption?: string;
  className?: string;
};

export function CustomCombobox({
  options,
  value,
  onChange,
  label,
  btnClass,
  contentClass,
  isloading,
  onClick,
  placeholder,
  labelOption,
  className,
}: ComboboxDemoProps) {
  const [open, setOpen] = React.useState(false);
  const [defaultValue, setDefaultValue] = React.useState<Option | undefined>()
  const { isRTL } = useDirection();

  const handleSelect = (currentValue: string | number) => {
    const newValue = currentValue === value ? null : currentValue;
    onChange?.(newValue);
    setOpen(false);
  };

  const handleClick = () => {
    onClick?.();
  };
  React.useEffect(() => {
    onClick?.();
  }, [])

  React.useEffect(() => {
    setDefaultValue(options.find(
      (option) => option.id.toString() === value?.toString()
    ))
  }, [options])

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          onClick={handleClick}
          aria-expanded={open}
          className={cn("w-full justify-between text", btnClass, className)}
          dir={isRTL ? "rtl" : "ltr"}
          onFocus={() => setDefaultValue(options.find(
            (option) => option.id.toString() === value?.toString()
          ))
          }
        >
          {defaultValue?.name || defaultValue?.id || label}
          <CircleChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-max min-w-full p-0"
        dir={isRTL ? "rtl" : "ltr"}
      >
        <Command className={cn(contentClass)} dir={isRTL ? "rtl" : "ltr"}>
          <CommandInput placeholder={placeholder} dir={isRTL ? "rtl" : "ltr"} />
          <CommandList>
            <CommandEmpty>{labelOption}</CommandEmpty>
            <CommandGroup>
              {isloading ? (
                <div className="flex justify-center items-center">
                  <LoadingSpinner className="h-5 w-5" />
                </div>
              ) : (
                options.map((option) => (
                  <CommandItem
                    key={option.id}
                    value={option.id.toString()}
                    onSelect={() => handleSelect(option.id)}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        value?.toString() === option.id.toString()
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                    {option.name}
                  </CommandItem>
                ))
              )}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
