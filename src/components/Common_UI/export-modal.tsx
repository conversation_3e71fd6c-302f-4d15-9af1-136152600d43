"use client";
import React, { useEffect, useRef, useState } from "react";
import { AgGridReact } from "ag-grid-react";
import { Button } from "@/components/ui/button";
import { ColDef } from "ag-grid-community";
import { Download, FileDown } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { LoadingSpinner } from "./loading";

type ExportType = "Current Data" | "All Data" | null;

type ExportModalProps = {
  columnDefs: ColDef[];
  currentData: any[];
  exportFileName: string;

  fetchAllData?: () => Promise<any[]>;
  totalItems?: number;
  translations?: {
    title: string;
    exportData: string;
    subTitle: string;
    currentData: string;
    allData: string;
    cancel: string;
    export: string;
  };
  triggerButtonPosition?: string;
  gridOptions?: any;
  onExportSuccess?: () => void;
};

export const ExportModal = ({
  columnDefs,
  currentData,
  exportFileName,
  fetchAllData,
  totalItems = currentData.length,
  translations = {
    title: "Export Data",
    exportData: "Export Data",
    subTitle: "Select Export Type",
    currentData: "Current Data",
    allData: "All Data",
    cancel: "Cancel",
    export: "Export",
  },
  triggerButtonPosition,
  gridOptions = {},
  onExportSuccess,
}: ExportModalProps) => {
  const gridRef = useRef<AgGridReact>(null);
  const [exportType, setExportType] = useState<ExportType>("Current Data");
  const [open, setOpen] = useState<boolean>(false);
  const [data, setData] = useState<any[]>(currentData);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // New state to store and track all data
  const [allData, setAllData] = useState<any[]>([]);
  const [allDataLoaded, setAllDataLoaded] = useState<boolean>(false);

  // Reset cached data when component unmounts or modal closes
  useEffect(() => {
    if (!open) {
      // Reset to current data when modal closes
      setData(currentData);
      setExportType("Current Data");
    }
  }, [open, currentData]);

  // Update data when currentData prop changes, but only if we're viewing current data
  useEffect(() => {
    if (exportType === "Current Data") {
      setData(currentData);
    }
  }, [currentData, exportType]);

  const handleExport = async () => {
    if (!gridRef.current) return;

    try {
      // If "All Data" is selected and we need to fetch it
      if (exportType === "All Data" && fetchAllData && !allDataLoaded) {
        setIsLoading(true);
        const fetchedData = await fetchAllData();
        setAllData(fetchedData);
        setAllDataLoaded(true);
        setData(fetchedData);

        // Wait for the next tick to ensure grid data is updated
        setTimeout(() => {
          gridRef.current?.api.exportDataAsExcel({
            fileName: `${exportFileName} - ${exportType}`,
            sheetName: exportFileName,
          });
          setIsLoading(false);
          setOpen(false);
          onExportSuccess?.();
        }, 100);
      } else {
        // Export current data
        gridRef.current.api.exportDataAsExcel({
          fileName: `${exportFileName} - ${exportType}`,
          sheetName: exportFileName,
        });
        setOpen(false);
        onExportSuccess?.();
      }
    } catch (error) {
      console.error("Export failed:", error);
      setIsLoading(false);
    }
  };

  const handleSelectExportType = async (value: ExportType) => {
    setExportType(value);

    // Only fetch all data when "All Data" is selected and we haven't already loaded it
    if (value === "All Data" && fetchAllData && !allDataLoaded) {
      setIsLoading(true);
      try {
        const fetchedData = await fetchAllData();
        setAllData(fetchedData);
        setAllDataLoaded(true);
        setData(fetchedData);
      } catch (error) {
        console.error("Failed to fetch all data:", error);
      } finally {
        setIsLoading(false);
      }
    } else if (value === "All Data" && allDataLoaded) {
      // Use cached data if already loaded
      setData(allData);
    } else if (value === "Current Data") {
      // Switch back to current data
      setData(currentData);
    }
  };

  // Only show "All Data" option if fetchAllData is provided
  const showAllDataOption = !!fetchAllData;

  return (
    <TooltipProvider>
      <Popover
        open={open}
        onOpenChange={(newOpen) => {
          // Reset data state when closing
          if (!newOpen && open) {
            setData(currentData);
            setExportType("Current Data");
          }
          setOpen(newOpen);
        }}
      >
        <Tooltip>
          <TooltipTrigger asChild>
            <PopoverTrigger asChild>
              <Button
                size={"icon"}
                variant={"outline"}
                className={triggerButtonPosition + " transition-all"}
              >
                <Download />
              </Button>
            </PopoverTrigger>
          </TooltipTrigger>
          <TooltipContent side="left">
            <p>{translations.exportData}</p>
          </TooltipContent>
        </Tooltip>

        {open && (
          <PopoverContent className="w-80 p-0 shadow-md" align="start">
            <div className="p-3 flex items-center gap-2 border-b">
              <FileDown className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-medium">{translations.title}</h3>
            </div>

            <div className="p-4">
              <div className="mb-3">
                <h4 className="text-sm font-medium mb-2">
                  {translations.subTitle}
                </h4>
                <RadioGroup
                  value={exportType || undefined}
                  onValueChange={(value) =>
                    handleSelectExportType(value as ExportType)
                  }
                  className="space-y-2"
                >
                  <div className="flex items-center space-x-2 rounded-md border p-2">
                    <RadioGroupItem value="Current Data" id="current" />
                    <Label htmlFor="current" className="flex-1 cursor-pointer">
                      <div className="flex justify-between items-center">
                        <span>{translations.currentData}</span>
                        <Badge variant="secondary" className="ml-2 text-xs">
                          {currentData.length}
                        </Badge>
                      </div>
                    </Label>
                  </div>

                  {showAllDataOption && (
                    <div className="flex items-center space-x-2 rounded-md border p-2 ">
                      <RadioGroupItem value="All Data" id="all" />
                      <Label htmlFor="all" className="flex-1 cursor-pointer">
                        <div className="flex justify-between items-center">
                          <span>{translations.allData}</span>
                          <Badge variant="secondary" className="ml-2 text-xs">
                            {totalItems}
                          </Badge>
                        </div>
                      </Label>
                    </div>
                  )}
                </RadioGroup>
              </div>
            </div>

            <Separator />

            <div className="p-3 flex justify-end gap-2">
              <Button onClick={() => setOpen(false)} variant="ghost" size="sm">
                {translations.cancel}
              </Button>
              <Button
                onClick={handleExport}
                disabled={isLoading}
                size="sm"
                className="flex items-center gap-1"
              >
                {isLoading ? (
                  <LoadingSpinner className="h-4 w-4" />
                ) : (
                  <FileDown className="h-4 w-4" />
                )}
                {translations.export}
              </Button>
            </div>
          </PopoverContent>
        )}
      </Popover>

      {/* Hidden grid for export functionality */}
      {open && (
        <div className="hidden">
          <AgGridReact
            ref={gridRef}
            columnDefs={columnDefs}
            rowData={data}
            {...gridOptions}
          />
        </div>
      )}
    </TooltipProvider>
  );
};
