"use client";

import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { localesTypes } from "@/i18n/routing";
import { formatNumberByLocale } from "@/utils/helper-function";
import { useParams } from "next/navigation";
import React from "react";

export default function Component({
  label,
  max,
  min,
  value,
  onChange,
  onCommit,
}: {
  label: string;
  max: number;
  min: number;
  value: number[];
  onChange: React.Dispatch<React.SetStateAction<number[]>>;
  onCommit: (value: number[]) => void;
}) {
  const params = useParams()
  return (
    <div className="space-y-4 pb-2">
      <div className="flex items-center justify-between gap-2">
        <Label className="leading-6 text-xs">{label}</Label>
        <output className="text-xs font-medium tabular-nums">
          {formatNumberByLocale(value[0], params.locale as localesTypes)} - {formatNumberByLocale(value[1], params.locale as localesTypes)}
        </output>
      </div>
      <Slider
        value={value}
        onValueChange={onChange}
        onValueCommit={onCommit}
        max={max}
        min={min}
        aria-label="Dual range slider with output"
      />
    </div>
  );
}
