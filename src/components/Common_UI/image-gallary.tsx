import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { Download, X } from "lucide-react";
import { LoadingSpinner } from "./loading";

type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  children: React.ReactNode;
  containerClass?: string;
  parentClass?: string;
  handleDownload: () => void;
  isZippingAll?: boolean;
};

export default function ImageGallary({
  onOpenChange,
  open,
  title,
  children,
  containerClass,
  parentClass,
  handleDownload,
  isZippingAll,
}: Props) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className={cn(
          // Base responsive styles
          "w-[95vw] max-w-[95vw] h-[90vh] max-h-[90vh]",
          // Tablet and up
          "sm:w-[90vw] sm:max-w-[90vw] sm:h-[85vh] sm:max-h-[85vh]",
          // Desktop
          "lg:w-[80vw] lg:max-w-[1268px] lg:h-[850px]",
          // Ensure proper overflow handling
          "overflow-hidden p-0",
          parentClass
        )}
        close="hidden"
      >
        <DialogHeader className="px-4 py-3 sm:px-6 sm:py-4 border-b shrink-0">
          <DialogTitle className="text-sm sm:text-base lg:text-lg pr-16 sm:pr-20">
            {title}
          </DialogTitle>

          {/* Download Button */}
          {typeof isZippingAll === "boolean" && (
            <Button
              onClick={handleDownload}
              variant="link"
              className="absolute top-2 right-12 sm:right-16 p-1 sm:p-2"
              size="icon"
            >
              {isZippingAll ? (
                <LoadingSpinner className="h-3 w-3 sm:h-4 sm:w-4" />
              ) : (
                <Download className="h-3 w-3 sm:h-4 sm:w-4" />
              )}
            </Button>
          )}

          {/* Close Button */}
          <Button
            onClick={() => onOpenChange(false)}
            variant="link"
            className="absolute top-2 right-2 sm:right-4 p-1 sm:p-2"
            size="icon"
          >
            <X className="h-3 w-3 sm:h-4 sm:w-4" />
          </Button>
        </DialogHeader>

        {/* Scrollable Content Area */}
        <div
          className={cn(
            "flex-1 overflow-auto px-4 py-3 sm:px-6 sm:py-3",
            "h-[calc(90vh-104px)] max-h-[calc(90vh-104px)]",
            // Tablet and up
            "sm:h-[calc(85vh-104px)] sm:max-h-[calc(85vh-104px)]",
            // Desktop
            "lg:h-[calc(850px-104px)]"
          )}
        >
          <div className={cn("w-full h-full", containerClass)}>{children}</div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
