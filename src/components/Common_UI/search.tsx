"use client";
import { useSearchPara<PERSON>, useRouter } from "next/navigation";
import { XIcon } from "lucide-react";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { useProgress } from "@bprogress/next";

function Search({
  type,
  fieldName,
  placeholder,
}: {
  type: string;
  fieldName: string;
  placeholder?: string;
}) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [inputValue, setInputValue] = useState(""); // Manage the input state
  const params = new URLSearchParams(
    Array.from(Array.from(searchParams.entries()))
  );
  const { start } = useProgress();

  function handleInputChange(value: string) {
    setInputValue(value);

    if (!value || value === "") {
      params.delete(fieldName);
    } else {
      params.set(fieldName, value.toLowerCase());
    }

    params.delete("sortby");
    params.delete("page");
    start();
    router.push(`?${params.toString()}`);
  }

  function handleClearInput() {
    setInputValue("");
    params.delete(fieldName);
    router.replace(`?${params.toString()}`);
  }

  return (
    <div className="flex flex-col relative gap-3 shadow-none rounded-none h-fit">
      <Input
        type={type}
        value={inputValue} // Bind the input value to the state
        onChange={(e) => handleInputChange(e.target.value)}
        placeholder={placeholder || "search"}
        className="shadow-none"
      />
      <XIcon
        className="cursor-pointer absolute top-1 right-1 w-5 pt-1"
        onClick={handleClearInput} // Clear the input when clicking the X icon
      />
    </div>
  );
}

export default Search;
