"use client";
import { User<PERSON>enIcon, Camera, UserRound } from "lucide-react";
import React, { createRef, SetStateAction, useRef, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "../ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import Cropper, { ReactCropperElement } from "react-cropper";
import "cropperjs/dist/cropper.css";
import { useTranslations } from "next-intl";
import { LoadingSpinner } from "./loading";


interface PhotoProps {
  defaultImage?: string;
  onImageCropped: (croppedImage: string, setCloseDialog: React.Dispatch<SetStateAction<boolean>>) => void;
  isUpdating: boolean,
  isAsync: boolean
}

export default function Photo({
  defaultImage,
  onImageCropped,
  isAsync,
  isUpdating
}: PhotoProps) {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [image, setImage] = useState<string | null>(null);
  const [open, setOpen] = useState<boolean>(false);
  const [cropData, setCropData] = useState<string>("#");
  const t = useTranslations("profile");
  const cropperRef = createRef<ReactCropperElement>();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setOpen(true);
        setImage(reader.result as string);
      };
      reader.readAsDataURL(files[0]);
    }
  };

  const handleOpenFileExplorer = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const getCropData = () => {
    if (typeof cropperRef.current?.cropper !== "undefined") {
      const croppedCanvas = cropperRef.current?.cropper.getCroppedCanvas();
      const croppedImage = croppedCanvas.toDataURL();
      setCropData(!isAsync ? croppedImage : '#');
      onImageCropped(croppedImage, setOpen);
    }
  };

  const handleClose = () => {
    setOpen(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className="flex justify-center relative">
      <Avatar className={`h-40 w-40 rounded-full`}>
        <AvatarImage
          src={cropData !== "#" ? cropData : defaultImage || "/logo.png"}
          alt="Profile"
        />
        <AvatarFallback className="rounded-lg">
          <UserRound className="h-16 w-16" />
        </AvatarFallback>
      </Avatar>
      <Button
        onClick={handleOpenFileExplorer}
        size="icon"
        className="absolute transition-all duration-300 mx-auto bottom-1 right-1 text-white cursor-pointer"
        type="button"
      >
        <UserPenIcon />
      </Button>
      <Input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
      />
      <Dialog open={open} onOpenChange={handleClose} key={"update-photo"}>
        <DialogContent className="rounded-lg max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold">
              {t("image-preveiw.label")}
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              {t("image-preveiw.adjust_crop")}
            </DialogDescription>
          </DialogHeader>
          <div className="bg-muted/30 rounded-lg p-2">
            {image && (
              <Cropper
                ref={cropperRef}
                style={{
                  maxHeight: "500px",
                  minHeight: "300px",
                  maxWidth: "100%",
                  backgroundColor: "transparent",
                  borderRadius: "10px",
                }}
                initialAspectRatio={1}
                viewMode={1}
                src={image}
                minCropBoxHeight={10}
                minCropBoxWidth={10}
                background={false}
                responsive={true}
                autoCropArea={1}
                checkOrientation={true}
                guides={true}
                aspectRatio={1}
                autoCrop={false}
                movable={true}
                scalable={false}
                zoomable={true}
              />
            )}
          </div>
          <div className="flex justify-end gap-4 mt-4">
            <Button
              variant="outline"
              onClick={handleClose}
              className="rounded-md"
            >
              {t("image-preveiw.cancel")}
            </Button>
            <Button onClick={getCropData} className="px-6 rounded-md">
              <Camera className="h-4 w-4 mr-2" />
              {isUpdating ? <LoadingSpinner className="h-5 w-5" /> : t("image-preveiw.crop")}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
