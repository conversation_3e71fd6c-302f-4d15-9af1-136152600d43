// BaseCarouselComponent
"use client";
import {
  Carousel,
  CarouselMainContainer,
  CarouselNext,
  CarouselPrevious,
  CarouselThumbsContainer,
  SliderMainItem,
  SliderThumbItem,
} from "@/components/ui/extension/carousel";
import Image from "next/image";
import ZoomImage from "./zoom-image";
import { getImageSizeUrl } from "@/utils/imageURL";
import { useCallback, useEffect, useState } from "react";

const CarouselComponent = ({
  images,
  isGoogleDrive = true,
  onImageClick,
  Component,
  Thumbs,
  selected,
  setApi,
}: {
  images: any[];
  isGoogleDrive: boolean;
  onImageClick?: (imageUrl: string) => void;
  Component: React.ElementType;
  Thumbs: React.ElementType;
  selected: number;
  setApi: React.Dispatch<React.SetStateAction<any>>;
}) => {
  return (
    <div className="w-full h-full flex items-center justify-center bg-transparent">
      <Carousel className="w-full h-full" jumpTo={selected} setApi={setApi}>
        <CarouselNext className="h-10 w-10 right-2" />
        <CarouselPrevious className="h-10 w-10 left-2" />
        <CarouselMainContainer className="w-full h-full">
          {images.length > 0 ? (
            images.map((img: any, index: number) => {
              return isGoogleDrive ? (
                <Component key={index} index={index} fileId={img} />
              ) : (
                <ImagesCarousel
                  img={img}
                  key={img.id}
                  onImageClick={onImageClick}
                />
              );
            })
          ) : (
            <SliderMainItem className="w-full h-full">
              <div className="w-full h-full flex items-center justify-center rounded-xl bg-background">
                <Image
                  src="/placeholder1.jpg"
                  width={400}
                  height={200}
                  alt="Placeholder Image"
                  className="w-full h-full rounded-3xl object-contain"
                />
              </div>
            </SliderMainItem>
          )}
        </CarouselMainContainer>
        <div className="absolute bottom-2 left-1/2 rounded-md -translate-x-1/2 w-[800px]">
          <CarouselThumbsContainer className="gap-x-1 p-0 w-[300px] rounded-full h-[5.5rem] bg-inherit">
            {images?.length > 0 ? (
              images.map((img: any, index: number) => {
                return (
                  <SliderThumbItem
                    key={index}
                    index={index}
                    className="w-max rounded-md basis-[35.5%] flex justify-center p-0 m-0 h-20"
                  >
                    {isGoogleDrive ? (
                      <Thumbs key={index} fileId={img} index={index} />
                    ) : (
                      <Image
                        src={getImageSizeUrl({
                          url: img.url,
                          size: 1024,
                        })}
                        fill
                        blurDataURL="/placeholder.svg"
                        placeholder="blur"
                        alt={`Vehicle Image ${img.id}`}
                        className={`object-contain rounded-none h-24 w-full`}
                      />
                    )}
                  </SliderThumbItem>
                );
              })
            ) : (
              <Image
                src="/placeholder1.jpg"
                width={400}
                height={200}
                alt="Placeholder Image"
                className="w-full h-full rounded-3xl object-cover"
              />
            )}
          </CarouselThumbsContainer>
        </div>
      </Carousel>
    </div>
  );
};

function ImagesCarousel({
  img,
  onImageClick,
}: {
  img: any;
  onImageClick?: (imageUrl: string) => void;
}) {
  return (
    <SliderMainItem
      className="bg-transparent w-full h-full"
      onMouseEnter={() => onImageClick?.(img.url || "")}
    >
      <div
        tabIndex={0} // Make div focusable
        className=" flex items-center justify-center h-full w-full"
      >
        <ZoomImage file={img} isGoogleDrive={false} />
      </div>
    </SliderMainItem>
  );
}

export default CarouselComponent;

export const useDotButton = (emblaApi: any) => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState([]);

  const onDotButtonClick = useCallback(
    (index: number) => {
      if (!emblaApi) return;
      emblaApi.scrollTo(index);
    },
    [emblaApi]
  );

  const onInit = useCallback((emblaApi: any) => {
    setScrollSnaps(emblaApi.scrollSnapList());
  }, []);

  const onSelect = useCallback((emblaApi: any) => {
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, []);

  useEffect(() => {
    if (!emblaApi) return;

    onInit(emblaApi);
    onSelect(emblaApi);
    emblaApi.on("reInit", onInit).on("reInit", onSelect).on("select", onSelect);
  }, [emblaApi, onInit, onSelect]);

  return {
    selectedIndex,
    scrollSnaps,
    onDotButtonClick,
  };
};
