"use client";

import { useSearchParams, useRouter } from "next/navigation";
import { XCircle, Search, Loader2 } from "lucide-react";
import { useState, useEffect, useTransition, useRef } from "react";
import { Input } from "@/components/ui/input";
import { useDirection } from "@/hooks/useDirection";
import { cn } from "@/lib/utils";

function InputSearch({
  type,
  fieldName,
  placeholder = "Search...",
  isClose = true,
  className,
  debounceMs = 800,
}: {
  type: string;
  fieldName: string;
  placeholder?: string;
  isClose?: boolean;
  className?: string;
  debounceMs?: number;
}) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [inputValue, setInputValue] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const [isPending, startTransition] = useTransition();
  const { isRTL } = useDirection();

  const abortControllerRef = useRef<AbortController | null>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const initialValue = searchParams.get(fieldName) || "";
    setInputValue(initialValue);
  }, [searchParams, fieldName]);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  function handleInputChange(value: string) {
    setInputValue(value);

    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    debounceTimerRef.current = setTimeout(() => {
      if (abortController.signal.aborted) {
        return;
      }

      const params = new URLSearchParams(Array.from(searchParams.entries()));

      if (!value || value === "") {
        params.delete(fieldName);
      } else {
        params.set(fieldName, value);
      }

      params.delete("page");

      startTransition(() => {
        if (!abortController.signal.aborted) {
          try {
            router.push(`?${params.toString()}`);
          } catch (error: any) {
            if (error.name !== "AbortError") {
              console.error("Navigation error:", error);
            }
          }
        }
      });
    }, debounceMs);
  }

  function handleClearInput() {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    setInputValue("");
    const params = new URLSearchParams(Array.from(searchParams.entries()));
    params.delete(fieldName);

    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    startTransition(() => {
      if (!abortController.signal.aborted) {
        try {
          router.replace(`?${params}`);
        } catch (error: any) {
          if (error.name !== "AbortError") {
            console.error("Navigation error:", error);
          }
        }
      }
    });
  }

  return (
    <div
      className={cn(
        "relative group transition-all duration-200",
        isFocused ? "ring-2 ring-primary/30 rounded-lg" : "",
        className
      )}
    >
      <div className="relative flex items-center">
        <Search
          className={cn(
            "absolute w-4 h-4 text-muted-foreground transition-all duration-200",
            isRTL ? "right-3" : "left-3",
            inputValue && "text-primary"
          )}
        />

        <Input
          type={type}
          value={inputValue}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          className={cn(
            "pl-10 pr-10 border transition-all duration-200 bg-background",
            "focus:ring-0 focus:border-primary",
            isFocused ? "border-primary shadow-sm" : "border-input",
            isPending ? "bg-muted/20" : ""
          )}
        />

        <div
          className={`absolute top-1/2 -translate-y-1/2 ${
            isRTL ? "left-3" : "right-3"
          }`}
        >
          {isPending ? (
            <div className="flex items-center justify-center w-5 h-5">
              <Loader2 className="w-4 h-4 animate-spin text-primary" />
            </div>
          ) : (
            isClose &&
            inputValue && (
              <XCircle
                className="w-5 h-5 text-muted-foreground hover:text-foreground transition-colors cursor-pointer"
                onClick={handleClearInput}
              />
            )
          )}
        </div>
      </div>

      {isPending && (
        <div className="absolute bottom-0 left-0 h-0.5 bg-primary animate-pulse rounded-full w-full" />
      )}
    </div>
  );
}

export default InputSearch;
