"use client";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>read<PERSON>rumb<PERSON><PERSON>,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { useDirection } from "@/hooks/useDirection";
import { Link } from "@/i18n/routing";
import { usePathname } from "next/navigation";
import React, { useState, useEffect, FunctionComponent } from "react";
import { Bell, ChevronDown, ChevronLeft, ChevronRight } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Language from "../localization/langauge";
import NotificationWindow from "../notifications/notification-window";
import { useSession } from "next-auth/react";
import { useGetNotification } from "../notifications/services/useGetNotification";
import { useNotification } from "@/context/notification-context";
import NotificationBadgeWithCount from "../Common_UI/notification-badge";
import { Button } from "../ui/button";

interface AppHeaderProps {
  breadcrumbs: { name: string; href: string }[];
  dropdownItems?: { name: string; href: string }[];
}

const AppHeader: FunctionComponent<AppHeaderProps> = ({
  breadcrumbs = [],
  dropdownItems = [],
}) => {
  const { dir, isRTL } = useDirection();
  const pathname = usePathname();
  const [lastPath, setLastPath] = useState<string>("");
  const session = useSession();
  const { setNotifications, totalNotification } = useNotification();
  useGetNotification();
  const totalUnreadNotification =
    (totalNotification.announcement || 0) +
    (totalNotification.arrival_notice || 0) +
    (totalNotification.transaction || 0);

  const normalize = (str: string) =>
    str.toLowerCase().replace(/\s+/g, " ").trim();

  useEffect(() => {
    if (dropdownItems.length === 0) return;

    const pathParts = pathname.split("/").filter(Boolean);
    const lastSegment = pathParts[pathParts.length - 1];
    const decodedLastSegment = decodeURIComponent(lastSegment);
    const normalizedLastSegment = normalize(decodedLastSegment);
    const matchingItem = dropdownItems.find((item) => {
      const itemPathParts = item.href.split("/").filter(Boolean);
      const encodedItemLastSegment = itemPathParts[itemPathParts.length - 1];
      let decodedItemLastSegment;
      try {
        decodedItemLastSegment = decodeURIComponent(encodedItemLastSegment);
      } catch (error: any) {
        console.error(error?.message);

        decodedItemLastSegment = encodedItemLastSegment;
      }
      const normalizedItemLastSegment = normalize(decodedItemLastSegment);
      return normalizedItemLastSegment === normalizedLastSegment;
    });

    setLastPath(matchingItem ? matchingItem.name : "All");
  }, [pathname, dropdownItems]);

  return (
    <header
      className="sticky z-50 top-0 flex h-16 shrink-0 items-center justify-between gap-2 border-b bg-background px-4"
      dir={dir}
    >
      <div className="flex items-center">
        <SidebarTrigger className="rtl:-mr-1 -ml-1" />
        <Separator orientation="vertical" className="rtl:ml-2 mr-2 h-4" />
        <Breadcrumb>
          <BreadcrumbList>
            {breadcrumbs.slice(0, -1).map((item, i) => (
              <React.Fragment key={i}>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink asChild>
                    <Link href={item.href}>{item.name}</Link>
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block">
                  {isRTL ? <ChevronLeft /> : <ChevronRight />}
                </BreadcrumbSeparator>
              </React.Fragment>
            ))}
            <BreadcrumbItem>
              {dropdownItems.length > 0 ? (
                <DropdownMenu>
                  <DropdownMenuTrigger className="hover:text-foreground font-medium flex items-center gap-1">
                    {lastPath || breadcrumbs.at(-1)?.name}
                    <ChevronDown className="w-4 h-4" />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start">
                    {dropdownItems.map((item, index) => (
                      <DropdownMenuItem key={index} asChild>
                        <Link
                          href={item.href}
                          onClick={() => setLastPath(item.name)}
                        >
                          {item.name}
                        </Link>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <span className="font-medium">{breadcrumbs.at(-1)?.name}</span>
              )}
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <div className="flex items-center gap-4">
        <NotificationWindow />
        {session.data?.user_type && session.data?.user_type === "customer" && (
          <Button
            className="relative flex items-center gap-2 hover:bg-accent rounded-md"
            onClick={() => setNotifications(true)}
            variant={"link"}
            size={"icon"}
          >
            <div className="inline-flex items-center justify-center p-2 rounded-md">
              {" "}
              {/* Add your button styling classes here */}
              <Bell />
            </div>

            {/* Badge remains the same */}
            {totalUnreadNotification > 0 && (
              <NotificationBadgeWithCount
                count={totalUnreadNotification}
                className={`absolute -top-1 ${isRTL ? "left-0" : "right-0"}`}
              />
            )}
          </Button>
        )}
        <Language className="w-full h-full aspect-square md:w-[300px] md:aspect-auto" />
      </div>
    </header>
  );
};

export default AppHeader;
