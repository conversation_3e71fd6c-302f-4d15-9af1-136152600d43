import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { SidebarMenuButton, SidebarMenuItem } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { ChevronRight } from "lucide-react";
import React from "react";
type FilterCollapseProps = {
  label: string;
  children: React.ReactNode;
  className?: string;
  isOpen: boolean;
  onToggle: () => void;
};
const FilterCollapse = ({
  children,
  label,
  className,
  isOpen,
  onToggle,
}: FilterCollapseProps) => {
  return (
    <Collapsible
      className="group/collapsible shadow-inner "
      open={isOpen}
      onOpenChange={onToggle}
    >
      <SidebarMenuItem className="list-none">
        <CollapsibleTrigger asChild>
          <SidebarMenuButton
            className={cn("flex justify-between min-w-full h-10 ", className)}
            variant={"outline"}
          >
            <p className={"font-semibold w-full rtl:text-right  p-4"}>
              {label}
            </p>
            <ChevronRight className="ml-auto w-full rtl:mr-auto transition-transform duration-200 rtl:rotate-180 group-data-[state=open]/collapsible:rotate-90" />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent className="p-1">{children}</CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  );
};

export default FilterCollapse;
