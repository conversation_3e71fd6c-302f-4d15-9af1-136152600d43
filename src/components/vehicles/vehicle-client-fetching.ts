import { useFetchClient } from "@/utils/axios";
import { useQuery } from "@tanstack/react-query";
type imagesType = {
  google_images: {
    photo: any[];
    auction: any[];
  };
  images: any[];
};

interface GetVehicleImagesOptions {
  vehicleId: number;
  isGoogleDrive: boolean;
  url?: string;
}

export function useGetVehicleImages({
  vehicleId,
  isGoogleDrive,
  url = "/v2/vehicles/vehicleImages",
}: GetVehicleImagesOptions) {
  const fetchClient = useFetchClient();

  const { data, isLoading, isError } = useQuery({
    queryKey: ["vehicleImages", vehicleId, isGoogleDrive],
    queryFn: async (): Promise<imagesType | undefined> => {
      const response = await fetchClient(
        `${url}?vechicle_id=${vehicleId}&is_google=${isGoogleDrive}`
      );
      return response.data;
    },
    enabled: Boolean(vehicleId),
  });
  const images = {
    images: data?.images || [],
    google_images: data?.google_images || {
      photo: [],
      auction: [],
    },
  };
  return { images, isError, isLoading };
}

export const useGetVehicleImageFromGoogleDrive = (fileId: string) => {
  const fetchClient = useFetchClient();

  return useQuery({
    queryKey: ["vehicle-image", fileId],
    queryFn: async () => {
      const res = await fetchClient(
        `/v2/vehicles/get-image-url?fileId=${fileId}`,
        {
          responseType: "blob",
        }
      );
      const blobUrl = URL.createObjectURL(res.data);
      return blobUrl;
    },
    enabled: !!fileId,
    staleTime: 10 * 60 * 1000,
  });
};
