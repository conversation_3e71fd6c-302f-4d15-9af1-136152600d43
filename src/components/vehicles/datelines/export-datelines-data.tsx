"use client";
import React, { useMemo } from "react";

import { ColDef } from "ag-grid-community";
import { format, formatDate } from "date-fns";

import { getDestinationInventory } from "../services/vehicle-service";

import moment from "moment";

import { ExportModal } from "@/components/Common_UI/export-modal";
import { toast } from "sonner";
import { useTranslations } from "next-intl";

type Props = {
  records: {
    page: number;
    per_page: number;
    total: number;
    data: any[];
  };
};

export default function ExportDatelines({ records }: Props) {
  const t = useTranslations("export-modal");

  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        field: "is_printed",
        headerName: "Is Printed",
      },

      {
        headerName: "Vehicle Description",
        valueGetter: (params) => {
          return `${params?.data?.year} ${params?.data?.make} ${params?.data?.model} ${params?.data?.color} `;
        },
      },

      { field: "vin", headerName: "VIN" },

      { field: "lot_number", headerName: "Lot Number" },

      { field: "title_number", headerName: "Title Number" },

      {
        field: "purchased_at",
        headerName: "Purchase Date",
        valueGetter: (params) =>
          params?.data?.purchased_at
            ? formatDate(params?.data?.purchased_at, "yyyy MMM dd")
            : "",
      },

      {
        field: "created_at",
        headerName: "Report Date",
        valueGetter: (params) =>
          params?.data?.created_at
            ? formatDate(params?.data?.created_at, "yyyy MMM dd")
            : "",
      },

      {
        field: "payment_date",
        headerName: "Payment Date",
        valueGetter: (params) =>
          params?.data?.payment_date
            ? formatDate(params?.data?.payment_date, "yyyy MMM dd")
            : "",
      },

      {
        field: "vehicle_towings",
        headerName: "Tow Request Date",
        valueGetter: (params) =>
          params?.data?.request_for_pickup_date
            ? formatDate(params?.data?.request_for_pickup_date, "yyyy MMM dd")
            : "",
      },

      {
        field: "pickup_date",
        headerName: "Pick Up Date",
        valueGetter: (params) =>
          params?.data?.pickup_date
            ? formatDate(params?.data?.pickup_date, "yyyy MMM dd")
            : "",
      },

      {
        field: "deliver_date",
        headerName: "Delivery Date",
        valueGetter: (params) =>
          params.data?.deliver_date
            ? format(new Date(params.data?.deliver_date), "yyyy-MM-dd")
            : "",
      },

      {
        field: "pickup_date_from_purchase",
        headerName: "Pickup Date From Purchase",
        valueGetter: (params) => {
          const pickupDate = params?.data?.pickup_date
            ? moment(params.data.pickup_date)
            : null;
          const purchaseDate = params?.data?.purchased_at;

          if (pickupDate && purchaseDate) {
            const diffDays = pickupDate.diff(purchaseDate, "days");

            return diffDays + " days";
          }

          return "";
        },
      },

      {
        field: "pickup_date_from_report",
        headerName: "Pick Up Date Frm Report",
        valueGetter: (params) => {
          const pickupDate = params?.data?.pickup_date
            ? moment(params.data.pickup_date)
            : null;
          const reportDate = params?.data?.created_at;

          if (pickupDate && reportDate) {
            const diffDays = pickupDate.diff(reportDate, "days");
            return diffDays + " days";
          }

          return "";
        },
      },

      {
        field: "destination_name",
        headerName: "Point Of Destinations",
        valueGetter: (params) => params?.data?.destinations?.name,
      },

      {
        field: "customer_comment",
        headerName: "Comment",
      },
    ],
    []
  );
  const fetchAllData = async (): Promise<any[]> => {
    const response = await getDestinationInventory({
      params: {
        page: 1,
        per_page: records.total,
        search: "",
        exactMatch: false,
        filterData: "",
      },
    });
    return response.data;
  };

  return (
    <ExportModal
      columnDefs={colDefs}
      currentData={records.data}
      exportFileName="Vehicle DateLines"
      fetchAllData={fetchAllData}
      totalItems={records.total}
      translations={{
        title: t("title"),
        exportData: t("sub-title"),
        subTitle: t("sub-title"),
        currentData: t("current-data"),
        allData: t("all-data"),
        cancel: t("cancel"),
        export: t("export"),
      }}
      onExportSuccess={() => toast("Vehicle DateLines Export Completed")}
    />
  );
}
