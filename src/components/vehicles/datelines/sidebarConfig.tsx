import { SideBarDef } from "ag-grid-community";
import { FilterModel } from "../cell-renderers/filter-modal";

export const sidebarConfig: string | boolean | string[] | SideBarDef = {
  hiddenByDefault: false,
  toolPanels: [
    {
      id: "columns",
      labelDefault: "Columns",
      labelKey: "columns",
      iconKey: "columns",
      toolPanel: "agColumnsToolPanel",
      toolPanelParams: {
        suppressRowGroups: true,
        suppressValues: true,
        suppressPivots: true,
        suppressPivotMode: true,
        suppressSideButtons: true,
      },
    },
    {
      id: "filters",
      labelDefault: "Filters",
      labelKey: "filters",
      iconKey: "filter",
      width: 290,
      toolPanel: FilterModel,
    },
  ],
};
