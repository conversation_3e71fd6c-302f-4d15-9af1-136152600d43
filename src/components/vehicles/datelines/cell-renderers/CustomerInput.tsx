"use client";
import { useState, type FunctionComponent } from "react";
import { Popover, PopoverTrigger } from "@radix-ui/react-popover";
import { PopoverContent } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { MessageSquareText, MapPin } from "lucide-react";
import { useTranslations } from "next-intl";
import type { CustomCellRendererProps } from "ag-grid-react";
import { CommentModal } from "../../dialog-modal/comment-modal";
import { PODModal } from "../../dialog-modal/pod-modal";

export const CustomerInput: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("vehicle-datelines-datatable.body");

  const [commentOpen, setCommentOpen] = useState<boolean>(false);
  const [destinationOpen, setDestinationOpen] = useState<boolean>(false);

  const [newComment, setNewComment] = useState<string | null>(
    data?.customer_comment ?? null
  );

  const [selectedDestinationName, setSelectedDestinationName] = useState<
    string | null
  >(data?.destinations?.name ?? null);

  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[85px]">{t("comment")}:</div>

        <Popover open={commentOpen} onOpenChange={setCommentOpen}>
          <PopoverTrigger asChild>
            <Button
              size="icon"
              variant="ghost"
              className="text-blue-500 border-none -mt-2"
            >
              <MessageSquareText />
            </Button>
          </PopoverTrigger>

          <PopoverContent className="w-80 min-h-32 flex flex-col gap-y-4">
            <h1 className="font-bold py-2">Vehicle Comment</h1>
            <CommentModal
              vehicleId={data?.id}
              defaultValue={newComment || ""}
              onSuccess={(updatedComment) => {
                setNewComment(updatedComment);
                setCommentOpen(false);
              }}
              close={() => setCommentOpen(false)}
            />
          </PopoverContent>
        </Popover>
      </div>

      <Separator className="my-1" />

      {/* Point of Destination Section */}
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[90px]">{t("point-of-destination")}:</div>

        <Popover open={destinationOpen} onOpenChange={setDestinationOpen}>
          <PopoverTrigger asChild>
            <Button
              size="icon"
              variant="ghost"
              className="text-blue-500 -mt-2 text-left justify-start w-fulls"
            >
              {selectedDestinationName || <MapPin />}
            </Button>
          </PopoverTrigger>

          <PopoverContent className="w-80 min-h-32 flex flex-col gap-y-4">
            <h1 className="font-bold py-2">Add Point Of Destination</h1>
            <PODModal
              vehicleId={data?.id}
              defaultValue={selectedDestinationName}
              onSuccess={(newDestination) => {
                setSelectedDestinationName(newDestination);
                setDestinationOpen(false); // Close on success
              }}
              close={() => setDestinationOpen(false)} // Close on cancel
            />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};
