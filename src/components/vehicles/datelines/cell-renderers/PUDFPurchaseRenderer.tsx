import { Separator } from "@/components/ui/separator";
import type { CustomCellRendererProps } from "ag-grid-react";
import moment from "moment";
import { useTranslations } from "next-intl";
import { FunctionComponent } from "react";

export const PUDFPurchaseRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data }) => {
  const t = useTranslations("vehicle-datelines-datatable.body");

  const pickup_date = data?.pickup_date ? moment(data.pickup_date) : null;
  const purchased_at = data?.purchased_at ? moment(data.purchased_at) : null;
  const created_at = data?.created_at ? moment(data.created_at) : null;

  const pickupDaysFromPurchase =
    pickup_date && purchased_at
      ? pickup_date.diff(purchased_at, "days") + " days"
      : "";

  const pickupDaysFromReport =
    pickup_date && created_at
      ? pickup_date.diff(created_at, "days") + " days"
      : "";

  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[100px]">{t("pick-up-days-from-purchase")}: </div>
        {pickupDaysFromPurchase}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[100px]">{t("pick-up-days-from-report")}: </div>
        {pickupDaysFromReport}
      </div>
    </div>
  );
};
