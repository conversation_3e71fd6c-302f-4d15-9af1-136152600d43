import { Separator } from "@/components/ui/separator";
import type { CustomCellRendererProps } from "ag-grid-react";
import { formatDate } from "date-fns";
import { useTranslations } from "next-intl";
import { type FunctionComponent } from "react";
export const PurchaseDateRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data }) => {
  const t = useTranslations("vehicle-datelines-datatable.body");
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[97px]">{t("purchase-date")}:</div>
        {data?.purchased_at
          ? formatDate(data?.purchased_at, "yyyy MMM dd")
          : ""}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[97px]">{t("report-date")}:</div>
        {data?.created_at ? formatDate(data?.created_at, "yyyy MMM dd") : ""}
      </div>
    </div>
  );
};
