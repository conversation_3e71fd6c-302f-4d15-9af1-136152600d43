import { Separator } from "@/components/ui/separator";
import type { CustomCellRendererProps } from "ag-grid-react";
import { formatDate } from "date-fns";
import { useTranslations } from "next-intl";

import { type FunctionComponent } from "react";

export const PickUpDateRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("vehicle-datelines-datatable.body");
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[120px]">{t("tow-request-date")}: </div>
        {data?.request_for_pickup_date
          ? formatDate(data?.request_for_pickup_date, "yyyy MMM dd")
          : ""}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[120px]">{t("pick-up-date")}: </div>
        {data?.pickup_date ? formatDate(data?.pickup_date, "yyyy MMM dd") : ""}
      </div>
    </div>
  );
};
