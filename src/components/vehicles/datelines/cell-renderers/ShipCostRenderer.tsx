"use client";
import { Separator } from "@/components/ui/separator";
import { FunctionComponent } from "react";
import { useTranslations } from "next-intl";
import { CustomCellRendererProps } from "ag-grid-react";
import { formatDate } from "date-fns";
export const PaymentDateRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data }) => {
  const t = useTranslations("vehicle-datelines-datatable.body");
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[90px]">{t("payment-date")}:</div>
        {data?.payment_date
          ? formatDate(data?.payment_date, "yyyy MMM dd")
          : ""}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[90px]">{t("delivery-date")}:</div>
        {data?.deliver_date
          ? formatDate(data?.deliver_date, "yyyy MMM dd")
          : ""}
      </div>
    </div>
  );
};
