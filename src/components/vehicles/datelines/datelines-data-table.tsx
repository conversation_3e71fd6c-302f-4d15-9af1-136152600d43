"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";

import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MultiFilterModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import { useMemo, useRef, useState } from "react";

import { PickUpDateRenderer } from "./cell-renderers/PickUpDateRenderer";
import { PurchaseDateRenderer } from "./cell-renderers/PurchaseDateRenderer";
import { ContainerRenderer } from "./cell-renderers/ContainerRenderer";
import { PUDFPurchaseRenderer } from "./cell-renderers/PUDFPurchaseRenderer";
import { PaymentDateRenderer } from "./cell-renderers/ShipCostRenderer";
import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import useSidebarConfig from "../sidebarConfig";
import { Props } from "../services/config";
import { CustomerInput } from "./cell-renderers/CustomerInput";
import VehicleRenderer from "./cell-renderers/VehicleRenderer";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);

const paginationPageSizeSelector = [5, 10, 20];
export const DateLines = ({ records, gridRefProps, exportColDefs }: Props) => {
  const gridRef = useRef<AgGridReact>(null);
  const t = useTranslations("vehicle-datelines-datatable");
  const sidebarConfig = useSidebarConfig();
  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: "#",
        cellDataType: "text",
        minWidth: 50,
        cellStyle: {
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        },
        valueGetter: (params) => {
          if (params.node) {
            return params.node.rowIndex ? params.node.rowIndex + 1 : 1;
          }
        },
      },
      {
        field: "vehicle",
        headerName: t("header.vehicle"),
        cellDataType: "text",
        cellRenderer: VehicleRenderer,
        minWidth: 325,
      },
      {
        field: "container_number",
        headerName: t("header.container"),
        cellRenderer: ContainerRenderer,
        minWidth: 190,
      },
      {
        field: "purchase_date",
        headerName: t("header.purchase-date"),
        minWidth: 210,
        cellRenderer: PurchaseDateRenderer,
      },

      {
        field: "payment_date",
        headerName: t("header.payment-date"),
        minWidth: 200,
        cellRenderer: PaymentDateRenderer,
      },
      {
        field: "purhcase",
        headerName: t("header.pudf-purchase"),
        minWidth: 220,
        cellRenderer: PUDFPurchaseRenderer,
      },
      {
        field: "pick_up_date",
        headerName: t("header.pick-up-date"),
        cellRenderer: PickUpDateRenderer,
        minWidth: 230,
      },
      {
        field: "comment",
        headerName: t("header.comment"),
        cellRenderer: CustomerInput,
        minWidth: 200,
      },
    ],
    [t]
  );
  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
    }),
    []
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  const [quickFilterText] = useState<string>();

  const selectionColumnDef = useMemo(() => {
    return {
      minWidth: 44,
    };
  }, []);
  const { isRTL } = useDirection();

  return (
    <>
      <AgGridDataTable
        enableRtl={isRTL ? true : false}
        ref={gridRefProps || gridRef}
        selectionColumnDef={selectionColumnDef}
        columnDefs={exportColDefs || colDefs}
        rowData={records?.data || []}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        pagination={false}
        paginationPageSize={records?.per_page ?? 20}
        paginationPageSizeSelector={paginationPageSizeSelector}
        quickFilterText={quickFilterText}
        rowHeight={100}
        colResizeDefault="shift"
        headerHeight={60}
        sideBar={sidebarConfig}
        rowClassRules={{
          "row-even": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 === 0
                ? false
                : true
              : true;
          },
          "row-odd": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 !== 0
                ? false
                : true
              : true;
          },
        }}
      />
    </>
  );
};
