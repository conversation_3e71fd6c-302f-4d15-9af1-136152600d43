import React, { useState } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { Download, Info, Loader } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import CustomDialog from "@/components/Common_UI/custom-dialog";
import { getGoogleDriveImageSizeUrl } from "@/utils/imageURL";
import CarouselComponent from "../Common_UI/custom-carousel";
import { useGetVehicleImageFromGoogleDrive } from "./vehicle-client-fetching";
import { SliderMainItem } from "../ui/extension/carousel";
import ZoomImage from "../Common_UI/zoom-image";

export default function VehicleImagesFromGoogleDrive({
  fileId,
  images,
  download,
  isDownloading,
}: {
  fileId: string;
  images: any[];
  download: () => void;
  isDownloading: boolean;
}) {
  const {
    data: imageUrl,
    isLoading,
    error,
  } = useGetVehicleImageFromGoogleDrive(fileId);
  const [openCarousel, setOpenCarousel] = useState(false);
  const [downloadLink, setDownloadLink] = useState("");
  const handleCopyLink = (imagelurl: string) => {
    setDownloadLink(imagelurl);
  };

  if (error)
    return (
      <div className="flex justify-center items-center w-full h-full">
        <Skeleton className="w-36 h-36">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size={"icon"}>
                  <Info />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Error loading image</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </Skeleton>
      </div>
    );
  if (isLoading)
    return (
      <div className="outline outline-1 outline-border size-full flex items-center justify-center rounded-xl overflow-hidden bg-background">
        <Skeleton className="rounded-md object-cover h-full w-full" />
      </div>
    );
  const iconClass =
    "absolute w-5 h-5  cursor-pointer top-2 right-2 z-50 text-primary";
  return (
    <>
      <div className="outline relative outline-1 outline-border size-full flex items-center justify-center rounded-xl overflow-hidden bg-background">
        {isDownloading ? (
          <Loader className={iconClass + " animate-spin "} />
        ) : (
          <Download className={iconClass} onClick={() => download()} />
        )}
        <img
          src={`${imageUrl}`}
          alt="Vehicle"
          onClick={() => setOpenCarousel(true)}
          className="rounded-md object-cover h-full w-full"
        />
      </div>
      <CustomDialog
        openModal={openCarousel}
        setOpenModal={() => setOpenCarousel(false)}
        title="Vehicle Image"
        isGoogleDrive={true}
        downloadLink={getGoogleDriveImageSizeUrl({
          url: downloadLink,
          size: 1024,
        })}
      >
        <CarouselComponent
          images={images}
          isGoogleDrive={true}
          onImageClick={handleCopyLink}
          Component={GoogleDriveImagesCarousel}
          Thumbs={GoogleDriveImagesThumbsCarousel}
          selected={0}
          setApi={() => { }} // Set the API to null
        />
      </CustomDialog>
    </>
  );
}

export function GoogleDriveImagesCarousel({
  fileId,
  index,
}: {
  fileId: string;
  index: number;
}) {
  const {
    data: imageUrl,
    isLoading,
    error,
  } = useGetVehicleImageFromGoogleDrive(fileId);

  if (isLoading)
    return (
      <div className="relative flex justify-center items-center h-[100vh] w-[100vw]">
        <Skeleton className="min-w-[80vw] min-h-[90vh] flex justify-center items-center" />
      </div>
    );
  if (error)
    return (
      <div className="relative flex justify-center items-center h-[100vh] w-[100vw]">
        Something went wrong When loading image
      </div>
    );
  return (
    <SliderMainItem className="bg-transparent w-full h-full">
      <div
        tabIndex={0} // Make div focusable
        className=" flex items-center justify-center h-full w-full"
      >
        <ZoomImage
          file={{
            url: imageUrl || "#",
            id: `${index}`,
            name: `${imageUrl}` + index,
          }}
          isGoogleDrive={true}
        />
      </div>
    </SliderMainItem>
  );
}

export function GoogleDriveImagesThumbsCarousel({
  fileId,
  index,
}: {
  fileId: string;
  index: number;
}) {
  const {
    data: imageUrl,
    isLoading,
    error,
  } = useGetVehicleImageFromGoogleDrive(fileId);

  if (isLoading)
    return (
      <div className="relative flex justify-center items-center h-[100vh] w-[100vw]">
        <Skeleton className="min-w-[80vw] min-h-[90vh] flex justify-center items-center" />
      </div>
    );
  if (error)
    return (
      <div className="relative flex justify-center items-center h-[100vh] w-[100vw]">
        Something went wrong When loading image
      </div>
    );
  return (
    <img
      src={
        getGoogleDriveImageSizeUrl({
          url: imageUrl,
          size: 1024,
        }) || "/placeholder.svg"
      }
      alt={`Vehicle Image ${index}`}
      className={`object-cover  h-full min-w-full w-max overflow-hidden `}
    />
  );
}
