"use client";
import PaginationComponent from "@/components/Common_UI/use-paginition";
import { CostAnalysis } from "@/components/vehicles/cost-analysis/cost-analysis-data-table";
import { useResponsive } from "@/hooks/use-mobile";
import { transformObject } from "@/lib/transferObject";
import React from "react";
import { loadCostAnalysisData } from "../../services/veihicle-actions";
import { CostAnalysisMobile } from "../cost-analysis-data-table-mobile";
// Define allowed keys (Only these fields will be processed)
const allowedKeys = [
  "con",
  "loc",
  "des",
  "pri_from",
  "pri_to",
  "payment_from",
  "payment_to",
  "carstate",
  "vin",
  "lot_number",
  "make",
  "model",
  "year",
  "from_purchased_at",
  "to_purchased_at",
  "from_payment_date",
  "to_payment_date",
  "from_deliver_date",
  "to_deliver_date",
  "checked",
  "is_printed",
];

// Define dynamic mapping (Customize as needed)
const mapping = {
  con: "container_id",
  des: "destination_id",
  loc: "point_of_loading",
  inv: "invoice_number",
  pri_from: "price.min",
  pri_to: "price.max",
  payment_from: "payment_received.min",
  payment_to: "payment_received.max",
  carstate: "carstate",
  lot_number: "lot_number",
  vin: "vin",
  make: "make",
  model: "model",
  year: "year",
  from_purchased_at: "purchased_at.from",
  to_purchased_at: "purchased_at.to",
  from_payment_date: "payment_date.from",
  to_payment_date: "payment_date.to",
  from_deliver_date: "deliver_date.from",
  to_deliver_date: "deliver_date.to",
  checked: "bool@@customer_checked",
  is_printed: "bool@@is_printed",
};

type PageProps = {
  searchParams: any;
  records: any;
  baseState: string;
};

const CostAnalysisClient = ({
  records,
  baseState,
  searchParams,
}: PageProps) => {
  const boundLoadMoreData = loadCostAnalysisData.bind(
    null,
    baseState,
    searchParams
  );
  const { isMobile } = useResponsive();
  const initialParams = {
    search: searchParams?.search || "",
    filterData:
      Object.keys(
        transformObject(searchParams, allowedKeys, mapping, [
          "lot_number",
          "year",
          "vin",
        ])
      ).length !== 0
        ? JSON.stringify(
            transformObject(searchParams, allowedKeys, mapping, [
              "lot_number",
              "year",
              "vin",
            ])
          )
        : "",
  };
  const componentKey = [
    searchParams?.search || "no-search",
    baseState || "no-status",
    initialParams.filterData || "no-filters",
    searchParams?.page || "1",
  ].join("-");

  return (
    <>
      {isMobile ? (
        <div className="h-[calc(100vh-160px)] ">
          <CostAnalysisMobile
            key={componentKey}
            records={records}
            onLoadMoreData={boundLoadMoreData}
            initialParams={initialParams}
          />
        </div>
      ) : (
        <>
          <div className="">
            <div className="h-[calc(100vh-188px)] ">
              <CostAnalysis records={records} />
              <PaginationComponent
                count={records?.total || 0}
                pageSize={records?.per_page || 0}
              />
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default CostAnalysisClient;
