import { Separator } from "@/components/ui/separator";
import type { CustomCellRendererProps } from "ag-grid-react";
import { useTranslations } from "next-intl";
import { type FunctionComponent } from "react";

export const ContainerRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("vehicle-cost-analysis-datatable.body");
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        {/* <div className="min-w-[90px]">{t("lot-number")}:</div> */}
        <div className="min-w-[90px]">{t("lot-number")}:</div>
        {data?.lot_number}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[90px]">{t("title-number")}:</div>
        {data?.title_number}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[90px]">{t("cn")}:</div>
        {data?.containers ? data?.containers.container_number : ""}
      </div>
    </div>
  );
};
