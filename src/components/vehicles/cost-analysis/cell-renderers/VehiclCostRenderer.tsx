import { Separator } from "@/components/ui/separator";
import type { CustomCellRendererProps } from "ag-grid-react";
import { useTranslations } from "next-intl";
import { type FunctionComponent } from "react";

export const VehiclCostRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("vehicle-cost-analysis-datatable.body");
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 0,
  });
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[97px]">{t("vehicle-cost")}:</div>
        {formatter.format(data?.price)}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[97px]">{t("towing")}:</div>
        {data?.vehicle_costs?.towing_cost
          ? formatter.format(data?.vehicle_costs?.towing_cost)
          : ""}
      </div>

      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[97px]">{t("dismental-cost")}:</div>
        {data?.vehicle_costs
          ? formatter.format(data?.vehicle_costs?.dismantal_cost)
          : ""}
      </div>
    </div>
  );
};
