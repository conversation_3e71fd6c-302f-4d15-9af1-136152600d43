import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import {
  Hash,
  CarFront,
  CircleCheck,
  CircleX,
  MapPin,
  MessageSquareText,
} from "lucide-react";
import Image from "next/image";
import { CustomTooltip } from "@/components/Common_UI/custom-tooltip";
import { useResponsive } from "@/hooks/use-mobile";
import { getImageSizeUrl } from "@/utils/imageURL";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";
import { culcHelper } from "@/lib/helper";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { PODModal } from "../../dialog-modal/pod-modal";
import { CommentModal } from "../../dialog-modal/comment-modal";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  <PERSON>er<PERSON>itle,
} from "@/components/ui/drawer";
import { useTranslations } from "next-intl";
import { useDirection } from "@/hooks/useDirection";

export default function VehicleRenderer({ data, node, api }: any) {
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const { isMobile } = useResponsive();
  const total = culcHelper(data, "total_cost");
  const { isRTL } = useDirection();
  const t = useTranslations("vehicle-cost-analysis-datatable.drawer");
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 0,
  });

  const handleClick = () => {
    if (isMobile) {
      setIsSheetOpen(true);
    } else {
      if (api && node) {
        api.setRowNodeExpanded(node, !node.expanded, true);
      }
    }
  };

  // Destination state
  const [selectedDestinationName, setSelectedDestinationName] = useState<
    string | null
  >(data?.destinations?.name ?? null);
  const [destinationOpen, setDestinationOpen] = useState<boolean>(false);

  // Comment state
  const [newComment, setNewComment] = useState<string | null>(
    data?.customer_comment ?? null
  );
  const [commentOpen, setCommentOpen] = useState<boolean>(false);

  const detailRows = [
    {
      label: t("year"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.year}
        </span>
      ),
    },
    {
      label: t("make"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.make}
        </span>
      ),
    },
    {
      label: t("model"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.model}
        </span>
      ),
    },
    {
      label: t("color"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.color}
        </span>
      ),
    },
    {
      label: t("lot-number"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.lot_number}
        </span>
      ),
    },
    {
      label: t("title-number"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.title_number}
        </span>
      ),
    },
    {
      label: t("container-number"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.containers?.container_number}
        </span>
      ),
    },
    {
      label: t("is-printed"),
      value: (
        <span className="text-xs flex items-center justify-end gap-1">
          {data?.is_printed ? (
            <CircleCheck className="w-4 h-4 text-primary" />
          ) : (
            <CircleX className="w-4 h-4 text-red-600" />
          )}
        </span>
      ),
    },
    {
      label: t("vehicle-cost"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {formatter.format(Number(data?.price || 0))}
        </span>
      ),
    },
    {
      label: t("towing-cost"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {formatter.format(Number(data?.vehicle_costs?.towing_cost || 0))}
        </span>
      ),
    },
    {
      label: t("dismantale-cost"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {formatter.format(Number(data?.vehicle_costs?.dismantal_cost || 0))}
        </span>
      ),
    },
    {
      label: t("storage-cost"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {formatter.format(
            Number(data?.vehicle_costs?.pgl_storage_costs || 0)
          )}
        </span>
      ),
    },
    {
      label: t("ship-cost"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {formatter.format(Number(data?.vehicle_costs?.ship_cost || 0))}
        </span>
      ),
    },
    {
      label: t("custom-cost"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {formatter.format(
            Number(data?.vehicle_costs?.dubai_custom_cost || 0)
          )}
        </span>
      ),
    },
    {
      label: t("other-cost"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {formatter.format(Number(data?.vehicle_costs?.other_cost || 0))}
        </span>
      ),
    },
    {
      label: t("total"),
      value: (
        <div className="flex justify-end">
          <Badge
            className={`bg-blue-600/10 text-blue-400 border-none text-[10px]`}
            variant="outline"
          >
            {formatter.format(Number(total || 0))}
          </Badge>
        </div>
      ),
    },
    {
      label: t("point-of-destination"),
      value: (
        <Popover open={destinationOpen} onOpenChange={setDestinationOpen}>
          <div className="flex justify-end">
            <PopoverTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                className="text-blue-500 bg-blue-500/10 h-5 flex items-center gap-2 text-xs"
              >
                {selectedDestinationName || <MapPin />}
              </Button>
            </PopoverTrigger>
          </div>

          <PopoverContent className="w-70 min-h-32 flex text-xs flex-col gap-y-2 mr-5">
            <h1 className="font-bold py-2">Add Destination</h1>
            <PODModal
              vehicleId={data?.id}
              defaultValue={selectedDestinationName}
              onSuccess={(newDestination) => {
                setSelectedDestinationName(newDestination);
                setDestinationOpen(false);
              }}
              close={() => setDestinationOpen(false)}
            />
          </PopoverContent>
        </Popover>
      ),
    },

    {
      label: t("comment"),
      value: (
        <Popover open={commentOpen} onOpenChange={setCommentOpen}>
          <div className="flex justify-end">
            <PopoverTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                className="text-blue-500 bg-blue-500/10 h-5 flex items-center gap-2 text-xs"
              >
                <MessageSquareText />
              </Button>
            </PopoverTrigger>
          </div>

          <PopoverContent className="w-70  min-h-32 flex text-xs flex-col gap-y-2 mr-5">
            <h1 className="font-bold py-2">Add Your Comment</h1>
            <CommentModal
              vehicleId={data?.id}
              defaultValue={newComment || ""}
              onSuccess={(updatedComment) => {
                setNewComment(updatedComment);
                setCommentOpen(false);
              }}
              close={() => setCommentOpen(false)}
            />
          </PopoverContent>
        </Popover>
      ),
    },
  ];

  return (
    <>
      <div
        className="flex items-center h-full gap-2 hover:cursor-pointer md:hover:cursor-default lg:hover:cursor-default group"
        onClick={handleClick}
      >
        <Image
          src={`${
            data?.cover_photo
              ? getImageSizeUrl({ url: data?.cover_photo, size: 250 })
              : "/placeholder1.jpg"
          }`}
          alt={"sample"}
          height={60}
          width={60}
          className={`rounded-sm`}
        />
        <div className="flex flex-col justify-center h-full select-text leading-5 flex-1">
          <div className="flex items-center gap-1.5">
            <div className="w-full overflow-hidden text-ellipsis leading-5 text-xs whitespace-nowrap px-2">
              {data?.vin}
            </div>
          </div>
          <div className="overflow-hidden max-w-48 text-ellipsis leading-5 text-xs whitespace-nowrap ">
            <CustomTooltip tooltip={`${data?.year} ${data?.make}`}>
              <p className="text-ellipsis whitespace-nowrap font-normal text-xs px-2 text-primary/70">
                {data?.year} {data?.make}
              </p>
            </CustomTooltip>
            <CustomTooltip tooltip={`${data?.model} ${data?.color}`}>
              <p className="overflow-x-hidden text-ellipsis whitespace-nowrap font-normal text-xs px-2 text-primary/70">
                {data?.model} {data?.color}
              </p>
            </CustomTooltip>
          </div>
        </div>
      </div>
      <Drawer open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <DrawerContent
          className="h-[80vh] rounded-t-3xl border-t-2 border-primary px-4 mb-1"
          dir={isRTL ? "rtl" : "ltr"}
        >
          <DrawerHeader className="sr-only">
            <DrawerTitle>Vehicle Details</DrawerTitle>
            <DrawerDescription>
              Detailed information about {data?.vin}
            </DrawerDescription>
          </DrawerHeader>
          <div className="flex flex-col h-full">
            <div className="px-1 py-2">
              <div className="flex items-center gap-2">
                <span className="font-semibold text-xs">
                  {data?.year} {data?.make} {data?.model} {data?.color}
                </span>
              </div>
              <div className="flex items-center gap-2 justify-between py-2">
                <span className="flex items-center text-xs text-primary">
                  <Hash className="w-4 h-4" />
                  {data?.lot_number}
                </span>
                <div className="flex items-center gap-2">
                  <CarFront className="w-4 h-4 text-primary" />
                  <span className="text-xs">{data?.vin}</span>
                </div>
              </div>
            </div>
            <div className="flex-1 overflow-y-auto">
              <div className="rounded-lg overflow-hidden border">
                <Table>
                  <TableBody>
                    {detailRows.map((row, index) => (
                      <TableRow
                        key={row.label}
                        className={`${
                          index % 2 === 0 ? "bg-primary/5" : "bg-primary/10"
                        } `}
                      >
                        <TableCell className="py-1 px-2 text-xs">
                          {row.label}
                        </TableCell>
                        <TableCell className="py-1 px-2 text-right text-xs">
                          {row.value}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
}
