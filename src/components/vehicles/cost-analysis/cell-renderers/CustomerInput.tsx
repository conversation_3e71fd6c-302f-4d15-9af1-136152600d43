import { Separator } from "@/components/ui/separator";
import type { CustomCellRendererProps } from "ag-grid-react";
import { Check, MapPin, MessageSquareText, X } from "lucide-react";
import { useTranslations } from "next-intl";
import {  useState, type FunctionComponent } from "react";
import { PODModal } from "../../dialog-modal/pod-modal";
import { CommentModal } from "../../dialog-modal/comment-modal";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";

export const CustomerInput: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("vehicle-cost-analysis-datatable.body");

  const [newComment, setNewComment] = useState<string | null>(
    data?.customer_comment ?? null
  );
  const [openDestination, setOpenDestination] = useState<boolean>(false);
  const [openComment, setOpenComment] = useState<boolean>(false);
  const [selectedDestinationName, setSelectedDestinationName] = useState<
    string | null
  >(data?.destinations?.name ?? null);

  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[90px]">{t("is-printed")}:</div>
        {data?.is_printed ? (
          <Check className="text-blue-500 w-4 h-4" />
        ) : (
          <X className="text-red-500 w-4 h-4" />
        )}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[85px]">{t("comment")}:</div>
        <Popover open={openComment} onOpenChange={setOpenComment}>
          <PopoverTrigger asChild>
            <Button
              size="icon"
              variant="ghost"
              className="text-blue-500 border-none -mt-2"
            >
              <MessageSquareText />
            </Button>
          </PopoverTrigger>

          <PopoverContent className="w-80 min-h-32 flex flex-col gap-y-4">
            <h1 className="font-bold py-2">Vehicle Comment</h1>
            <CommentModal
              vehicleId={data?.id}
              defaultValue={newComment || ""}
              onSuccess={(updatedComment) => {
                setNewComment(updatedComment);
                setOpenComment(false);
              }}
              close={() => setOpenComment(false)}
            />
          </PopoverContent>
        </Popover>
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[90px]">{t("pod")}:</div>
        <Popover open={openDestination} onOpenChange={setOpenDestination}>
          <PopoverTrigger asChild>
            <Button
              size="icon"
              variant="ghost"
              className="text-blue-500 -mt-2 text-left justify-start w-fulls"
            >
              {selectedDestinationName || <MapPin />}
            </Button>
          </PopoverTrigger>

          <PopoverContent className="w-80 min-h-32 flex flex-col gap-y-4">
            <h1 className="font-bold py-2">Add Point Of Destination</h1>
            <PODModal
              vehicleId={data?.id}
              defaultValue={selectedDestinationName}
              onSuccess={(newDestination) => {
                setSelectedDestinationName(newDestination);
                setOpenDestination(false);
              }}
              close={() => setOpenDestination(false)}
            />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};
