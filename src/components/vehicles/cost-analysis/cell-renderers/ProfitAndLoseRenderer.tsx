import { culc<PERSON>elper } from "@/lib/helper";
import type { CustomCellRendererProps } from "ag-grid-react";
import { type FunctionComponent } from "react";
export const ProfitAndLoseRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data }) => {
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 0,
  });
  const total = culcHelper(data, "total_cost");
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="px-2 bg-blue-600/10 text-blue-400 rounded-sm">
          {formatter.format(Number(total))}
        </div>
      </div>
    </div>
  );
};
