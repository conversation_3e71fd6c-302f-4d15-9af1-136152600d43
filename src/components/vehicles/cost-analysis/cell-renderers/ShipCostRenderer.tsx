"use client";
import { Separator } from "@/components/ui/separator";
import {  FunctionComponent } from "react";
import { useTranslations } from "next-intl";
import { CustomCellRendererProps } from "ag-grid-react";
export const ShipCostRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 0,
  });
  const t = useTranslations("vehicle-cost-analysis-datatable.body");
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[70px]">{t("ship-cost")}:</div>
        {data?.vehicle_costs
          ? formatter.format(data?.vehicle_costs?.ship_cost)
          : ""}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[70px]">{t("strg-pol")}:</div>
        {data?.vehicle_costs
          ? formatter.format(data?.vehicle_costs?.pgl_storage_costs)
          : ""}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[70px]">{t("title-cost")}:</div>
        {data?.vehicle_costs
          ? formatter.format(data?.vehicle_costs?.title_charge)
          : ""}
      </div>
    </div>
  );
};
