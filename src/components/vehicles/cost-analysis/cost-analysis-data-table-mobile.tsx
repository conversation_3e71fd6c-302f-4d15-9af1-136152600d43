"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";

import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MultiFilterModule,
  ServerSideRowModelModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import { useMemo, useRef, useState } from "react";
import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import { Props } from "../services/config";
import useSidebarConfig from "../sidebarConfig";
import { useServerSideDatasource } from "@/hooks/use-infinite-scroll";
import VehicleRenderer from "./cell-renderers/VehicleRenderer";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ServerSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);

interface CostAnalysisDataTableMobileProps extends Props {
  onLoadMoreData: (params: {
    page: number;
    per_page: number;
    search?: string;
    filterData?: string;
    state?: string;
  }) => Promise<any>;
  initialParams?: {
    search?: string;
    filterData?: string;
    state?: string;
  };
}

export const CostAnalysisMobile = ({
  gridRefProps,
  exportColDefs,
  onLoadMoreData,
  initialParams,
}: CostAnalysisDataTableMobileProps) => {
  const gridRef = useRef<AgGridReact>(null);
  const t = useTranslations("vehicle-cost-analysis-datatable");
  const sidebarConfig = useSidebarConfig();
  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        field: "vehicle",
        headerName: t("header.vehicle"),
        cellDataType: "text",
        cellRenderer: VehicleRenderer,
      },
    ],
    [t]
  );

  const { onGridReady } = useServerSideDatasource({
    onLoadMoreData,
    initialParams,
    pageSize: 20,
  });

  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
    }),
    []
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  const [quickFilterText] = useState<string>();

  const { isRTL } = useDirection();

  return (
    <>
      <AgGridDataTable
        enableRtl={isRTL}
        ref={gridRefProps || gridRef}
        columnDefs={exportColDefs || colDefs}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        rowModelType="serverSide"
        onGridReady={onGridReady}
        rowBuffer={0}
        cacheBlockSize={20}
        maxBlocksInCache={5}
        pagination={false}
        masterDetail={false}
        quickFilterText={quickFilterText}
        rowHeight={120}
        colResizeDefault="shift"
        headerHeight={50}
        sideBar={sidebarConfig}
        rowClassRules={{
          "row-even": (params) =>
            params.node.rowIndex ? params.node.rowIndex % 2 !== 0 : true,
          "row-odd": (params) =>
            params.node.rowIndex ? params.node.rowIndex % 2 === 0 : true,
        }}
        suppressHorizontalScroll={true}
        suppressColumnVirtualisation={true}
      />
    </>
  );
};
