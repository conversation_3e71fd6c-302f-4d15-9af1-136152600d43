"use client";
import React, { useMemo } from "react";
import { culc<PERSON>elper } from "@/lib/helper";
import { ExportModal } from "@/components/Common_UI/export-modal";
import { toast } from "sonner";
import { useTranslations } from "next-intl";
import { ColDef } from "ag-grid-enterprise";
import { getCostAnalysis } from "../services/vehicle-service";

type Props = {
  records: {
    page: number;
    per_page: number;
    total: number;
    data: any[];
  };
};
export default function ExportCostAnalysis({ records }: Props) {
  const t = useTranslations("export-modal");

  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        field: "is_printed",
        headerName: "Is Printed",
      },

      {
        headerName: "Vehicle Description",
        valueGetter: (params) => {
          return `${params?.data?.year} ${params?.data?.make} ${params?.data?.model} ${params?.data?.color} `;
        },
      },

      { field: "vin", headerName: "VIN" },

      { field: "lot_number", headerName: "Lot Number" },

      { field: "title_number", headerName: "Title Number" },

      {
        headerName: "Container Number",
        valueGetter: (params) => params?.data?.containers?.container_number,
      },

      {
        field: "price",
        headerName: "Vehicle Cost",
      },

      {
        headerName: "Towing",
        valueGetter: (params) => params?.data?.vehicle_towings?.tow_amount,
      },

      {
        headerName: "Dismental Cost",
        valueGetter: (params) => params?.data?.vehicle_costs?.dismantal_cost,
      },

      {
        headerName: "Ship Cost",
        valueGetter: (params) => {
          return params?.data?.vehicle_costs?.ship_cost;
        },
      },

      {
        headerName: "Storage POL",
        valueGetter: (params) => {
          return params?.data?.vehicle_costs?.pgl_storage_costs;
        },
      },
      {
        headerName: "Title Cost",
        valueGetter: (params) => {
          return params?.data?.vehicle_costs?.title_charge;
        },
      },

      {
        headerName: "Custom",
        valueGetter: (params) => {
          return params?.data?.vehicle_costs?.dubai_custom_cost;
        },
      },

      {
        headerName: "Other Cost",
        valueGetter: (params) => {
          return params?.data?.vehicle_costs?.other_cost;
        },
      },

      {
        headerName: "Description",
        valueGetter: (params) => {
          return params?.data?.vehicle_costs?.add_information;
        },
      },

      {
        headerName: "Total",
        valueGetter: (params) => {
          return culcHelper(params?.data, "total_cost");
        },
      },

      {
        headerName: "Profit/Lose",
        valueGetter: (params) => {
          return culcHelper(params?.data, "profit");
        },
      },

      {
        headerName: "Percent Profit",
        valueGetter: (params) => {
          return culcHelper(params?.data, "percent_profit");
        },
      },

      {
        headerName: "Destinations",
        valueGetter: (params) => params?.data?.destinations?.name,
      },

      {
        field: "customer_comment",
        headerName: "Comment",
      },
    ],
    []
  );
  const fetchAllData = async (): Promise<any[]> => {
    const response = await getCostAnalysis({
      params: {
        state: "",
        page: 1,
        per_page: records.total,
        search: "",
        exactMatch: false,
        filterData: "",
      },
    });
    return response.data;
  };

  return (
    <ExportModal
      columnDefs={colDefs}
      currentData={records.data}
      exportFileName="Vehicle Cost Analysis"
      fetchAllData={fetchAllData}
      totalItems={records.total}
      translations={{
        title: t("title"),
        exportData: t("sub-title"),
        subTitle: t("sub-title"),
        currentData: t("current-data"),
        allData: t("all-data"),
        cancel: t("cancel"),
        export: t("export"),
      }}
      onExportSuccess={() => toast("Vehicle Cost Analysis Export Completed")}
    />
  );
}
