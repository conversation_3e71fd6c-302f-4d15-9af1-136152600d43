"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";

import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MultiFilterModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import { useMemo, useRef, useState } from "react";

import { VehiclCostRenderer } from "./cell-renderers/VehiclCostRenderer";
import { ContainerRenderer } from "./cell-renderers/ContainerRenderer";
import { ShipCostRenderer } from "./cell-renderers/ShipCostRenderer";
import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import { Props } from "../services/config";
import { ProfitAndLoseRenderer } from "./cell-renderers/ProfitAndLoseRenderer";
import { CustomerInput } from "./cell-renderers/CustomerInput";
import { ProfLoseRenderer } from "./cell-renderers/ProfLoseRenderer";
import { DescriptionRenderer } from "./cell-renderers/DescriptionRenderer";
import useSidebarConfig from "../sidebarConfig";
import VehicleRenderer from "./cell-renderers/VehicleRenderer";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);

const paginationPageSizeSelector = [5, 10, 20];
export const CostAnalysis = ({
  records,
  gridRefProps,
  exportColDefs,
}: Props) => {
  const gridRef = useRef<AgGridReact>(null);
  const t = useTranslations("vehicle-cost-analysis-datatable");
  const sidebarConfig = useSidebarConfig();
  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: "#",
        cellDataType: "text",
        minWidth: 50,
        cellStyle: {
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        },
        valueGetter: (params) => {
          if (params.node) {
            return params.node.rowIndex ? params.node.rowIndex + 1 : 1;
          }
        },
      },
      {
        field: "vehicle",
        headerName: t("header.vehicle"),
        cellDataType: "text",
        cellRenderer: VehicleRenderer,
        minWidth: 325,
      },
      {
        field: "container_number",
        headerName: t("header.container"),
        cellRenderer: ContainerRenderer,
        minWidth: 200,
      },
      {
        field: "vehicle_cost",
        headerName: t("header.cost"),
        minWidth: 185,
        cellRenderer: VehiclCostRenderer,
      },

      {
        field: "ship_cost",
        headerName: t("header.cost"),
        minWidth: 130,
        cellRenderer: ShipCostRenderer,
      },
      {
        field: "prof-lose",
        headerName: t("header.cost"),
        cellRenderer: ProfLoseRenderer,
        minWidth: 150,
      },

      {
        field: "total",
        headerName: t("header.total"),
        cellRenderer: ProfitAndLoseRenderer,
        minWidth: 100,
      },
      {
        field: "comment",
        headerName: t("header.comment"),
        cellRenderer: CustomerInput,
        minWidth: 200,
      },
      {
        field: "description",
        headerName: t("header.description"),
        minWidth: 190,
        cellRenderer: DescriptionRenderer,
      },
    ],
    [t]
  );
  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
    }),
    []
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  const [quickFilterText] = useState<string>();

  const selectionColumnDef = useMemo(() => {
    return {
      minWidth: 44,
    };
  }, []);
  const { isRTL } = useDirection();

  return (
    <>
      <AgGridDataTable
        enableRtl={isRTL ? true : false}
        ref={gridRefProps || gridRef}
        selectionColumnDef={selectionColumnDef}
        columnDefs={exportColDefs || colDefs}
        rowData={records?.data || []}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        pagination={false}
        paginationPageSize={records?.per_page ?? 20}
        paginationPageSizeSelector={paginationPageSizeSelector}
        quickFilterText={quickFilterText}
        rowHeight={100}
        colResizeDefault="shift"
        headerHeight={60}
        sideBar={sidebarConfig}
        rowClassRules={{
          "row-even": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 === 0
                ? false
                : true
              : true;
          },
          "row-odd": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 !== 0
                ? false
                : true
              : true;
          },
        }}
      />
    </>
  );
};
