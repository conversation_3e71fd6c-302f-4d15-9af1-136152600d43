"use client";

import { Check, LucideIcon } from "lucide-react";
import * as LucideIcons from "lucide-react";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface Step {
  id: string;
  name: string;
  icon: string;
  subSteps?: Array<{ id: string; name: string }>;
}

interface VehicleStepperProps {
  steps: Step[];
  currentStep: number;
  completedSteps: number[];
  onStepClick: (stepIndex: number) => void;
  isRTL?: boolean;
  formValues?: Record<string, any>;
}

export function VehicleStepper({
  steps,
  currentStep,
  completedSteps,
  onStepClick,
  isRTL,
  formValues = {},
}: VehicleStepperProps) {
  const [hoveredStep, setHoveredStep] = useState<number | null>(null);

  // Helper function to check if a field is complete
  const isFieldComplete = (value: any) => {
    if (value === undefined || value === null) return false;
    if (typeof value === "string") return value.trim() !== "";
    return true;
  };

  // Fixed height constants
  const stepHeight = 30; // Height of the main step (in pixels)
  const subStepHeight = 34; // Height of each sub-step (in pixels)
  const stepSpacing = 32; // Space between steps (in pixels)

  // Calculate the maximum number of sub-steps across all steps
  const maxSubItems = Math.max(
    ...steps.map((step) => step.subSteps?.length || 0)
  );

  // Calculate the fixed height for the sub-steps container
  const subStepsContainerHeight = maxSubItems * subStepHeight;

  return (
    <div
      className={`p-2 ${
        isRTL ? "rounded-r-lg border-r" : "rounded-l-lg border-r"
      } w-56 border-border/50 overflow-y-auto`}
      style={{
        minHeight: `calc(5rem + ${
          steps.length * stepHeight
        }px + ${subStepsContainerHeight}px + ${
          (steps.length - 1) * stepSpacing
        }px)`,
      }}
    >
      <ol className="relative">
        {steps.map((step, index) => {
          const Icon = LucideIcons[
            step.icon as keyof typeof LucideIcons
          ] as LucideIcon;
          const isLastStep = index === steps.length - 1;
          const isCompleted = completedSteps.includes(index);
          const isActive = index === currentStep;
          const isHovered = hoveredStep === index;
          const nextAvailableStep = Math.min(
            Math.max(...completedSteps, -1) + 1,
            steps.length - 1
          );
          const isClickable =
            isCompleted || index === nextAvailableStep || index === currentStep;

          // Check if this step has visible sub-steps
          const hasVisibleSubSteps =
            isActive && step.subSteps && step.subSteps.length > 0;

          // Calculate the height for this step
          const thisStepHeight = hasVisibleSubSteps
            ? stepHeight + subStepsContainerHeight
            : stepHeight;

          return (
            <li
              key={step.id}
              className={cn(
                "relative",
                isLastStep ? "" : `mb-${stepSpacing}px`
              )}
              style={{
                minHeight: thisStepHeight,
                marginBottom: isLastStep ? 0 : stepSpacing,
              }}
            >
              <div
                className={cn(
                  "flex items-start",
                  isRTL ? "flex-row-reverse" : ""
                )}
                onClick={() => {
                  if (isClickable) {
                    onStepClick(index);
                  }
                }}
                onMouseEnter={() => setHoveredStep(index)}
                onMouseLeave={() => setHoveredStep(null)}
              >
                <div className="relative">
                  {/* Hover effect for steps */}
                  {isClickable && (
                    <div
                      className={cn(
                        "absolute inset-0 rounded-full scale-150 opacity-0 transition-opacity duration-200",
                        isHovered ? "opacity-10 bg-primary" : ""
                      )}
                    />
                  )}
                  <div
                    className={cn(
                      "relative w-8 h-8 flex items-center justify-center rounded-full transition-all duration-300 border-[2px]",
                      isCompleted
                        ? "bg-green-500 border-green-500 text-white"
                        : isActive
                        ? "bg-transparent border-green-500 text-white"
                        : "bg-gray-300 border-gray-300 text-gray-500",
                      isHovered && isClickable && !isCompleted
                        ? "border-primary text-primary shadow-sm"
                        : "",
                      isHovered && isCompleted
                        ? "bg-green-600 border-green-600 shadow-md scale-110"
                        : ""
                    )}
                  >
                    {isCompleted ? (
                      <Check
                        className={cn(
                          "w-[16px] h-[16px] transition-transform",
                          isHovered ? "scale-110" : ""
                        )}
                      />
                    ) : (
                      <Icon
                        className={cn(
                          "w-[16px] h-[16px] transition-all duration-300",
                          isActive ? "text-green-500" : "",
                          isHovered && isClickable ? "scale-110" : ""
                        )}
                      />
                    )}
                  </div>

                  {/* Connector line */}
                  {!isLastStep && (
                    <div
                      className={cn(
                        "absolute",
                        isRTL ? "right-[1rem]" : "left-[1rem]",
                        "top-8 w-[2px]",
                        isRTL ? "translate-x-1/2" : "-translate-x-1/2",
                        "transition-all duration-300",
                        index < currentStep || isCompleted
                          ? "bg-green-500"
                          : "bg-gray-300",
                        isHovered && index < currentStep ? "bg-green-600" : ""
                      )}
                      style={{
                        height: `${
                          stepSpacing +
                          (hasVisibleSubSteps ? subStepsContainerHeight : 0)
                        }px`,
                      }}
                    />
                  )}
                </div>
                <span
                  className={cn(
                    "text-sm font-medium transition-all duration-300 cursor-pointer mt-1",
                    isRTL ? "ml-auto mr-2.5" : "ml-2.5",
                    isCompleted
                      ? "text-green-500"
                      : isActive
                      ? "text-green-500"
                      : "text-gray-500",
                    isHovered && isClickable ? "font-semibold" : "",
                    isHovered && isCompleted ? "text-green-600" : "",
                    isHovered && isClickable && !isCompleted && !isActive
                      ? "text-primary"
                      : "",
                    isHovered && isClickable
                      ? isRTL
                        ? "-translate-x-0.5"
                        : "translate-x-0.5"
                      : ""
                  )}
                >
                  {step.name}
                </span>
              </div>

              {/* Sub-steps with fixed container height */}
              {hasVisibleSubSteps && (
                <div style={{ height: `${subStepsContainerHeight}px` }}>
                  <ol
                    className={cn("mt-2 space-y-2", isRTL ? "mr-10" : "ml-10")}
                  >
                    {step?.subSteps?.map((subStep) => {
                      const isSubComplete = isFieldComplete(
                        formValues[subStep.id]
                      );
                      return (
                        <li
                          key={subStep.id}
                          className={cn(
                            "flex items-center h-6",
                            isRTL ? "flex-row-reverse" : ""
                          )}
                        >
                          <div
                            className={cn(
                              "w-4 h-4 flex items-center justify-center",
                              isRTL ? "ml-2" : "mr-2"
                            )}
                          >
                            {isSubComplete ? (
                              <Check className="w-3 h-3 text-green-500" />
                            ) : (
                              <div className="w-2 h-2 rounded-full bg-gray-300" />
                            )}
                          </div>
                          <span className="text-sm text-gray-600">
                            {subStep.name}
                          </span>
                        </li>
                      );
                    })}
                  </ol>
                </div>
              )}
            </li>
          );
        })}
      </ol>
    </div>
  );
}
