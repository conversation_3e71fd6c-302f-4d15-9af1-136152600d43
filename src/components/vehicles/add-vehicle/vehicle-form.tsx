"use client";
import { useLocationContext } from "@/context/get-POLoading-context";
import { useDirection } from "@/hooks/useDirection";
import { getAutoComplete } from "@/utils/autoComplete/autComplete-client";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import type React from "react";
import { useState, useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useTranslations } from "next-intl";
import { ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { VehicleStepper } from "./vehicle-stepper";
import { StepHeader } from "./step-header";
import { useAddVehicle } from "../services/use-add-vehicle";
import { useEditVehicle } from "../services/use-edit-vehicle";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { CustomCombobox } from "@/components/Common_UI/custom-combobox";
import { Button } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/Common_UI/loading";
import { useFetchClient } from "@/utils/axios";

type Option = {
  name: string;
  id: string | number;
};

// Zod schema for form validation
export const FormFieldSchema = z.object({
  vin: z
    .string({
      required_error: "Vin is required",
      invalid_type_error: "Vin must not be empty",
    })
    .nonempty()
    .transform((val) => val.trim())
    .refine((val) => val.length > 0, {
      message: "Vin must not be empty",
    }),
  auction_name: z.string().nullable().optional(),
  year: z.string().nullable().optional(),
  make: z.string().nullable().optional(),
  model: z.string().nullable().optional(),
  lot_number: z.string().nullable().optional(),
  color: z.string().nullable().optional(),
  point_of_loading: z.preprocess(
    (val) => (val === undefined || val === null ? undefined : Number(val)),
    z.number({
      required_error: "POL (Port of Loading) is required",
      invalid_type_error: "POL (Port of Loading) must be one of the options",
    })
  ),
  is_title_exist: z.preprocess(
    (val) => (val === "true" ? true : val === "false" ? false : val),
    z.boolean().optional().nullable()
  ),
  title_number: z.string().nullable().optional(),
  title_state: z.string().nullable().optional(),
  receiver_name: z.string().nullable().optional(),
  destination_id: z.preprocess(
    (val) => (val === undefined || val === null ? undefined : Number(val)),
    z.number({
      required_error: "POD (Port of Destination) is required",
      invalid_type_error:
        "POD (Port of Destination) must be one of the options",
    })
  ),
  is_key_present: z.preprocess(
    (val) => (val === "true" ? true : val === "false" ? false : val),
    z.boolean().optional().nullable()
  ),
  weight: z.preprocess(
    (val) => (val === undefined || val === null ? undefined : Number(val)),
    z
      .number({
        required_error: "Weight is required",
        invalid_type_error: "Weight must be a number",
      })
      .refine((val) => val > 0, {
        message: "Weight must be greater than zero",
      })
      .transform((val) => String(val))
      .optional()
  ),
  price: z.preprocess(
    (val) => (val === undefined || val === null ? undefined : Number(val)),
    z
      .number({
        required_error: "Price is required",
        invalid_type_error: "Price must be a number",
      })
      .refine((val) => val > 0, {
        message: "Price must be greater than zero",
      })
      .optional()
  ),
});

export type FormFields = z.infer<typeof FormFieldSchema>;

// Define steps with sub-steps for the stepper

export default function VehicleForm({
  open,
  setOpen,
  formData,
  isEdit,
}: {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  formData?: FormFields & {
    id?: number;
    carstate?: string;
    pol_locations?: any;
  };
  isEdit?: boolean;
}) {
  const t = useTranslations("add-vehicle-form");
  const router = useRouter();
  const { addVehicle, isPending } = useAddVehicle();
  const { editVehicle, isPending: isPendingEdit } = useEditVehicle();
  const { isRTL } = useDirection();
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [localErrors, setLocalErrors] = useState<{ [key: string]: string }>({});
  const fetchClient = useFetchClient();

  const steps = [
    {
      id: "vehicle-info",
      name: t("stepper.vehicle-information"),
      icon: "Car",
      subSteps: [
        { id: "vin", name: t("stepper.sub-items.vin") },
        { id: "auction_name", name: t("stepper.sub-items.auction_name") },
        { id: "year", name: t("stepper.sub-items.year") },
        { id: "make", name: t("stepper.sub-items.make") },
        { id: "model", name: t("stepper.sub-items.model") },
        { id: "color", name: t("stepper.sub-items.color") },
        { id: "price", name: t("stepper.sub-items.price") },
        { id: "lot_number", name: t("stepper.sub-items.lot_number") },
      ],
    },
    {
      id: "title-info",
      name: t("stepper.title-key-information"),
      icon: "Key",
      subSteps: [
        { id: "is_title_exist", name: t("stepper.sub-items.title-exist") },
        { id: "title_number", name: t("stepper.sub-items.title-number") },
        { id: "title_state", name: t("stepper.sub-items.title-state") },
        { id: "is_key_present", name: t("stepper.sub-items.key-present") },
      ],
    },
    {
      id: "shipping-info",
      name: t("stepper.shipping-information"),
      icon: "Truck",
      subSteps: [
        {
          id: "point_of_loading",
          name: t("stepper.sub-items.point_of_loading"),
        },
        {
          id: "destination_id",
          name: t("stepper.sub-items.point_of_destination"),
        },
        { id: "weight", name: t("stepper.sub-items.weight") },

        { id: "receiver_name", name: t("stepper.sub-items.receiver_name") },
      ],
    },
    {
      id: "review",
      name: t("stepper.review-and-submit"),
      icon: "FileText",
      subSteps: [],
    },
  ];
  const form = useForm<FormFields>({
    resolver: zodResolver(FormFieldSchema),
    defaultValues: {
      vin: "",
      auction_name: "",
      year: "",
      make: "",
      model: "",
      lot_number: "",
      color: "",
      title_number: null,
      title_state: null,
      receiver_name: "",
      is_title_exist: undefined,
      is_key_present: undefined,
      destination_id: undefined,
      point_of_loading: undefined,
      weight: undefined,
      price: undefined,
    },
    mode: "onChange",
  });

  useEffect(() => {
    if (open) {
      setCurrentStep(0);
      setCompletedSteps([]);
      const resetValues: any = formData
        ? {
            ...formData,
            destination_id: formData.destination_id?.toString(),
            point_of_loading: formData.pol_locations?.id?.toString(),
            is_title_exist: formData.is_title_exist?.toString(),
            is_key_present: formData.is_key_present?.toString(),
          }
        : {
            vin: "",
            auction_name: "",
            year: "",
            make: "",
            model: "",
            lot_number: "",
            color: "",
            title_number: null,
            title_state: null,
            receiver_name: "",
            is_title_exist: undefined,
            is_key_present: undefined,
            destination_id: undefined,
            point_of_loading: undefined,
            weight: undefined,
            price: undefined,
          };

      form.reset(resetValues);
    }
  }, [open, formData, form]);

  const onSubmit = async (data: FormFields) => {
    try {
      if (!formData?.id) {
        await addVehicle(data, {
          onSuccess: () => {
            toast.success("Vehicle Added Successfully!");
          },
        });
      } else {
        const updateData = { ...data, id: formData.id };
        await editVehicle(updateData);
        toast.success("Vehicle Updated Successfully!");
      }

      setOpen(false);
      form.reset();
      router.refresh();
    } catch (error: any) {
      toast.error("Operation failed. Please try again.", error);
    }
  };

  const { data } = useQuery({
    queryKey: ["vehicleCreateDest"],
    queryFn: () =>
      getAutoComplete({ column: "name", model: "destinations" }, fetchClient),
  });

  const formattedOptions: Option[] =
    data?.data
      ?.filter((item: any) => item.name)
      .map((item: any) => ({
        name: item.name,
        id: item.id.toString(),
      })) || [];

  const { map }: any = useLocationContext();
  const locations: Option[] = map((item: any) => ({
    name: item.name,
    id: item.id.toString(),
  }));

  const errors = form.formState.errors;
  const isSubmitting = isPending || isPendingEdit;

  const handleStepClick = (stepIndex: number) => {
    if (
      completedSteps.includes(stepIndex) ||
      stepIndex === currentStep ||
      stepIndex ===
        Math.min(Math.max(...completedSteps, -1) + 1, steps.length - 1)
    ) {
      setCurrentStep(stepIndex);
    }
  };

  const handleNext = async () => {
    if (currentStep === 0) {
      const result = await form.trigger([
        "vin",
        "auction_name",
        "year",
        "make",
        "model",
        "color",
        "price",
      ]);
      if (!result) return;
    } else if (currentStep === 1) {
      const result = await form.trigger([
        "is_title_exist",
        "title_number",
        "title_state",
        "is_key_present",
      ]);
      if (!result) return;
    } else if (currentStep === 2) {
      const result = await form.trigger([
        "point_of_loading",
        "destination_id",
        "weight",
        "lot_number",
        "receiver_name",
      ]);
      if (!result) return;
    }

    if (!completedSteps.includes(currentStep)) {
      setCompletedSteps((prev) => [...prev, currentStep]);
    }

    setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
  };

  const handlePrevious = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  };

  const checkVinExists = async () => {
    const vin = form.getValues("vin");
    if (!vin || (isEdit && formData?.vin === vin)) return;

    try {
      const res = await fetchClient(`/v2/vehicles/vin/${vin}`);
      if (res.data?.result === true) {
        setLocalErrors((prev) => ({
          ...prev,
          vin: "This VIN is already registered",
        }));
      } else {
        setLocalErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors.vin;
          return newErrors;
        });
      }
    } catch (error: any) {
      console.error("Error checking VIN:", error);
      if (error.response?.data?.result === true) {
        setLocalErrors((prev) => ({
          ...prev,
          vin: "This VIN is already registered",
        }));
      } else {
        setLocalErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors.vin;
          return newErrors;
        });
        toast.error("Error checking VIN. Please try again.");
      }
    }
  };

  const formErrors = form.formState.errors;
  const combinedErrors = { ...formErrors, ...localErrors };

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!open) form.reset();
        setOpen(open);
      }}
    >
      <DialogContent
        className="w-full h-full max-w-[100vw] sm:max-w-[90vw] md:max-w-[85vw] lg:max-w-[1068px] sm:max-h-[666px] overflow-hidden sm:p-6"
        dir={isRTL ? "rtl" : "ltr"}
      >
        <DialogHeader>
          <DialogTitle className="text-lg sm:text-xl md:text-2xl font-semibold text-center">
            {formData?.id ? t("edit-vehicle") : t("add-vehicle")}
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-col lg:flex-row overflow-hidden lg:gap-4">
          {/* Stepper - hidden on mobile, shown on desktop */}
          {!isRTL && (
            <div className="hidden lg:block lg:w-70">
              <VehicleStepper
                steps={steps}
                currentStep={currentStep}
                completedSteps={completedSteps}
                onStepClick={handleStepClick}
                isRTL={isRTL}
                formValues={form.watch()}
              />
            </div>
          )}

          {/* Mobile Progress Indicator */}
          <div className="lg:hidden mb-2">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium">
                Step {currentStep + 1} of {steps.length}
              </span>
              <span className="text-sm text-muted-foreground">
                {Math.round(((currentStep + 1) / steps.length) * 100)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${((currentStep + 1) / steps.length) * 100}%`,
                }}
              />
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="h-full flex flex-col justify-between"
            >
              <div className="flex-1 overflow-y-auto max-h-[60vh] lg:max-h-[70vh]">
                {currentStep === 0 && (
                  <div className="space-y-2 sm:space-y-6">
                    <StepHeader title={t("stepper.vehicle-information")} />
                    <div
                      className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6"
                      dir={isRTL ? "rtl" : "ltr"}
                    >
                      <div className="space-y-1">
                        <Label
                          htmlFor={"vin"}
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.vin")}{" "}
                          <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          {...form.register("vin")}
                          placeholder={t("labels.vin")}
                          className={cn(
                            "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20 text-sm sm:text-base",
                            combinedErrors.vin ? "border-red-500" : ""
                          )}
                          onBlur={checkVinExists}
                        />
                        {combinedErrors.vin && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {combinedErrors.vin.message || localErrors.vin}
                          </p>
                        )}
                      </div>

                      <div className="space-y-1">
                        <Label
                          htmlFor={"auction_name"}
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.auction_name")}
                        </Label>
                        <Input
                          {...form.register("auction_name")}
                          placeholder={t("labels.auction_name")}
                          className={cn(
                            "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20 text-sm sm:text-base",
                            errors.auction_name ? "border-red-500" : ""
                          )}
                        />
                        {errors.auction_name && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {errors.auction_name.message}
                          </p>
                        )}
                      </div>

                      <div className="space-y-1">
                        <Label
                          htmlFor={"year"}
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.year")}
                        </Label>
                        <Input
                          {...form.register("year")}
                          placeholder={t("labels.year")}
                          className={cn(
                            "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20 text-sm sm:text-base",
                            errors.year ? "border-red-500" : ""
                          )}
                        />
                        {errors.year && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {errors.year.message}
                          </p>
                        )}
                      </div>

                      <div className="space-y-1">
                        <Label
                          htmlFor={"make"}
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.make")}
                        </Label>
                        <Input
                          {...form.register("make")}
                          placeholder={t("labels.make")}
                          className={cn(
                            "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20 text-sm sm:text-base",
                            errors.make ? "border-red-500" : ""
                          )}
                        />
                        {errors.make && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {errors.make.message}
                          </p>
                        )}
                      </div>

                      <div className="space-y-1">
                        <Label
                          htmlFor={"model"}
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.model")}
                        </Label>
                        <Input
                          {...form.register("model")}
                          placeholder={t("labels.model")}
                          className={cn(
                            "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20 text-sm sm:text-base",
                            errors.model ? "border-red-500" : ""
                          )}
                        />
                        {errors.model && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {errors.model.message}
                          </p>
                        )}
                      </div>

                      <div className="space-y-1">
                        <Label
                          htmlFor={"color"}
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.color")}
                        </Label>
                        <Input
                          {...form.register("color")}
                          placeholder={t("labels.color")}
                          className={cn(
                            "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20 text-sm sm:text-base",
                            errors.color ? "border-red-500" : ""
                          )}
                        />
                        {errors.color && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {errors.color.message}
                          </p>
                        )}
                      </div>

                      <div className="space-y-1">
                        <Label
                          htmlFor={"price"}
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.price")}{" "}
                          <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          type="number"
                          {...form.register("price", { valueAsNumber: true })}
                          placeholder={t("labels.price")}
                          className={cn(
                            "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20 text-sm sm:text-base",
                            errors.price ? "border-red-500" : ""
                          )}
                        />
                        {errors.price && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {errors.price.message}
                          </p>
                        )}
                      </div>
                      <div className="space-y-1">
                        <Label
                          htmlFor={"lot_number"}
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.lot_number")}
                        </Label>
                        <Input
                          {...form.register("lot_number")}
                          placeholder={t("labels.lot_number")}
                          className={cn(
                            "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20 text-sm sm:text-base",
                            errors.lot_number ? "border-red-500" : ""
                          )}
                        />
                        {errors.lot_number && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {errors.lot_number.message}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {currentStep === 1 && (
                  <div className="space-y-4 sm:space-y-6">
                    <StepHeader title={t("stepper.title-key-information")} />
                    <div
                      className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6"
                      dir={isRTL ? "rtl" : "ltr"}
                    >
                      {/* Title Exists Field */}
                      <div className="space-y-1">
                        <Label
                          htmlFor="is_title_exist"
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.is_title_exist")}
                        </Label>
                        <Controller
                          name="is_title_exist"
                          control={form.control}
                          render={({ field }) => (
                            <CustomCombobox
                              options={[
                                { name: "Yes", id: "true" },
                                { name: "No", id: "false" },
                              ]}
                              btnClass="w-full justify-between text-sm sm:text-base"
                              contentClass="rounded-lg border shadow-md w-full"
                              label={t("labels.is_title_exist")}
                              placeholder={t("labels.is_title_exist")}
                              value={field.value?.toString()}
                              onChange={(value) =>
                                field.onChange(value ? value === "true" : null)
                              }
                              className={cn(
                                "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20",
                                errors.is_title_exist ? "border-red-500" : ""
                              )}
                            />
                          )}
                        />
                        {errors.is_title_exist && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {errors.is_title_exist.message}
                          </p>
                        )}
                      </div>

                      {/* Key Present Field */}
                      <div className="space-y-1">
                        <Label
                          htmlFor="is_key_present"
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.is_key_present")}
                        </Label>
                        <Controller
                          name="is_key_present"
                          control={form.control}
                          render={({ field }) => (
                            <CustomCombobox
                              options={[
                                { name: "Yes", id: "true" },
                                { name: "No", id: "false" },
                              ]}
                              btnClass="w-full justify-between text-sm sm:text-base"
                              contentClass="rounded-lg border shadow-md w-full"
                              label={t("labels.is_key_present")}
                              placeholder={t("labels.is_key_present")}
                              value={field.value?.toString()}
                              onChange={(value) =>
                                field.onChange(value ? value === "true" : null)
                              }
                              className={cn(
                                "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20",
                                errors.is_key_present ? "border-red-500" : ""
                              )}
                            />
                          )}
                        />
                        {errors.is_key_present && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {errors.is_key_present.message}
                          </p>
                        )}
                      </div>

                      <div className="space-y-1">
                        <Label
                          htmlFor={"title_number"}
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.title_number")}
                        </Label>
                        <Input
                          {...form.register("title_number")}
                          placeholder={t("labels.title_number")}
                          className={cn(
                            "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20 text-sm sm:text-base",
                            errors.title_number ? "border-red-500" : ""
                          )}
                        />
                        {errors.title_number && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {errors.title_number.message}
                          </p>
                        )}
                      </div>

                      <div className="space-y-1">
                        <Label
                          htmlFor={"title_state"}
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.title_state")}
                        </Label>
                        <Input
                          {...form.register("title_state")}
                          placeholder={t("labels.title_state")}
                          className={cn(
                            "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20 text-sm sm:text-base",
                            errors.title_state ? "border-red-500" : ""
                          )}
                        />
                        {errors.title_state && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {errors.title_state.message}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {currentStep === 2 && (
                  <div className="space-y-4 sm:space-y-6">
                    <StepHeader title={t("stepper.shipping-information")} />
                    <div
                      className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6"
                      dir={isRTL ? "rtl" : "ltr"}
                    >
                      <div className="space-y-1">
                        <Label
                          htmlFor={"destination_id"}
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.point_of_destination")}{" "}
                          <span className="text-red-500">*</span>
                        </Label>
                        <Controller
                          name="destination_id"
                          control={form.control}
                          render={({ field }) => (
                            <CustomCombobox
                              options={formattedOptions}
                              btnClass="w-full justify-between text-sm sm:text-base"
                              contentClass="rounded-lg border shadow-md w-full"
                              label={t("labels.point_of_destination")}
                              placeholder={t("labels.point_of_destination")}
                              value={field.value?.toString()}
                              onChange={(value) =>
                                field.onChange(value ? Number(value) : null)
                              }
                              className={cn(
                                "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20",
                                errors.destination_id ? "border-red-500" : ""
                              )}
                            />
                          )}
                        />
                        {errors.destination_id && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {errors.destination_id.message}
                          </p>
                        )}
                      </div>

                      <div className="space-y-1">
                        <Label
                          htmlFor={"point_of_loading"}
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.pol_locations")}{" "}
                          <span className="text-red-500">*</span>
                        </Label>
                        <Controller
                          name="point_of_loading"
                          control={form.control}
                          render={({ field }) => (
                            <CustomCombobox
                              options={locations}
                              btnClass="w-full justify-between text-sm sm:text-base"
                              contentClass="rounded-lg border shadow-md w-full"
                              label={t("labels.pol_locations")}
                              placeholder={t("labels.pol_locations")}
                              value={field.value?.toString()}
                              onChange={(value) =>
                                field.onChange(value ? Number(value) : null)
                              }
                              className={cn(
                                "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20",
                                errors.point_of_loading ? "border-red-500" : ""
                              )}
                            />
                          )}
                        />
                        {errors.point_of_loading && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {errors.point_of_loading.message}
                          </p>
                        )}
                      </div>

                      <div className="space-y-1">
                        <Label
                          htmlFor={"weight"}
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.weight")}{" "}
                          <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          type="number"
                          {...form.register("weight", { valueAsNumber: true })}
                          placeholder={t("labels.weight")}
                          className={cn(
                            "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20 text-sm sm:text-base",
                            errors.weight ? "border-red-500" : ""
                          )}
                        />
                        {errors.weight && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {errors.weight.message}
                          </p>
                        )}
                      </div>

                      <div className="space-y-1">
                        <Label
                          htmlFor={"receiver_name"}
                          className="text-sm sm:text-base font-semibold"
                        >
                          {t("labels.receiver_name")}
                        </Label>
                        <Input
                          {...form.register("receiver_name")}
                          placeholder={t("labels.receiver_name")}
                          className={cn(
                            "w-[calc(100%-1rem)] transition-all duration-200 focus:ring-2 focus:ring-primary/20 text-sm sm:text-base",
                            errors.receiver_name ? "border-red-500" : ""
                          )}
                        />
                        {errors.receiver_name && (
                          <p className="text-red-500 text-xs sm:text-sm">
                            {errors.receiver_name.message}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {currentStep === 3 && (
                  <div className="space-y-4 sm:space-y-6">
                    <StepHeader title={t("stepper.review-and-submit")} />
                    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6 bg-muted/30 p-3 sm:p-4 rounded-lg">
                      <div className="space-y-4">
                        <h3 className="font-semibold text-base sm:text-lg">
                          {t("stepper.vehicle-information")}
                        </h3>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-xs sm:text-sm font-medium">
                              {t("labels.vin")}:
                            </span>
                            <span className="text-xs sm:text-sm">
                              {form.getValues("vin")}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs sm:text-sm font-medium">
                              {t("labels.year")}:
                            </span>
                            <span className="text-xs sm:text-sm">
                              {form.getValues("year") || "—"}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs sm:text-sm font-medium">
                              {t("labels.make")}:
                            </span>
                            <span className="text-xs sm:text-sm">
                              {form.getValues("make") || "—"}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs sm:text-sm font-medium">
                              {t("labels.model")}:
                            </span>
                            <span className="text-xs sm:text-sm">
                              {form.getValues("model") || "—"}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs sm:text-sm font-medium">
                              {t("labels.color")}:
                            </span>
                            <span className="text-xs sm:text-sm">
                              {form.getValues("color") || "—"}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs sm:text-sm font-medium">
                              {t("labels.price")}:
                            </span>
                            <span className="text-xs sm:text-sm">
                              {form.getValues("price") || "—"}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="font-semibold text-base sm:text-lg">
                          {t("stepper.title-key-information")}
                        </h3>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-xs sm:text-sm font-medium">
                              {t("labels.is_title_exist")}:
                            </span>
                            <span className="text-xs sm:text-sm">
                              {form.getValues("is_title_exist") === true
                                ? "Yes"
                                : form.getValues("is_title_exist") === false
                                ? "No"
                                : "—"}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs sm:text-sm font-medium">
                              {t("labels.title_number")}:
                            </span>
                            <span className="text-xs sm:text-sm">
                              {form.getValues("title_number") || "—"}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs sm:text-sm font-medium">
                              {t("labels.title_state")}:
                            </span>
                            <span className="text-xs sm:text-sm">
                              {form.getValues("title_state") || "—"}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs sm:text-sm font-medium">
                              {t("labels.is_key_present")}:
                            </span>
                            <span className="text-xs sm:text-sm">
                              {form.getValues("is_key_present") === true
                                ? "Yes"
                                : form.getValues("is_key_present") === false
                                ? "No"
                                : "—"}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="font-semibold text-base sm:text-lg">
                          {t("stepper.shipping-information")}
                        </h3>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-xs sm:text-sm font-medium">
                              {t("labels.pol_locations")}:
                            </span>
                            <span className="text-xs sm:text-sm">
                              {locations.find(
                                (loc) =>
                                  loc.id.toString() ===
                                  form.getValues("point_of_loading")?.toString()
                              )?.name || "—"}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs sm:text-sm font-medium">
                              {t("labels.point_of_destination")}:
                            </span>
                            <span className="text-xs sm:text-sm">
                              {formattedOptions.find(
                                (opt) =>
                                  opt.id.toString() ===
                                  form.getValues("destination_id")?.toString()
                              )?.name || "—"}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs sm:text-sm font-medium">
                              {t("labels.weight")}:
                            </span>
                            <span className="text-xs sm:text-sm">
                              {form.getValues("weight") || "—"}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs sm:text-sm font-medium">
                              {t("labels.lot_number")}:
                            </span>
                            <span className="text-xs sm:text-sm">
                              {form.getValues("lot_number") || "—"}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-xs sm:text-sm font-medium">
                              {t("labels.receiver_name")}:
                            </span>
                            <span className="text-xs sm:text-sm">
                              {form.getValues("receiver_name") || "—"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex flex-col sm:flex-row justify-between gap-3 sm:gap-4 mt-4 sm:mt-6 pt-4 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={
                    currentStep === 0 ? () => setOpen(false) : handlePrevious
                  }
                  className="w-full sm:w-auto px-4 py-2 text-sm sm:text-base order-2 sm:order-1"
                >
                  {currentStep === 0
                    ? t("buttons.cancel")
                    : t("buttons.previous")}
                </Button>

                <Button
                  type="button"
                  onClick={
                    currentStep === steps.length - 1
                      ? form.handleSubmit(onSubmit)
                      : handleNext
                  }
                  disabled={isSubmitting}
                  className="w-full sm:w-auto px-4 py-2 text-sm sm:text-base order-1 sm:order-2"
                >
                  {isSubmitting ? (
                    <LoadingSpinner className="h-4 w-4 sm:h-5 sm:w-5" />
                  ) : currentStep === steps.length - 1 ? (
                    isEdit ? (
                      t("buttons.edit")
                    ) : (
                      t("buttons.submit")
                    )
                  ) : (
                    <span className="flex items-center gap-1">
                      {t("buttons.next")}{" "}
                      <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4" />
                    </span>
                  )}
                </Button>
              </div>
            </form>
          </div>

          {/* RTL Stepper */}
          {isRTL && (
            <div className="hidden lg:block lg:w-80 xl:w-96">
              <VehicleStepper
                steps={steps}
                currentStep={currentStep}
                completedSteps={completedSteps}
                onStepClick={handleStepClick}
                isRTL={isRTL}
                formValues={form.watch()}
              />
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
