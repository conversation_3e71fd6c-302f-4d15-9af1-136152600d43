"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";

import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import { useCallback, useMemo, useRef, useState } from "react";

import { CenterCell } from "./cell-renderers/CenterCell";
import VehicleRenderer from "./cell-renderers/VehicleRenderer";
import { LotNumberRenderer as LotNumberRenderer } from "./cell-renderers/ContainerRenderer";
import { StatusRenderer } from "./cell-renderers/StatusRenderer";
import { LocationRenderer } from "./cell-renderers/LocationRenderer";

import DetailCellRenderer from "./cell-renderers/detailCellRenderer";

import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import { Props } from "../services/config";
import useSidebarConfig from "./sidebarConfig";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);

const paginationPageSizeSelector = [5, 10, 20];
export const InventoryPOL = ({
  records,
  gridRefProps,
  exportColDefs,
}: Props) => {
  const gridRef = useRef<AgGridReact>(null);
  const t = useTranslations("vehicle-inventory-pol-datatable");
  const sidebarConfig = useSidebarConfig();
  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: "#",
        cellDataType: "text",
        minWidth: 50,
        cellStyle: {
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        },
        valueGetter: (params) => {
          if (params.node) {
            return params.node.rowIndex ? params.node.rowIndex + 1 : 1;
          }
        },
      },
      {
        field: "vehicle",
        headerName: t("header.vehicle"),
        cellDataType: "text",
        cellRenderer: VehicleRenderer,
        minWidth: 370,
      },
      {
        field: "carstate",
        headerName: t("header.status"),
        minWidth: 150,
        cellRenderer: StatusRenderer,
      },
      {
        field: "lot_number",
        headerName: t("header.lot-number"),
        cellRenderer: LotNumberRenderer,
        minWidth: 200,
      },
      {
        field: "auction_pic",
        headerName: t("header.auc-pics"),
        minWidth: 80,
        cellRenderer: CenterCell,
      },
      {
        field: "locations",
        headerName: t("header.locations"),
        minWidth: 200,
        cellRenderer: LocationRenderer,
      },
      // {
      //   field: "auction_balance",
      //   headerName: t("header.balance"),
      //   cellRenderer: AuctionRenderer,
      //   minWidth: 130,
      // },
    ],
    [t]
  );

  const detailCellRenderer = useCallback(DetailCellRenderer, []);

  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: true,
    }),
    []
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  const [quickFilterText] = useState<string>();

  const selectionColumnDef = useMemo(() => {
    return {
      minWidth: 44,
    };
  }, []);
  const { isRTL } = useDirection();

  return (
    <>
      <AgGridDataTable
        enableRtl={isRTL ? true : false}
        ref={gridRefProps || gridRef}
        selectionColumnDef={selectionColumnDef}
        columnDefs={exportColDefs || colDefs}
        rowData={records?.data || []}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        pagination={false}
        paginationPageSize={records?.per_page ?? 20}
        paginationPageSizeSelector={paginationPageSizeSelector}
        masterDetail
        detailCellRenderer={detailCellRenderer}
        detailCellRendererParams={{ t }}
        quickFilterText={quickFilterText}
        rowHeight={72}
        colResizeDefault="shift"
        headerHeight={60}
        sideBar={sidebarConfig}
        rowClassRules={{
          "row-even": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 === 0
                ? false
                : true
              : true;
          },
          "row-odd": (params) => {
            return params.node.rowIndex
              ? params.node.rowIndex % 2 !== 0
                ? false
                : true
              : true;
          },
        }}
        detailRowAutoHeight
      />
    </>
  );
};
