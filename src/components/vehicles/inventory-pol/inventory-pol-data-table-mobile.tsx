"use client";
import type {
  Col<PERSON>ef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";
import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  ServerSideRowModelModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import { useMemo, useRef, useState } from "react";
import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import { useServerSideDatasource } from "@/hooks/use-infinite-scroll";
import useSidebarConfig from "./sidebarConfig";
import AgGridDataTable from "@/components/ag-grid/ag-grid-data-table";
import { Props } from "../services/config";
import VehicleRenderer from "./cell-renderers/VehicleRenderer";

ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ServerSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);
interface VehicleDataTableMobileProps extends Props {
  onLoadMoreData: (params: {
    page: number;
    per_page: number;
    search?: string;
    filterData?: string;
    state?: string;
  }) => Promise<any>;
  initialParams?: {
    search?: string;
    filterData?: string;
    state?: string;
  };
}

export const VehicleInventroyPolDataTableMobile = ({
  gridRefProps,
  exportColDefs,
  onLoadMoreData,
  initialParams,
}: VehicleDataTableMobileProps) => {
  const t = useTranslations("datatable");

  const [quickFilterText] = useState<string>();
  const gridRef = useRef<AgGridReact>(null);
  const { isRTL } = useDirection();

  const colDefs: ColDef[] = [
    {
      field: "vehicle",
      headerName: t("header.vehicle"),
      cellDataType: "text",
      cellRenderer: VehicleRenderer,
    },
  ];

  const { onGridReady } = useServerSideDatasource({
    onLoadMoreData,
    initialParams,
    pageSize: 20,
  });

  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: false,
    }),
    []
  );
  const sidebarConfig = useSidebarConfig();
  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  return (
    <>
      <AgGridDataTable
        enableRtl={isRTL}
        ref={gridRefProps || gridRef}
        columnDefs={exportColDefs || colDefs}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        rowModelType="serverSide"
        onGridReady={onGridReady}
        rowBuffer={0}
        cacheBlockSize={20}
        maxBlocksInCache={5}
        pagination={false}
        masterDetail={false}
        quickFilterText={quickFilterText}
        rowHeight={120}
        colResizeDefault="shift"
        headerHeight={50}
        sideBar={sidebarConfig}
        rowClassRules={{
          "row-even": (params) =>
            params.node.rowIndex ? params.node.rowIndex % 2 !== 0 : true,
          "row-odd": (params) =>
            params.node.rowIndex ? params.node.rowIndex % 2 === 0 : true,
        }}
        suppressHorizontalScroll={true}
        suppressColumnVirtualisation={true}
        // debug={true}
      />
    </>
  );
};
