"use client";
import React, { useMemo } from "react";
import { ColDef } from "ag-grid-community";
import { format } from "date-fns";
import { formatDateFromNow } from "@/utils/calculate-age-at-pgl";
import { getLocationsInventory } from "../services/vehicle-service";
import { useTranslations } from "next-intl";
import { ExportModal } from "@/components/Common_UI/export-modal";
import { toast } from "sonner";
type Props = {
  locationId: number;
  records: {
    page: number;
    per_page: number;
    total: number;
    data: any[];
  };
};
export default function ExportInventoryPOLVehicleData({
  records,
  locationId,
}: Props) {
  const t = useTranslations("export-modal");

  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        field: "is_printed",
        headerName: "Is Printed",
      },

      {
        field: "photo_link",
        headerName: "Photo Link",
      },

      {
        field: "auction_photos_link",
        headerName: "Auc <PERSON>",
      },

      {
        field: "is_key_present",
        headerName: "Key Present",
      },

      {
        headerName: "Vehicle Description",
        valueGetter: (params) => {
          return `${params?.data?.year} ${params?.data?.make} ${params?.data?.model} ${params?.data?.color} `;
        },
      },

      { field: "vin", headerName: "VIN Number" },

      { field: "lot_number", headerName: "Lot Number" },

      { field: "title_status", headerName: "Title Status" },

      { field: "title_number", headerName: "Title Number" },

      {
        field: "deliver_date",
        headerName: "Delivery Date",
        valueGetter: (params) =>
          params.data?.deliver_date
            ? format(new Date(params.data?.deliver_date), "yyyy-MM-dd")
            : "",
      },

      {
        headerName: "Age At Pgl",
        valueGetter: (param) => formatDateFromNow(param?.data?.deliver_date),
      },

      {
        field: "auction_name",
        headerName: "Auction Name",
      },

      {
        field: "title_state",
        headerName: "Title State",
      },

      {
        field: "ship_as",
        headerName: "Ship As",
      },

      {
        field: "carstate",
        headerName: "Status",
      },

      {
        field: "title_receive_date",
        headerName: "Title Receive Date",
      },

      {
        field: "pol_locations",
        headerName: "Point Of Loading",
        valueGetter: (params) => params?.data?.pol_locations?.name,
      },

      {
        field: "destination_name",
        headerName: "Destinations",
        valueGetter: (params) => params?.data?.destinations?.name,
      },

      {
        field: "receiver_name",
        headerName: "Reciver Name",
      },

      {
        field: "customer_comment",
        headerName: "Comment",
      },
    ],
    []
  );

  const fetchAllData = async (): Promise<any[]> => {
    const response = await getLocationsInventory({
      params: {
        locationId: locationId,
        page: 1,
        per_page: records.total,
        search: "",
        exactMatch: false,
        filterData: "",
      },
    });
    return response.data;
  };

  return (
    <ExportModal
      columnDefs={colDefs}
      currentData={records.data}
      exportFileName="Vehicle Inventory Point Of Loading"
      fetchAllData={fetchAllData}
      totalItems={records.total}
      translations={{
        title: t("title"),
        exportData: t("sub-title"),
        subTitle: t("sub-title"),
        currentData: t("current-data"),
        allData: t("all-data"),
        cancel: t("cancel"),
        export: t("export"),
      }}
      onExportSuccess={() =>
        toast("Vehicle Inventory Point Of Loading Export Completed")
      }
    />
  );
}
