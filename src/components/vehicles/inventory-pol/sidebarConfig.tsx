import { FilterModel } from "../cell-renderers/filter-modal";
import { useMemo } from "react";
import { useTranslations } from "next-intl";

const useSidebarConfig = () => {
  const t = useTranslations("datatable");

  const sidebarConfig = useMemo(
    () => ({
      hiddenByDefault: false,
      toolPanels: [
        {
          id: "columns",
          labelDefault: t("sidebar.columns"),
          labelKey: "columns",
          iconKey: "columns",
          toolPanel: "agColumnsToolPanel",
          toolPanelParams: {
            suppressRowGroups: true,
            suppressValues: true,
            suppressPivots: true,
            suppressPivotMode: true,
            suppressColumnFilter: true,
            suppressColumnSelectAll: true,
            suppressColumnExpandAll: true,
          },
        },
        {
          id: "filters",
          labelDefault: t("sidebar.filters"),
          labelKey: "filters",
          iconKey: "filter",
          width: 290,
          toolPanel: FilterModel,
        },
      ],
    }),
    [t]
  );

  return sidebarConfig;
};

export default useSidebarConfig;
