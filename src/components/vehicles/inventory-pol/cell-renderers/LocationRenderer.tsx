"use client";
import { PODModal } from "@/components/vehicles/dialog-modal/pod-modal";
import { Separator } from "@/components/ui/separator";
import { MapPin } from "lucide-react";
import { useState, FunctionComponent } from "react";
import { useTranslations } from "next-intl";
import { CustomCellRendererProps } from "ag-grid-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";

export const LocationRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("datatable.body");
  const [selectedDestinationName, setSelectedDestinationName] = useState<
    string | null
  >(data?.destinations?.name ?? null);
  const [destinationOpen, setDestinationOpen] = useState<boolean>(false);

  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap py-1 px-2">
        <div className="min-w-[54px]">{t("from")}:</div>
        {data?.pol_locations?.name}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[54px]">{t("to")}:</div>

        <Popover open={destinationOpen} onOpenChange={setDestinationOpen}>
          <PopoverTrigger asChild>
            <Button
              size="icon"
              variant="ghost"
              className="text-blue-500 -mt-2 text-left justify-start w-fulls"
            >
              {selectedDestinationName || <MapPin />}
            </Button>
          </PopoverTrigger>

          <PopoverContent className="w-80 min-h-32 flex flex-col gap-y-4">
            <h1 className="font-bold py-2">Add Point Of Destination</h1>
            <PODModal
              vehicleId={data?.id}
              defaultValue={selectedDestinationName}
              onSuccess={(newDestination) => {
                setSelectedDestinationName(newDestination);
                setDestinationOpen(false); // Close on success
              }}
              close={() => setDestinationOpen(false)} // Close on cancel
            />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};
