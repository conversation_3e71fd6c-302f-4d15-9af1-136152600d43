"use client";
import { Separator } from "@/components/ui/separator";
import { CustomCellRendererProps } from "ag-grid-react";
import {
  Check,
  CircleHelp,
  MessageSquareText,
  UserPlus,
  X,
} from "lucide-react";
import React, { FunctionComponent, useState } from "react";
import { useTranslations } from "next-intl";
import { Checkbox } from "@/components/ui/checkbox";
import { formatDate } from "date-fns";

import { useMutation, useQueryClient } from "@tanstack/react-query";

import { toast } from "sonner";
import { formatDateFromNow } from "@/utils/calculate-age-at-pgl";

import { CommentModal } from "../../dialog-modal/comment-modal";
import { AddReceiverName } from "../../dialog-modal/add-reciver-name";
import { Photos } from "../../custom-carousel";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { useFetchClient } from "@/utils/axios";
import { allowedEmails } from "../../services/config";
import { useSession } from "next-auth/react";

const DetailCellRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const [newComment, setNewComment] = useState<string | null>(
    data?.customer_comment ?? null
  );
  const [commentOpen, setCommentOpen] = useState<boolean>(false);

  const [selectedReceiverName, setSelectedReceiverName] = useState<
    string | null
  >(data?.receiver_name ?? null);
  const [receiverOpen, setReceiverOpen] = useState<boolean>(false);

  const [checked, setChecked] = useState(data?.customer_checked || false);
  const t = useTranslations("vehicle-inventory-pol-datatable");
  const queryClient = useQueryClient();
  const fetchClient = useFetchClient();

  const mutation = useMutation({
    mutationFn: async (check: boolean) => {
      const response = await fetchClient(
        `/v2/vehicles/editCustomerChecked/${data?.id}`,
        {
          data: { customer_checked: check },
          method: "PATCH",
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["id", data?.id],
      });
      toast.success("Vehicle Checked!");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Something went wrong!");
    },
  });
  const handleIsChecked = (value: boolean) => {
    mutation.mutate(value);
    setChecked(value);
  };
  const { data: session } = useSession();
  const userEmail = session?.profile?.loginable?.email?.toLowerCase() || "";
  const isGoogleImages = data?.auction_photos_link?.includes("drive.google.com") || data?.photo_link?.includes("drive.google.com")
    ? true
    : false;
  const isAuctionPhoto =
    allowedEmails.includes(userEmail) &&
    data?.auction_photos_link?.includes("drive.google.com");

  return (
    <div
      className="rounded-lg overflow-hidden shadow-md h-auto  w-full border"
      key={data?.id}
    >
      <div className="w-full text-sm">
        <table className="min-w-full table-auto">
          <thead className="bg-sidebar">
            <tr className="w-full font-medium">
              <th className="px-4 py-2 text-left font-medium text-sm"></th>
              <th className="px-4 py-2 text-left font-medium text-sm">
                {t("vehicle-inventory-datatable-pol-details.header.title")}
              </th>
              <th className="px-4 py-2 text-left font-medium text-sm">
                {t("vehicle-inventory-datatable-pol-details.header.status")}
              </th>
              <th className="px-4 py-2 text-left font-medium text-sm">
                {t("vehicle-inventory-datatable-pol-details.header.general")}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td rowSpan={7} className="pl-11 w-80 ">
                <Photos vehicleId={data?.id} vin={data?.data}
                  isGoogleImages={isGoogleImages}
                  photosLink={{ auction: data?.auction_photos_link, photo: data?.photo_link }}
                  isAuctionPhoto={isAuctionPhoto}
                  isShowTabs={allowedEmails.includes(userEmail)} />
              </td>
              <td className="px-4 py-1"></td>
            </tr>
            <tr className="border-b border-black/10">
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-inventory-datatable-pol-details.body.ship-as")}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.containers?.bookings?.vessels?.etd ? (
                      formatDate(
                        data?.containers?.bookings?.vessels?.etd,
                        "yyyy MMM dd"
                      )
                    ) : (
                      <div className="h-4 w-4"></div>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-inventory-datatable-pol-details.body.checked")}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    <Checkbox
                      checked={checked}
                      onCheckedChange={handleIsChecked}
                    />
                  </div>
                </div>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 py-1 font-medium">
                    {t(
                      "vehicle-inventory-datatable-pol-details.body.reciver-name"
                    )}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap  px-2">
                    <Popover open={receiverOpen} onOpenChange={setReceiverOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="text-blue-500 -mt-2 text-left justify-start w-full"
                        >
                          {selectedReceiverName || <UserPlus />}
                        </Button>
                      </PopoverTrigger>

                      <PopoverContent className="w-80 min-h-32 flex flex-col gap-y-4">
                        <h1 className="font-bold py-2">Add Receiver Name</h1>
                        <AddReceiverName
                          vehicleId={data?.id}
                          defaultValue={selectedReceiverName || ""}
                          onSuccess={(updatedName) => {
                            setSelectedReceiverName(updatedName);
                            setReceiverOpen(false);
                          }}
                          close={() => setReceiverOpen(false)}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </td>
            </tr>
            <tr className="border-b border-black/10">
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5 max-w-56">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t(
                      "vehicle-inventory-datatable-pol-details.body.title-status"
                    )}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis text-wrap px-2">
                    {data?.title_status ? (
                      data?.title_status
                    ) : (
                      <div className="h-4 w-4"></div>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t(
                      "vehicle-inventory-datatable-pol-details.body.is-printed"
                    )}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.is_printed ? (
                      <Check className="text-blue-500 w-4 h-4" />
                    ) : (
                      <X className="text-red-500 w-4 h-4" />
                    )}
                  </div>
                </div>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col gap-2 justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 py-1 font-medium">
                    {t("vehicle-inventory-datatable-pol-details.body.comment")}
                  </div>
                  <Separator />
                  <div className="max-w-52 flex overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    <Popover open={commentOpen} onOpenChange={setCommentOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="text-blue-500 -mt-2 text-left justify-start w-full"
                        >
                          {<MessageSquareText />}
                        </Button>
                      </PopoverTrigger>

                      <PopoverContent className="w-80 min-h-32 flex flex-col gap-y-4">
                        <h1 className="font-bold py-2">Add Your Comment</h1>
                        <CommentModal
                          vehicleId={data?.id}
                          defaultValue={newComment || ""}
                          onSuccess={(updatedComment) => {
                            setNewComment(updatedComment);
                            setCommentOpen(false);
                          }}
                          close={() => setCommentOpen(false)}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </td>
            </tr>
            <tr className="border-b border-black/10">
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t(
                      "vehicle-inventory-datatable-pol-details.body.title-number"
                    )}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.title_number ? (
                      data?.title_number
                    ) : (
                      <div className="h-4 w-4"></div>
                    )}
                  </div>
                </div>
              </td>

              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-inventory-datatable-pol-details.body.is-key")}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.is_title_exist ? (
                      <Check className="text-blue-500 w-4 h-4" />
                    ) : data?.is_title_exist === "null" ? (
                      <CircleHelp />
                    ) : (
                      <X className="text-red-500 w-4 h-4" />
                    )}
                  </div>
                </div>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t(
                      "vehicle-inventory-datatable-pol-details.body.age-at-pgl"
                    )}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.deliver_date ? (
                      formatDateFromNow(data?.deliver_date)
                    ) : (
                      <div className="h-4 w-4"></div>
                    )}
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DetailCellRenderer;
