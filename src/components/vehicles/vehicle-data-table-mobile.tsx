"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";
import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  ServerSideRowModelModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import { useMemo, useRef, useState } from "react";
import AgGridDataTable from "../ag-grid/ag-grid-data-table";
import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import { Props } from "./services/config";
import { CircleP<PERSON>, Filter, Columns3 } from "lucide-react";
import { Button } from "@/components/ui/button";
import InputSearch from "@/components/Common_UI/InputSearch";
import VehicleForm from "./add-vehicle/vehicle-form";
import ExportVehicleData from "./export-vehicle-data";
import VehicleRenderer from "./cell-renderers/VehicleRenderer";
import { useServerSideDatasource } from "@/hooks/use-infinite-scroll";
import { AssignCell } from "./cell-renderers/vehicle-assign";
import { useSession } from "next-auth/react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { FilterModel } from "./cell-renderers/filter-modal";

// Register AG Grid modules
ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ServerSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);

interface VehicleDataTableMobileProps extends Props {
  onLoadMoreData: (params: {
    page: number;
    per_page: number;
    search?: string;
    filterData?: string;
    state?: string;
  }) => Promise<any>;
  initialParams?: {
    search?: string;
    filterData?: string;
    state?: string;
  };
}

export const VehicleDataTableMobile = ({
  records,
  gridRefProps,
  exportColDefs,
  onLoadMoreData,
  initialParams,
}: VehicleDataTableMobileProps) => {
  const t = useTranslations("datatable");
  const t1 = useTranslations("sidebar");
  const [quickFilterText] = useState<string>();
  const [openVehicleForm, setOpenVehicleForm] = useState(false);
  const [editingVehicle, setEditingVehicle] = useState<any>(undefined);
  const [showColumnsPanel, setShowColumnsPanel] = useState(false);
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);
  const gridRef = useRef<AgGridReact>(null);
  const { isRTL } = useDirection();
  const session = useSession();
  const has_customer = session?.data?.profile?.companies?.has_customer;

  const handleEdit = (vehicleData: any) => {
    setEditingVehicle(vehicleData);
    setOpenVehicleForm(true);
  };

  const handleAdd = () => {
    setEditingVehicle(undefined);
    setOpenVehicleForm(false);
    setTimeout(() => setOpenVehicleForm(true), 0);
  };

  const toggleColumnVisibility = (field: string, visible: boolean) => {
    const gridApi = gridRef.current?.api;
    if (gridApi) {
      gridApi.setColumnsVisible([field], visible);
    }
  };

  const getAllColumns = () => {
    const gridApi = gridRef.current?.api;
    if (gridApi) {
      return gridApi.getColumns();
    }
    return [];
  };

  // Custom Header Component
  const VehicleHeaderComponent = () => {
    return (
      <div className="flex items-center justify-between w-full h-full">
        <span>{t("header.vehicle")}</span>
        <div className="flex items-center gap-1">
          {/* Columns Panel Toggle */}
          <Popover open={showColumnsPanel} onOpenChange={setShowColumnsPanel}>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="sm" className="p-1 h-6 w-6">
                <Columns3 className="h-3 w-3" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64" align="end">
              <div className="space-y-2">
                <h4 className="font-medium text-sm">{t("sidebar.columns")}</h4>
                <div className="space-y-2">
                  {getAllColumns()?.map((column) => {
                    const colDef = column.getColDef();
                    const isVisible = column.isVisible();
                    return (
                      <div
                        key={column.getColId()}
                        className="flex items-center space-x-2"
                      >
                        <input
                          type="checkbox"
                          checked={isVisible}
                          onChange={(e) =>
                            toggleColumnVisibility(
                              column.getColId(),
                              e.target.checked
                            )
                          }
                          className="rounded border-gray-300"
                        />
                        <label className="text-sm">
                          {colDef.headerName || column.getColId()}
                        </label>
                      </div>
                    );
                  })}
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* Filters Panel Toggle */}
          <Popover open={showFiltersPanel} onOpenChange={setShowFiltersPanel}>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="sm" className="p-1 h-6 w-6">
                <Filter className="h-3 w-3" />
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className="w-80 p-0"
              align="end"
              sideOffset={14}
              side="bottom"
            >
              <div className="max-h-[calc(100vh-200px)] overflow-y-auto w-full">
                <FilterModel />
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    );
  };

  const colDefs: ColDef[] = [
    {
      field: "vehicle",
      headerName: t("header.vehicle"),
      headerComponent: VehicleHeaderComponent,
      cellDataType: "text",
      cellRenderer: VehicleRenderer,
      cellRendererParams: {
        t,
        onEdit: handleEdit,
      },
    },
    {
      headerName: "",
      colId: "rowNum",
      pinned: isRTL ? "left" : "right",
      minWidth: 30,
      maxWidth: 30,
      cellRenderer: AssignCell,
      hide: has_customer ? false : true,
    },
  ];

  const { onGridReady } = useServerSideDatasource({
    onLoadMoreData,
    initialParams,
    pageSize: 20,
  });

  const defaultColDef = useMemo<ColDef>(
    () => ({
      resizable: false,
    }),
    []
  );

  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  return (
    <>
      <div className="flex justify-end px-1 mb-2 w-full">
        <div className="flex flex-row items-center gap-1 w-full sm:w-auto">
          <ExportVehicleData records={records} />
          <InputSearch
            type="text"
            fieldName="search"
            placeholder={t1("nav.search")}
            className="w-full sm:w-auto"
          />
          <Button onClick={handleAdd} variant={"outline"} size={"icon"}>
            <CirclePlus className="w-6 h-6 cursor-pointer" />
          </Button>
        </div>
      </div>

      <AgGridDataTable
        enableRtl={isRTL}
        ref={gridRefProps || gridRef}
        columnDefs={exportColDefs || colDefs}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        rowModelType="serverSide"
        onGridReady={onGridReady}
        rowBuffer={0}
        cacheBlockSize={20}
        maxBlocksInCache={5}
        pagination={false}
        masterDetail={false}
        quickFilterText={quickFilterText}
        rowHeight={90}
        colResizeDefault="shift"
        headerHeight={50}
        rowClassRules={{
          "row-even": (params) =>
            params.node.rowIndex ? params.node.rowIndex % 2 !== 0 : true,
          "row-odd": (params) =>
            params.node.rowIndex ? params.node.rowIndex % 2 === 0 : true,
        }}
        suppressHorizontalScroll={true}
        suppressColumnVirtualisation={true}
      />

      <VehicleForm
        open={openVehicleForm}
        setOpen={setOpenVehicleForm}
        formData={editingVehicle}
        isEdit={!!editingVehicle}
      />
    </>
  );
};
