



export const headers = [
  {
    id: "id",
    label: "ID",
    width: "auto",
    wrap: true,
    pdfWidth: 50,
    algin: "right",
  },
  {
    id: "docs",
    label: "Docs",
    sortable: false,
    align: "left",
  },
  {
    id: "edit",
    label: "Edit",

    sortable: false,
  },
  {
    id: "photo_link",
    label: "Photo",
    pdfWidth: 50,
  },
  {
    id: "auction_photos_link",
    label: "Auc Pics",
    pdfWidth: 50,
  },
  {
    id: "aes_filling_link",
    label: "AES Link",
    pdfWidth: 50,
  },
  {
    id: "hat_number",
    label: "Hat#",
    pdfWidth: 50,
    width: 100,
    wrap: true,
    align: "right",
  },
  {
    id: "yards_location",
    label: "Yard Loc",
    pdfWidth: 60,
    sortColumnName: "yards_location.name",
    align: "left",
  },
  {
    id: "is_key_present",
    label: "Key",
    pdfWidth: 50,
  },
  {
    id: "vehicle_description",
    label: "Vehicle Description",
    wrap: true,
    pdfWidth: 150,
    sortColumnName: "year",
    align: "left",
  },
  {
    id: "vin",
    label: "Vin",
    pdfWidth: 120,
    align: "right",
  },
  {
    id: "lot_number",
    label: "Lot#",
    pdfWidth: 80,
    align: "right",
  },
  {
    id: "company",
    label: "Company",
    wrap: true,
    width: 200,
    pdfWidth: 100,
    sortColumnName: "customers.companies.name",
  },
  {
    id: "towing_company",
    label: "Towing Company",
    pdfWidth: 100,
    sortColumnName: "vehicle_towings.towing_company",
  },
  {
    id: "load_type",
    label: "Load Type",
    pdfWidth: 50,
    align: "center",
  },

  {
    id: "auction_city",
    label: "Auction City",
    pdfWidth: 50,
    // align: 'center',
  },

  {
    id: "customer_remark",
    label: "Customer Remark",
    pdfWidth: 120,
  },
  {
    id: "carstate",
    label: "Status",
    pdfWidth: 80,
  },
  {
    id: "pglu",
    label: "PLGU",
    pdfWidth: 80,
  },
  {
    id: "pglu_inventory",
    label: "PLGU Inv",
    pdfWidth: 80,
    align: "center",
  },
  {
    id: "united_trading",
    label: "United Trading Inv",
    align: "center",
  },
  {
    id: "title_number",
    label: "Title No",
    pdfWidth: 100,
    align: "right",
  },
  {
    id: "title_state",
    label: "Title State",
  },
  {
    id: "points_of_loading",
    label: "Point of Loading",
    pdfWidth: 80,
    sortColumnName: "pol_locations.name",
  },
  {
    id: "destination_name",
    label: "Port Of Destination",
    pdfWidth: 80,
    sortColumnName: "destinations.name",
  },
  {
    id: "price",
    label: "Vehicle Price",
    pdfWidth: 60,
    align: "right",
  },
  {
    id: "auction_payment",
    label: "Balance",
    pdfWidth: 60,
    align: "right",
  },
  {
    id: "buyer_number",
    label: "Account Number",
    pdfWidth: 60,
    align: "right",
  },
  {
    id: "gate_pass_in",
    label: "GPI",
    pdfWidth: 60,
  },
  {
    id: "weight",
    label: "Weight",
    pdfWidth: 60,
    align: "right",
  },
  {
    id: "account_owner",
    label: "Account Owner",
    pdfWidth: 60,
  },
  {
    id: "age_at_pgl",
    label: "Age at PGL W/H",
    sortColumnName: "deliver_date",
    align: "right",
  },

  {
    id: "purchased_at",
    label: " Purchase Date",
  },

  {
    id: "charges_on_customer",
    label: "Towing Charges on Customers",
    sortColumnName: "vehicle_costs.towing_cost",
    align: "right",
  },
  {
    id: "charges_on_pgl",
    label: "Tow Cost on PGL",
    sortColumnName: "vehicle_towings.tow_amount",
    align: "right",
  },
  {
    id: "payment_date",
    label: "Payment Date",
  },
  {
    id: "payment_date_to_pgl",
    label: "Payment Date To PGL",
  },
  {
    id: "pickup_date",
    label: "Pick Up Date",
  },
  {
    id: "deliver_date",
    label: "Delivery Date",
  },
  {
    id: "container_number",
    label: "Container No",
    pdfWidth: 90,
    sortColumnName: "containers.container_number",
    align: "right",
  },
  {
    id: "container_status",
    label: "Container Status",
    pdfWidth: 90,
    sortColumnName: "containers.status",
    align: "center",
  },

  {
    id: "booking_number",
    label: " Booking No",
    pdfWidth: 90,
    sortColumnName: "containers.bookings.booking_number",
    sortColumnName2: "containers.booking_suffix",
    align: "right",
  },

  {
    id: "title_status",
    label: "Title Status",
  },
  {
    id: "title_receive_date",
    label: "Title Receive",
  },
  {
    id: "loading_date",
    label: "Loading Date",
    sortColumnName: "containers.loading_date",
  },
  {
    id: "etd",
    label: "ETD",
    sortColumnName: "containers.bookings.vessels.etd",
  },
  {
    id: "arrive_date",
    label: "Arrival Date",
    sortColumnName: "containers.bookings.eta",
  },
  {
    id: "created_at",
    label: "RPGL Date",
  },
  {
    id: "vehicle_document_link",
    label: "Doc",
  },
  {
    id: "auction_invoice",
    label: "Auc inv link",
  },
  {
    id: "tax_document_link",
    label: "Tax Doc",
  },
  {
    id: "pickup_photos",
    label: "Pick up Photos Compared",
  },
  {
    id: "delivery_photos",
    label: "Delivery Photos Compared",
  },
  {
    id: "ach",
    label: "ACH Payment Approved",
  },
  {
    id: "remark",
    label: "Remark",
  },
  {
    id: "of_loading_photo",
    label: "OfLoading Photo Link",
  },
  {
    id: "of_loading_video",
    label: "OfLoading Video Link",
  },
  {
    id: "gate_pass",
    label: "GatePass",
  },
  {
    id: "receiver_name",
    label: "Receiver Name",
    pdfWidth: 60,
  },
  {
    id: "comment",
    label: "Comment",
    pdfWidth: 60,
  },
  {
    id: "transaction_no",
    label: "Transaction No.",
    pdfWidth: 100,
  },
  {
    id: "transaction_by",
    label: "Transaction By",
    pdfWidth: 100,
  },
  {
    id: "transaction_at",
    label: "Transaction At",
    pdfWidth: 100,
  },
  {
    id: "inspection",
    label: "Inspection",
    align: "center",
  },
  {
    id: "inspector_name",
    label: "Inspector Name",
    align: "center",
  },
  {
    id: "status_changed_at",
    label: "Age of Status",
    sortColumnName: "status_changed_at",
    align: "right",
  },
  {
    id: "created_by",
    label: "Created By",
    pdfWidth: 100,
  },
  {
    id: "created_at",
    label: "Created At",
    pdfWidth: 100,
  },
  {
    id: "updated_by",
    label: "Updated By",
    pdfWidth: 100,
  },
  {
    id: "updated_at",
    label: "Updated At",
  },
];
export const on_the_way = [
  {
    id: "id",
    label: "ID",
    pdfWidth: 50,
  },
  {
    id: "edit",
    label: "Edit",

    sortable: false,
  },
  {
    id: "auction_photos_link",
    label: "Auc Pics",
    pdfWidth: 50,
  },
  {
    id: "vehicle_description",
    label: "Vehicle Description",
    wrap: true,
    pdfWidth: 150,
    sortColumnName: "year",
    align: "left",
  },
  {
    id: "vin",
    label: "Vin Number",
    pdfWidth: 120,
    align: "right",
  },
  {
    id: "lot_number",
    label: "Lot#",
    pdfWidth: 80,
    align: "right",
  },
  {
    id: "price",
    label: "Vehicle Price",
    pdfWidth: 60,
    align: "right",
  },
  {
    id: "auction_city",
    label: "Auction City",
    pdfWidth: 100,
  },
  {
    id: "towing_company",
    label: "Towing Company",
    pdfWidth: 100,
    sortColumnName: "vehicle_towings.towing_company",
  },
  {
    id: "company",
    label: "Company",
    pdfWidth: 80,
    sortColumnName: "customers.companies.name",
  },
  {
    id: "points_of_loading",
    label: "Point of Loading",
    pdfWidth: 80,
    sortColumnName: "pol_locations.name",
  },
  {
    id: "purchased_at",
    label: " Purchase Date",
  },
  {
    id: "purchased_duration",
    label: "Days From Purchase",
  },
  {
    id: "payment_date",
    label: "Payment Date",
  },
  {
    id: "auction_name",
    label: "Auction",
    pdfWidth: 50,
  },
  {
    id: "carstate",
    label: "Status",
  },
  {
    id: "buyer_number",
    label: "Account Number",
    pdfWidth: 60,
    align: "right",
  },
  {
    id: "created_at",
    label: "Report Date",
  },
  {
    id: "yards_location",
    label: "Yard Loc",
    pdfWidth: 60,
    sortColumnName: "yards_location.name",
  },
  {
    id: "load_type",
    label: "Load Type",
    pdfWidth: 50,
    align: "center",
  },
  {
    id: "towing_status",
    label: "Towing Status",
    pdfWidth: 60,
  },
  {
    id: "customer_remark",
    label: "Customer Remark",
    pdfWidth: 120,
  },
  {
    id: "dispatch_remark",
    label: "Dispatch Remark",
    pdfWidth: 120,
  },
  {
    id: "last_title_follow_up_date2",
    label: "Last Title Follow Date",
    sortColumnName: "last_title_follow_up_date",
  },
  {
    id: "title_receive_date2",
    label: "Title Receive Date",
    sortColumnName: "title_receive_date",
  },

  {
    id: "title_status",
    label: "Title Status",
  },
  {
    id: "title_status_step_two",
    label: "Title Status Step Two",
  },
  {
    id: "account_owner",
    label: "Account Owner",
    pdfWidth: 60,
  },
  {
    id: "receiver_name",
    label: "Receiver Name",
    pdfWidth: 60,
  },
  {
    id: "comment",
    label: "Comment",
    pdfWidth: 60,
  },
  {
    id: "halfcut_status",
    label: "Half Cut Status",
    pdfWidth: 60,
  },
  {
    id: "payment_date_to_pgl",
    label: "Payment Date To PGL",
  },
  {
    id: "deliver_date",
    label: "Delivery Date",
  },
  {
    id: "pickup_date",
    label: "Pick Up Date",
  },
  {
    id: "posted_by_in_central_dispatch",
    label: "Posted By In CD",
  },
  {
    id: "pickup_due_date",
    label: "Pick Up Due Date",
  },
  {
    id: "pickup_date_from_purchase",
    label: "Pick Up Days From Purchase",
  },
  {
    id: "pickup_date_from_report",
    label: "Pick Up Days From Report",
    sortColumnName: "created_at",
  },
  {
    id: "charges_on_customer",
    label: "Towing Charges on Customers",
    sortColumnName: "vehicle_costs.towing_cost",
    align: "right",
  },
  {
    id: "charges_on_pgl",
    label: "Tow Cost on PGL",
    sortColumnName: "vehicle_towings.tow_amount",
    align: "right",
  },
  {
    id: "inspection",
    label: "Inspection",
    align: "center",
  },
  {
    id: "inspector_name",
    label: "Inspector Name",
    align: "center",
  },
  {
    id: "status_changed_at",
    label: "Age of Status",
    sortColumnName: "status_changed_at",
    align: "right",
  },
];
export const pending = [
  {
    id: "id",
    label: "ID",
    pdfWidth: 50,
  },
  {
    id: "yards_location",
    label: "Yard Loc",
    pdfWidth: 60,
    sortColumnName: "yards_location.name",
  },
  {
    id: "vehicle_description",
    label: "Vehicle Description",
    wrap: true,
    pdfWidth: 150,
    sortColumnName: "year",
    align: "left",
  },
  {
    id: "vin",
    label: "Vin Number",
    pdfWidth: 120,
    align: "right",
  },
  {
    id: "lot_number",
    label: "Lot Number",
    pdfWidth: 80,
    align: "right",
  },
  {
    id: "hat_number",
    label: "Hat#",
    pdfWidth: 60,
    width: 100,
    wrap: true,
    align: "right",
  },
  {
    id: "load_type",
    label: "Load Type",
    pdfWidth: 50,
    align: "center",
  },
  {
    id: "auction_city",
    label: "Auction City",
    pdfWidth: 100,
  },
  {
    id: "auction_name",
    label: "Auction",
    pdfWidth: 50,
  },
  {
    id: "carstate",
    label: "Status",
  },
  {
    id: "towing_company",
    label: "Towing Company",
    pdfWidth: 100,
    sortColumnName: "vehicle_towings.towing_company",
  },
  {
    id: "towing_status",
    label: "ToWing Status",
    pdfWidth: 60,
  },
  {
    id: "company",
    label: "Company",
    pdfWidth: 80,
    sortColumnName: "customers.companies.name",
  },
  {
    id: "customer_remark",
    label: "Customer Remark",
    pdfWidth: 120,
  },
  {
    id: "purchased_at",
    label: " Purchase Date",
  },
  {
    id: "purchased_duration",
    label: "Days From Purchase",
  },
  {
    id: "receiver_name",
    label: "Receiver Name",
    pdfWidth: 60,
  },
  {
    id: "comment",
    label: "Comment",
    pdfWidth: 60,
  },
  {
    id: "created_at",
    label: "Report Date",
  },
  {
    id: "payment_date",
    label: "Payment Date",
  },
  {
    id: "payment_date_to_pgl",
    label: "Payment Date To PGL",
  },

  {
    id: "posted_by_in_central_dispatch",
    label: "Posted By In CD",
  },
  {
    id: "pickup_due_date",
    label: "Pick Up Due Date",
  },
  {
    id: "pickup_date_duration",
    label: "Pick Up Days From Purchase",
    sortColumnName: "purchased_at",
  },
  {
    id: "pickup_date_from_report",
    label: "Pick Up Days From Report",
    sortColumnName: "pickup_date",
  },
  {
    id: "points_of_loading",
    label: "Point of Loading",
    pdfWidth: 80,
    sortColumnName: "pol_locations.name",
  },
  {
    id: "charges_on_customer",
    label: "Towing Charges on Customers",
    sortColumnName: "vehicle_costs.towing_cost",
    align: "right",
  },
  {
    id: "charges_on_pgl",
    label: "Tow Cost on PGL",
    sortColumnName: "vehicle_towings.tow_amount",
    align: "right",
  },
  {
    id: "status_changed_at",
    label: "Age of Status",
    sortColumnName: "status_changed_at",
    align: "right",
  },
];
export const on_hand_no_title = [
  {
    id: "id",
    label: "ID",
    pdfWidth: 50,
  },
  {
    id: "photo_link",
    label: "Photo Link",
    sortable: false,
    pdfWidth: 50,
    align: "right",
  },

  {
    id: "lot_number",
    label: "Lot#",
    pdfWidth: 80,
    align: "right",
  },
  {
    id: "account_owner",
    label: "Account Owner",
    pdfWidth: 60,
  },
  {
    id: "buyer_number",
    label: "Account Number",
    pdfWidth: 60,
    align: "right",
  },
  {
    id: "yards_location",
    label: "Yard Loc",
    pdfWidth: 60,
    sortColumnName: "yards_location.name",
  },
  {
    id: "vehicle_description",
    label: "Vehicle Description",
    wrap: true,
    pdfWidth: 150,
    sortColumnName: "year",
    align: "left",
  },
  {
    id: "vin",
    label: "Vin Number",
    pdfWidth: 120,
    align: "right",
  },
  {
    id: "company",
    label: "Company",
    sortColumnName: "customers.companies.name",
  },
  {
    id: "customer_remark",
    label: "Customer Remark",
    pdfWidth: 120,
  },
  {
    id: "last_title_follow_up_date2",
    label: "Last Title Follow Date",
    sortColumnName: "last_title_follow_up_date",
  },
  {
    id: "title_number",
    label: "Title Number",
    pdfWidth: 100,
    align: "right",
  },
  {
    id: "title_status",
    label: "Title Status Step One",
  },
  {
    id: "receiver_name",
    label: "Receiver Name",
    pdfWidth: 60,
  },
  {
    id: "comment",
    label: "Comment",
    pdfWidth: 60,
  },
  {
    id: "title_status_step_two",
    label: "Title Status Step Two",
  },
  {
    id: "title_receive_date2",
    label: "Title Receive Date",
    sortColumnName: "title_receive_date",
  },
  {
    id: "points_of_loading",
    label: "Point of Loading",
    pdfWidth: 80,
    sortColumnName: "pol_locations.name",
  },
  {
    id: "destination_name",
    label: "Point Of Destination",
    pdfWidth: 80,
    sortColumnName: "destinations.name",
  },
  {
    id: "payment_date",
    label: "Payment Date",
  },
  {
    id: "payment_date_to_pgl",
    label: "Payment Date To PGL",
  },
  {
    id: "deliver_date",
    label: "Delivery Date",
  },
  {
    id: "hat_number",
    label: "Hat#",
    pdfWidth: 60,
    width: 100,
    wrap: true,
    align: "right",
  },

  {
    id: "age_at_pgl",
    label: "Age at PGL W/H",
    sortColumnName: "deliver_date",
    // culc:'price,tax,'
  },
  {
    id: "load_type",
    label: "Load Type",
    pdfWidth: 50,
    align: "center",
  },
  {
    id: "auction_city",
    label: "Auction City",
    pdfWidth: 100,
  },

  {
    id: "auction_name",
    label: "Auction",
    pdfWidth: 50,
  },
  {
    id: "charges_on_customer",
    label: "Towing Charges on Customers",
    sortColumnName: "vehicle_costs.towing_cost",
    align: "right",
  },
  {
    id: "charges_on_pgl",
    label: "Tow Cost on PGL",
    sortColumnName: "vehicle_towings.tow_amount",
    align: "right",
  },
  {
    id: "status_changed_at",
    label: "Age of Status",
    sortColumnName: "status_changed_at",
    align: "right",
  },
];
export const on_hand_with_title = [
  {
    id: "id",
    label: "ID",
    pdfWidth: 50,
  },
  {
    id: "photo_link",
    label: "Photo Link",
    sortable: false,
    pdfWidth: 50,
  },
  {
    id: "lot_number",
    label: "Lot#",
    pdfWidth: 80,
    align: "right",
  },
  {
    id: "hat_number",
    label: "Hat#",
    pdfWidth: 60,
    width: 100,
    wrap: true,
    align: "right",
  },
  {
    id: "yards_location",
    label: "Yard Loc",
    pdfWidth: 60,
    sortColumnName: "yards_location.name",
  },
  {
    id: "vehicle_description",
    label: "Vehicle Description",
    wrap: true,
    pdfWidth: 150,
    sortColumnName: "year",
    align: "left",
  },
  {
    id: "vin",
    label: "Vin Number",
    pdfWidth: 120,
    align: "right",
  },
  {
    id: "company",
    label: "Company",
    sortColumnName: "customers.companies.name",
  },
  {
    id: "load_type",
    label: "Load Type",
    pdfWidth: 50,
    align: "center",
  },
  {
    id: "customer_remark",
    label: "Customer Remark",
    pdfWidth: 120,
  },
  {
    id: "price",
    label: "Vehicle Price",
    pdfWidth: 60,
    align: "right",
  },
  {
    id: "deliver_date",
    label: "Delivery Date",
  },

  {
    id: "age_at_pgl",
    label: "Age at PGL W/H",
    sortColumnName: "deliver_date",
  },
  {
    id: "buyer_number",
    label: "Account Number",
    pdfWidth: 60,
  },
  {
    id: "title_number",
    label: "Title Number",
    pdfWidth: 100,
    align: "right",
  },
  {
    id: "title_state",
    label: "Title State",
  },
  {
    id: "points_of_loading",
    label: "Point of Loading",
    pdfWidth: 80,
    sortColumnName: "pol_locations.name",
  },
  {
    id: "destination_name",
    label: "Point Of Destination",
    pdfWidth: 80,
    sortColumnName: "destinations.name",
  },
  {
    id: "payment_date",
    label: "Payment Date",
  },
  {
    id: "payment_date_to_pgl",
    label: "Payment Date To PGL",
  },
  {
    id: "title_status",
    label: "Title Status Step One",
  },
  {
    id: "title_status_step_two",
    label: "Title Status Step Two",
  },
  {
    id: "charges_on_customer",
    label: "Towing Charges on Customers",
    sortColumnName: "vehicle_costs.towing_cost",
    align: "right",
  },
  {
    id: "charges_on_pgl",
    label: "Tow Cost on PGL",
    sortColumnName: "vehicle_towings.tow_amount",
    align: "right",
  },
  {
    id: "status_changed_at",
    label: "Age of Status",
    sortColumnName: "status_changed_at",
    align: "right",
  },
];

export const shipped = [
  {
    id: "id",
    label: "ID",
    pdfWidth: 50,
  },

  {
    id: "photo_link",
    label: "Photo Link",

    pdfWidth: 50,
  },
  {
    id: "lot_number",
    label: "Lot#",
    pdfWidth: 80,
    align: "right",
  },
  {
    id: "vehicle_description",
    label: "Vehicle Description",
    wrap: true,
    pdfWidth: 150,
    sortColumnName: "year",
    align: "left",
  },
  {
    id: "vin",
    label: "Vin Number",
    pdfWidth: 120,
    align: "right",
  },
  {
    id: "is_key_present",
    label: "Key",
    pdfWidth: 50,
  },
  {
    id: "container_number",
    label: "Container No",
    pdfWidth: 90,
    sortColumnName: "containers.container_number",
  },
  {
    id: "booking_number",
    label: " Booking No",
    pdfWidth: 90,
    sortColumnName: "containers.bookings.booking_number",
    sortColumnName2: "containers.booking_suffix",
    align: "right",
  },
  {
    id: "yards_location",
    label: "Yard Loc",
    pdfWidth: 60,
    sortColumnName: "yards_location.name",
  },
  {
    id: "company",
    label: "Company",
    sortColumnName: "customers.companies.name",
  },
  {
    id: "customer_remark",
    label: "Customer Remark",
    pdfWidth: 120,
  },
  {
    id: "price",
    label: "Vehicle Price",
    pdfWidth: 60,
    align: "right",
  },
  {
    id: "halfcut_status",
    label: "Half Cut Status",
    pdfWidth: 60,
  },
  {
    id: "deliver_date",
    label: "Delivery Date",
  },
  {
    id: "payment_date",
    label: "Payment Date",
  },
  {
    id: "payment_date_to_pgl",
    label: "Payment Date To PGL",
  },
  {
    id: "receiver_name",
    label: "Receiver Name",
    pdfWidth: 60,
  },
  {
    id: "comment",
    label: "Comment",
    pdfWidth: 60,
  },
  {
    id: "estimate_time_arrival",
    label: "ETA",
  },
  {
    id: "estimate_time_driver",
    label: "ETD",
  },
  {
    id: "points_of_loading",
    label: "Point of Loading",
    pdfWidth: 80,
    sortColumnName: "pol_locations.name",
  },
  {
    id: "destination_name",
    label: "Point Of Destination",
    pdfWidth: 80,
    sortColumnName: "destinations.name",
  },
  {
    id: "ship_as",
    label: "Ship As",
  },
  {
    id: "inspection",
    label: "Inspection",
    align: "center",
  },
  {
    id: "inspector_name",
    label: "Inspector Name",
    align: "center",
  },
  {
    id: "status_changed_at",
    label: "Age of Status",
    sortColumnName: "status_changed_at",
    align: "right",
  },
];

export const on_hand_with_load = [
  {
    id: "id",
    label: "ID",
    pdfWidth: 50,
  },

  {
    id: "photo_link",
    label: "Photo Link",

    pdfWidth: 50,
  },
  {
    id: "lot_number",
    label: "Lot#",
    pdfWidth: 80,
    align: "right",
  },
  {
    id: "hat_number",
    label: "Hat#",
    pdfWidth: 50,
    width: 100,
    wrap: true,
    align: "right",
  },
  {
    id: "vehicle_description",
    label: "Vehicle Description",
    wrap: true,
    pdfWidth: 150,
    sortColumnName: "year",
    align: "left",
  },
  {
    id: "vin",
    label: "Vin Number",
    pdfWidth: 120,
    align: "right",
  },
  {
    id: "is_key_present",
    label: "Key",
    pdfWidth: 50,
  },
  // {
  //   id: 'container_number',
  //   label: 'Container No',
  //   pdfWidth: 90,
  //   sortColumnName: 'containers.container_number',
  // },
  {
    id: "booking_number",
    label: " Booking No",
    pdfWidth: 90,
    sortColumnName: "containers.bookings.booking_number",
    sortColumnName2: "containers.booking_suffix",
    align: "right",
  },
  {
    id: "yards_location",
    label: "Yard Loc",
    pdfWidth: 60,
    sortColumnName: "yards_location.name",
  },
  {
    id: "company",
    label: "Company",
    sortColumnName: "customers.companies.name",
  },
  {
    id: "load_type",
    label: "Load Type",
    pdfWidth: 50,
    align: "center",
  },
  {
    id: "customer_remark",
    label: "Customer Remark",
    pdfWidth: 120,
  },
  {
    id: "price",
    label: "Vehicle Price",
    pdfWidth: 60,
    align: "right",
  },
  {
    id: "halfcut_status",
    label: "Half Cut Status",
    pdfWidth: 60,
  },
  {
    id: "deliver_date",
    label: "Delivery Date",
  },
  {
    id: "payment_date",
    label: "Payment Date",
  },
  {
    id: "payment_date_to_pgl",
    label: "Payment Date To PGL",
  },
  {
    id: "receiver_name",
    label: "Receiver Name",
    pdfWidth: 60,
  },
  {
    id: "comment",
    label: "Comment",
    pdfWidth: 60,
  },
  {
    id: "estimate_time_arrival",
    label: "ETA",
  },
  {
    id: "estimate_time_driver",
    label: "ETD",
  },
  {
    id: "points_of_loading",
    label: "Point of Loading",
    pdfWidth: 80,
    sortColumnName: "pol_locations.name",
  },
  {
    id: "destination_name",
    label: "Point Of Destination",
    pdfWidth: 80,
    sortColumnName: "destinations.name",
  },
  {
    id: "ship_as",
    label: "Ship As",
  },
  {
    id: "status_changed_at",
    label: "Age of Status",
    sortColumnName: "status_changed_at",
    align: "right",
  },
];
