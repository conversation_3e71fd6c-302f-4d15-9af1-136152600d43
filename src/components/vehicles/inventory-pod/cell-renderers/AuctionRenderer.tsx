import CopartLogo from "@/components/Common_UI/coport-logo";
import IAA<PERSON>ogo from "@/components/Common_UI/iaai-logo";
import type { CustomCellRendererProps } from "ag-grid-react";

import { type FunctionComponent } from "react";

export const AuctionRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 0,
  });

  return (
    <div className="flex flex-col justify-center items-center w-full h-full leading-5">
      {data.auction_name?.toLowerCase() === "copart" ||
      data.auction_name?.toLowerCase() === "coport" ? (
        <CopartLogo />
      ) : (
        <IAAILogo />
      )}
      <div className="rounded-md  flex leading-[22px] text-xs font-semibold overflow-hidden">
        <div className="px-2 bg-blue-600/10 text-blue-400">
          {formatter.format(data?.price)}
        </div>
        <div className="px-2 bg-green-600/10 text-green-500 dark:text-green-300">
          {formatter.format(
            data.price -
              data?.payments
                ?.map((payment: any) => payment?.amount_applied || 0)
                ?.reduce(
                  (accumulator: number, current: any) =>
                    +accumulator + +current,
                  0
                )
          )}
        </div>
      </div>
    </div>
  );
};
