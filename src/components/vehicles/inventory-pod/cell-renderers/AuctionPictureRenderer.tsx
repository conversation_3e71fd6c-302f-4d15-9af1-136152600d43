import type { CustomCellRendererProps } from "ag-grid-react";
import { ImageOff, Images as ImagesIcon } from "lucide-react";
import Link from "next/link";
import { type FunctionComponent } from "react";

export const AuctionPictureRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data }) => {
  return (
    <div className="flex flex-col justify-center h-full select-text leading-5">
      <div className="flex justify-center items-center w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        {
          <Link
            href={data?.auction_photos_link ? data?.auction_photos_link : "#"}
            target="_blank"
          >
            {data?.auction_photos_link ? (
              <ImagesIcon className="text-blue-600 w-5" />
            ) : (
              <ImageOff className="text-red-600 w-5" />
            )}
          </Link>
        }
      </div>
    </div>
  );
};
