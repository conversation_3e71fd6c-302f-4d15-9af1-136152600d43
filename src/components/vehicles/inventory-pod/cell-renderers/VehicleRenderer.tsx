import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import {
  ChevronRight,
  Hash,
  CarFront,
  CircleCheck,
  CircleX,
  MapPin,
  UserPlus,
  MessageSquareText,
} from "lucide-react";
import Image from "next/image";
import { CustomTooltip } from "@/components/Common_UI/custom-tooltip";
import { useResponsive } from "@/hooks/use-mobile";
import { getImageSizeUrl } from "@/utils/imageURL";
import { removeUnderScore } from "@/utils/commons";
import { colorSystem, ColorSystemKey } from "@/lib/constant";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";
import { useDirection } from "@/hooks/useDirection";
import { formatDate } from "date-fns";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { PODModal } from "../../dialog-modal/pod-modal";
import { AddReceiverName } from "../../dialog-modal/add-reciver-name";
import { CommentModal } from "../../dialog-modal/comment-modal";
import { Checkbox } from "@/components/ui/checkbox";
import { formatDateFromNow } from "@/utils/calculate-age-at-pgl";
import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { useTranslations } from "next-intl";

export default function VehicleRenderer({ data, node, api }: any) {
  const [isExpanded, setIsExpanded] = useState(node?.expanded || false);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const { isMobile } = useResponsive();
  const { isRTL } = useDirection();
  const fetchClient = useFetchClient();
  const queryClient = useQueryClient();
  const [checked, setChecked] = useState(data?.customer_checked || false);
  const t = useTranslations("vehicle-inventory-pod-datatable.drawer");

  useEffect(() => {
    if (node) {
      const expandListener = () => setIsExpanded(node.expanded);
      node.addEventListener("expandedChanged", expandListener);
      return () => node.removeEventListener("expandedChanged", expandListener);
    }
  }, []); // Remove node from dependency array to prevent infinite re-renders

  const colors = colorSystem[data?.carstate as ColorSystemKey] || {
    bg: "bg-green-500/10",
    txt: "text-green-500",
  };

  const handleClick = () => {
    if (isMobile) {
      setIsSheetOpen(true);
    } else {
      if (api && node) {
        api.setRowNodeExpanded(node, !node.expanded, true);
      }
    }
  };

  // check vehicle
  const mutation = useMutation({
    mutationFn: async (check: boolean) => {
      const response = await fetchClient(
        `/v2/vehicles/editCustomerChecked/${data?.id}`,
        {
          data: { customer_checked: check },
          method: "PATCH",
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["id", data?.id],
      });
      toast.success("Vehicle Checked!");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Something went wrong!");
    },
  });
  const handleIsChecked = (value: boolean) => {
    mutation.mutate(value);
    setChecked(value);
  };

  // Destination state
  const [selectedDestinationName, setSelectedDestinationName] = useState<
    string | null
  >(data?.destinations?.name ?? null);
  const [destinationOpen, setDestinationOpen] = useState<boolean>(false);

  // Receiver state
  const [selectedReceiverName, setSelectedReceiverName] = useState<
    string | null
  >(data?.receiver_name ?? null);
  const [receiverOpen, setReceiverOpen] = useState<boolean>(false);

  // Comment state
  const [newComment, setNewComment] = useState<string | null>(
    data?.customer_comment ?? null
  );
  const [commentOpen, setCommentOpen] = useState<boolean>(false);

  const detailRows = [
    {
      label: t("status"),
      value: (
        <div className="flex justify-end">
          <Badge
            className={`${colors.bg} ${colors.txt} border-none text-[8px] flex items-center gap-2`}
            variant="outline"
          >
            {data?.carstate === "shipped" && !data?.containers?.container_number
              ? "ON THE WAY"
              : removeUnderScore(data?.carstate)?.toUpperCase()}
          </Badge>
        </div>
      ),
    },
    {
      label: t("year"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.year}
        </span>
      ),
    },
    {
      label: t("make"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.make}
        </span>
      ),
    },
    {
      label: t("model"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.model}
        </span>
      ),
    },
    {
      label: t("color"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.color}
        </span>
      ),
    },
    {
      label: t("is-printed"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {" "}
          {data?.is_printed ? (
            <CircleCheck className="w-4 h-4 text-primary" />
          ) : (
            <CircleX className="w-4 h-4 text-red-600" />
          )}
        </span>
      ),
    },
    {
      label: t("is-key"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {" "}
          {data?.is_key_present ? (
            <CircleCheck className="w-4 h-4 text-primary" />
          ) : (
            <CircleX className="w-4 h-4 text-red-600" />
          )}
        </span>
      ),
    },
    {
      label: t("title-state"),
      value: (
        <span className="text-xs flex items-center justify-end gap-1">
          {data?.title_state ? (
            <CircleCheck className="w-4 h-4 text-primary" />
          ) : (
            <CircleX className="w-4 h-4 text-red-600" />
          )}
        </span>
      ),
    },
    {
      label: t("title-number"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.title_number}
        </span>
      ),
    },
    {
      label: t("age-at-pgl"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.deliver_date && formatDateFromNow(data?.deliver_date)}
        </span>
      ),
    },
    {
      label: t("point-of-loading"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.pol_locations?.name || "."}
        </span>
      ),
    },
    {
      label: t("point-of-destination"),
      value: (
        <Popover open={destinationOpen} onOpenChange={setDestinationOpen}>
          <div className="flex justify-end">
            <PopoverTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                className="text-blue-500 bg-blue-500/10 h-5 flex items-center gap-2 text-xs"
              >
                {selectedDestinationName || <MapPin />}
              </Button>
            </PopoverTrigger>
          </div>

          <PopoverContent className="w-70 min-h-32 flex text-xs flex-col gap-y-2 mr-5">
            <h1 className="font-bold py-2">Add Destination</h1>
            <PODModal
              vehicleId={data?.id}
              defaultValue={selectedDestinationName}
              onSuccess={(newDestination) => {
                setSelectedDestinationName(newDestination);
                setDestinationOpen(false);
              }}
              close={() => setDestinationOpen(false)}
            />
          </PopoverContent>
        </Popover>
      ),
    },
    {
      label: t("receiver-name"),
      value: (
        <Popover open={receiverOpen} onOpenChange={setReceiverOpen}>
          <div className="flex justify-end">
            <PopoverTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                className="text-blue-500 bg-blue-500/10 h-5 flex items-center gap-2 text-xs"
              >
                {selectedReceiverName || <UserPlus />}
              </Button>
            </PopoverTrigger>
          </div>

          <PopoverContent className="w-70  min-h-32 flex text-xs flex-col gap-y-2 mr-5">
            <h1 className="font-bold py-2">Add Receiver Name</h1>
            <AddReceiverName
              vehicleId={data?.id}
              defaultValue={selectedReceiverName || ""}
              onSuccess={(updatedName) => {
                setSelectedReceiverName(updatedName);
                setReceiverOpen(false);
              }}
              close={() => setReceiverOpen(false)}
            />
          </PopoverContent>
        </Popover>
      ),
    },
    {
      label: t("comment"),
      value: (
        <Popover open={commentOpen} onOpenChange={setCommentOpen}>
          <div className="flex justify-end">
            <PopoverTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                className="text-blue-500 bg-blue-500/10 h-5 flex items-center gap-2 text-xs"
              >
                <MessageSquareText />
              </Button>
            </PopoverTrigger>
          </div>

          <PopoverContent className="w-70  min-h-32 flex text-xs flex-col gap-y-2 mr-5">
            <h1 className="font-bold py-2">Add Your Comment</h1>
            <CommentModal
              vehicleId={data?.id}
              defaultValue={newComment || ""}
              onSuccess={(updatedComment) => {
                setNewComment(updatedComment);
                setCommentOpen(false);
              }}
              close={() => setCommentOpen(false)}
            />
          </PopoverContent>
        </Popover>
      ),
    },

    {
      label: t("check"),
      value: (
        <div className="text-xs flex items-center gap-2 justify-end px-3">
          <Checkbox
            className="w-3.5 h-3.5"
            checked={checked}
            onCheckedChange={handleIsChecked}
          />
        </div>
      ),
    },
    {
      label: t("ship-date"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.containers?.bookings?.vessels?.etd &&
            formatDate(data?.containers?.bookings?.vessels?.etd, "MMM d, yyyy")}
        </span>
      ),
    },
    {
      label: t("delivery-date"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.deliver_date && formatDate(data?.deliver_date, "MMM d, yyyy")}
        </span>
      ),
    },
  ];

  return (
    <>
      <div
        className="flex items-center h-full gap-2 hover:cursor-pointer group"
        onClick={handleClick}
      >
        <Image
          src={`${
            data?.cover_photo
              ? getImageSizeUrl({ url: data?.cover_photo, size: 250 })
              : "/placeholder1.jpg"
          }`}
          alt={"sample"}
          height={60}
          width={60}
          className={`rounded-sm`}
        />
        <div className="flex flex-col justify-center h-full select-text leading-5 flex-1">
          <div className="flex items-center gap-1.5">
            <div className="w-full overflow-hidden text-ellipsis leading-5 text-xs whitespace-nowrap px-2">
              {data?.vin}
            </div>
          </div>
          <div className="overflow-hidden max-w-48 text-ellipsis leading-5 text-xs whitespace-nowrap ">
            <CustomTooltip tooltip={`${data?.year} ${data?.make}`}>
              <p className="text-ellipsis whitespace-nowrap font-normal text-xs px-2 text-primary/70">
                {data?.year} {data?.make}
              </p>
            </CustomTooltip>
            <CustomTooltip tooltip={`${data?.model} ${data?.color}`}>
              <p className="overflow-x-hidden text-ellipsis whitespace-nowrap font-normal text-xs px-2 text-primary/70">
                {data?.model} {data?.color}
              </p>
            </CustomTooltip>
          </div>
        </div>

        {!isMobile && (
          <span
            className={`transition-transform duration-200 ${
              isRTL
                ? isExpanded
                  ? "rotate-90"
                  : "rotate-180"
                : isExpanded
                ? "rotate-90"
                : "rotate-0"
            }`}
          >
            <ChevronRight className="w-4 h-4" />
          </span>
        )}
      </div>
      <Drawer open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <DrawerContent
          className="h-[80vh] rounded-t-3xl border-t-2 border-primary px-4 mb-1"
          dir={isRTL ? "rtl" : "ltr"}
        >
          <DrawerHeader className="sr-only">
            <DrawerTitle>Vehicle Details</DrawerTitle>
            <DrawerDescription>
              Detailed information about {data?.vin}
            </DrawerDescription>
          </DrawerHeader>
          <div className="flex flex-col h-full">
            <div className="px-1 py-2">
              <div className="flex items-center gap-2">
                <span className="font-semibold text-xs">
                  {data?.year} {data?.make} {data?.model} {data?.color}
                </span>
              </div>
              <div className="flex items-center gap-2 justify-between py-2">
                <span className="flex items-center text-xs text-primary">
                  <Hash className="w-4 h-4" />
                  {data?.lot_number}
                </span>
                <div className="flex items-center gap-2">
                  <CarFront className="w-4 h-4 text-primary" />
                  <span className="text-xs">{data?.vin}</span>
                </div>
              </div>
            </div>
            <div className="flex-1 overflow-y-auto">
              <div className="rounded-lg overflow-hidden border">
                <Table>
                  <TableBody>
                    {detailRows.map((row, index) => (
                      <TableRow
                        key={row.label}
                        className={`${
                          index % 2 === 0 ? "bg-primary/5" : "bg-primary/10"
                        } `}
                      >
                        <TableCell className="py-1 px-2 text-xs">
                          {row.label}
                        </TableCell>
                        <TableCell className="py-1 px-2 text-right text-xs">
                          {row.value}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  );
}
