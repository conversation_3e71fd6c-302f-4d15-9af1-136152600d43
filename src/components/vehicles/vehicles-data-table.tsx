"use client";
import type {
  ColDef,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from "ag-grid-community";
import {
  AllCommunityModule,
  ClientSideRowModelModule,
  CsvExportModule,
  ModuleRegistry,
} from "ag-grid-community";
import {
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MasterDetailModule,
  MultiFilterModule,
  SetFilterModule,
  SideBarModule,
} from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import { useCallback, useMemo, useRef, useState } from "react";
import AgGridDataTable from "../ag-grid/ag-grid-data-table";
import { AuctionRenderer } from "./cell-renderers/AuctionRenderer";
import { CenterCell } from "./cell-renderers/CenterCell";

import { ContainerRenderer } from "./cell-renderers/ContainerRenderer";
import { LocationRenderer } from "./cell-renderers/LocationRenderer";
import { useDirection } from "@/hooks/useDirection";
import { useTranslations } from "next-intl";
import { Props } from "./services/config";
import DetailCellRenderer from "./cell-renderers/detailCellRenderer";
import { AssignCell } from "./cell-renderers/vehicle-assign";
import useSidebarConfig from "./sidebarConfig";
import { CirclePlus } from "lucide-react";
import { Button } from "@/components/ui/button";
import InputSearch from "@/components/Common_UI/InputSearch";
import VehicleForm from "./add-vehicle/vehicle-form";
import { ActionRenderer } from "./cell-renderers/ActionRenderer";
import ExportVehicleData from "./export-vehicle-data";
import { ShippingCostRenderer } from "./cell-renderers/ShippingCostRenderer";
import { CustomerProfitRenderer } from "./cell-renderers/CustomerProfitRenderer";
import { PaidAmountRenderer } from "./cell-renderers/PaidAmountRenderer";
import { UnpaidAmountRenderer } from "./cell-renderers/UnpaidAmountRenderer";
import { StorageCostRenderer } from "./cell-renderers/StorageCostRenderer";
import VehicleRenderer from "./cell-renderers/VehicleRenderer";
import { useResponsive } from "@/hooks/use-mobile";
import { useSession } from "next-auth/react";

// Register AG Grid modules
ModuleRegistry.registerModules([
  AllCommunityModule,
  ClientSideRowModelModule,
  ExcelExportModule,
  SetFilterModule,
  MultiFilterModule,
  MasterDetailModule,
  SideBarModule,
  FiltersToolPanelModule,
  ColumnsToolPanelModule,
  CsvExportModule,
]);

const paginationPageSizeSelector = [5, 10, 20];

export const VehicleDataTable = ({
  records,
  gridRefProps,
  exportColDefs,
}: Props) => {
  const t = useTranslations("datatable");
  const t1 = useTranslations("sidebar");
  const t3 = useTranslations("customer-vehicles-datatable");
  const [quickFilterText] = useState<string>();
  const [openVehicleForm, setOpenVehicleForm] = useState(false);
  const [editingVehicle, setEditingVehicle] = useState<any>(undefined);
  const gridRef = useRef<AgGridReact>(null);
  const { isRTL } = useDirection();
  const { isMobile } = useResponsive();

  const handleEdit = (vehicleData: any) => {
    setEditingVehicle(vehicleData);
    setOpenVehicleForm(true);
  };

  const handleAdd = () => {
    setEditingVehicle(undefined);
    setOpenVehicleForm(false);
    setTimeout(() => setOpenVehicleForm(true), 0);
  };
  const session = useSession();
  const has_customer = session?.data?.profile?.companies?.has_customer;

  const colDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: "#",
        cellDataType: "text",
        minWidth: 35,
        maxWidth: 35,
        cellStyle: {
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          text: "xs",
        },
        valueGetter: (params) =>
          params.node?.rowIndex ? params.node.rowIndex + 1 : 1,
      },

      {
        field: "vehicle",
        headerName: t("header.vehicle"),
        cellDataType: "text",
        cellRenderer: VehicleRenderer,
        minWidth: 310,
        cellRendererParams: {
          t,
        },
      },
      {
        field: "action",
        headerName: t("header.action"),
        minWidth: 70,
        cellRenderer: ActionRenderer,
        cellRendererParams: {
          onEdit: handleEdit,
        },
      },
      {
        field: "container_number",
        headerName: t("header.container"),
        cellRenderer: ContainerRenderer,
        minWidth: 165,
      },
      {
        field: "title",
        headerName: t("header.title"),
        minWidth: 230,
        cellRenderer: CenterCell,
      },
      {
        field: "locations",
        headerName: t("header.locations"),
        minWidth: 175,
        cellRenderer: LocationRenderer,
      },

      {
        field: "auction_balance",
        headerName: t("header.auction"),
        cellRenderer: AuctionRenderer,
        minWidth: 115,
      },

      {
        field: "customer_of_customer",
        headerName: t3("header.customer-name"),
        cellRenderer: (params: any) => (
          <div className="flex items-center h-full">
            {
              params?.data?.vehicles_customer_of_customer?.at(0)
                ?.customer_of_customer?.fullname
            }
          </div>
        ),
        minWidth: 140,
        hide: has_customer ? false : true,
      },
      {
        headerName: t3("header.ship-cost"),
        cellRenderer: ShippingCostRenderer,
        minWidth: 120,
        hide: has_customer ? false : true,
      },
      {
        headerName: t3("header.storage-charge"),
        cellRenderer: StorageCostRenderer,
        minWidth: 130,
        hide: has_customer ? false : true,
      },
      {
        headerName: t3("header.customer-profit"),
        cellRenderer: CustomerProfitRenderer,
        minWidth: 130,
        hide: has_customer ? false : true,
      },
      {
        headerName: t3("header.paid-amount"),
        hide: has_customer ? false : true,
        minWidth: 170,
        cellRenderer: PaidAmountRenderer,
      },
      {
        headerName: t3("header.balance"),
        minWidth: 170,
        cellRenderer: UnpaidAmountRenderer,
        hide: has_customer ? false : true,
      },
      {
        headerName: "",
        colId: "rowNum",
        pinned: isRTL ? "left" : "right",
        minWidth: 40,
        maxWidth: 40,
        cellRenderer: AssignCell,
        hide: has_customer ? false : true,
      },
    ],
    [isMobile, has_customer, isRTL, t, t3, handleEdit]
  );

  const sidebarConfig = useSidebarConfig();
  const detailCellRenderer = useCallback(DetailCellRenderer, []);
  const defaultColDef = useMemo<ColDef>(() => ({ resizable: true }), []);
  const autoSizeStrategy:
    | SizeColumnsToFitGridStrategy
    | SizeColumnsToFitProvidedWidthStrategy
    | SizeColumnsToContentStrategy = {
    type: "fitGridWidth",
  };

  return (
    <>
      <div className="flex justify-end px-1 mb-2">
        <div className="flex items-center gap-1">
          <ExportVehicleData records={records} />
          <InputSearch
            type="text"
            fieldName="search"
            placeholder={t1("nav.search")}
          />
          <Button onClick={handleAdd} variant={"outline"} size={"icon"}>
            <CirclePlus className="w-6 h-6 cursor-pointer" />
          </Button>
        </div>
      </div>

      <AgGridDataTable
        enableRtl={isRTL}
        ref={gridRefProps || gridRef}
        columnDefs={exportColDefs || colDefs}
        rowData={records?.data || []}
        defaultColDef={defaultColDef}
        autoSizeStrategy={autoSizeStrategy}
        pagination={false}
        paginationPageSize={records?.per_page ?? 20}
        paginationPageSizeSelector={paginationPageSizeSelector}
        masterDetail={isMobile ? false : true}
        detailCellRenderer={isMobile ? null : detailCellRenderer}
        detailCellRendererParams={{ t }}
        quickFilterText={quickFilterText}
        rowHeight={100}
        colResizeDefault="shift"
        headerHeight={60}
        sideBar={sidebarConfig}
        rowClassRules={{
          "row-even": (params) =>
            params.node.rowIndex ? params.node.rowIndex % 2 !== 0 : true,
          "row-odd": (params) =>
            params.node.rowIndex ? params.node.rowIndex % 2 === 0 : true,
        }}
        detailRowAutoHeight
      />

      <VehicleForm
        open={openVehicleForm}
        setOpen={setOpenVehicleForm}
        formData={editingVehicle}
        isEdit={!!editingVehicle}
      />
    </>
  );
};
