"use client";
import React, { useMemo } from "react";
import { ColDef } from "ag-grid-community";
import { useTranslations } from "next-intl";
import { getVehicle } from "./services/vehicle-service";
import { formatDateFromNow } from "@/utils/calculate-age-at-pgl";
import { VehicleType } from "./services/config";
import { ExportModal } from "../Common_UI/export-modal";
import { format, formatDate } from "date-fns";
import { toast } from "sonner";

// Props for the wrapper component
interface Props {
  records: {
    page: number;
    per_page: number;
    total: number;
    data: VehicleType[];
  };
}

const isValidDate = (date: any): boolean => {
  return date && !isNaN(new Date(date).getTime());
};

export default function ExportVehicleData({ records }: Props) {
  // Translation hook scoped to the export-modal namespace
  const t = useTranslations("export-modal");

  // Column definitions memoized for performance
  const columnDefs = useMemo<ColDef[]>(
    () => [
      { field: "customer_checked", headerName: "Checked" },

      { field: "is_printed", headerName: "Is Printed" },

      { field: "is_key_present", headerName: "Key Present" },

      {
        headerName: "Vehicle Description",
        valueGetter: (params) => {
          return `${params?.data?.year} ${params?.data?.make} ${params?.data?.model} ${params?.data?.color} `;
        },
      },

      { field: "vin", headerName: "VIN" },

      { field: "lot_number", headerName: "Lot Number" },

      { field: "title_status", headerName: "Title Status" },

      { field: "title_number", headerName: "Title Number" },

      { field: "purchased_at", headerName: "Purchase Date" },

      {
        field: "pol_locations",
        headerName: "Point Of Loading",
        valueParser: (params) => params?.data?.pol_locations?.name,
      },

      {
        field: "destination_name",
        headerName: "Destinations",
        valueGetter: (params) => params?.data?.destinations?.name,
      },

      { field: "year", headerName: "Year" },

      { field: "model", headerName: "Model" },

      { field: "make", headerName: "Make" },

      { field: "color", headerName: "Color" },

      { field: "carstate", headerName: "State" },

      {
        field: "deliver_date",
        headerName: "Delivery Date",
        valueGetter: (params) =>
          params.data?.deliver_date && isValidDate(params.data.deliver_date)
            ? format(new Date(params.data.deliver_date), "yyyy-MM-dd")
            : "",
      },

      {
        headerName: "Age At Pgl",
        valueGetter: (param) =>
          param.data?.deliver_date && isValidDate(param.data.deliver_date)
            ? formatDateFromNow(param.data.deliver_date)
            : "",
      },

      { field: "auction_name", headerName: "Payment Date" },

      {
        headerName: "Container Number",
        valueGetter: (params) => params?.data?.containers?.container_number,
      },

      {
        headerName: "Loading Date",
        valueGetter: (params) => params?.data?.containers?.loading_date,
      },

      {
        headerName: "Ship Date",
        valueGetter: (params) =>
          params.data?.containers?.bookings?.vessels?.etd &&
          isValidDate(params.data.containers.bookings.vessels.etd)
            ? formatDate(
                params.data.containers.bookings.vessels.etd,
                "yyyy-MMM-dd"
              )
            : "",
      },

      {
        headerName: "Arrival Date",
        valueGetter: (params) =>
          params.data?.containers?.bookings?.eta &&
          isValidDate(params.data.containers.bookings.eta)
            ? formatDate(params.data.containers.bookings.eta, "yyyy-MMM-dd")
            : "",
      },

      {
        headerName: "ETA",
        valueGetter: (params) =>
          params.data?.containers?.bookings?.eta &&
          isValidDate(params.data.containers.bookings.eta)
            ? formatDate(params.data.containers.bookings.eta, "yyyy-MMM-dd")
            : "",
      },

      {
        field: "title_receive_date",
        headerName: "Title Recive Date",
        valueGetter: (params) =>
          params.data?.title_receive_date &&
          isValidDate(params.data.title_receive_date)
            ? formatDate(params.data.title_receive_date, "yyyy MMM dd")
            : "",
      },

      {
        field: "is_title_exist",
        headerName: "Is Title",
      },

      {
        field: "buyer_number",
        headerName: "Account Number",
      },

      {
        field: "receiver_name",
        headerName: "Reciver Name",
      },

      {
        field: "customer_comment",
        headerName: "Comment",
      },
    ],
    []
  );

  // Async function to fetch all vehicle data
  const fetchAllData = async (): Promise<VehicleType[]> => {
    const response = await getVehicle({
      params: {
        state: "",
        page: 1,
        per_page: records.total,
        search: "",
        exactMatch: false,
        filterData: "",
      },
    });
    return response.data;
  };
  return (
    <div className="flex justify-end">
      <div>
        <ExportModal
          columnDefs={columnDefs}
          currentData={records.data}
          exportFileName="Vehicle Data"
          fetchAllData={fetchAllData}
          totalItems={records.total}
          translations={{
            title: t("title"),
            exportData: t("sub-title"),
            subTitle: t("sub-title"),
            currentData: t("current-data"),
            allData: t("all-data"),
            cancel: t("cancel"),
            export: t("export"),
          }}
          onExportSuccess={() => toast("Vehicle Export Completed")}
        />
      </div>
    </div>
  );
}
