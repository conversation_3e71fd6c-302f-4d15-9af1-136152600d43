"use client";
import { useMutation } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
export const useDeleteVehicle = () => {
  const fetchClient = useFetchClient();

  const { mutate: deleteVehicle, isPending } = useMutation({
    mutationFn: async (data: number[]) =>
      await fetchClient(`/v2/vehicles/${data}`, {
        method: "DELETE",
      }),
  });
  return { deleteVehicle, isPending };
};
