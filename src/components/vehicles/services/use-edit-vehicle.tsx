"use client";
import { useMutation } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
export const useEditVehicle = () => {
  const fetchClient = useFetchClient();

  const { mutate: editVehicle, isPending } = useMutation({
    mutationFn: async (data: any) =>
      await fetchClient(`/v2/vehicles/${data.id}`, {
        data: data,
        method: "PATCH",
      }),
  });
  return { editVehicle, isPending };
};
