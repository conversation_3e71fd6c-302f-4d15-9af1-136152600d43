"use server";

import api from "@/utils/axios-server";
import { VehicleParamType } from "./config";
import { removeMatchingValue } from "@/utils/helper-function";
import axios from "@/utils/axios-server";

export async function getVehicle({ params }: { params: VehicleParamType }) {
  removeMatchingValue(params, "state", ["1620278055.png", "undefined"]);
  try {
    const response = await api.get(`/v2/vehicles`, {
      params: { ...params },
    });
    return response.data;
  } catch (error) {
    throw new Error(error as any);
  }
}

export async function getCostAnalysis({
  params,
}: {
  params: VehicleParamType;
}) {
  try {
    const response = await api.get(`/v2/vehicles/costAnalysis`, {
      params: { ...params },
    });

    return response.data;
  } catch (error) {
    throw new Error(error as any);
  }
}

export async function getDateLines({ params }: { params: VehicleParamType }) {
  try {
    const response = await api.get(`/v2/vehicles/dateLines`, {
      params: { ...params },
    });

    return response.data;
  } catch (error) {
    throw new Error(error as any);
  }
}

export async function getDestinationInventory({ params }: { params: any }) {
  try {
    const response = await api.get(`/v2/vehicles/destinationInventory`, {
      params: { ...params },
    });
    return response.data;
  } catch (error) {
    throw new Error(error as any);
  }
}

export async function getLocationsInventory({ params }: { params: any }) {
  try {
    const response = await api.get(`/v2/vehicles/locationInventory`, {
      params: { ...params },
    });

    return response.data;
  } catch (error) {
    throw new Error(error as any);
  }
}

export const getAllLocations = async () => {
  try {
    const res = await api.get("/v2/vehicles/getAllLocations");
    if (res.status === 200) {
      return res.data.data;
    }
  } catch (error) {
    throw new Error(error as any);
  }
};

export const getDestinations = async () => {
  try {
    const res = await api.get("/v2/vehicles/getDestination");
    if (res.status === 200) {
      return res.data.data;
    }
  } catch (error) {
    throw new Error(error as any);
  }
};

export const getAuctionPayment = async (id: number) => {
  try {
    const response = await axios(`/v2/auction_payment/${id}`);
    if (response.status === 200) {
      return response.data;
    }
    console.error("Error fetching auction payment:", response.data);
    throw new Error("Failed to fetch auction payment data");
  } catch (error) {
    console.error("Error fetching auction payment:", error);
    throw error;
  }
};
