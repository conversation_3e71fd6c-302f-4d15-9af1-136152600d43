"use client";
import { useMutation } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
export const useAddVehicle = () => {
  const fetchClient = useFetchClient();

  const { mutate: addVehicle, isPending } = useMutation({
    mutationFn: async (data: any) => {
      await fetchClient(`/v2/vehicles`, {
        data: data,
        method: "POST",
      });
    },
  });

  return { addVehicle, isPending };
};
