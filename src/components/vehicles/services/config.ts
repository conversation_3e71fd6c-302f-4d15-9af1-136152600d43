import { ColDef } from "ag-grid-enterprise";
import { AgGridReact } from "ag-grid-react";
import { RefObject } from "react";

export type VehicleParamType = {
  state: string | undefined;
  page: number;
  per_page: number;
  search: string;
  exactMatch: boolean;
  filterData: any;
};
export type PageProps = {
  params: Promise<any>;
  searchParams: any;
};

export type VehicleType = {
  id: number;
  vin: string;
  is_title_exist: boolean;
  lot_number: string;
  year: string;
  make: string;
  model: string;
  loading_cities: { city_name: string };
  containers: {
    container_number: string;
    loading_date: string;
    bookings: {
      booking_number: string;
      eta: string;
      vessels: {
        etd: string;
      };
    };
  };
  color: string;
  weight: string;
  price: number;
  title_number: string;
  title_state: string;
  title_status: string | null;
  title_receive_date: string | null;
  hat_number: string | null;
  customer_remark: string | null;
  carstate: string;
  halfcut_status: string;
  payment_date: string;
  buyer_number: string;
  vehicle_document_link: string;
  auction_invoice: string;
  photo_link: string;
  is_key_present: boolean;
  note: string;
  auction_name: string;
  auction_city: string;
  date_posted_in_central_dispatch: string;
  posted_by_in_central_dispatch: string | null;
  pickup_due_date: string | null;
  purchased_at: string;
  pickup_date: string;
  deliver_date: string;
  ship_as: string | null;
  destination_id: number | null;
  cover_photo: string | null;
  receiver_name: string | null;
  customer_comment: string | null;
  is_printed: boolean;
  vehicle_towings: {
    tow_amount: number;
    towing_company: string;
  };
  vehicle_costs: {
    dismantle_cost: number;
    ship_cost: number;
    pgl_storage_costs: number;
    title_charge: number;
    dubai_custom_cost: number;
    other_cost: number;
    sales_cost: number;
    towing_cost: number;
    vehicle_price: number;
  };
  vehicle_images: string[];
  pol_locations: {
    name: string;
  };
  destinations: string | null;
  created_at: string;
  request_for_pickup_date: string;
  updated_at: string;
  customer_checked: boolean;
};
export type Props = {
  gridTheme?: string;
  isDarkMode?: boolean;
  records: {
    page: number;
    per_page: number;
    total: number;
    data: VehicleType[];
  };
  gridRefProps?: RefObject<AgGridReact | null>;
  exportColDefs?: ColDef[];
};

export const allowedEmails = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
];
export const extractBranchShortCut = (value: string) => {
  if (!value) return "";
  const match = value.match(/\(([^)]+)\)/);
  return match ? match[0] : "";
};
