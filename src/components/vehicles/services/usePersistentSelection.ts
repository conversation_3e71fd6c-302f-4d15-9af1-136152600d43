import { GridReadyEvent } from "ag-grid-enterprise";
import { useCallback, useEffect, useState } from "react";

export const usePersistentSelection = (storageKey: string) => {
  const [selectedIds, setSelectedIds] = useState<number[]>(() => {
    if (typeof window === "undefined") return [];
    const stored = localStorage.getItem(storageKey);
    return stored ? JSON.parse(stored) : [];
  });

  // Keep localStorage in sync
  useEffect(() => {
    localStorage.setItem(storageKey, JSON.stringify(selectedIds));
  }, [selectedIds, storageKey]);

  // When grid is ready, set selection state on the nodes from storage
  const onGridReady = useCallback(
    (params: GridReadyEvent) => {
      params.api.forEachNode((node) => {
        if (node.data?.id) {
          node.setSelected(selectedIds.includes(node.data.id));
        }
      });
    },
    [selectedIds]
  );

  // Update storage whenever a row is selected/unselected
  const onRowSelected = useCallback((event: any) => {
    const id = event.data.id;
    setSelectedIds((prev) =>
      event.node.isSelected()
        ? [...new Set([...prev, id])] // Ensure unique IDs
        : prev.filter((i) => i !== id)
    );
  }, []);

  return { selectedIds, onGridReady, onRowSelected };
};
