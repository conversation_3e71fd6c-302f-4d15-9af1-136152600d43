"use server";

import {
  getCostAnalysis,
  getDateLines,
  getDestinationInventory,
  getLocationsInventory,
  getVehicle,
} from "@/components/vehicles/services/vehicle-service";
import { transformObject } from "@/lib/transferObject";

// Define allowed keys (Only these fields will be processed)
const allowedKeys = [
  "con",
  "loc",
  "des",
  "pri_from",
  "pri_to",
  "payment_from",
  "payment_to",
  "carstate",
  "vin",
  "lot_number",
  "make",
  "model",
  "year",
  "from_purchased_at",
  "to_purchased_at",
  "from_payment_date",
  "to_payment_date",
  "from_deliver_date",
  "to_deliver_date",
  "checked",
  "is_printed",
];

const mapping = {
  des: "destination_id",
  con: "container_id",
  loc: "point_of_loading",
  inv: "invoice_number",
  pri_from: "price.min",
  pri_to: "price.max",
  payment_from: "payment_received.min",
  payment_to: "payment_received.max",
  carstate: "carstate",
  lot_number: "lot_number",
  vin: "vin",
  make: "make",
  model: "model",
  year: "year",
  from_purchased_at: "purchased_at.from",
  to_purchased_at: "purchased_at.to",
  from_payment_date: "payment_date.from",
  to_payment_date: "payment_date.to",
  from_deliver_date: "deliver_date.from",
  to_deliver_date: "deliver_date.to",
  checked: "bool@@customer_checked",
  is_printed: "bool@@is_printed",
};

// Server action for loading vehicle data
export async function loadVehicleData(
  baseState: string,
  searchParams: any,
  params: {
    page: number;
    per_page: number;
    search?: string;
    filterData?: string;
  }
) {
  try {
    const transformedFilters = transformObject(
      searchParams,
      allowedKeys,
      mapping,
      ["lot_number", "year", "vin"]
    );

    const result = await getVehicle({
      params: {
        state: baseState,
        page: params.page,
        per_page: params.per_page,
        search: params.search || "",
        exactMatch: false,
        filterData:
          Object.keys(transformedFilters).length !== 0
            ? JSON.stringify(transformedFilters)
            : "",
      },
    });
    const response = {
      data: Array.isArray(result.data) ? result.data : [],
      total: result.total || 0,
      page: params.page,
      per_page: params.per_page,
      success: true,
    };
    return response;
  } catch (error) {
    console.error(error);
    return {
      data: [],
      total: 0,
      page: params.page,
      per_page: params.per_page,
      success: false,
    };
  }
}

export async function loadCostAnalysisData(
  baseState: string,
  searchParams: any,
  params: {
    page: number;
    per_page: number;
    search?: string;
    filterData?: string;
  }
) {
  try {
    const transformedFilters = transformObject(
      searchParams,
      allowedKeys,
      mapping,
      ["lot_number", "year", "vin"]
    );

    const result = await getCostAnalysis({
      params: {
        state: baseState,
        page: params.page,
        per_page: params.per_page,
        search: params.search || "",
        exactMatch: false,
        filterData:
          Object.keys(transformedFilters).length !== 0
            ? JSON.stringify(transformedFilters)
            : "",
      },
    });
    const response = {
      data: Array.isArray(result.data) ? result.data : [],
      total: result.total || 0,
      page: params.page,
      per_page: params.per_page,
      success: true,
    };
    return response;
  } catch (error) {
    console.error(error);
    return {
      data: [],
      total: 0,
      page: params.page,
      per_page: params.per_page,
      success: false,
    };
  }
}

export async function loadDateLinesData(
  baseState: string,
  searchParams: any,
  params: {
    page: number;
    per_page: number;
    search?: string;
    filterData?: string;
  }
) {
  try {
    const transformedFilters = transformObject(
      searchParams,
      allowedKeys,
      mapping,
      ["lot_number", "year", "vin"]
    );

    const result = await getDateLines({
      params: {
        state: baseState,
        page: params.page,
        per_page: params.per_page,
        search: params.search || "",
        exactMatch: false,
        filterData:
          Object.keys(transformedFilters).length !== 0
            ? JSON.stringify(transformedFilters)
            : "",
      },
    });
    const response = {
      data: Array.isArray(result.data) ? result.data : [],
      total: result.total || 0,
      page: params.page,
      per_page: params.per_page,
      success: true,
    };
    return response;
  } catch (error) {
    console.error(error);
    return {
      data: [],
      total: 0,
      page: params.page,
      per_page: params.per_page,
      success: false,
    };
  }
}

export async function loadPointOfDestinationData(
  baseState: string,
  searchParams: any,
  params: {
    destinationId: number;
    page: number;
    per_page: number;
    search?: string;
    filterData?: string;
  }
) {
  try {
    const transformedFilters = transformObject(
      searchParams,
      allowedKeys,
      mapping,
      ["lot_number", "year", "vin"]
    );

    const result = await getDestinationInventory({
      params: {
        destinationId: params.destinationId,
        state: baseState,
        page: params.page,
        per_page: params.per_page,
        search: params.search || "",
        exactMatch: false,
        filterData:
          Object.keys(transformedFilters).length !== 0
            ? JSON.stringify(transformedFilters)
            : "",
      },
    });
    const response = {
      data: Array.isArray(result.data) ? result.data : [],
      total: result.total || 0,
      page: params.page,
      per_page: params.per_page,
      success: true,
    };
    return response;
  } catch (error) {
    console.error(error);
    return {
      data: [],
      total: 0,
      page: params.page,
      per_page: params.per_page,
      success: false,
    };
  }
}

export async function loadPointOfLoadingData(
  baseState: string,
  searchParams: any,
  params: {
    locationId: number;
    page: number;
    per_page: number;
    search?: string;
    filterData?: string;
  }
) {
  try {
    const transformedFilters = transformObject(
      searchParams,
      allowedKeys,
      mapping,
      ["lot_number", "year", "vin"]
    );
    const result = await getLocationsInventory({
      params: {
        locationId: params.locationId,
        state: baseState,
        page: params.page,
        per_page: params.per_page,
        search: params.search || "",
        exactMatch: false,
        filterData:
          Object.keys(transformedFilters).length !== 0
            ? JSON.stringify(transformedFilters)
            : "",
      },
    });
    const response = {
      data: Array.isArray(result.data) ? result.data : [],
      total: result.total || 0,
      page: params.page,
      per_page: params.per_page,
      success: true,
    };
    return response;
  } catch (error) {
    console.error(error);
    return {
      data: [],
      total: 0,
      page: params.page,
      per_page: params.per_page,
      success: false,
    };
  }
}
