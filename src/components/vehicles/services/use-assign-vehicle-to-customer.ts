"use client";
import { useMutation } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
export const useAssigVehicleToCustomer = () => {
  const fetchClient = useFetchClient();

  const { mutate: assigVehicleToCustomer, isPending } = useMutation({
    mutationFn: async (data: any) => {
      return await fetchClient(`/v2/customer-of-customer-vehicle`, {
        data: data,
        method: "POST",
      });
    },
  });
  return { assigVehicleToCustomer, isPending };
};

export const useAssingVehicleToCustomerEdit = () => {
  const fetchClient = useFetchClient();

  const { mutate: assigVehicleToCustomerEdit, isPending } = useMutation({
    mutationFn: async (data: any) => {
      const { id, ...body } = data;
      return await fetchClient(`/v2/customer-of-customer-vehicle/${id}`, {
        data: body,
        method: "PATCH",
      });
    },
  });
  return { assigVehicleToCustomerEdit, isPending };
}

export const useUnAssingVehicleToCustomer = () => {
  const fetchClient = useFetchClient();

  const { mutate: unAssigVehicleToCustomer, isPending } = useMutation({
    mutationFn: async (id) => {
      return await fetchClient(`/v2/customer-of-customer-vehicle/unassigned/${id}`, {
        method: "PATCH"
      });
    },
  });
  return { unAssigVehicleToCustomer, isPending };
}