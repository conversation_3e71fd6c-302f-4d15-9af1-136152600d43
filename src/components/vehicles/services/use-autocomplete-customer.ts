"use client";

import { useQuery } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";

export function useGetCustomerAutoComplete() {
  const fetchClient = useFetchClient();

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["customer-autocomplete"],
    queryFn: async () => {
      const response = await fetchClient(
        `/v2/customer-of-customer/autocomplete`
      );
      return response.data;
    },
    enabled: false,
  });

  return {
    data,
    isLoading,
    refetch,
  };
}
