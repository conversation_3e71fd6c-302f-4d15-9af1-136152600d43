import { useState, useEffect, useRef } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { useFetchClient } from "@/utils/axios";

interface CommentModalProps {
  vehicleId: number;
  defaultValue: string;
  onSuccess: (newComment: string) => void;
  close: () => void;
}

export function CommentModal({
  vehicleId,
  defaultValue,
  onSuccess,
  close,
}: CommentModalProps) {
  const [comment, setComment] = useState(defaultValue || "");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const queryClient = useQueryClient();
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fetchClient = useFetchClient();

  useEffect(() => {
    textareaRef.current?.focus();
  }, []);

  const mutation = useMutation({
    mutationFn: async () => {
      if (!comment.trim()) {
        const message = "Comment cannot be empty.";
        setErrorMessage(message);
        toast.error(message);
        return;
      }
      setErrorMessage(null);
      const response = await fetchClient("/v2/vehicles/addVehicleComment", {
        data: {
          vehicle_id: vehicleId,
          comment: comment.trim(),
        },
        method: "PATCH",
      });

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["vehicleComments", vehicleId],
      });
      toast.success("Comment updated successfully!");
      onSuccess(comment.trim());
      close();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Something went wrong!");
    },
  });

  // Handle Enter key to submit
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      mutation.mutate();
    }
  };

  return (
    <div className="min-w-72 flex flex-col gap-3">
      <Textarea
        ref={textareaRef}
        placeholder="Type your message here..."
        value={comment}
        onChange={(e) => setComment(e.target.value)}
        onKeyDown={handleKeyDown}
      />
      {errorMessage && <p className="text-red-500 text-sm">{errorMessage}</p>}

      <div className="flex justify-end gap-2">
        <Button onClick={close} variant="outline">
          Cancel
        </Button>
        <Button
          type="button"
          onClick={() => mutation.mutate()}
          disabled={mutation.isPending || !comment.trim()}
        >
          {mutation.isPending ? "Saving..." : "Save changes"}
        </Button>
      </div>
    </div>
  );
}
