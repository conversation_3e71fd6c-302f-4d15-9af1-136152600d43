"use client";

import * as React from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Check, CircleChevronDown, Loader } from "lucide-react";
import { z } from "zod";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useDirection } from "@/hooks/useDirection";
import { useFetchClient } from "@/utils/axios";

const destinationSchema = z.object({
  selectedDestination: z.number().min(1, "Select a destination"),
});

type Destination = {
  id: number;
  name: string;
};

export function PODModal({
  vehicleId,
  defaultValue,
  onSuccess,
  close,
}: {
  vehicleId: number;
  defaultValue: string | null;
  onSuccess: (newDestination: string) => void;
  close: () => void;
}) {
  const queryClient = useQueryClient();
  const [selectedDestination, setSelectedDestination] = useState<number | null>(
    null
  );
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [open, setOpen] = useState(false);
  const fetchClient = useFetchClient();
  const { data: options = [], isLoading } = useQuery<Destination[]>({
    queryKey: ["get" + "destinations"],
    queryFn: async () => {
      const response = await fetchClient(`/v2/vehicles/destinations`);
      return response.data?.data || [];
    },
  });

  useEffect(() => {
    if (options.length > 0) {
      const defaultOption = options.find(
        (option) => option.name === defaultValue
      );
      if (defaultOption) {
        setSelectedDestination(defaultOption.id);
      } else if (selectedDestination === null) {
        setSelectedDestination(options[0].id);
      }
    }
  }, [options, defaultValue]);

  const mutation = useMutation({
    mutationFn: async () => {
      const validation = destinationSchema.safeParse({ selectedDestination });

      if (!validation.success) {
        setErrorMessage(validation.error.errors[0].message);
        toast.error(validation.error.errors[0].message);
        return;
      }
      setErrorMessage(null);

      const response = await fetchClient(`/v2/vehicles/editDestination`, {
        data: {
          vehicle_id: vehicleId,
          destination_id: selectedDestination as number,
        },
        method: "PATCH",
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["edit" + "destinations"] });
      toast.success("Destination updated successfully!");

      if (selectedDestination) {
        const selectedOption = options.find(
          (option) => option.id === selectedDestination
        );
        if (selectedOption) {
          onSuccess(selectedOption.name);
        }
      }

      close();
    },
    onError: (error) => {
      toast.error(
        error instanceof Error ? error.message : "Failed to update destination"
      );
    },
  });

  const handleSelect = (currentValue: number) => {
    setSelectedDestination(currentValue);
    setOpen(false);
  };
  const { isRTL } = useDirection();
  return (
    <div className="min-w-72 flex flex-col gap-5">
      {isLoading ? (
        <p className="flex items-center gap-2">
          <Loader className="animate-spin" />
        </p>
      ) : (
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className="w-full justify-between"
              dir={isRTL ? "rtl" : "ltr"}
            >
              {selectedDestination
                ? options.find((option) => option.id === selectedDestination)
                    ?.name
                : "Select an option..."}

              <CircleChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent dir={isRTL ? "rtl" : "ltr"}>
            <Command>
              <CommandInput placeholder="Search option..." />
              <CommandList>
                <CommandEmpty>No options found.</CommandEmpty>
                <CommandGroup>
                  {options.map((option) => (
                    <CommandItem
                      key={option.id}
                      onSelect={() => handleSelect(option.id)}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          selectedDestination === option.id
                            ? "opacity-100"
                            : "opacity-0"
                        )}
                      />
                      {option.name}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      )}

      {errorMessage && <p className="text-red-500 text-sm">{errorMessage}</p>}

      <div className="flex justify-end gap-2">
        <Button
          type="button"
          variant="outline"
          className="w-24"
          onClick={close}
        >
          Cancel
        </Button>

        <Button
          type="button"
          className="w-24"
          onClick={() => mutation.mutate()}
          disabled={mutation.isPending || !selectedDestination}
        >
          {mutation.isPending ? <Loader className="animate-spin" /> : "Save"}
        </Button>
      </div>
    </div>
  );
}
