import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { useFetchClient } from "@/utils/axios";

interface AddReceiverNameProps {
  vehicleId: number;
  defaultValue: string;
  onSuccess: (newReceiverName: string) => void;
  close: () => void;
}

export function AddReceiverName({
  vehicleId,
  defaultValue,
  onSuccess,
  close,
}: AddReceiverNameProps) {
  const [receiverName, setReceiverName] = useState(defaultValue || "");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const queryClient = useQueryClient();
  const fetchClient = useFetchClient();

  const mutation = useMutation({
    mutationFn: async () => {
      if (!receiverName.trim()) {
        setErrorMessage("Receiver name cannot be empty.");
        toast.error("Receiver name cannot be empty.");
        return;
      }

      setErrorMessage(null);

      return fetchClient("/v2/vehicles/addReceiverName", {
        data: {
          vehicle_id: vehicleId,
          receiver_name: receiverName.trim(),
        },
        method: "PATCH",
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["vehicleReceiver", vehicleId],
      });

      toast.success("Receiver Name Updated!");
      onSuccess(receiverName.trim());
      close();
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Something went wrong!");
    },
  });

  return (
    <div className="min-w-72 flex flex-col gap-3">
      <Input
        placeholder="Enter receiver name..."
        value={receiverName}
        onChange={(e) => setReceiverName(e.target.value)}
      />
      {errorMessage && <p className="text-red-500 text-sm">{errorMessage}</p>}

      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={close}>
          Cancel
        </Button>
        <Button
          type="button"
          onClick={() => mutation.mutate()}
          disabled={mutation.isPending || !receiverName.trim()}
        >
          {mutation.isPending ? "Saving..." : "Save changes"}
        </Button>
      </div>
    </div>
  );
}
