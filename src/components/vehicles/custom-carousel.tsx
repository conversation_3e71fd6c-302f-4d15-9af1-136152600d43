import React, { useState, useRef, useCallback } from "react";
import { toast } from "sonner";
import { Loader, CircleX } from "lucide-react";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import {
  Carousel,
  CarouselMainContainer,
  CarouselNext,
  CarouselPrevious,
  CarouselThumbsContainer,
  SliderMainItem,
} from "@/components/ui/extension/carousel";
import Image from "next/image";
import { useGetVehicleImages } from "../vehicles/vehicle-client-fetching";
import { useDownloadMultipleImages } from "@/hooks/use-download-mutiple-images";
import { useFetchClient } from "@/utils/axios";
import { getImageSizeUrl } from "@/utils/imageURL";
import ImageGallary from "@/components/Common_UI/image-gallary";
import ImagesInGoogleDriveRenderer from "./cell-renderers/imagesInGoogleDriveRenderer";
import { downloadGoogleDriveImages } from "@/hooks/use-download-image";
import SytemImages from "../shipments/cell-renderers/sytemImages";
import { cn } from "@/lib/utils";

interface ImageData {
  url: string;
  id: number;
  type?: string;
  name?: string;
}

interface PhotosProps {
  vehicleId: number;
  vin: string;
  isGoogleImages?: boolean;
  photosLink?: { auction?: string; photo?: string };
  isAuctionPhoto?: boolean;
  isShowTabs?: boolean;
  width?: string;
  height?: string;
  carouselHeight?: string;
  maxWidth?: string;
  url?: string;
}

const Photos = ({
  url = "/v2/vehicles/vehicleImages",
  vehicleId,
  vin,
  isGoogleImages = false,
  photosLink,
  isAuctionPhoto,
  isShowTabs,
  width = "18rem",
  height = "auto",
  carouselHeight = "260px",
  maxWidth = "18rem",
}: PhotosProps) => {
  const fetchClient = useFetchClient();
  const [openGallery, setOpenGallery] = useState<boolean>(false);
  const [isDownloading, setIsDownloading] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>("warehouse");
  const clickLock = useRef<boolean>(false);
  const GoogleDriveDownload =
    activeTab === "warehouse" && photosLink?.photo
      ? photosLink?.photo
      : activeTab === "auction" && photosLink?.auction
      ? photosLink.auction
      : undefined;

  const { images, isLoading, isError } = useGetVehicleImages({
    vehicleId: vehicleId,
    isGoogleDrive: isAuctionPhoto ?? false,
    url: url,
  });

  const {
    handleSelect,
    isZipping,
    handleDownloadAll,
    isZippingAll,
    handleDownload,
    selectedImages,
    setSelectedImages,
  } = useDownloadMultipleImages();

  const wareHouseImages: ImageData[] =
    images?.images?.filter((el: any) => el?.type === "warehouse") ?? [];
  const auctionImages: ImageData[] =
    images?.images?.filter((el: any) => el?.type === "auction") ?? [];
  const googleDriveFileIds: string[] =
    activeTab === "warehouse"
      ? images?.google_images.photo
      : images?.google_images.auction;

  // Handle gallery opening
  const handleGalleryOpen = useCallback(() => {
    if (!clickLock.current) {
      clickLock.current = true;
      setOpenGallery(true);
      setSelectedImages([]); // Reset selections when opening gallery

      // Reset lock after a short delay
      setTimeout(() => {
        clickLock.current = false;
      }, 300);
    }
  }, [setSelectedImages]);

  // Fixed download for Google Drive images
  const downloadGoogleDriveZip = async (fileIds: string[]) => {
    try {
      setIsDownloading(true);

      // Prepare the data for the API request
      const response = await fetchClient(
        "/v2/vehicles/google-drive/download-images",
        {
          data: { fileIds },
          responseType: "blob",
          method: "POST",
        }
      );

      // Create a URL for the blob and trigger download
      const blobUrl = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = blobUrl;
      link.download = `${vin || "vehicle"}-drive-images.zip`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      window.URL.revokeObjectURL(blobUrl);
      document.body.removeChild(link);

      toast.success("Google Drive images downloaded successfully!");
    } catch (error) {
      console.error("Failed to download Google Drive images:", error);
      toast.error("Failed to download Google Drive images");
    } finally {
      setIsDownloading(false);
    }
  };

  const handleGalleryDownload = async () => {
    // If there are selected images, download only those
    if (selectedImages.length > 0) {
      handleDownload();
      return;
    }

    const activeImages =
      activeTab === "warehouse" ? wareHouseImages : auctionImages;
    const relevantGoogleDriveFileIds =
      activeTab === "warehouse" && isGoogleImages
        ? images?.google_images.photo
        : activeTab === "auction" && isAuctionPhoto
        ? images?.google_images.auction
        : [];

    // Handle Google Drive folder download (from URL)
    if (isGoogleImages && GoogleDriveDownload) {
      try {
        setIsDownloading(true);

        // Extract folder ID from Google Drive link
        const match = GoogleDriveDownload?.match(/folders\/([^?]+)/);
        if (!match) {
          toast.error("Invalid Google Drive folder link");
          return;
        }

        const folderId = match[1];
        await downloadGoogleDriveImages(
          folderId,
          "/v2/vehicles/google-drive/download-images",
          `${vin}-images`,
          fetchClient
        );

        toast.success("Images downloaded successfully!");
      } catch (error) {
        console.error("Download failed:", error);
        toast.error("Failed to download Google Drive images");
      } finally {
        setIsDownloading(false);
      }
      return;
    }

    // Handle Google Drive file IDs download
    if (relevantGoogleDriveFileIds.length > 0) {
      await downloadGoogleDriveZip(relevantGoogleDriveFileIds);
      return;
    }

    // Handle system images download
    if (activeImages.length > 0) {
      handleDownloadAll(
        activeImages.map((image) =>
          getImageSizeUrl({ url: image?.url, size: 1024 })
        )
      );
    } else {
      toast.error("No images to download");
    }
  };

  const activeImages =
    activeTab === "warehouse" ? wareHouseImages : auctionImages;
  const showGoogleDriveImages =
    (activeTab === "warehouse" && isGoogleImages) ||
    (activeTab === "auction" &&
      isAuctionPhoto &&
      googleDriveFileIds.length > 0);

  // Render content for current tab - warehouse or auction
  const renderTabContent = useCallback(
    (tabType: "warehouse" | "auction") => {
      const currentImages =
        tabType === "warehouse" ? wareHouseImages : auctionImages;
      const shouldShowGoogleDrive =
        (tabType === "warehouse" && isGoogleImages) ||
        (tabType === "auction" &&
          isAuctionPhoto &&
          googleDriveFileIds.length > 0);

      return (
        <div className="flex flex-col gap-2">
          <Carousel
            className="p-1"
            style={{ maxWidth: maxWidth, width: width }}
            setApi={() => {}}
          >
            <CarouselNext />
            <CarouselPrevious />
            <div>
              <CarouselMainContainer>
                {isLoading ? (
                  <SliderMainItem className="bg-transparent">
                    <div
                      className="outline outline-1 w-full outline-border size-full flex items-center justify-center rounded-xl overflow-hidden bg-background"
                      style={{ height: carouselHeight }}
                    >
                      <Loader className="animate-spin" />
                    </div>
                  </SliderMainItem>
                ) : isError ? (
                  <SliderMainItem className="bg-transparent">
                    <div
                      className="outline outline-1 outline-border size-full flex items-center justify-center rounded-xl bg-background"
                      style={{ height: carouselHeight }}
                    >
                      <p className="text-red-500 flex items-center justify-center">
                        <CircleX className="w-6 h-6 mr-2" />
                        Error Loading Images
                      </p>
                    </div>
                  </SliderMainItem>
                ) : (
                  <>
                    {currentImages && currentImages.length > 0 ? (
                      currentImages.map((image: ImageData) => (
                        <SliderMainItem
                          key={image.id}
                          className="bg-transparent rounded-xl"
                        >
                          <div
                            className="outline outline-1 outline-border flex items-center justify-center rounded-xl overflow-hidden bg-background cursor-pointer"
                            style={{
                              maxWidth: `calc(${maxWidth} - 8px)`,
                              height: carouselHeight,
                            }}
                            onClick={handleGalleryOpen}
                          >
                            <AspectRatio
                              ratio={1}
                              className="bg-muted rounded-xl"
                            >
                              <Image
                                src={`${process.env.NEXT_PUBLIC_MINIO_ENDPOINT}${image.url}`}
                                fill
                                alt={`Image ${image.id}`}
                                className="rounded-md hover:cursor-pointer object-cover"
                                style={{ maxWidth: `calc(${maxWidth} - 8px)` }}
                              />
                            </AspectRatio>
                          </div>
                        </SliderMainItem>
                      ))
                    ) : shouldShowGoogleDrive &&
                      googleDriveFileIds.length > 0 ? (
                      googleDriveFileIds.map(
                        (fileId: string, index: number) => (
                          <SliderMainItem
                            key={index}
                            className="bg-transparent rounded-xl"
                          >
                            <div
                              className="outline outline-1 outline-border flex items-center justify-center rounded-xl overflow-hidden bg-background cursor-pointer h-full w-full"
                              style={{ height: carouselHeight }}
                              onClick={handleGalleryOpen}
                            >
                              <AspectRatio
                                ratio={1}
                                className="bg-muted rounded-xl"
                              >
                                <ImagesInGoogleDriveRenderer
                                  fileId={fileId}
                                  images={googleDriveFileIds}
                                  isInGallery={false}
                                  selectedItem={index}
                                  defaultHeight={carouselHeight}
                                />
                              </AspectRatio>
                            </div>
                          </SliderMainItem>
                        )
                      )
                    ) : (
                      <SliderMainItem className="bg-transparent">
                        <div
                          className="outline outline-1 outline-border size-full flex items-center justify-center rounded-xl bg-background"
                          style={{ height: carouselHeight }}
                        >
                          <AspectRatio ratio={1} className="bg-muted">
                            <Image
                              src="/placeholder1.jpg"
                              width={400}
                              height={200}
                              alt="Placeholder Image"
                              className="h-full w-full rounded-md object-cover"
                            />
                          </AspectRatio>
                        </div>
                      </SliderMainItem>
                    )}
                  </>
                )}
              </CarouselMainContainer>
              <div className="absolute bottom-2 left-1/2 -translate-x-1/2">
                <CarouselThumbsContainer className="gap-x-1" />
              </div>
            </div>
          </Carousel>
        </div>
      );
    },
    [
      wareHouseImages,
      auctionImages,
      isLoading,
      isError,
      googleDriveFileIds,
      isGoogleImages,
      isAuctionPhoto,
      handleGalleryOpen,
      width,
      maxWidth,
      carouselHeight,
    ]
  );

  return (
    <div style={{ width, height }}>
      <Tabs
        defaultValue="warehouse"
        className="p-1"
        style={{ width }}
        onValueChange={(value) => setActiveTab(value)}
      >
        {isShowTabs && (
          <TabsList className="min-w-full flex justify-between">
            <TabsTrigger value="warehouse" className="w-full">
              Warehouse
            </TabsTrigger>
            <TabsTrigger value="auction" className="w-full">
              Auction
            </TabsTrigger>
          </TabsList>
        )}

        <TabsContent value="warehouse">
          {renderTabContent("warehouse")}
        </TabsContent>
        <TabsContent value="auction">{renderTabContent("auction")}</TabsContent>
      </Tabs>

      {/* Image Gallery Modal */}
      <ImageGallary
        open={openGallery}
        onOpenChange={(open) => {
          setOpenGallery(open);
          if (!open) {
            clickLock.current = false;
          }
        }}
        title={`Images: ${vin}`}
        containerClass={
          isLoading ||
          isError ||
          (!activeImages.length && !googleDriveFileIds.length)
            ? "flex justify-center items-center min-h-[300px] sm:min-h-[400px] lg:min-h-[500px]"
            : cn(
                // Responsive grid layout
                "grid gap-2 sm:gap-3 lg:gap-4 overflow-auto",
                // Mobile: 2 columns
                "grid-cols-2",
                // Tablet: 3-4 columns
                "sm:grid-cols-3 md:grid-cols-4",
                // Desktop: 5-7 columns based on screen size
                "lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7",
                // Auto rows with responsive height
                "auto-rows-[120px] sm:auto-rows-[140px] md:auto-rows-[160px] lg:auto-rows-[180px]"
              )
        }
        parentClass=""
        handleDownload={handleGalleryDownload}
        isZippingAll={isZipping || isZippingAll || isDownloading}
      >
        {isLoading ? (
          <div className="flex justify-center items-center w-full h-full">
            <Loader className="animate-spin h-6 w-6 sm:h-8 sm:w-8" />
          </div>
        ) : (
          <>
            {/* System Images */}
            {activeImages &&
              activeImages.length > 0 &&
              activeImages.map((image: ImageData, index: number) => (
                <SytemImages
                  key={index}
                  image={image}
                  imagesArray={activeImages}
                  onSelectImage={(imageUrl) => handleSelect(imageUrl)}
                  title="Vehicle Images"
                  className="w-full h-full object-cover rounded-md transition-transform hover:scale-105"
                  clickedItem={index}
                />
              ))}

            {/* Google Drive Images */}
            {showGoogleDriveImages &&
              googleDriveFileIds.length > 0 &&
              googleDriveFileIds.map((fileId: string, index: number) => (
                <ImagesInGoogleDriveRenderer
                  key={index}
                  fileId={fileId}
                  images={googleDriveFileIds}
                  className="w-full h-full object-cover rounded-md hover:cursor-pointer transition-transform hover:scale-105"
                  isInGallery={true}
                  selectedItem={index}
                  defaultHeight="h-full"
                />
              ))}

            {/* Error or No Images */}
            {isError && (
              <div className="col-span-full flex justify-center items-center py-8 sm:py-12">
                <p className="font-semibold text-center text-sm sm:text-base text-red-600">
                  Error Loading Images
                </p>
              </div>
            )}

            {!activeImages?.length &&
              (!showGoogleDriveImages || !googleDriveFileIds.length) && (
                <div className="col-span-full flex justify-center items-center py-8 sm:py-12">
                  <p className="font-semibold text-center text-sm sm:text-base text-gray-500">
                    No Images Available
                  </p>
                </div>
              )}
          </>
        )}
      </ImageGallary>
    </div>
  );
};

export { Photos };
