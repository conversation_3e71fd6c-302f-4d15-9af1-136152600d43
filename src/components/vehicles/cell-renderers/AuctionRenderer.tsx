import CopartLogo from "@/components/Common_UI/coport-logo";
import IAAILogo from "@/components/Common_UI/iaai-logo";
import type { CustomCellRendererProps } from "ag-grid-react";
import { useState } from "react";
import { PaymentDialog } from "./AuctionPaymentDialog";

export const AuctionRenderer = ({ data }: CustomCellRendererProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 2,
    minimumFractionDigits: 0,
  });

  const totalPayments =
    data?.payments
      ?.map((payment: any) => Number(payment?.amount_applied) || 0)
      ?.reduce(
        (accumulator: any, current: any) => +accumulator + +current,
        0
      ) || 0;

  const vehiclePrice = data?.price || 0;
  const unPaidCal = Number(vehiclePrice) - Number(totalPayments);
  const unPaid = unPaidCal < 1 ? 0 : unPaidCal;

  const isPaidInFull = unPaid <= 0;

  const isCopart =
    data?.auction_name?.toLowerCase() === "copart" ||
    data?.auction_name?.toLowerCase() === "coport";

  return (
    <div className="flex flex-col justify-center items-center text-xs w-full h-full leading-5">
      <div className="mb-1">{isCopart ? <CopartLogo /> : <IAAILogo />}</div>

      {data?.account_owner?.trim().toLowerCase() === "pgl" ? (
        <div
          className="rounded-md flex leading-[22px] text-xs font-semibold overflow-hidden hover:cursor-pointer transition-all duration-200"
          onClick={() => setIsDialogOpen(true)}
        >
          <div
            className={`py-1 px-2 ${
              isPaidInFull
                ? "bg-green-600/10 text-green-500 text-xs dark:text-green-300"
                : "bg-red-600/10 text-red-500 text-xs dark:text-red-300"
            } flex items-center`}
          >
            {formatter.format(unPaid)}
          </div>
          <div className="py-1 px-2 bg-blue-600/10 text-xs text-blue-500 dark:text-blue-500 flex items-center">
            {formatter.format(vehiclePrice)}
          </div>
        </div>
      ) : (
        ""
      )}

      {/* Use the reusable PaymentDialog component */}
      <PaymentDialog
        isOpen={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        vehicleId={data?.id}
        vehicleData={data}
      />
    </div>
  );
};
