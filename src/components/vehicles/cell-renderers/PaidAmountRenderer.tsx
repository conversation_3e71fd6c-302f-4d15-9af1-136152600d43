import type { CustomCellRendererProps } from "ag-grid-react";
import { PaidAmountComponent } from "./PaidAmountForm";

export const PaidAmountRenderer = ({
  data,
  api,
  node,
}: CustomCellRendererProps) => {
  const handlePaymentUpdate = () => {
    // Refresh the ag-Grid row to update both paid and unpaid amounts
    if (api && node) {
      api.redrawRows({ rowNodes: [node] });
    }
  };

  const customerData =
    data?.vehicles_customer_of_customer?.at(0)?.customer_of_customer;
  const customerName = customerData?.fullname;

  return (
    <div className="flex flex-col justify-center items-center text-xs w-full h-full leading-5">
      <div className="flex items-center gap-1 mb-1">
        <PaidAmountComponent
          data={data}
          onPaymentUpdate={handlePaymentUpdate}
          showDialog={true}
          className={`${customerName ? "mt-4" : "mt-9"}`}
        />
      </div>
    </div>
  );
};
