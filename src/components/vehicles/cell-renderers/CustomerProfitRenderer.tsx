import { FunctionComponent } from "react";
import { CustomCellRendererProps } from "ag-grid-react";
import { CustomerProfitForm } from "./CustomerProfitForm"; // Adjust import path as needed

export const CustomerProfitRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data, api, node }) => {
  const handleValueChange = (newValue: number) => {
    if (data && node) {
      data.customer_profit = newValue;
      node.setData({ ...data, customer_profit: newValue });
    }

    if (api && node) {
      api.refreshCells({
        rowNodes: [node],
        force: true,
      });
    }
  };

  return (
    <CustomerProfitForm
      initialValue={data?.customer_profit ?? null}
      dataId={data.id}
      onValueChange={handleValueChange}
    />
  );
};
