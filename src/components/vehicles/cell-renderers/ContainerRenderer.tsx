import { Separator } from "@/components/ui/separator";
import type { CustomCellRendererProps } from "ag-grid-react";
import { formatDate } from "date-fns";
import { useTranslations } from "next-intl";
import { type FunctionComponent } from "react";

export const ContainerRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("datatable.body");
  return (
    <div className="flex flex-col justify-center h-full text-xs select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[35px]">{t("cn")}:</div>{" "}
        {data?.containers?.container_number}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[35px]">{t("etd")}:</div>{" "}
        {data?.containers?.bookings?.vessels?.etd
          ? formatDate(data?.containers?.bookings?.vessels?.etd, "yyyy MMM dd")
          : ""}
      </div>
      <Separator className="my-1" />
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[35px]">{t("eta")}:</div>{" "}
        {data?.containers?.bookings?.eta
          ? formatDate(data.containers?.bookings?.eta, "yyyy MMM dd")
          : ""}
      </div>
    </div>
  );
};
