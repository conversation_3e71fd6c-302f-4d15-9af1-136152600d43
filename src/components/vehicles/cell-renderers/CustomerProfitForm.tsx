import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CirclePlus, DollarSign, Loader2 } from "lucide-react";
import { useState, useEffect, forwardRef, useImperativeHandle } from "react";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useFetchClient } from "@/utils/axios";
import { toast } from "sonner";

const customerProfitSchema = z.object({
  customer_profit: z.coerce
    .number({
      required_error: "Customer profit is required",
      invalid_type_error: "Please enter a valid number",
    })
    .positive("Customer profit must be positive")
    .finite("Customer profit must be a finite number"),
});

type CustomerProfitFormData = z.infer<typeof customerProfitSchema>;

interface CustomerProfitFormProps {
  initialValue?: number | null;
  dataId: string | number;
  onValueChange?: (newValue: number) => void;
  className?: string;
  size?: "sm" | "default";
  variant?: "ghost" | "default" | "outline";
  showLabel?: boolean;
}

export interface CustomerProfitFormRef {
  getCurrentValue: () => number | null;
  updateValue: (value: number | null) => void;
}

export const CustomerProfitForm = forwardRef<
  CustomerProfitFormRef,
  CustomerProfitFormProps
>(
  (
    {
      initialValue,
      dataId,
      onValueChange,
      className = "",
      size = "sm",
      variant = "ghost",
      showLabel = true,
    },
    ref
  ) => {
    const t = useTranslations("customer-vehicles-datatable.customer-profit");
    const fetchClient = useFetchClient();
    const [customerProfit, setCustomerProfit] = useState<number | null>(
      initialValue ?? null
    );
    const [popover, setPopover] = useState<boolean>(false);
    const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
    const [submitSuccess, setSubmitSuccess] = useState<boolean>(false);

    const form = useForm<CustomerProfitFormData>({
      resolver: zodResolver(customerProfitSchema),
      defaultValues: {
        customer_profit: customerProfit || 0,
      },
    });

    useImperativeHandle(ref, () => ({
      getCurrentValue: () => customerProfit,
      updateValue: (value: number | null) => {
        setCustomerProfit(value);
        form.reset({ customer_profit: value || 0 });
      },
    }));

    useEffect(() => {
      if (initialValue !== undefined) {
        setCustomerProfit(initialValue);
      }
    }, [initialValue]);

    useEffect(() => {
      if (popover) {
        form.reset({
          customer_profit: customerProfit || 0,
        });
        setSubmitSuccess(false);
      }
    }, [popover, customerProfit, form]);

    useEffect(() => {
      if (submitSuccess) {
        setPopover(false);
        setSubmitSuccess(false);
      }
    }, [submitSuccess]);

    const onSubmit = async (values: CustomerProfitFormData) => {
      setIsSubmitting(true);

      try {
        const response = await fetchClient(
          `/v2/vehicles/customer-profit/${dataId}`,
          {
            method: "PATCH",
            data: { customer_profit: values.customer_profit },
          }
        );

        if (!response) {
          toast.error("Failed to update customer profit");
          return;
        }

        const newValue = values.customer_profit;
        setCustomerProfit(newValue);

        if (onValueChange) {
          onValueChange(newValue);
        }

        setSubmitSuccess(true);
        toast.success("Customer profit updated successfully");
      } catch (error: any) {
        form.setError("root", {
          type: "manual",
          message: error.message || "An unexpected error occurred",
        });
      } finally {
        setIsSubmitting(false);
      }
    };

    const handleCancel = () => {
      form.reset();
      setPopover(false);
    };

    const formatter = new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 2,
    });

    return (
      <div
        className={`flex flex-col justify-center h-full text-xs select-text leading-5 ${className}`}
      >
        <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap">
          <Popover open={popover} onOpenChange={setPopover}>
            <PopoverTrigger asChild>
              <Button
                size={size}
                variant={variant}
                className={`
                  text-left text-[11px] justify-start w-full h-8 px-0
                  ${
                    customerProfit
                      ? "text-primary hover:text-primary/80 font-medium"
                      : "text-muted-foreground hover:text-foreground"
                  }
                  transition-colors duration-200 
                `}
              >
                <div className="flex items-center">
                  {customerProfit ? (
                    <>
                      <span className="truncate">
                        {formatter.format(customerProfit)}
                      </span>
                    </>
                  ) : (
                    <>
                      <span className="flex items-center gap-1">
                        <CirclePlus />
                        {t("add-profit")}
                      </span>
                    </>
                  )}
                </div>
              </Button>
            </PopoverTrigger>

            <PopoverContent className="w-80 p-0" side="bottom" align="end">
              <div className="p-4 space-y-4">
                {showLabel && (
                  <>
                    <div className="space-y-1">
                      <h4 className="font-medium text-sm">{t("label")}</h4>
                      <p className="text-xs text-muted-foreground">
                        {t("description")}
                      </p>
                    </div>
                  </>
                )}

                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-4"
                  >
                    <FormField
                      control={form.control}
                      name="customer_profit"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-xs font-medium">
                            {t("profit-amount")}
                          </FormLabel>
                          <FormControl>
                            <div className="relative">
                              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                              <Input
                                {...field}
                                type="number"
                                min="0"
                                placeholder="0.00"
                                disabled={isSubmitting}
                                className="pl-9 text-sm"
                                autoComplete="off"
                                onChange={(e) => {
                                  const value =
                                    e.target.value === ""
                                      ? ""
                                      : parseFloat(e.target.value);
                                  field.onChange(value);
                                }}
                                value={field.value || ""}
                              />
                            </div>
                          </FormControl>
                          <FormMessage className="text-xs" />
                        </FormItem>
                      )}
                    />

                    {form.formState.errors.root && (
                      <Alert variant="destructive">
                        <AlertDescription className="text-xs">
                          {form.formState.errors.root.message}
                        </AlertDescription>
                      </Alert>
                    )}

                    <div className="flex justify-end gap-2 pt-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleCancel}
                        disabled={isSubmitting}
                        className="text-xs"
                      >
                        {t("cancel")}
                      </Button>
                      <Button
                        type="submit"
                        size="sm"
                        disabled={
                          isSubmitting ||
                          !form.formState.isValid ||
                          !form.watch("customer_profit")
                        }
                        className="text-xs min-w-[70px]"
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="h-3 w-3 animate-spin mr-1" />
                            {t("saving")}
                          </>
                        ) : (
                          t("save")
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>
    );
  }
);

CustomerProfitForm.displayName = "CustomerProfitForm";
