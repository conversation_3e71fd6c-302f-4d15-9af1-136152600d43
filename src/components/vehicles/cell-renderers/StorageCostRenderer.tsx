import { useState, FunctionComponent } from "react";
import { CustomCellRendererProps } from "ag-grid-react";
import { StorageCostForm } from "./StorageCostForm";
export const StorageCostRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data, api, node }) => {
  const [storageCost, setStorageCost] = useState<number | null>(
    data?.storage_charges ?? null
  );

  const handleStorageCostUpdate = (newValue: number) => {
    setStorageCost(newValue);
    if (data && node) {
      data.storage_charges = newValue;
      node.setData({ ...data, storage_charges: newValue });
      if (api) {
        api.refreshCells({
          rowNodes: [node],
          columns: ["storage_charges"],
          force: true,
        });
      }
    }
  };

  return (
    <div className="flex flex-col justify-center h-full text-xs select-text leading-5">
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <StorageCostForm
          storageCost={storageCost}
          itemId={data.id}
          trigger={
            <button className="text-xs text-right hover:text-green-600 transition-colors">
              {data?.storage_charges
                ? `${data.storage_charges.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}`
                : "Add Storage"}
            </button>
          }
          onStorageCostUpdate={handleStorageCostUpdate}
        />
      </div>
    </div>
  );
};
