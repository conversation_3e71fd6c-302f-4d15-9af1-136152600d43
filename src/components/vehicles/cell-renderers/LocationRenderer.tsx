import { PODModal } from "@/components/vehicles/dialog-modal/pod-modal";
import { AddReceiverName } from "@/components/vehicles/dialog-modal/add-reciver-name";
import { Separator } from "@/components/ui/separator";
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { MapPin, UserPlus } from "lucide-react";
import { useState, FunctionComponent } from "react";
import { useTranslations } from "next-intl";
import { CustomCellRendererProps } from "ag-grid-react";

export const LocationRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("datatable.body");

  // Destination state
  const [selectedDestinationName, setSelectedDestinationName] = useState<
    string | null
  >(data?.destinations?.name ?? null);
  const [destinationOpen, setDestinationOpen] = useState<boolean>(false);

  // Receiver state
  const [selectedReceiverName, setSelectedReceiverName] = useState<
    string | null
  >(data?.receiver_name ?? null);
  const [receiverOpen, setReceiverOpen] = useState<boolean>(false);

  return (
    <div className="flex flex-col justify-center h-full text-xs select-text leading-5">
      {/* From with tooltip */}
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap py-1 px-2">
        <div className="min-w-[60px]">{t("from")}:</div>
        {data?.pol_locations?.name ? (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="cursor-help font-medium">
                  {data.pol_locations.name}
                </div>
              </TooltipTrigger>
              <TooltipContent
                side="right"
                className="p-4 bg-popover border rounded-lg shadow-lg"
                sideOffset={5}
              >
                <div className="font-medium mb-2 pb-2 border-b">
                  {t("from")}
                </div>
                <div>{data.pol_locations.name}</div>
                {data.pol_locations.address && (
                  <div>{data.pol_locations.address}</div>
                )}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : (
          <div className="font-medium">-</div>
        )}
      </div>

      <Separator className="my-1" />

      {/* To */}
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[60px]">{t("to")}:</div>

        <Popover open={destinationOpen} onOpenChange={setDestinationOpen}>
          <PopoverTrigger asChild>
            <Button
              size="icon"
              variant="ghost"
              className="text-blue-500 -mt-2 text-left text-xs justify-start w-full"
            >
              {selectedDestinationName || <MapPin />}
            </Button>
          </PopoverTrigger>

          <PopoverContent className="w-70 min-h-32 flex text-xs flex-col gap-y-4">
            <h1 className="font-bold py-2">{t("add-destination")}</h1>
            <PODModal
              vehicleId={data?.id}
              defaultValue={selectedDestinationName}
              onSuccess={(newDestination) => {
                setSelectedDestinationName(newDestination);
                setDestinationOpen(false);
              }}
              close={() => setDestinationOpen(false)}
            />
          </PopoverContent>
        </Popover>
      </div>

      <Separator className="my-1" />

      {/* Receiver */}
      <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
        <div className="min-w-[60px]">{t("receiver")}:</div>

        <Popover open={receiverOpen} onOpenChange={setReceiverOpen}>
          <PopoverTrigger asChild>
            <Button
              size="icon"
              variant="ghost"
              className="text-blue-500 -mt-2 text-left text-xs justify-start w-full"
            >
              {selectedReceiverName || <UserPlus />}
            </Button>
          </PopoverTrigger>

          <PopoverContent className="w-70 min-h-32 flex text-xs flex-col gap-y-4">
            <h1 className="font-bold py-2">{t("add-receiver-name")}</h1>
            <AddReceiverName
              vehicleId={data?.id}
              defaultValue={selectedReceiverName || ""}
              onSuccess={(updatedName) => {
                setSelectedReceiverName(updatedName);
                setReceiverOpen(false);
              }}
              close={() => setReceiverOpen(false)}
            />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};
