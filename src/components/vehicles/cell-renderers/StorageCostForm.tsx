import { Separator } from "@/components/ui/separator";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CirclePlus, DollarSign, Loader2 } from "lucide-react";
import { useState, FunctionComponent, useEffect, ReactNode } from "react";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useFetchClient } from "@/utils/axios";
import { toast } from "sonner";

const StorageCostSchema = z.object({
  storage_charges: z.coerce
    .number({
      required_error: "storage amount is required",
      invalid_type_error: "Please enter a valid number",
    })
    .positive("storage amount must be positive")
    .finite("storage amount must be a finite number"),
});

type StorageCostFormData = z.infer<typeof StorageCostSchema>;

interface StorageCostFormProps {
  storageCost: number | null;
  itemId: string | number;
  onStorageCostUpdate?: (newValue: number) => void;
  trigger?: ReactNode;
  triggerClassName?: string;
  popoverSide?: "top" | "bottom" | "left" | "right";
  popoverAlign?: "start" | "center" | "end";
}

export const StorageCostForm: FunctionComponent<StorageCostFormProps> = ({
  storageCost,
  itemId,
  onStorageCostUpdate,
  trigger,
  triggerClassName = "",
  popoverSide = "bottom",
  popoverAlign = "start",
}) => {
  const t = useTranslations("customer-vehicles-datatable.storage-charge");
  const fetchClient = useFetchClient();
  const [currentStorageCost, setCurrentStorageCost] = useState<number | null>(
    storageCost
  );
  const [popover, setPopover] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitSuccess, setSubmitSuccess] = useState<boolean>(false);

  const form = useForm<StorageCostFormData>({
    resolver: zodResolver(StorageCostSchema),
    defaultValues: {
      storage_charges: currentStorageCost || 0,
    },
  });

  useEffect(() => {
    setCurrentStorageCost(storageCost);
  }, [storageCost]);

  useEffect(() => {
    if (popover) {
      form.reset({
        storage_charges: currentStorageCost || 0,
      });
      setSubmitSuccess(false);
    }
  }, [popover, currentStorageCost, form]);

  useEffect(() => {
    if (submitSuccess) {
      setPopover(false);
      setSubmitSuccess(false);
    }
  }, [submitSuccess]);

  const onSubmit = async (values: StorageCostFormData) => {
    setIsSubmitting(true);

    try {
      const response = await fetchClient(
        `/v2/vehicles/storage-charge/${itemId}`,
        {
          method: "PATCH",
          data: { storage_charges: values.storage_charges },
        }
      );
      if (!response) {
        toast.error("Failed to update storage amount");
        return;
      }
      setCurrentStorageCost(values.storage_charges);
      onStorageCostUpdate?.(values.storage_charges);
      setSubmitSuccess(true);
      toast.success("storage amount updated successfully");
    } catch (error: any) {
      form.setError("root", {
        type: "manual",
        message: error.message || "An unexpected error occurred",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.reset();
    setPopover(false);
  };

  const formatDisplayValue = (value: number | null) => {
    if (value === null || value === undefined) return null;
    return `${value.toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };

  const DefaultTrigger = () => (
    <Button
      size="sm"
      variant="ghost"
      className={`
        text-left text-xs justify-start w-full h-8 px-2
        ${
          currentStorageCost
            ? "text-green-600 hover:text-green-700 font-medium"
            : "text-muted-foreground hover:text-foreground"
        }
        transition-colors duration-200
        ${triggerClassName}
      `}
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
      }}
    >
      <div className="flex items-center  w-full">
        {currentStorageCost ? (
          <>
            <span className="truncate">
              {formatDisplayValue(currentStorageCost)}
            </span>
          </>
        ) : (
          <>
            <span className="flex items-center gap-1">
              <CirclePlus className="h-3 w-3" />
              {t("add-storage")}
            </span>
          </>
        )}
      </div>
    </Button>
  );

  return (
    <Popover open={popover} onOpenChange={setPopover}>
      <PopoverTrigger asChild>
        <div onClick={(e) => e.stopPropagation()}>
          {trigger || <DefaultTrigger />}
        </div>
      </PopoverTrigger>

      <PopoverContent
        className="w-80 p-0"
        side={popoverSide}
        align={popoverAlign}
      >
        <div className="p-4 space-y-4">
          <div className="space-y-1">
            <h4 className="font-medium text-sm">{t("label")}</h4>
            <p className="text-xs text-muted-foreground">{t("description")}</p>
          </div>

          <Separator />

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="storage_charges"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-xs font-medium">
                      {t("storage-amount")}
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          {...field}
                          type="number"
                          min="0"
                          step="0.01"
                          placeholder="0.00"
                          disabled={isSubmitting}
                          className="pl-9 text-sm"
                          autoComplete="off"
                          autoFocus
                          onChange={(e) => {
                            const value =
                              e.target.value === ""
                                ? ""
                                : parseFloat(e.target.value);
                            field.onChange(value);
                          }}
                          value={field.value || ""}
                        />
                      </div>
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              {form.formState.errors.root && (
                <Alert variant="destructive">
                  <AlertDescription className="text-xs">
                    {form.formState.errors.root.message}
                  </AlertDescription>
                </Alert>
              )}

              <div className="flex justify-end gap-2 pt-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                  disabled={isSubmitting}
                  className="text-xs"
                >
                  {t("cancel")}
                </Button>
                <Button
                  type="submit"
                  size="sm"
                  disabled={
                    isSubmitting ||
                    !form.formState.isValid ||
                    !form.watch("storage_charges")
                  }
                  className="text-xs min-w-[70px]"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-3 w-3 animate-spin mr-1" />
                      {t("saving")}
                    </>
                  ) : (
                    t("save")
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </PopoverContent>
    </Popover>
  );
};
