import type { CustomCellRendererProps } from "ag-grid-react";
import { type FunctionComponent } from "react";

export const ShippingCostRenderer: FunctionComponent<
  CustomCellRendererProps
> = ({ data }) => {
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 2,
  });
  const mixShippingData: any = data?.mix_shipping_vehicles?.[0] || {};
  const mixShippingCharges = mixShippingData.mix_shipping_vehicle_charges || [];
  const totalMixShippingCharges = mixShippingCharges.reduce(
    (sum: any, charge: any) => Number(sum) + (Number(charge.value) || 0),
    0
  );
  const transportationFee =
    (Number(mixShippingData.freight) || 0) +
    (Number(mixShippingData.vat_and_custom) || 0) +
    (Number(mixShippingData.tow_amount) || 0) +
    (Number(mixShippingData.clearance) || 0) +
    Number(totalMixShippingCharges);

  return (
    <div className="flex items-center h-full select-text">
      <div className="rounded-md px-2 flex leading-[22px] text-xs font-semibold overflow-hidden bg-green-500/10 text-green-500">
        {formatter.format(transportationFee)}
      </div>
    </div>
  );
};
