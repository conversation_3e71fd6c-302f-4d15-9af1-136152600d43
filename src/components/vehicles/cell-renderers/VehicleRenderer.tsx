import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import {
  ChevronRight,
  Hash,
  CarFront,
  Eye,
  EyeOff,
  CircleCheck,
  CircleX,
  Pencil,
  MapPin,
  UserPlus,
  MessageSquareText,
  Ship,
  Car,
  User,
  MoveLeft,
  DollarSign,
  CirclePlus,
} from "lucide-react";
import Image from "next/image";
import { useResponsive } from "@/hooks/use-mobile";
import { getImageSizeUrl, getGoogleDriveImageSizeUrl } from "@/utils/imageURL";
import { removeUnderScore } from "@/utils/commons";
import { colorSystem, ColorSystemKey } from "@/lib/constant";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";
import { useDirection } from "@/hooks/useDirection";
import { Link } from "@/i18n/routing";
import { formatDate } from "date-fns";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Checkbox } from "@/components/ui/checkbox";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
import { PODModal } from "../dialog-modal/pod-modal";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { AddReceiverName } from "../dialog-modal/add-reciver-name";
import { CommentModal } from "../dialog-modal/comment-modal";
import { formatDateFromNow } from "@/utils/calculate-age-at-pgl";
import {
  Drawer,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { useTranslations } from "next-intl";
import { StorageCostForm } from "./StorageCostForm";
import { CustomerProfitForm } from "./CustomerProfitForm";
import { PaidAmountComponent } from "./PaidAmountForm";
import { UnpaidAmountDisplay } from "./UnPaidAmountComponent";
import { useSession } from "next-auth/react";
import CustomDialog from "@/components/Common_UI/custom-dialog";
import CarouselComponent, {
  useDotButton,
} from "@/components/Common_UI/custom-carousel";
import { useGetVehicleImages } from "@/components/vehicles/vehicle-client-fetching";
import { SliderMainItem } from "@/components/ui/extension/carousel";
import ZoomImage from "@/components/Common_UI/zoom-image";
import {
  GoogleDriveImagesCarousel,
  GoogleDriveImagesThumbsCarousel,
} from "@/components/vehicles/vehicle-images-from-google-drive";
import CopartLogo from "@/components/Common_UI/coport-logo";
import IAAILogo from "@/components/Common_UI/iaai-logo";
import { PaymentDialog } from "./AuctionPaymentDialog";

export default function VehicleRenderer({ data, node, api, onEdit }: any) {
  const [isExpanded, setIsExpanded] = useState(node?.expanded || false);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const { isMobile } = useResponsive();
  const { isRTL } = useDirection();
  const fetchClient = useFetchClient();
  const queryClient = useQueryClient();
  const [checked, setChecked] = useState(data?.customer_checked || false);
  const session = useSession();
  const has_customer = session?.data?.profile?.companies?.has_customer;
  const t = useTranslations("datatable.vehicle-drawer");
  const [openCarousel, setOpenCarousel] = useState(false);
  const [currentImage, setCurrentImage] = useState("");
  const [downloadLink, setDownloadLink] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const isGoogleImages =
    data?.auction_photos_link?.includes("drive.google.com") ||
    data?.photo_link?.includes("drive.google.com")
      ? true
      : false;

  const [carouselApi, setCarouselApi] = useState();
  const { selectedIndex } = useDotButton(carouselApi);

  const { images } = useGetVehicleImages({
    vehicleId: data?.id,
    isGoogleDrive: isGoogleImages,
  });

  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 2,
  });
  useEffect(() => {
    if (node) {
      const expandListener = () => setIsExpanded(node.expanded);
      node.addEventListener("expandedChanged", expandListener);
      return () => node.removeEventListener("expandedChanged", expandListener);
    }
  }, [node]);

  const colors = colorSystem[data?.carstate as ColorSystemKey] || {
    bg: "bg-green-500/10",
    txt: "text-green-500",
  };

  const handleClick = (e: any) => {
    e.stopPropagation();
    if (isMobile) {
      setIsSheetOpen(true);
    } else {
      if (api && node) {
        api.setRowNodeExpanded(node, !node.expanded, true);
      }
    }
  };

  const handleImageClicked = (imageUrl: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (isMobile) {
      if (isGoogleImages && data?.vehicle_images?.length > 0) {
        window.open(data?.photo_link ?? data?.auction_photos_link, "_blank");
      } else {
        setOpenCarousel(true);
        setCurrentImage(imageUrl);
      }
    }
  };

  const handleCopyClick = () => {
    if (currentImage) {
      navigator.clipboard.writeText(
        getImageSizeUrl({ url: currentImage, size: 1024 })
      );
      toast.success("Image URL copied!");
    }
  };

  const handleDownloadLink = (imageUrl: string) => {
    setDownloadLink(imageUrl);
  };

  const singleDownload =
    images?.google_images.photo && images.images.length === 0
      ? getGoogleDriveImageSizeUrl({
          url: queryClient.getQueryData<string>([
            "vehicle-image",
            images?.google_images.photo[selectedIndex],
          ]),
        })
      : getImageSizeUrl({ url: downloadLink, size: 1024 });

  const mutation = useMutation({
    mutationFn: async (check: boolean) => {
      const response = await fetchClient(
        `/v2/vehicles/editCustomerChecked/${data?.id}`,
        {
          data: { customer_checked: check },
          method: "PATCH",
        }
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["id", data?.id],
      });
      toast.success("Vehicle Checked!");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Something went wrong!");
    },
  });
  const handleIsChecked = (value: boolean) => {
    mutation.mutate(value);
    setChecked(value);
  };

  // Destination state
  const [selectedDestinationName, setSelectedDestinationName] = useState<
    string | null
  >(data?.destinations?.name ?? null);
  const [destinationOpen, setDestinationOpen] = useState<boolean>(false);

  // Receiver state
  const [selectedReceiverName, setSelectedReceiverName] = useState<
    string | null
  >(data?.receiver_name ?? null);
  const [receiverOpen, setReceiverOpen] = useState<boolean>(false);

  // Comment state
  const [newComment, setNewComment] = useState<string | null>(
    data?.customer_comment ?? null
  );
  const [commentOpen, setCommentOpen] = useState<boolean>(false);

  //Shipping Cost

  const mixShippingData: any = data?.mix_shipping_vehicles?.[0] || {};
  const mixShippingCharges = mixShippingData.mix_shipping_vehicle_charges || [];
  const mixPayments = mixShippingData.payments || [];

  // Calculate total of mix_shipping_vehicle_charges
  const totalMixShippingCharges = mixShippingCharges.reduce(
    (sum: any, charge: any) => Number(sum) + (Number(charge.value) || 0),
    0
  );

  // Calculate Transportation Fee
  const transportationFee =
    (Number(mixShippingData.freight) || 0) +
    (Number(mixShippingData.vat_and_custom) || 0) +
    (Number(mixShippingData.tow_amount) || 0) +
    (Number(mixShippingData.clearance) || 0) +
    Number(totalMixShippingCharges);

  // Calculate discount
  const discount = data?.mix_shipping_vehicles?.[0]?.discount
    ? data?.mix_shipping_vehicles[0].discount
    : 0;

  const exchangeRate = data?.mix_shipping_vehicles?.[0]?.mix_shipping_invoices
    ? data?.mix_shipping_vehicles[0].mix_shipping_invoices.exchange_rate
    : 0;

  const isCalculatedDiscount = discount && exchangeRate > 0;
  const calculatedDiscount = Number(isCalculatedDiscount)
    ? Number(discount) / Number(exchangeRate)
    : 0;

  // Get invoice status and type to determine if accordion should be displayed
  const mixInvoiceStatus = mixShippingData?.mix_shipping_invoices?.status || "";
  const mixInvoiceType = mixShippingData?.mix_shipping_invoices?.type || "";

  const shouldDisplayAccordion =
    ["paid", "open", "past_due"].includes(mixInvoiceStatus.toLowerCase()) &&
    mixInvoiceType.toLowerCase() === "mix";

  // Calculate auction payments (from data.payments, not mix_shipping_vehicles payments)
  const auctionPayments =
    data?.payments
      ?.filter((payment: any) => payment.type === "auction")
      .reduce(
        (sum: any, payment: any) =>
          Number(sum) + (Number(payment.amount_applied) || 0),
        0
      ) || 0;

  // Calculate unpaid amount for auction
  const auctionUnpaidCalc =
    (Number(data?.price) || 0) - Number(auctionPayments);
  const auctionUnpaid = auctionUnpaidCalc < 1 ? 0 : auctionUnpaidCalc;

  // Calculate total payments for transportation
  const transportationPayments =
    mixPayments?.reduce(
      (sum: number, payment: any) =>
        Number(sum) + (Number(payment.amount_applied) || 0),
      0
    ) || 0;

  // Calculate unpaid amount for transportation
  const transportationUnpaid =
    Number(transportationFee) -
    Number(transportationPayments) -
    Number(calculatedDiscount);

  // Calculate TOTAL AMOUNT (Grand Total)
  const grandTotal = shouldDisplayAccordion
    ? Number(data?.price || 0) + Number(transportationFee)
    : Number(data?.price || 0);

  // Calculate TOTAL UNPAID
  const totalUnpaidCalc = shouldDisplayAccordion
    ? Number(auctionUnpaid) + Number(transportationUnpaid)
    : Number(auctionUnpaid);

  const totalUnpaid = totalUnpaidCalc < 1 ? 0 : totalUnpaidCalc;
  // Calculate grand total paid and unpaid
  const totalPaid = shouldDisplayAccordion
    ? Number(auctionPayments) + Number(transportationPayments)
    : Number(auctionPayments) || 0;

  //Storage Cost
  const handleStorageCostUpdate = (newValue: number) => {
    if (data && node) {
      data.storage_charges = newValue;
      node.setData({ ...data, storage_charges: newValue });

      if (api) {
        api.refreshCells({
          rowNodes: [node],
          columns: ["storage_charges"],
          force: true,
        });
      }
    }
  };
  const mapChargeToAllocationType = (charge: any) => {
    if (charge.key) {
      return charge.key;
    }

    const nameMapping: { [key: string]: string } = {
      freight: "freight",
      vat_and_custom: "vat_custom",
      tow_amount: "towing",
      clearance: "clearance",
    };

    return nameMapping[charge.name] || charge.name;
  };

  // Customer Profit
  const handleCustomerProfitChange = (newValue: number) => {
    if (data && node) {
      data.customer_profit = newValue;
      node.setData({ ...data, customer_profit: newValue });
      if (api) {
        api.refreshCells({
          rowNodes: [node],
          columns: ["customer_profit"],
          force: true,
        });
      }
    }
  };

  const chargesData = [
    // Standard charges
    {
      name: "Freight",
      amount: mixShippingData.freight || 0,
      type: "freight",
      key: "freight",
    },
    {
      name: "VAT and Custom",
      amount: mixShippingData.vat_and_custom || 0,
      type: "vat_custom",
      key: "vat_custom",
    },
    {
      name: "Tow Amount",
      amount: mixShippingData.tow_amount || 0,
      type: "towing",
      key: "towing",
    },
    {
      name: "Clearance",
      amount: mixShippingData.clearance || 0,
      type: "clearance",
      key: "clearance",
    },

    ...mixShippingCharges.map((charge: any) => ({
      name: charge.name
        .replace(/_/g, " ")
        .replace(/\b\w/g, (l: string) => l.toUpperCase()),
      amount: charge.value || 0,
      type: mapChargeToAllocationType(charge),
      key: charge.key || charge.name,
    })),
  ].filter((charge) => charge.amount > 0);

  const getPaymentForChargeType = (chargeType: string) => {
    let totalPayment = 0;
    mixPayments.forEach((payment: any) => {
      if (payment.payment_allocations) {
        payment.payment_allocations.forEach((allocation: any) => {
          if (allocation.type === chargeType) {
            totalPayment += Number(allocation.amount) || 0;
          }
        });
      }
    });
    return totalPayment;
  };

  const vehicleInformation = [
    {
      label: "",
      value: <></>,
    },
    {
      label: t("status"),
      value: (
        <div className="flex justify-end">
          <Badge
            className={`${colors.bg} ${colors.txt} border-none text-[8px] flex items-center gap-2`}
            variant="outline"
          >
            {data?.carstate === "shipped" && !data?.containers?.container_number
              ? "ON THE WAY"
              : removeUnderScore(data?.carstate)?.toUpperCase()}
          </Badge>
        </div>
      ),
    },
    {
      label: t("auction-name"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.auction_name || "."}
        </span>
      ),
    },
    {
      label: t("year"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.year}
        </span>
      ),
    },
    {
      label: t("make"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.make}
        </span>
      ),
    },
    {
      label: t("model"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.model}
        </span>
      ),
    },
    {
      label: t("color"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.color}
        </span>
      ),
    },
    {
      label: t("container-number"),
      value: (
        <span className="text-xs flex items-center gap-2 justify-end">
          {data?.containers?.container_number}
        </span>
      ),
    },
    {
      label: t("is-printed"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {" "}
          {data?.is_printed ? (
            <CircleCheck className="w-4 h-4 text-primary" />
          ) : (
            <CircleX className="w-4 h-4 text-red-600" />
          )}
        </span>
      ),
    },
    {
      label: t("is-key"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {" "}
          {data?.is_key_present ? (
            <CircleCheck className="w-4 h-4 text-primary" />
          ) : (
            <CircleX className="w-4 h-4 text-red-600" />
          )}
        </span>
      ),
    },
    {
      label: t("title-state"),
      value: (
        <span className="text-xs flex items-center justify-end gap-1">
          {data?.title_state ? (
            <CircleCheck className="w-4 h-4 text-primary" />
          ) : (
            <CircleX className="w-4 h-4 text-red-600" />
          )}
        </span>
      ),
    },
    {
      label: t("title-status"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {data?.title_status}
        </span>
      ),
    },
    {
      label: t("title-number"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {data?.title_number}
        </span>
      ),
    },
    {
      label: t("account-number"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {data?.buyer_number || "."}
        </span>
      ),
    },
    {
      label: t("age-at-pgl"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {data?.deliver_date && formatDateFromNow(data?.deliver_date)}
        </span>
      ),
    },
    {
      label: t("point-of-loading"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {data?.pol_locations?.name || "."}
        </span>
      ),
    },
    {
      label: t("point-of-destination"),
      value: (
        <Popover open={destinationOpen} onOpenChange={setDestinationOpen}>
          <div className="flex justify-end">
            <PopoverTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                className="text-blue-500 bg-blue-500/10 h-5 flex items-center gap-2 text-xs"
              >
                {selectedDestinationName || <MapPin />}
              </Button>
            </PopoverTrigger>
          </div>

          <PopoverContent className="w-70 min-h-32 flex text-xs flex-col gap-y-2 mr-5">
            <h1 className="font-bold py-2">Add Destination</h1>
            <PODModal
              vehicleId={data?.id}
              defaultValue={selectedDestinationName}
              onSuccess={(newDestination) => {
                setSelectedDestinationName(newDestination);
                setDestinationOpen(false);
              }}
              close={() => setDestinationOpen(false)}
            />
          </PopoverContent>
        </Popover>
      ),
    },
    {
      label: t("receiver-name"),
      value: (
        <Popover open={receiverOpen} onOpenChange={setReceiverOpen}>
          <div className="flex justify-end">
            <PopoverTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                className="text-blue-500 bg-blue-500/10 h-5 flex items-center gap-2 text-xs"
              >
                {selectedReceiverName || <UserPlus />}
              </Button>
            </PopoverTrigger>
          </div>

          <PopoverContent className="w-70  min-h-32 flex text-xs flex-col gap-y-2 mr-5">
            <h1 className="font-bold py-2">Add Receiver Name</h1>
            <AddReceiverName
              vehicleId={data?.id}
              defaultValue={selectedReceiverName || ""}
              onSuccess={(updatedName) => {
                setSelectedReceiverName(updatedName);
                setReceiverOpen(false);
              }}
              close={() => setReceiverOpen(false)}
            />
          </PopoverContent>
        </Popover>
      ),
    },
    {
      label: t("comment"),
      value: (
        <Popover open={commentOpen} onOpenChange={setCommentOpen}>
          <div className="flex justify-end">
            <PopoverTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                className="text-blue-500 bg-blue-500/10 h-5 flex items-center gap-2 text-xs"
              >
                <MessageSquareText />
              </Button>
            </PopoverTrigger>
          </div>

          <PopoverContent className="w-70  min-h-32 flex text-xs flex-col gap-y-2 mr-5">
            <h1 className="font-bold py-2">Add Your Comment</h1>
            <CommentModal
              vehicleId={data?.id}
              defaultValue={newComment || ""}
              onSuccess={(updatedComment) => {
                setNewComment(updatedComment);
                setCommentOpen(false);
              }}
              close={() => setCommentOpen(false)}
            />
          </PopoverContent>
        </Popover>
      ),
    },
    {
      label: t("check"),
      value: (
        <div className="flex items-center gap-2 justify-end text-xs px-2">
          <Checkbox
            className="w-3.5 h-3.5"
            checked={checked}
            onCheckedChange={handleIsChecked}
          />
        </div>
      ),
    },
    {
      label: t("auction-invoice-link"),
      value: (
        <div className="flex items-center gap-2 justify-end">
          <Link
            href={`${data?.auction_invoice || "#"}`}
            className="flex items-center justify-center "
            target="_blank"
          >
            {data?.auction_invoice ? (
              <Eye className="w-4 h-4 text-primary text-xs" />
            ) : (
              <EyeOff className="w-4 h-4 text-red-600 text-xs" />
            )}
          </Link>
        </div>
      ),
    },
    {
      label: "ETA",
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {data?.containers?.bookings?.eta &&
            formatDate(data?.containers?.bookings?.eta, "MMM d, yyyy")}{" "}
        </span>
      ),
    },
    {
      label: "ETD",
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {data?.containers?.bookings?.vessels?.etd &&
            formatDate(data?.containers?.bookings?.vessels?.etd, "MMM d, yyyy")}
        </span>
      ),
    },
    {
      label: t("purchased-date"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {data?.purchased_at && formatDate(data?.purchased_at, "MMM d, yyyy")}
        </span>
      ),
    },
    {
      label: t("title-received-date"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {data?.title_receive_date &&
            formatDate(data?.title_receive_date, "MMM d, yyyy")}
        </span>
      ),
    },
    {
      label: t("ship-date"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {data?.containers?.bookings?.vessels?.etd &&
            formatDate(data?.containers?.bookings?.vessels?.etd, "MMM d, yyyy")}
        </span>
      ),
    },
    {
      label: t("loading-date"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {data?.containers?.loading_date &&
            formatDate(data?.containers?.loading_date, "MMM d, yyyy")}
        </span>
      ),
    },
    {
      label: t("pick-up-date"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {data?.pickup_date && formatDate(data?.pickup_date, "MMM d, yyyy")}
        </span>
      ),
    },
    {
      label: t("delivery-date"),
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {data?.deliver_date && formatDate(data?.deliver_date, "MMM d, yyyy")}
        </span>
      ),
    },
  ];
  const createChargesRowsSimple = () => {
    const chargesRows = [];

    chargesRows.push({
      label: "Transportation Fee",
      value: (
        <span className="text-purple-500 bg-purple-500/10 rounded-md px-1 ">
          {formatter.format(transportationFee)}
        </span>
      ),
    });

    chargesData.forEach((charge) => {
      const paymentAmount = getPaymentForChargeType(charge?.type);
      const balance = Number(charge.amount) - Number(paymentAmount);

      chargesRows.push({
        label: charge?.name,
        value: (
          <div className="flex flex-col gap-1 items-end text-xs">
            <div className="rounded-md px-2 flex leading-[22px] text-xs font-semibold overflow-hidden bg-blue-500/10 text-blue-500">
              {formatter.format(charge.amount)}
            </div>
            <div className="text-[10px] text-muted-foreground">
              Paid: {formatter.format(paymentAmount)} Due:{" "}
              <span className={balance > 0 ? "text-red-500" : "text-green-500"}>
                {formatter.format(Math.max(0, balance))}
              </span>
            </div>
          </div>
        ),
      });
    });

    return chargesRows;
  };

  const financialInformation = [
    {
      label: "",
      value: <></>,
    },
    {
      label: "Vehicle Price",
      value: (
        <div className="flex flex-col gap-1 items-end text-xs">
          <div className="rounded-md px-2 flex leading-[22px] text-xs font-semibold overflow-hidden bg-blue-500/10 text-blue-500">
            {formatter.format(data?.price || 0)}
          </div>
          <div className="text-[10px] text-muted-foreground">
            Paid: {formatter.format(auctionPayments)} Due:{" "}
            <span
              className={
                auctionUnpaidCalc > 0 ? "text-red-500" : "text-green-500"
              }
            >
              {formatter.format(Math.max(0, auctionUnpaid))}
            </span>
          </div>
        </div>
      ),
    },

    ...createChargesRowsSimple(),
    {
      label: "Total Amount",
      value: (
        <span className="text-xs">
          {formatter.format(Math.max(0, grandTotal))}
        </span>
      ),
    },
    {
      label: "Paid Amount",
      value: (
        <span className="text-xs">
          {formatter.format(Math.max(0, totalPaid))}
        </span>
      ),
    },
    {
      label: "Due Balance",
      value: (
        <span className="text-xs">
          {formatter.format(Math.max(0, totalUnpaid))}
        </span>
      ),
    },
  ];
  const customerOfCustomer = [
    {
      label: "",
      value: <></>,
    },
    {
      label: "Customer Name",
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          {
            data?.vehicles_customer_of_customer?.at(0)?.customer_of_customer
              ?.fullname
          }
        </span>
      ),
    },
    {
      label: "Vehicle Price",
      value: (
        <span className="text-xs">{formatter.format(data?.price || 0)}</span>
      ),
    },
    {
      label: "Shipping Cost",
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          <div className="rounded-md px-2 flex leading-[22px] text-xs font-semibold overflow-hidden bg-green-500/10 text-green-500">
            {shouldDisplayAccordion && formatter.format(transportationFee)}
          </div>
        </span>
      ),
    },
    {
      label: "Storage Charge",
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          <StorageCostForm
            storageCost={data?.storage_charges ?? null}
            itemId={data?.id}
            onStorageCostUpdate={handleStorageCostUpdate}
            trigger={
              <button className="text-xs text-right hover:text-green-600 transition-colors">
                {data?.storage_charges ? (
                  `${data.storage_charges.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2,
                  })}`
                ) : (
                  <span className="flex items-center gap-1">
                    <CirclePlus className="h-4 w-4" />
                    Add Storage
                  </span>
                )}
              </button>
            }
            popoverSide="left"
            popoverAlign="end"
          />
        </span>
      ),
    },
    {
      label: "Customer Profit",
      value: (
        <span className="flex items-center justify-end text-xs">
          <CustomerProfitForm
            initialValue={data?.customer_profit ?? null}
            dataId={data.id}
            onValueChange={handleCustomerProfitChange}
            showLabel={true}
          />
        </span>
      ),
    },
    {
      label: "Total Amount",
      value: (
        <span className="text-xs">
          {formatter.format(
            Number(data?.price) +
              Number(data?.customer_profit) +
              Number(data?.storage_charges) +
              Number(shouldDisplayAccordion ? transportationFee : 0)
          )}
        </span>
      ),
    },
    {
      label: "Paid Amount",
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          <PaidAmountComponent
            data={data}
            onPaymentUpdate={() => {
              api?.refreshCells({
                rowNodes: [node],
                columns: ["paid_amount"],
                force: true,
              });
            }}
            showDialog={true}
            className=""
          />

          <button
            className="text-xs text-right hover:text-green-600 transition-colors"
            onClick={() => {
              onEdit(data);
            }}
          ></button>
        </span>
      ),
    },

    {
      label: "Balance",
      value: (
        <span className="flex items-center gap-2 justify-end text-xs">
          <UnpaidAmountDisplay data={data} />
        </span>
      ),
    },
  ];

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (data?.carstate === "added_by_customer") {
      if (onEdit) {
        onEdit(data);
      }
    } else {
      toast.error("Cannot edit this vehicle.");
    }
  };

  // Handle Auction Name
  const isCopart =
    data?.auction_name?.toLowerCase() === "copart" ||
    data?.auction_name?.toLowerCase() === "coport";

  // Handle Tracking Link
  const handleTracking = (
    shippingLine: string | undefined,
    containerNumber: string | undefined
  ) => {
    switch (shippingLine) {
      case "MAERSK":
        return `https://www.maersk.com/tracking/${containerNumber}`;
      case "ONE":
        return "https://ecomm.one-line.com/one-ecom/manage-shipment/cargo-tracking";
      case "MSC":
        return "https://www.msc.com/en/track-a-shipment?agencyPath=mwi";
      case "HMM":
        return "https://www.hmm21.com/e-service/general/trackNTrace/TrackNTrace.do";
      case "CMA CGM":
        return "https://www.cma-cgm.com/ebusiness/tracking";
      case "Hapag-Lloyd":
        return `https://www.hapag-lloyd.com/en/online-business/track/track-by-container-solution.html?container=${containerNumber}`;
      case "Yang Ming":
        return `https://e-solution.yangming.com/e-service/track_trace/track_trace_cargo_tracking.aspx`;
      case "Evergreen":
        return `https://ct.shipmentlink.com/servlet/TDB1_CargoTracking.do`;
      default:
        return "";
    }
  };
  const trackingUrl = handleTracking(
    data?.containers?.bookings?.vessels?.steamshiplines?.name,
    data?.containers?.container_number
  );

  const hasValidTracking = [
    "MAERSK",
    "ONE",
    "MSC",
    "HMM",
    "CMA CGM",
    "Hapag-Lloyd",
    "Yang Ming",
    "Evergreen",
  ].includes(data?.containers?.bookings?.vessels?.steamshiplines?.name || "");

  return (
    <>
      <div className="flex items-center h-full">
        <div
          className="grid grid-cols-12 gap-2 h-full hover:cursor-pointer group w-full"
          onClick={handleClick}
        >
          <div className="col-span-2 flex items-center justify-center">
            <Image
              src={`${
                data?.cover_photo
                  ? getImageSizeUrl({ url: data?.cover_photo, size: 250 })
                  : "/placeholder1.jpg"
              }`}
              alt={"sample"}
              height={isMobile ? 55 : 60}
              width={isMobile ? 55 : 60}
              className={`rounded-sm hover:cursor-pointer`}
              onClick={(e) => handleImageClicked(data?.cover_photo || "", e)}
            />
          </div>
          <div
            className={`${
              isMobile ? "col-span-6" : "col-span-8"
            } flex flex-col justify-center h-full select-text leading-5`}
          >
            <div className="flex items-center justify-start">
              <div
                className={`rounded-md px-1 sm:px-2 md:px-2 lg:px-2 flex items-center justify-center text-[10px] sm:text-xs md:text-xs lg:text-xs font-semibold ${colors.bg} ${colors.txt}`}
              >
                {data?.carstate === "shipped" &&
                !data?.containers?.container_number
                  ? "On Hand With Load"
                  : removeUnderScore(data?.carstate)}
              </div>
            </div>

            <div className="flex items-center sm:mb-1 md:mb-1 lg:mb-1">
              <div className="overflow-hidden text-ellipsis text-[10px] sm:text-xs md:text-xs lg:text-xs whitespace-nowrap px-1 sm:px-2 md:px-2 lg:px-2 sm:py-0.5 md:py-0.5 lg:py-0.5 bg-gray-50 dark:bg-gray-800 rounded">
                {data?.vin}
              </div>
            </div>

            {/* Vehicle Info Rows */}
            <div className="sm:space-y-0.5 md:space-y-0.5 lg:space-y-0.5">
              <p className="text-ellipsis whitespace-nowrap font-normal text-[10px] sm:text-xs md:text-xs lg:text-xs px-1 sm:px-2 md:px-2 lg:px-2 text-primary/70">
                {data?.year} {data?.make}
              </p>
              <p className="overflow-hidden text-ellipsis whitespace-nowrap font-normal text-[10px] sm:text-xs md:text-xs lg:text-xs px-1 sm:px-2 md:px-2 lg:px-2 text-primary/70">
                {data?.model} {data?.color}
              </p>
            </div>
          </div>

          <div
            className={`${
              isMobile ? "col-span-4" : "col-span-2"
            } flex flex-col items-center justify-center h-full gap-2`}
          >
            {isMobile && (
              <>
                <div className="flex items-center justify-end leading-5 w-full mb-1 gap-2">
                  <div className="flex items-center justify-center">
                    {isCopart ? (
                      <CopartLogo width="35px" height="30px" />
                    ) : (
                      <IAAILogo width="30px" height="30px" />
                    )}
                  </div>

                  {(() => {
                    const hasContainer =
                      data?.containers?.container_number != null &&
                      String(data?.containers?.container_number).trim() !== "";

                    const hasValidUrl =
                      trackingUrl != null && String(trackingUrl).trim() !== "";

                    const showTrackingLink =
                      hasContainer && hasValidUrl && hasValidTracking;

                    return (
                      <div className="flex-shrink-0 relative">
                        {showTrackingLink ? (
                          <Link href={trackingUrl} target="_blank">
                            <Button
                              variant="outline"
                              size="sm"
                              className="bg-blue-500/10 dark:bg-blue-500/10 text-blue-500 dark:text-blue-500 h-6 w-6"
                            >
                              <Ship className="w-3 h-3" />
                            </Button>
                            <div className="absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                          </Link>
                        ) : (
                          <div className="relative">
                            <Button
                              variant="outline"
                              size="sm"
                              className="bg-red-500/10 dark:bg-red-500/10 text-red-500 dark:text-red-500 h-6 w-6"
                              disabled
                            >
                              <Ship className="w-3 h-3 opacity-50" />
                            </Button>
                            <div className="absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </div>

                {/* Financial Info */}
                <div className="your-existing-vehicle-renderer-classes">
                  {data?.account_owner?.trim().toLowerCase() === "pgl" ? (
                    <div
                      className="rounded-md flex justify-end leading-[16px] text-[10px] sm:text-xs md:text-xs lg:text-xs font-semibold overflow-hidden hover:cursor-pointer transition-all duration-200 mr-auto"
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsDialogOpen(true);
                      }}
                    >
                      <div
                        className={`py-0 px-2 ${
                          totalUnpaid <= 0
                            ? "bg-green-600/10 text-green-500 text-[10px] sm:text-xs md:text-xs lg:text-xs dark:text-green-300"
                            : "bg-red-600/10 text-red-500 text-[10px] sm:text-xs md:text-xs lg:text-xs dark:text-red-300"
                        } flex items-center`}
                      >
                        {formatter.format(totalUnpaid)}
                      </div>
                      <div className="py-0 px-2 bg-blue-600/10 text-[10px] sm:text-xs md:text-xs lg:text-xs text-blue-500 dark:text-blue-500 flex items-center">
                        {formatter.format(grandTotal)}
                      </div>
                    </div>
                  ) : (
                    ""
                  )}
                </div>
              </>
            )}

            {data?.carstate === "added_by_customer" && isMobile && (
              <Button
                onClick={handleEdit}
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 min-w-0 min-h-0 hover:bg-primary/10"
                disabled={data.carstate !== "added_by_customer"}
              >
                <Pencil className="h-4 w-4" />
              </Button>
            )}

            {!isMobile && (
              <span
                className={`transition-transform duration-200 ${
                  isRTL
                    ? isExpanded
                      ? "rotate-90"
                      : "rotate-180"
                    : isExpanded
                    ? "rotate-90"
                    : "rotate-0"
                }`}
              >
                <ChevronRight className="w-4 h-4" />
              </span>
            )}
          </div>
        </div>
      </div>

      <CustomDialog
        openModal={openCarousel}
        setOpenModal={setOpenCarousel}
        title={`Images: ${data?.vin}`}
        handleCopyClick={handleCopyClick}
        downloadLink={singleDownload}
        isGoogleDrive={
          images?.google_images.photo && images?.google_images.photo.length > 0
        }
      >
        <>
          {images && images.images && images.images.length > 0 && (
            <CarouselComponent
              images={images.images}
              isGoogleDrive={false}
              onImageClick={handleDownloadLink}
              Component={ImagesCarousel}
              Thumbs={GoogleDriveImagesThumbsCarousel}
              selected={0}
              setApi={setCarouselApi}
            />
          )}
          {images?.google_images?.photo && images.images.length === 0 && (
            <CarouselComponent
              images={images?.google_images.photo}
              isGoogleDrive={true}
              Component={GoogleDriveImagesCarousel}
              Thumbs={GoogleDriveImagesThumbsCarousel}
              selected={0}
              setApi={setCarouselApi}
            />
          )}
        </>
      </CustomDialog>

      <Drawer open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <DrawerContent
          className="h-[100vh] rounded-t-3xl border-t-2 border-primary px-4 pb-8"
          dir={isRTL ? "rtl" : "ltr"}
        >
          <DrawerHeader className="sr-only">
            <DrawerTitle>Vehicle Details</DrawerTitle>
            <DrawerDescription>
              Detailed information about {data?.vin}
            </DrawerDescription>
          </DrawerHeader>
          <div className="flex flex-col h-full">
            <div className="px-1">
              <span
                className="text-xs text-primary hover:cursor-pointer"
                onClick={() => setIsSheetOpen(false)}
              >
                <MoveLeft />
              </span>
              <div className="flex items-center gap-2">
                <span className="font-semibold text-xs">
                  {data?.year} {data?.make} {data?.model} {data?.color}
                </span>
              </div>
              <div className="flex items-center gap-2 justify-between py-2">
                <span className="flex items-center text-xs text-primary">
                  <Hash className="w-4 h-4" />
                  {data?.lot_number}
                </span>
                <div className="flex items-center gap-2">
                  <CarFront className="w-4 h-4 text-primary" />
                  <span className="text-xs">{data?.vin}</span>
                </div>
              </div>
            </div>
            <div className="flex-1 overflow-y-auto space-y-4 rounded-md">
              {/* Vehicle Details Table */}
              <div className="rounded-lg overflow-hidden border">
                <Table>
                  <TableBody>
                    {vehicleInformation.map((row, index) => {
                      if (row.label === "") {
                        return (
                          <TableRow
                            key={row.label + index}
                            className="bg-primary/15"
                          >
                            <TableCell
                              colSpan={2}
                              className="py-2 px-4 text-center"
                            >
                              <div className="flex items-center gap-2 justify-center text-xs">
                                <h1 className="font-bold text-xs text-primary">
                                  Vehicle Information
                                </h1>
                                <Car className="w-4 h-4 text-primary" />
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      }

                      // Regular data rows
                      return (
                        <TableRow
                          key={row.label + index}
                          className={`${
                            index % 2 === 0 ? "bg-primary/5" : "bg-primary/10"
                          }`}
                        >
                          <TableCell className="py-1 px-1 text-xs w-full">
                            {row.label}
                          </TableCell>
                          <TableCell className="py-1 px-2 text-right text-xs w-full">
                            {row.value}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>

              {/* Financial Information Table */}

              {shouldDisplayAccordion && (
                <div className="rounded-lg overflow-hidden border">
                  <Table>
                    <TableBody>
                      {financialInformation.map((row, index) => {
                        if (row.label === "") {
                          return (
                            <TableRow
                              key={row.label + index}
                              className="bg-primary/15"
                            >
                              <TableCell
                                colSpan={2}
                                className="py-2 px-4 text-center"
                              >
                                <div className="flex items-center gap-2 justify-center text-xs">
                                  <h1 className="font-bold text-xs text-primary">
                                    Finance Information
                                  </h1>
                                  <DollarSign className="w-4 h-4 text-primary" />
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        }

                        // Regular data rows
                        return (
                          <TableRow
                            key={row.label + index}
                            className={`${
                              index % 2 === 0 ? "bg-primary/5" : "bg-primary/10"
                            }`}
                          >
                            <TableCell className="py-1 px-1 text-xs w-full">
                              <span
                                className={
                                  row.label.startsWith("  └")
                                    ? "text-muted-foreground text-xs"
                                    : ""
                                }
                              >
                                {row.label}
                              </span>
                            </TableCell>
                            <TableCell className="py-1 px-2 text-right text-xs w-full">
                              {row.value}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              )}

              {/* Customer Information Table And Only show if has_customer is true */}
              {has_customer && (
                <div className="rounded-lg overflow-hidden border">
                  <Table>
                    <TableBody>
                      {customerOfCustomer.map((row, index) => {
                        if (row.label === "") {
                          return (
                            <TableRow
                              key={row.label + index}
                              className="bg-primary/15"
                            >
                              <TableCell
                                colSpan={2}
                                className="py-2 px-4 text-center"
                              >
                                <div className="flex items-center gap-2 justify-center text-xs">
                                  <h1 className="font-bold text-xs text-primary">
                                    Customer Information
                                  </h1>
                                  <User className="w-4 h-4 text-primary" />
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        }

                        // Regular data rows
                        return (
                          <TableRow
                            key={row.label + index}
                            className={`${
                              index % 2 === 0 ? "bg-primary/5" : "bg-primary/10"
                            }`}
                          >
                            <TableCell className="py-1 px-1 text-xs w-full">
                              {row.label}
                            </TableCell>
                            <TableCell className="py-1 px-2 text-right text-xs w-full">
                              {row.value}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
          </div>
        </DrawerContent>
      </Drawer>
      <PaymentDialog
        isOpen={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        vehicleId={data?.id}
        vehicleData={data}
      />
    </>
  );
}

function ImagesCarousel({
  img,
  onImageClick,
}: {
  img: any;
  onImageClick?: (imageUrl: string) => void;
}) {
  return (
    <SliderMainItem
      className="bg-transparent w-full h-full"
      onMouseEnter={() => onImageClick?.(img.url || "")}
    >
      <div
        tabIndex={0}
        className=" flex items-center justify-center h-full w-full"
      >
        <ZoomImage file={img} isGoogleDrive={false} />
      </div>
    </SliderMainItem>
  );
}
