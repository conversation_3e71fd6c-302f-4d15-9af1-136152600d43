import React, { useState } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { Info } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { But<PERSON> } from "@/components/ui/button";
import CarouselComponent, {
  useDotButton,
} from "../../Common_UI/custom-carousel";
import CustomDialog from "@/components/Common_UI/custom-dialog";
import { getGoogleDriveImageSizeUrl } from "@/utils/imageURL";
import { useTranslations } from "next-intl";
import { useGetVehicleImageFromGoogleDrive } from "../vehicle-client-fetching";
import { SliderMainItem } from "@/components/ui/extension/carousel";
import ZoomImage from "@/components/Common_UI/zoom-image";
import { cn } from "@/lib/utils";
import { useQueryClient } from "@tanstack/react-query";

interface ImagesInGoogleDriveRendererProps {
  fileId: string;
  images: string[];
  className?: string;
  isInGallery?: boolean;
  selectedItem: number;
  defaultHeight: string;
}

export default function ImagesInGoogleDriveRenderer({
  fileId,
  images,
  className,
  isInGallery = false,
  selectedItem,
  defaultHeight,
}: ImagesInGoogleDriveRendererProps) {
  const {
    data: imageUrl,
    isLoading,
    error,
  } = useGetVehicleImageFromGoogleDrive(fileId);

  const [openCarousel, setOpenCarousel] = useState(false);
  const [downloadLink, setDownloadLink] = useState("");
  const [api, setApi] = useState();
  const { selectedIndex } = useDotButton(api);
  const queryClient = useQueryClient();


  const handleCopyLink = (imageUrl: string) => {
    console.warn(downloadLink);
    setDownloadLink(imageUrl);
  };

  // Always open carousel with a single click when in gallery view
  const handleClick = () => {
    if (isInGallery) {
      setOpenCarousel(true);
    }
  };

  if (error)
    return (
      <div className="flex justify-center items-center w-full h-full">
        <Skeleton className="h-full w-full rounded-md">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon">
                  <Info />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Error loading image</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </Skeleton>
      </div>
    );

  if (isLoading)
    return (
      <div
        className={`flex justify-center items-center w-full ${cn(
          "",
          defaultHeight
        )}`}
      >
        <Skeleton className="h-full w-full" />
      </div>
    );

  return (
    <>
      <div
        className={
          className ||
          "w-full h-full flex justify-center items-center border rounded-md"
        }
        onClick={handleClick}
      >
        <img
          src={imageUrl || ""}
          alt="shipment"
          className="rounded-sm object-cover h-full w-full"
        />
      </div>
      {openCarousel && (
        <CustomDialog
          openModal={openCarousel}
          setOpenModal={() => setOpenCarousel(false)}
          title={"Vehicle Images"}
          isGoogleDrive={true}
          downloadLink={getGoogleDriveImageSizeUrl({
            url: queryClient.getQueryData<string>(["vehicle-image", images[selectedIndex]]) || imageUrl,
            size: 1024,
          })}
        >
          <CarouselComponent
            images={images}
            isGoogleDrive={true}
            onImageClick={handleCopyLink}
            Component={GoogleDriveImagesCarousel}
            Thumbs={GoogleDriveImagesThumbsCarousel}
            selected={selectedItem}
            setApi={setApi}
          />
        </CustomDialog>
      )}
    </>
  );
}

function GoogleDriveImagesCarousel({
  fileId,
  index,
}: {
  fileId: string;
  index: number;
}) {
  const {
    data: imageUrl,
    isLoading,
    error,
  } = useGetVehicleImageFromGoogleDrive(fileId);
  const t = useTranslations("shipment-datatable");

  if (isLoading)
    return (
      <div className="relative flex justify-center items-center h-[100vh] w-[100vw]">
        <Skeleton className="min-w-[80vw] min-h-[90vh] flex justify-center items-center" />
      </div>
    );

  if (error)
    return (
      <div className="relative flex justify-center items-center h-[100vh] w-[100vw]">
        {t("shipment_gallary.error_images_title")}
      </div>
    );

  return (
    <SliderMainItem className="bg-transparent w-full h-full">
      <div
        tabIndex={0}
        className="flex items-center justify-center h-full w-full"
      >
        <ZoomImage
          file={{
            url: imageUrl || "#",
            id: `${index}`,
            name: `${imageUrl}` + index,
          }}
          isGoogleDrive={true}
        />
      </div>
    </SliderMainItem>
  );
}

export function GoogleDriveImagesThumbsCarousel({
  fileId,
  index,
}: {
  fileId: string;
  index: number;
}) {
  const {
    data: imageUrl,
    isLoading,
    error,
  } = useGetVehicleImageFromGoogleDrive(fileId);
  const t = useTranslations("shipment-datatable");

  if (isLoading)
    return (
      <div className="relative flex justify-center items-center h-[100vh] w-[100vw]">
        <Skeleton className="min-w-[80vw] min-h-[90vh] flex justify-center items-center" />
      </div>
    );

  if (error)
    return (
      <div className="relative flex justify-center items-center h-[100vh] w-[100vw]">
        {t("shipment_gallary.error_images_title")}
      </div>
    );

  return (
    <img
      src={
        getGoogleDriveImageSizeUrl({
          url: imageUrl,
          size: 1024,
        }) || "/placeholder.svg"
      }
      alt={`Vehicle Image ${index}`}
      className="object-cover h-full min-w-full w-max overflow-hidden"
    />
  );
}
