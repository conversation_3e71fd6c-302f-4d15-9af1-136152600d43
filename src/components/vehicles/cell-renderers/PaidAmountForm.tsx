import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  DollarSign,
  CreditCard,
  Plus,
  Loader2,
  List,
  CirclePlus,
} from "lucide-react";
import { useState, useEffect, useCallback, useMemo } from "react";
import { useFetchClient } from "@/utils/axios";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";

const paymentSchema = z.object({
  payment_amount: z.coerce
    .number({
      required_error: "Payment amount is required",
      invalid_type_error: "Please enter a valid number",
    })
    .positive("Payment amount must be greater than 0")
    .finite("Payment amount must be a finite number"),
  payment_date: z
    .string()
    .min(1, "Payment date is required")
    .transform((val) => val.split("T")[0]),
});

type PaymentFormData = z.infer<typeof paymentSchema>;

interface Payment {
  id: number;
  payment_amount: number;
  payment_date: string;
  created_at: string;
}

interface CustomerData {
  customerId: number | string;
  vehicleId: number | string;
  customerName: string;
  vehiclePrice: number;
}

interface PaidAmountComponentProps {
  data: any;
  onPaymentUpdate?: () => void;
  showDialog?: boolean;
  className?: string;
}

export const PaidAmountComponent = ({
  data,
  onPaymentUpdate,
  showDialog = true,
  className = "",
}: PaidAmountComponentProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("add-customer-payment");
  const [isLoadingPayments, setIsLoadingPayments] = useState(false);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const t = useTranslations("customer_of_customer_payments");

  const formatter = useMemo(
    () =>
      new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
        maximumFractionDigits: 2,
        minimumFractionDigits: 0,
      }),
    []
  );

  const fetchClient = useFetchClient();

  const customerData = useMemo((): CustomerData => {
    const customerId =
      data?.vehicles_customer_of_customer?.at(0)?.customer_of_customer?.id;
    const vehicleId = data?.id;
    const customerName =
      data?.vehicles_customer_of_customer?.at(0)?.customer_of_customer
        ?.fullname;
    const vehiclePrice = (data?.price || 0) + (data?.customer_profit || 0);

    return {
      customerId,
      vehicleId,
      customerName,
      vehiclePrice,
    };
  }, [data]);

  const form = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      payment_amount: 0,
      payment_date: new Date().toISOString().split("T")[0],
    },
  });

  const calculations = useMemo(() => {
    const totalPaid = payments.reduce(
      (sum, payment) => sum + parseFloat(payment.payment_amount.toString()),
      0
    );
    const mixShippingData: any = data?.mix_shipping_vehicles?.[0] || {};
    const mixShippingCharges =
      mixShippingData.mix_shipping_vehicle_charges || [];
    const totalMixShippingCharges = mixShippingCharges.reduce(
      (sum: any, charge: any) => Number(sum) + (Number(charge.value) || 0),
      0
    );
    const transportationFee =
      (Number(mixShippingData.freight) || 0) +
      (Number(mixShippingData.vat_and_custom) || 0) +
      (Number(mixShippingData.tow_amount) || 0) +
      (Number(mixShippingData.clearance) || 0) +
      Number(totalMixShippingCharges);

    const vehiclePrice = data?.price || 0;
    const customerProfit = data?.customer_profit || 0;
    const totalCharges = vehiclePrice + customerProfit + transportationFee;
    const unPaid = totalCharges - totalPaid;

    return { totalPaid, unPaid };
  }, [payments, customerData.vehiclePrice, data]);

  const fetchPayments = useCallback(async () => {
    if (!customerData.customerId || !customerData.vehicleId) return;

    setIsLoadingPayments(true);
    try {
      const response = await fetchClient(
        `/v2/payments-v2/get-one-vehicle-payments`,
        {
          method: "GET",
          params: {
            vehicle_id: customerData.vehicleId,
          },
        }
      );
      if (response.data?.result && response.data?.data) {
        setPayments(response.data.data);
      }
    } catch (error) {
      toast.error("Error fetching payments" + error);
      setPayments([]);
    } finally {
      setIsLoadingPayments(false);
    }
  }, [customerData.customerId, customerData.vehicleId, fetchClient]);

  const onSubmit = useCallback(
    async (values: PaymentFormData) => {
      if (!customerData.customerId || !customerData.vehicleId) {
        toast.error("Missing customer or vehicle information");
        return;
      }

      setIsSubmitting(true);

      try {
        const paymentDateTime = new Date(
          values.payment_date + "T00:00:00.000Z"
        ).toISOString();

        const requestData = {
          method: "POST",
          params: {
            customer_id: parseInt(customerData.customerId.toString()),
            vehicle_id: parseInt(customerData.vehicleId.toString()),
          },
          data: {
            payment_amount: parseFloat(values.payment_amount.toString()),
            payment_date: paymentDateTime,
          },
        };

        const response = await fetchClient(
          `/v2/payments-v2/customer-of-customer-payments`,
          requestData
        );

        if (response.data?.result) {
          toast.success("Payment added successfully!");
          form.reset();
          setIsDialogOpen(false);

          // Refresh payments list
          await fetchPayments();

          // Call parent callback if provided
          if (onPaymentUpdate) {
            onPaymentUpdate();
          }
        } else {
          toast.error(response.data?.error || "Failed to add payment");
        }
      } catch (error: any) {
        form.setError("root", {
          type: "manual",
          message:
            error.response?.data?.message || "An unexpected error occurred",
        });
        toast.error("Failed to add payment");
      } finally {
        setIsSubmitting(false);
      }
    },
    [customerData, fetchClient, form, fetchPayments, onPaymentUpdate]
  );

  useEffect(() => {
    fetchPayments();
  }, [fetchPayments]);

  const PaidAmountDisplay = () => (
    <div
      className={`${className} ${
        showDialog ? "hover:cursor-pointer" : ""
      } transition-all duration-200`}
      onClick={showDialog ? () => setIsDialogOpen(true) : undefined}
    >
      <div className="rounded-md flex leading-[22px] text-xs font-semibold overflow-hidden">
        <div className="py-1 px-2 bg-blue-600/10 text-xs text-blue-500 dark:text-blue-500 flex items-center">
          {formatter.format(calculations.totalPaid)}
        </div>
      </div>
    </div>
  );

  if (!showDialog) {
    return <PaidAmountDisplay />;
  }

  return (
    <>
      <PaidAmountDisplay />

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[650px] p-0 h-[500px] max-h-[700px] min-h-[500px]">
          <DialogHeader className="px-6 flex justify-between items-center flex-row h-[60px] min-h-[60px]">
            <DialogTitle className="text-xl flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-primary" />
              {t("title")}
            </DialogTitle>
          </DialogHeader>

          <div
            className="px-6 pb-3 overflow-y-auto flex-1"
            style={{ height: "540px", maxHeight: "540px", minHeight: "540px" }}
          >
            <Tabs
              defaultValue="add-customer-payment"
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid grid-cols-2 mb-4">
                <TabsTrigger
                  value="add-customer-payment"
                  className="flex items-center gap-1 data-[state=active]:text-primary"
                >
                  <Plus className="h-4 w-4" />
                  {t("tabs.add-payment")}
                </TabsTrigger>
                <TabsTrigger
                  value="list-customer-payment"
                  className="flex items-center gap-1 data-[state=active]:text-primary"
                >
                  <CreditCard className="h-4 w-4" />
                  {t("tabs.payment-history")}
                </TabsTrigger>
              </TabsList>

              <TabsContent value="add-customer-payment" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <CirclePlus className="h-5 w-5 text-primary" />
                      {t("add-payment.title")}
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      {t("add-payment.customer")}: {customerData.customerName}
                    </p>
                  </CardHeader>
                  <CardContent>
                    <Form {...form}>
                      <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className="space-y-4"
                      >
                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="payment_amount"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  {t("add-payment.payment-amount")} ($)
                                </FormLabel>
                                <FormControl>
                                  <div className="relative">
                                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                    <Input
                                      {...field}
                                      type="number"
                                      step="0.01"
                                      min="0"
                                      placeholder="0.00"
                                      disabled={isSubmitting}
                                      className="pl-9"
                                      onChange={(e) => {
                                        const value =
                                          e.target.value === ""
                                            ? ""
                                            : parseFloat(e.target.value);
                                        field.onChange(value);
                                      }}
                                      value={field.value || ""}
                                    />
                                  </div>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="payment_date"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>
                                  {t("add-payment.payment-date")}
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    type="date"
                                    disabled={isSubmitting}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        {form.formState.errors.root && (
                          <Alert variant="destructive">
                            <AlertDescription>
                              {form.formState.errors.root.message}
                            </AlertDescription>
                          </Alert>
                        )}

                        <div className="pt-4">
                          <Button
                            type="submit"
                            className="w-full"
                            disabled={isSubmitting || !form.formState.isValid}
                          >
                            {isSubmitting ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                {t("add-payment.add-payment")}
                              </>
                            ) : (
                              <>
                                <Plus className="mr-2 h-4 w-4" />
                                {t("add-payment.add-payment")}
                              </>
                            )}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="list-customer-payment" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <List className="h-5 w-5 text-primary" />
                      {t("payment-history.title")}
                    </CardTitle>
                    {payments.length > 0 && (
                      <div className="flex justify-between text-sm text-muted-foreground">
                        <span>
                          {t("payment-history.total-payment")}:{" "}
                          {payments.length}
                        </span>
                        <span>
                          {t("payment-history.total-paid")}:{" "}
                          {formatter.format(calculations.totalPaid)}
                        </span>
                      </div>
                    )}
                  </CardHeader>
                  <CardContent>
                    {isLoadingPayments ? (
                      <div className="flex items-center justify-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin mr-2" />
                        {t("loading")}
                      </div>
                    ) : payments.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        <CreditCard className="h-12 w-12 mx-auto mb-2 opacity-50" />
                        <p>{t("no-data")}</p>
                      </div>
                    ) : (
                      <div className="border rounded-md overflow-hidden">
                        <div className="bg-secondary">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead className="h-10 w-1/3">
                                  {t("payment-history.payment-date")}
                                </TableHead>
                                <TableHead className="h-10 w-1/3">
                                  {t("payment-history.amount")}
                                </TableHead>
                                <TableHead className="h-10 w-1/3">
                                  {t("payment-history.created-at")}
                                </TableHead>
                              </TableRow>
                            </TableHeader>
                          </Table>
                        </div>

                        <div className="max-h-[150px] overflow-y-auto">
                          <Table>
                            <TableBody>
                              {payments.map((payment, index) => (
                                <TableRow
                                  key={payment.id ?? `payment-${index}`}
                                >
                                  <TableCell className="py-3 w-1/3">
                                    {new Date(
                                      payment.payment_date
                                    ).toLocaleDateString()}
                                  </TableCell>
                                  <TableCell className="font-medium py-3 w-1/3">
                                    {formatter.format(
                                      Number(payment.payment_amount)
                                    )}
                                  </TableCell>
                                  <TableCell className="text-muted-foreground py-3 w-1/3">
                                    {new Date(
                                      payment.created_at
                                    ).toLocaleDateString()}{" "}
                                    {new Date(
                                      payment.created_at
                                    ).toLocaleTimeString()}
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
