"use client";
import { Separator } from "@/components/ui/separator";
import { CustomCellRendererProps } from "ag-grid-react";
import { Eye, Link2, MessageSquareText } from "lucide-react";
import React, { FunctionComponent, useState } from "react";
import { CommentModal } from "../dialog-modal/comment-modal";
import { useTranslations } from "next-intl";
import { formatDate } from "date-fns";
import Link from "next/link";
import { formatDateFromNow } from "@/utils/calculate-age-at-pgl";
import { Photos } from "../custom-carousel";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { useSession } from "next-auth/react";
import { allowedEmails, extractBranchShortCut } from "../services/config";
import {
  Tooltip,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import Image from "next/image";
import { getImageSizeUrl } from "@/utils/imageURL";

const DetailCellRenderer: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const [newComment, setNewComment] = useState<string | null>(
    data?.customer_comment ?? null
  );
  const [commentOpen, setCommentOpen] = useState<boolean>(false);
  const t = useTranslations("datatable");

  const { data: session } = useSession();
  const userEmail = session?.profile?.loginable?.email?.toLowerCase() || "";

  // Determine if the auction invoice link should be shown

  const isGoogleImages =
    data?.auction_photos_link?.includes("drive.google.com") ||
    data?.photo_link?.includes("drive.google.com")
      ? true
      : false;
  const isAuctionPhoto =
    allowedEmails.includes(userEmail?.toLowerCase()) &&
    data?.auction_photos_link?.includes("drive.google.com");

  return (
    <div
      className="rounded-lg overflow-hidden shadow-md h-auto w-full border"
      key={data?.id}
    >
      <div className="w-full text-sm">
        <table className="min-w-full table-auto">
          <thead className="bg-sidebar">
            <tr className="w-full font-medium">
              <th className="px-4 py-2 text-left font-medium text-sm"></th>
              <th className="px-4 py-2 text-left font-medium text-sm">
                {t("vehicle-details.header.vehicle")}
              </th>
              <th className="px-4 py-2 text-left font-medium text-sm">
                {t("vehicle-details.header.dates")}
              </th>
              <th className="px-4 py-2 text-left font-medium text-sm">
                {t("vehicle-details.header.state")}
              </th>
              <th className="px-4 py-2 text-left font-medium text-sm">
                {t("vehicle-details.header.general")}
              </th>
            </tr>
          </thead>

          <tbody>
            <tr>
              <td rowSpan={8} className="pl-11 w-80 ">
                {!isGoogleImages ? (
                  <Photos
                    vehicleId={data?.id}
                    vin={data?.vin}
                    isGoogleImages={false}
                    photosLink={{
                      auction: data?.auction_photos_link,
                      photo: data?.photo_link,
                    }}
                    isAuctionPhoto={isAuctionPhoto}
                    isShowTabs={allowedEmails.includes(userEmail)}
                  />
                ) : (
                  <div className="flex flex-col gap-4 w-full max-w-xs relative">
                    <div className="relative">
                      <Image
                        src={
                          data?.cover_photo
                            ? getImageSizeUrl({
                                url: data?.cover_photo,
                                size: 250,
                              })
                            : "/placeholder1.jpg"
                        }
                        alt="Cover Photo"
                        width={400}
                        height={200}
                        className="w-full transition-all rounded-md object-cover"
                      />

                      {isAuctionPhoto ? (
                        <div className="absolute bottom-0 left-0 right-0 grid grid-cols-2 gap-2 p-2">
                          <Link
                            href={`${data?.photo_link ?? "#"}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="w-full transition-all"
                          >
                            <div className="px-4 py-2 bg-opacity-80 bg-primary text-white font-medium text-center rounded-md text-sm">
                              <div className="flex items-center justify-center gap-2 text-xs">
                                <span>Warehouse Images</span>
                              </div>
                            </div>
                          </Link>

                          <Link
                            href={`${data?.auction_photos_link ?? "#"}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="w-full transition-all"
                          >
                            <div className="px-2 py-2 bg-opacity-80 bg-primary text-white font-medium text-center rounded-md text-sm">
                              <div className="flex items-center justify-center gap-2 text-xs">
                                <span>Auction Images</span>
                              </div>
                            </div>
                          </Link>
                        </div>
                      ) : (
                        <Link
                          href={`${data?.photo_link ?? "#"}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="w-full transition-all"
                        >
                          <div className="absolute bottom-0 left-0 right-0 px-4 py-2 bg-opacity-80 bg-primary text-white font-medium text-center rounded-b-md">
                            <div className="flex items-center justify-center gap-2">
                              <Eye className="w-4 h-4" /> Click Here To View
                              Images
                            </div>
                          </div>
                        </Link>
                      )}
                    </div>
                  </div>
                )}
              </td>
              <td className="px-4 py-1"></td>
            </tr>
            <tr className="border-b border-black/10">
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-details.body.lot-number")}
                  </div>
                  <Separator className="my-2" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.lot_number}
                  </div>
                </div>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-details.body.ship-date")}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.containers?.bookings?.vessels?.etd ? (
                      formatDate(
                        data?.containers?.bookings?.vessels?.etd,
                        "yyyy MMM dd"
                      )
                    ) : (
                      <div className="h-4 w-4"></div>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-details.body.picked-up-date")}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.pickup_date ? (
                      formatDate(data?.pickup_date, "yyyy MMM dd")
                    ) : (
                      <div className="h-4 w-4"></div>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="font-medium">
                    {t("vehicle-details.body.auction-invoice-link")}
                  </div>
                  <Separator className="my-2" />
                  <div>
                    <Link
                      href={data?.auction_invoice || "#"}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Link2
                        className={`${
                          data?.auction_invoice
                            ? "text-blue-500 w-4 h-4"
                            : "text-red-500 w-4 h-4"
                        }`}
                      />
                    </Link>
                  </div>
                </div>
              </td>
            </tr>
            <tr className="border-b border-black/10">
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5 max-w-56">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-details.body.title-status")}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis text-wrap px-2">
                    {data?.trn ? (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="cursor-help font-medium truncate max-w-[200px]">
                              {data.trn} {data.title_delivery_location}
                            </div>
                          </TooltipTrigger>
                          <TooltipContent
                            side="right"
                            className="p-4 bg-popover border rounded-lg shadow-lg"
                            sideOffset={5}
                          >
                            <div className="font-medium mb-2 pb-2 border-b">
                              TRN
                            </div>
                            <div className="mb-2 pb-2 border-b">{data.trn}</div>
                            <div className="font-medium mb-2 pb-2 border-b">
                              {t("body.title-delivery-location")}
                            </div>
                            <div>{data.title_delivery_location}</div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ) : (
                      <div className="font-medium">{data?.title_status}</div>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-details.body.loading-date")}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.containers?.loading_date ? (
                      formatDate(data?.containers?.loading_date, "yyyy MMM dd")
                    ) : (
                      <div className="h-4 w-4"></div>
                    )}
                  </div>
                </div>
              </td>

              <td className="px-4 py-2">
                <div className="flex flex-col gap-2 justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-details.body.is-title")}
                  </div>
                  <Separator />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {!data?.is_title_exist ? (
                      <span className="text-red-500 pt-1">
                        <svg
                          width="15"
                          height="15"
                          viewBox="0 0 15 15"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M0.877075 7.49988C0.877075 3.84219 3.84222 0.877045 7.49991 0.877045C11.1576 0.877045 14.1227 3.84219 14.1227 7.49988C14.1227 11.1575 11.1576 14.1227 7.49991 14.1227C3.84222 14.1227 0.877075 11.1575 0.877075 7.49988ZM7.49991 1.82704C4.36689 1.82704 1.82708 4.36686 1.82708 7.49988C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C10.6329 13.1727 13.1727 10.6329 13.1727 7.49988C13.1727 4.36686 10.6329 1.82704 7.49991 1.82704ZM9.85358 5.14644C10.0488 5.3417 10.0488 5.65829 9.85358 5.85355L8.20713 7.49999L9.85358 9.14644C10.0488 9.3417 10.0488 9.65829 9.85358 9.85355C9.65832 10.0488 9.34173 10.0488 9.14647 9.85355L7.50002 8.2071L5.85358 9.85355C5.65832 10.0488 5.34173 10.0488 5.14647 9.85355C4.95121 9.65829 4.95121 9.3417 5.14647 9.14644L6.79292 7.49999L5.14647 5.85355C4.95121 5.65829 4.95121 5.3417 5.14647 5.14644C5.34173 4.95118 5.65832 4.95118 5.85358 5.14644L7.50002 6.79289L9.14647 5.14644C9.34173 4.95118 9.65832 4.95118 9.85358 5.14644Z"
                            fill="currentColor"
                            fillRule="evenodd"
                            clipRule="evenodd"
                          ></path>
                        </svg>
                      </span>
                    ) : (
                      <span className="text-green-500 pt-1">
                        <svg
                          width="15"
                          height="15"
                          viewBox="0 0 15 15"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor"
                            fillRule="evenodd"
                            clipRule="evenodd"
                          ></path>
                        </svg>
                      </span>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="font-medium">
                    {t("vehicle-details.body.document-link")}
                  </div>
                  <Separator className="my-2" />
                  <div>
                    {data?.vehicle_document_link ? (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Link
                              href={data?.vehicle_document_link || "#"}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <Link2
                                className={`${
                                  data?.vehicle_document_link
                                    ? "text-blue-500 w-4 h-4"
                                    : "text-red-500 w-4 h-4"
                                }`}
                              />
                            </Link>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Click to view vehicle document</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ) : (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <svg
                              width="16"
                              height="16"
                              viewBox="0 0 15 15"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M13.3536 2.35355C13.5488 2.15829 13.5488 1.84171 13.3536 1.64645C13.1583 1.45118 12.8417 1.45118 12.6464 1.64645L1.64645 12.6464C1.45118 12.8417 1.45118 13.1583 1.64645 13.3536C1.84171 13.5488 2.15829 13.5488 2.35355 13.3536L13.3536 2.35355ZM2.03735 8.46678C2.17398 9.12619 2.66918 9.67103 3.33886 9.89338L2.57833 10.6539C1.80843 10.2534 1.23784 9.53693 1.05815 8.66967C0.999538 8.38681 0.999604 8.06004 0.999703 7.56313L0.999711 7.50001L0.999703 7.43689C0.999604 6.93998 0.999538 6.61321 1.05815 6.33035C1.29846 5.17053 2.2379 4.28039 3.4182 4.055C3.70687 3.99988 4.04134 3.99993 4.56402 4.00001L4.62471 4.00001H5.49971C5.77585 4.00001 5.99971 4.22387 5.99971 4.50001C5.99971 4.77615 5.77585 5.00001 5.49971 5.00001H4.62471C4.02084 5.00001 3.78907 5.00225 3.60577 5.03725C2.80262 5.19062 2.19157 5.78895 2.03735 6.53324C2.00233 6.70225 1.99971 6.91752 1.99971 7.50001C1.99971 8.08251 2.00233 8.29778 2.03735 8.46678ZM12.9621 6.53324C12.8255 5.87397 12.3304 5.32922 11.661 5.10679L12.4215 4.34631C13.1912 4.74686 13.7616 5.46323 13.9413 6.33035C13.9999 6.61321 13.9998 6.93998 13.9997 7.43688L13.9997 7.50001L13.9997 7.56314C13.9998 8.06005 13.9999 8.38681 13.9413 8.66967C13.701 9.8295 12.7615 10.7196 11.5812 10.945C11.2925 11.0001 10.9581 11.0001 10.4354 11L10.3747 11H9.49971C9.22357 11 8.99971 10.7762 8.99971 10.5C8.99971 10.2239 9.22357 10 9.49971 10H10.3747C10.9786 10 11.2104 9.99777 11.3937 9.96277C12.1968 9.8094 12.8079 9.21108 12.9621 8.46678C12.9971 8.29778 12.9997 8.08251 12.9997 7.50001C12.9997 6.91752 12.9971 6.70225 12.9621 6.53324Z"
                                fill="currentColor"
                                fillRule="evenodd"
                                clipRule="evenodd"
                              ></path>
                            </svg>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Vehicle Document Not Available</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                </div>
              </td>
            </tr>
            <tr className="border-b border-black/10">
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-details.body.account-number")}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.buyer_number ? (
                      data?.buyer_number
                    ) : (
                      <div className="h-4 w-4"></div>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-details.body.purchase-date")}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.purchased_at ? (
                      formatDate(data?.purchased_at, "yyyy MMM dd")
                    ) : (
                      <div className="h-4 w-4"></div>
                    )}
                  </div>
                </div>
              </td>

              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-details.body.is-key")}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.is_key_present ? (
                      <span className="text-green-500 pt-1">
                        <svg
                          width="15"
                          height="15"
                          viewBox="0 0 15 15"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor"
                            fillRule="evenodd"
                            clipRule="evenodd"
                          ></path>
                        </svg>
                      </span>
                    ) : (
                      <span className="text-red-500 pt-1">
                        <svg
                          width="15"
                          height="15"
                          viewBox="0 0 15 15"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M0.877075 7.49988C0.877075 3.84219 3.84222 0.877045 7.49991 0.877045C11.1576 0.877045 14.1227 3.84219 14.1227 7.49988C14.1227 11.1575 11.1576 14.1227 7.49991 14.1227C3.84222 14.1227 0.877075 11.1575 0.877075 7.49988ZM7.49991 1.82704C4.36689 1.82704 1.82708 4.36686 1.82708 7.49988C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C10.6329 13.1727 13.1727 10.6329 13.1727 7.49988C13.1727 4.36686 10.6329 1.82704 7.49991 1.82704ZM9.85358 5.14644C10.0488 5.3417 10.0488 5.65829 9.85358 5.85355L8.20713 7.49999L9.85358 9.14644C10.0488 9.3417 10.0488 9.65829 9.85358 9.85355C9.65832 10.0488 9.34173 10.0488 9.14647 9.85355L7.50002 8.2071L5.85358 9.85355C5.65832 10.0488 5.34173 10.0488 5.14647 9.85355C4.95121 9.65829 4.95121 9.3417 5.14647 9.14644L6.79292 7.49999L5.14647 5.85355C4.95121 5.65829 4.95121 5.3417 5.14647 5.14644C5.34173 4.95118 5.65832 4.95118 5.85358 5.14644L7.50002 6.79289L9.14647 5.14644C9.34173 4.95118 9.65832 4.95118 9.85358 5.14644Z"
                            fill="currentColor"
                            fillRule="evenodd"
                            clipRule="evenodd"
                          ></path>
                        </svg>
                      </span>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-details.body.auction-city")}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.auction_cities
                      ? `${
                          data?.auction_cities?.city_name
                        } ${extractBranchShortCut(
                          data?.auction_cities?.loading_states?.parent?.name
                        )}`
                      : data?.auction_city}
                  </div>
                </div>
              </td>
            </tr>
            <tr className="border-black/10">
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-details.body.recieve-date")}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.title_receive_date ? (
                      formatDate(
                        new Date(data?.title_receive_date),
                        "yyyy MMM dd"
                      )
                    ) : (
                      <div className="h-4 w-4"></div>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-details.body.delivery-date")}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.deliver_date ? (
                      formatDate(data?.deliver_date, "yyyy MMM dd")
                    ) : (
                      <div className="h-4 w-4"></div>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-details.body.is-printed")}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.is_printed ? (
                      <span className="text-green-500 pt-1">
                        <svg
                          width="15"
                          height="15"
                          viewBox="0 0 15 15"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z"
                            fill="currentColor"
                            fillRule="evenodd"
                            clipRule="evenodd"
                          ></path>
                        </svg>
                      </span>
                    ) : (
                      <span className="text-red-500 pt-1">
                        <svg
                          width="15"
                          height="15"
                          viewBox="0 0 15 15"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M0.877075 7.49988C0.877075 3.84219 3.84222 0.877045 7.49991 0.877045C11.1576 0.877045 14.1227 3.84219 14.1227 7.49988C14.1227 11.1575 11.1576 14.1227 7.49991 14.1227C3.84222 14.1227 0.877075 11.1575 0.877075 7.49988ZM7.49991 1.82704C4.36689 1.82704 1.82708 4.36686 1.82708 7.49988C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C10.6329 13.1727 13.1727 10.6329 13.1727 7.49988C13.1727 4.36686 10.6329 1.82704 7.49991 1.82704ZM9.85358 5.14644C10.0488 5.3417 10.0488 5.65829 9.85358 5.85355L8.20713 7.49999L9.85358 9.14644C10.0488 9.3417 10.0488 9.65829 9.85358 9.85355C9.65832 10.0488 9.34173 10.0488 9.14647 9.85355L7.50002 8.2071L5.85358 9.85355C5.65832 10.0488 5.34173 10.0488 5.14647 9.85355C4.95121 9.65829 4.95121 9.3417 5.14647 9.14644L6.79292 7.49999L5.14647 5.85355C4.95121 5.65829 4.95121 5.3417 5.14647 5.14644C5.34173 4.95118 5.65832 4.95118 5.85358 5.14644L7.50002 6.79289L9.14647 5.14644C9.34173 4.95118 9.65832 4.95118 9.85358 5.14644Z"
                            fill="currentColor"
                            fillRule="evenodd"
                            clipRule="evenodd"
                          ></path>
                        </svg>
                      </span>
                    )}
                  </div>
                </div>
              </td>
              <td className="px-4 py-2">
                <div className="flex flex-col justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 font-medium">
                    {t("vehicle-details.body.age-at-pgl")}
                  </div>
                  <Separator className="my-1" />
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2">
                    {data?.deliver_date ? (
                      formatDateFromNow(data?.deliver_date)
                    ) : (
                      <div className="h-4 w-4"></div>
                    )}
                  </div>
                </div>
              </td>
            </tr>
            <tr className="border-black/10">
              <td colSpan={4}>
                <div className="flex flex-col gap-2 justify-center h-full select-text leading-5">
                  <div className="flex w-full overflow-hidden text-ellipsis whitespace-nowrap px-2 py-1 font-medium">
                    {t("vehicle-details.body.comment")}
                  </div>
                  <Separator />
                  <div className="max-w-52 flex overflow-hidden text-ellipsis text-wrap px-2">
                    <Popover open={commentOpen} onOpenChange={setCommentOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          size="icon"
                          variant="ghost"
                          className="text-blue-500 -mt-2 text-left justify-start w-full"
                        >
                          {<MessageSquareText />}
                        </Button>
                      </PopoverTrigger>

                      <PopoverContent className="w-80 min-h-32 flex flex-col gap-y-4">
                        <h1 className="font-bold py-2">Add Your Comment</h1>
                        <CommentModal
                          vehicleId={data?.id}
                          defaultValue={newComment || ""}
                          onSuccess={(updatedComment) => {
                            setNewComment(updatedComment);
                            setCommentOpen(false);
                          }}
                          close={() => setCommentOpen(false)}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DetailCellRenderer;
