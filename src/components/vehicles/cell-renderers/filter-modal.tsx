"use client";

import * as React from "react";
import { Sidebar } from "@/components/ui/sidebar";
import { RotateCcw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import FilterCollapse from "@/components/vehicles/filter-collapse";
import CustomSlider from "@/components/Common_UI/custom-slider";
import { ContainerSelector } from "@/components/Common_UI/auto-complete";
import { addDays } from "date-fns";
import { DateRange } from "react-day-picker";
import { CustomDateRangePicker } from "@/components/Common_UI/customer-range-date";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import InputSearch from "@/components/Common_UI/InputSearch";
import { useGetAutoComplete } from "@/utils/use-get-autocomplete";
import { Switch } from "@/components/ui/switch";
import { useTranslations } from "next-intl";
import { formatNumberByLocale } from "@/utils/helper-function";
import { localesTypes } from "@/i18n/routing";

type optionType = { label: string; value: string };
const ToggleOptionsData: optionType[] = [
  { value: "on_hand_with_title", label: "On Hand With Title" },
  { value: "added_by_customer", label: "Added By Customer" },
  { value: "on_hand_no_title", label: "On Hand No Title" },
  { value: "on_the_way", label: "On The Way" },
  { value: "shipped", label: "Shipped" },
  { value: "auction_paid", label: "Auction Paid" },
  {
    value: "auction_unpaid",
    label: "Auction Unpaid",
  },
];
const MAXIMUM_SIZE = 100000;
const MINMUM_SIZE = 200;
export function FilterModel({}: React.ComponentProps<typeof Sidebar> & {}) {
  const searchParams = useSearchParams();
  const urlParam = useParams();
  const router = useRouter();
  const t = useTranslations("filter-modal");
  const params = new URLSearchParams(Array.from(searchParams.entries()));
  const [openCollapse, toggleCollapse] = React.useState<string[]>([]);
  const clearSelectionsRef = React.useRef<(() => void) | null>(null);
  const [date, setDate] = React.useState<DateRange>(function () {
    const from = params.get("from");
    const to = params.get("to");
    return {
      from: from ? new Date(from) : new Date(2023, 0, 20),
      to: to ? new Date(to) : addDays(new Date(2023, 0, 20), 20),
    };
  });
  const [, setPaymentRecived] = React.useState([20000, 90000]);
  const [price, setPrice] = React.useState([20000, 90000]);
  const [isChecked, setIsChecked] = React.useState(() => {
    return searchParams.get("checked") === "true";
  });
  const [isPrinted, setIsPrinted] = React.useState(() => {
    return searchParams.get("is_printed") === "true";
  });

  const {
    data,
    isLoading,
    refetch,
    handleSearch: handleContainerSearch,
  } = useGetAutoComplete({
    column: "container_number",
    model: "containers",
    key: "con",
  });

  const {
    data: PODData,
    isLoading: PODIsLoading,
    refetch: PODRefetch,
    handleSearch: handlePODSearch,
  } = useGetAutoComplete({ column: "name", model: "destinations", key: "des" });

  const {
    data: locData,
    isLoading: locIsLoading,
    refetch: locRefetch,
    handleSearch: handleLocSearch,
  } = useGetAutoComplete({
    column: "name",
    model: "locations",
    key: "loc",
  });

  const handleSelectionContainer = (selected: any[]) => {
    params.delete("con");
    selected?.forEach((select) => params.append("con", select.id));
    router.push(`?${params}`);
  };
  const handleSelectionPointOfLocation = (selected: any[]) => {
    params.delete("loc");
    selected?.forEach((select) => params.append("loc", select.id));
    router.push(`?${params}`);
  };
  const handleSelectionPointOfDestinations = (selected: any[]) => {
    params.delete("des");
    selected?.forEach((select) => params.append("des", select.id));
    router.push(`?${params}`);
  };

  const handleSelectionVehicleStatus = (selected: any[]) => {
    params.delete("carstate");
    selected?.forEach((select) => params.append("carstate", select.value));
    router.push(`?${params}`);
  };

  const handlePrice = (selected: number[]) => {
    params.delete("pri_from");
    params.delete("pri_to");
    params.append("pri_from", `${selected[0]}`);
    params.append("pri_to", `${selected[1]}`);
    router.push(`?${params}`);
    setPrice(selected);
  };

  const handlePurchaseAt = (selected: DateRange) => {
    setDate(selected);
    if (selected.from && selected.to) {
      params.delete("from_purchased_at");
      params.delete("to_purchased_at");
      params.append(
        "from_purchased_at",
        selected.from.toISOString().split("T")[0]
      );
      params.append("to_purchased_at", selected.to.toISOString().split("T")[0]);
      router.push(`?${params}`);
    }
  };
  const handlePaymentDate = (selected: DateRange) => {
    setDate(selected);
    if (selected.from && selected.to) {
      params.delete("from_payment_date");
      params.delete("to_payment_date");
      params.append(
        "from_payment_date",
        selected.from.toISOString().split("T")[0]
      );
      params.append("to_payment_date", selected.to.toISOString().split("T")[0]);
      router.push(`?${params}`);
    }
  };
  const handleDeliverDate = (selected: DateRange) => {
    setDate(selected);
    if (selected.from && selected.to) {
      params.delete("from_deliver_date");
      params.delete("to_deliver_date");
      params.append(
        "from_deliver_date",
        selected.from.toISOString().split("T")[0]
      );
      params.append("to_deliver_date", selected.to.toISOString().split("T")[0]);
      router.push(`?${params}`);
    }
  };
  const handleCheckedChange = () => {
    const newValue = !isChecked;
    setIsChecked(newValue);
    params.delete("checked");
    if (newValue) {
      params.set("checked", "true");
    }
    router.push(`?${params}`, { scroll: false });
  };

  const handleIsPrintedChange = () => {
    const newValue = !isPrinted;
    setIsPrinted(newValue);
    params.delete("is_printed");
    if (newValue) {
      params.set("is_printed", "true");
    }
    router.push(`?${params}`, { scroll: false });
  };
  const handleClearFilters = () => {
    params.delete("inv");
    params.delete("con");
    params.delete("loc");
    params.delete("des");
    params.delete("carstate");
    params.delete("from_purchased_at");
    params.delete("to_purchased_at");
    params.delete("from_payment_date");
    params.delete("to_payment_date");
    params.delete("from_deliver_date");
    params.delete("to_deliver_date");
    params.delete("pri_from");
    params.delete("pri_to");
    params.delete("lot_number");
    params.delete("vin");
    params.delete("make");
    params.delete("model");
    params.delete("year");
    params.delete("checked");
    params.delete("is_printed");
    router.push(`?${params}`);
    setPrice([20000, 90000]);
    setDate({
      from: new Date(2023, 0, 20),
      to: addDays(new Date(2023, 0, 20), 20),
    });
    setPaymentRecived([20000, 90000]);
    setIsChecked(false);
    setIsPrinted(false);
    toggleCollapse([]);
    clearSelectionsRef.current?.();
  };
  return (
    <div className="mt-1 p-2 w-72">
      <div className="flex flex-row pl-3 pt-2 justify-between py-2">
        <h2 className="text-lg font-medium">{t("filters")}</h2>
        <Button
          onClick={() => {
            clearSelectionsRef.current?.();
            handleClearFilters();
          }}
          variant={"link"}
          size={"icon"}
          className="clear-filters"
        >
          <RotateCcw />
        </Button>
      </div>
      <div className="flex flex-col p-2 gap-2">
        <FilterCollapse
          label={t("vehicle-filter-modal.container")}
          className="p-2"
          isOpen={openCollapse?.includes("containers")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("containers")
                ? prev.filter((item) => item !== "containers")
                : [...prev, "containers"]
            )
          }
        >
          <ContainerSelector
            data={
              data?.data?.map((item: any) => ({ ...item, checked: false })) ||
              []
            }
            isLoading={isLoading}
            onFetch={refetch}
            onSearch={handleContainerSearch}
            onSelectionChange={handleSelectionContainer}
            selectedLabel={(count) =>
              `${t("checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            unselectedLabel={(count) =>
              `${t("not-checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            labelKey="container_number"
            valueKey="id"
            clearSelectionsRef={clearSelectionsRef}
            searchParamsKey="con"
          />
        </FilterCollapse>

        <FilterCollapse
          label={t("vehicle-filter-modal.point-of-loading")}
          className="p-2"
          isOpen={openCollapse?.includes("point_of_loading")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("point_of_loading")
                ? prev.filter((item) => item !== "point_of_loading")
                : [...prev, "point_of_loading"]
            )
          }
        >
          <ContainerSelector
            data={
              locData?.data?.map((item: any) => ({
                ...item,
                checked: false,
              })) || []
            }
            isLoading={locIsLoading}
            onFetch={locRefetch}
            onSearch={handleLocSearch}
            onSelectionChange={handleSelectionPointOfLocation}
            selectedLabel={(count) =>
              `${t("checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            unselectedLabel={(count) =>
              `${t("not-checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            labelKey="name"
            valueKey="id"
            clearSelectionsRef={clearSelectionsRef}
            searchParamsKey="loc"
          />
        </FilterCollapse>

        <FilterCollapse
          label={t("vehicle-filter-modal.point-of-destination")}
          className="p-2"
          isOpen={openCollapse?.includes("destinations")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("destinations")
                ? prev.filter((item) => item !== "destinations")
                : [...prev, "destinations"]
            )
          }
        >
          <ContainerSelector
            data={
              PODData?.data?.map((item: any) => ({
                ...item,
                checked: false,
              })) || []
            }
            isLoading={PODIsLoading}
            onFetch={PODRefetch}
            onSearch={(term) => {
              handlePODSearch(term);
              PODRefetch();
            }}
            onSelectionChange={handleSelectionPointOfDestinations}
            selectedLabel={(count) =>
              `${t("checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            unselectedLabel={(count) =>
              `${t("not-checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            labelKey="name"
            valueKey="id"
            clearSelectionsRef={clearSelectionsRef}
            searchParamsKey="des"
          />
        </FilterCollapse>

        <FilterCollapse
          label={t("vehicle-filter-modal.vehicle-status")}
          isOpen={openCollapse?.includes("carstate")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("carstate")
                ? prev.filter((item) => item !== "carstate")
                : [...prev, "carstate"]
            )
          }
        >
          <ContainerSelector
            data={
              ToggleOptionsData?.map((item: any) => ({
                ...item,
                checked: false,
              })) || []
            }
            onSelectionChange={handleSelectionVehicleStatus}
            selectedLabel={(count) =>
              `${t("checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            unselectedLabel={(count) =>
              `${t("not-checked-option")} (${formatNumberByLocale(
                count,
                urlParam.locale as localesTypes
              )})`
            }
            labelKey="label"
            valueKey="value"
            searchParamsKey="carstate"
            clearSelectionsRef={clearSelectionsRef}
          />
        </FilterCollapse>

        <FilterCollapse
          label={t("vehicle-filter-modal.data.label")}
          isOpen={openCollapse?.includes("data")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("data")
                ? prev.filter((item) => item !== "data")
                : [...prev, "data"]
            )
          }
        >
          <div className="py-2 flex flex-col gap-2">
            <InputSearch
              type="text"
              fieldName="lot_number"
              placeholder={t("vehicle-filter-modal.data.lot-number")}
            />
            <InputSearch
              type="text"
              fieldName="vin"
              placeholder={t("vehicle-filter-modal.data.vin")}
            />
            <div className="flex flex-row gap-x-2">
              <InputSearch
                type="text"
                fieldName="make"
                placeholder={t("vehicle-filter-modal.data.make")}
              />

              <InputSearch
                type="text"
                fieldName="model"
                placeholder={t("vehicle-filter-modal.data.model")}
              />
            </div>

            <InputSearch
              type="text"
              fieldName="year"
              placeholder={t("vehicle-filter-modal.data.year")}
            />
          </div>
        </FilterCollapse>

        <CustomSlider
          label={t("vehicle-filter-modal.price")}
          max={MAXIMUM_SIZE}
          min={MINMUM_SIZE}
          onCommit={handlePrice}
          onChange={setPrice}
          value={price}
        />
        <Separator />
        <FilterCollapse
          label={t("vehicle-filter-modal.date-renge.label")}
          isOpen={openCollapse?.includes("Date Range")}
          onToggle={() =>
            toggleCollapse((prev) =>
              prev.includes("Date Range")
                ? prev.filter((item) => item !== "Date Range")
                : [...prev, "Date Range"]
            )
          }
        >
          <div className="flex flex-col gap-4 pt-3">
            <Label htmlFor="purchased_at">
              {t("vehicle-filter-modal.date-renge.purchase-at")}
            </Label>
            <CustomDateRangePicker
              id="purchased_at"
              date={date}
              onChange={(selected) => handlePurchaseAt(selected as DateRange)}
            />
            <Label htmlFor="payment_date">
              {t("vehicle-filter-modal.date-renge.payment-date")}
            </Label>
            <CustomDateRangePicker
              id="payment_date"
              date={date}
              onChange={(selected) => handlePaymentDate(selected as DateRange)}
            />
            <Label htmlFor="deliver_date">
              {t("vehicle-filter-modal.date-renge.delivery-date")}
            </Label>
            <CustomDateRangePicker
              id="deliver_date"
              date={date}
              onChange={(selected) => handleDeliverDate(selected as DateRange)}
            />
          </div>
        </FilterCollapse>

        <div
          className="flex justify-between items-center border py-2 px-5 rounded-md bg-background h-10 cursor-pointer"
          onClick={handleCheckedChange}
        >
          <Label htmlFor="checked" className="flex items-center gap-2">
            <span className="text-sm">{t("vehicle-filter-modal.checked")}</span>
          </Label>
          <Switch
            id="checked"
            checked={isChecked}
            onCheckedChange={handleCheckedChange}
          />
        </div>
        <div
          className="flex justify-between items-center border py-2 px-5 rounded-md bg-background h-10 cursor-pointer"
          onClick={handleIsPrintedChange}
        >
          <Label htmlFor="is_printed" className="flex items-center gap-2">
            <span className="text-sm">
              {t("vehicle-filter-modal.is-printed")}
            </span>
          </Label>
          <Switch
            id="is_printed"
            checked={isPrinted}
            onCheckedChange={handleIsPrintedChange}
          />
        </div>
      </div>
    </div>
  );
}
