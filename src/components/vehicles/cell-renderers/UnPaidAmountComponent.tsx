import { useMemo } from "react";

interface Payment {
  payment_amount: string;
  payment_date: string;
  created_at: string;
}

interface UnpaidAmountDisplayProps {
  data: any;
  className?: string;
}

export const UnpaidAmountDisplay = ({ data }: UnpaidAmountDisplayProps) => {
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 2,
    minimumFractionDigits: 0,
  });

  const calculations = useMemo(() => {
    const payments: Payment[] = data?.customer_of_customer_payments || [];

    const totalPaid = payments.reduce(
      (sum, payment) => sum + parseFloat(payment.payment_amount.toString()),
      0
    );

    const mixShippingData: any = data?.mix_shipping_vehicles?.[0] || {};
    const mixShippingCharges =
      mixShippingData.mix_shipping_vehicle_charges || [];

    const totalMixShippingCharges = mixShippingCharges.reduce(
      (sum: any, charge: any) => Number(sum) + (Number(charge.value) || 0),
      0
    );

    const transportationFee =
      (Number(mixShippingData.freight) || 0) +
      (Number(mixShippingData.vat_and_custom) || 0) +
      (Number(mixShippingData.tow_amount) || 0) +
      (Number(mixShippingData.clearance) || 0) +
      Number(totalMixShippingCharges);

    const vehiclePrice = data?.price || 0;
    const customerProfit = data?.customer_profit || 0;
    const storageCharges = data?.storage_charges ?? 0;

    const totalCharges =
      vehiclePrice + customerProfit + transportationFee + storageCharges;

    const unPaid = totalCharges - totalPaid;

    return {
      unPaid,
      totalPaid,
      totalCharges,
      transportationFee,
      payments,
    };
  }, [data]);

  return (
    <div className="flex items-center h-full select-text">
      <div className="rounded-md px-2 flex leading-[22px] text-xs font-semibold overflow-hidden">
        {calculations.unPaid > 1
          ? `-${formatter.format(Math.abs(calculations.unPaid))}`
          : formatter.format(Math.abs(calculations.unPaid))}
      </div>
    </div>
  );
};
