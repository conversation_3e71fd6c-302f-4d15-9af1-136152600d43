import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>itle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import {
  DollarSign,
  CreditCard,
  CheckCircle,
  AlertCircle,
  Calculator,
  Receipt,
  ShoppingCart,
  Loader2,
  AlertTriangle,
  Ambulance,
} from "lucide-react";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useFetchClient } from "@/utils/axios";
import { useTranslations } from "next-intl";

interface AuctionPaymentResponse {
  id: number;
  payments: {
    id: number;
    amount_applied: number;
    state: string;
    type: string;
    payment_date: string;
  }[];
  mix_shipping_vehicles: {
    freight: number;
    vat_and_custom: number;
    tow_amount: number;
    clearance: number;
    discount: number;
    mix_shipping_vehicle_charges: {
      id: number;
      name: string;
      value: number;
    }[];
    mix_shipping_invoices: {
      exchange_rate: number;
      status: string;
      type: string;
    };
    payments: {
      id: number;
      amount_applied: number;
      state: string;
      type: string;
      payment_date: string;
      payment_allocations?: {
        id: number;
        amount: number;
        type: string;
      }[];
    }[];
  }[];
  price?: number;
  auction_name?: string;
}

interface PaymentDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  vehicleId: number;
  vehicleData?: {
    id: number;
    price?: number;
    auction_name?: string;
    payments?: any[];
  };
}

export const PaymentDialog = ({
  isOpen,
  onOpenChange,
  vehicleId,
}: PaymentDialogProps) => {
  const [activeTab, setActiveTab] = useState("finance");
  const t = useTranslations("auction-payment-model");
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 2,
    minimumFractionDigits: 0,
  });

  const fetchClient = useFetchClient();
  const getAuctionPayment = async (id: number) => {
    try {
      const response = await fetchClient(`/v2/vehicles/auction_payment/${id}`);
      if (response.status === 200) {
        return response.data;
      }
      console.error("Error fetching auction payment:", response.data);
      throw new Error("Failed to fetch auction payment data");
    } catch (error) {
      console.error("Error fetching auction payment:", error);
      throw error;
    }
  };

  const {
    data: paymentData,
    isLoading,
    error,
  } = useQuery<AuctionPaymentResponse>({
    queryKey: ["auctionPaymentVehicle", vehicleId],
    queryFn: () => getAuctionPayment(vehicleId),
    enabled: isOpen && !!vehicleId,
    staleTime: 5 * 60 * 1000,
    retry: 1,
  });

  // Format a date string
  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Extract mix_shipping_vehicles data
  const mixShippingData: any = paymentData?.mix_shipping_vehicles?.[0] || {};
  const mixShippingCharges = mixShippingData.mix_shipping_vehicle_charges || [];
  const mixPayments = mixShippingData.payments || [];
  const discount = paymentData?.mix_shipping_vehicles[0]?.discount
    ? paymentData?.mix_shipping_vehicles[0]?.discount
    : 0;
  const exchangeRate = paymentData?.mix_shipping_vehicles[0]
    ?.mix_shipping_invoices
    ? paymentData?.mix_shipping_vehicles[0]?.mix_shipping_invoices.exchange_rate
    : 0;
  const isCalculatedDiscount = discount && exchangeRate > 0;
  const calculatedDiscount = Number(isCalculatedDiscount)
    ? Number(discount) / Number(exchangeRate)
    : 0;

  // Get invoice status
  const mixInvoiceStatus = mixShippingData?.mix_shipping_invoices?.status || "";

  // Get invoice type
  const mixInvoiceType = mixShippingData?.mix_shipping_invoices?.type || "";

  const hasPreviousPaymentAllocation = () => {
    return mixPayments.some((payment: any) => {
      if (!payment.payment_allocations) return false;
      return payment.payment_allocations.some(
        (allocation: any) => allocation.type === "previous_payment"
      );
    });
  };

  // Check if accordion should be displayed
  const shouldDisplayAccordion =
    ["paid", "open", "past_due"].includes(mixInvoiceStatus.toLowerCase()) &&
    mixInvoiceType.toLowerCase() === "mix";

  // Helper function to get payment amount for a specific charge type
  const getPaymentForChargeType = (chargeType: string) => {
    let totalPayment = 0;
    mixPayments.forEach((payment: any) => {
      if (payment.payment_allocations) {
        payment.payment_allocations.forEach((allocation: any) => {
          if (allocation.type === chargeType) {
            totalPayment += Number(allocation.amount) || 0;
          }
        });
      }
    });
    return totalPayment;
  };

  // Dynamic function to map charge names/keys to allocation types
  const mapChargeToAllocationType = (charge: any) => {
    // First check if the charge has a key property
    if (charge.key) {
      return charge.key;
    }

    // Fallback to name-based mapping for standard charges
    const nameMapping: { [key: string]: string } = {
      freight: "freight",
      vat_and_custom: "vat_custom",
      tow_amount: "towing",
      clearance: "clearance",
    };

    return nameMapping[charge.name] || charge.name;
  };

  // Create charges data with payments and balances - now fully dynamic
  const chargesData = [
    // Standard charges
    {
      name: "Freight",
      amount: mixShippingData.freight || 0,
      type: "freight",
      key: "freight",
    },
    {
      name: "VAT and Custom",
      amount: mixShippingData.vat_and_custom || 0,
      type: "vat_custom",
      key: "vat_custom",
    },
    {
      name: "Tow Amount",
      amount: mixShippingData.tow_amount || 0,
      type: "towing",
      key: "towing",
    },
    {
      name: "Clearance",
      amount: mixShippingData.clearance || 0,
      type: "clearance",
      key: "clearance",
    },
    // Add charges from mix_shipping_vehicle_charges dynamically
    ...mixShippingCharges.map((charge: any) => ({
      name: charge.name
        .replace(/_/g, " ")
        .replace(/\b\w/g, (l: string) => l.toUpperCase()), // Capitalize each word
      amount: charge.value || 0,
      type: mapChargeToAllocationType(charge),
      key: charge.key || charge.name,
    })),
  ].filter((charge) => charge.amount > 0);

  // Calculate total of mix_shipping_vehicle_charges
  const totalMixShippingCharges = mixShippingCharges.reduce(
    (sum: any, charge: any) => Number(sum) + (Number(charge.value) || 0),
    0
  );

  // Calculate Transportation Fee
  const transportationFee =
    (Number(mixShippingData.freight) || 0) +
    (Number(mixShippingData.vat_and_custom) || 0) +
    (Number(mixShippingData.tow_amount) || 0) +
    (Number(mixShippingData.clearance) || 0) +
    Number(totalMixShippingCharges);

  // Calculate total payments for auction
  const auctionPayments =
    paymentData?.payments
      ?.filter((payment: any) => payment.type === "auction")
      .reduce(
        (sum: any, payment: any) =>
          Number(sum) + (Number(payment.amount_applied) || 0),
        0
      ) || 0;

  // Calculate unpaid amount for auction
  const auctionUnpaidCalc =
    (Number(paymentData?.price) || 0) - Number(auctionPayments);
  const auctionUnpaid = auctionUnpaidCalc < 1 ? 0 : auctionUnpaidCalc;

  // Calculate total payments for transportation
  const transportationPayments =
    mixPayments?.reduce(
      (sum: number, payment: any) =>
        Number(sum) + (Number(payment.amount_applied) || 0),
      0
    ) || 0;

  // Calculate unpaid amount for transportation
  const transportationUnpaid =
    Number(transportationFee) -
    Number(transportationPayments) -
    Number(calculatedDiscount);

  // Calculate grand total paid and unpaid
  const totalPaid = shouldDisplayAccordion
    ? Number(auctionPayments) + Number(transportationPayments)
    : Number(auctionPayments) || 0;

  const totalUnpaidCalc = shouldDisplayAccordion
    ? Number(auctionUnpaid) + Number(transportationUnpaid)
    : Number(auctionUnpaid);
  const totalUnpaid = totalUnpaidCalc < 1 ? 0 : totalUnpaidCalc;

  // Calculate grand total
  const grandTotal = shouldDisplayAccordion
    ? Number(paymentData?.price || 0) + Number(transportationFee)
    : Number(paymentData?.price || 0);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[750px] p-0 h-[700px] max-h-[700px] min-h-[600px] max-sm:h-screen max-sm:max-h-screen max-sm:min-h-screen max-sm:w-screen max-sm:max-w-none max-sm:rounded-none max-sm:m-0">
        <DialogHeader className="px-6 max-sm:px-4 flex justify-between items-center flex-row h-[60px] min-h-[60px] max-sm:border-b">
          <DialogTitle className="text-xl max-sm:text-lg flex items-center gap-2">
            <DollarSign className="h-5 w-5 max-sm:h-4 max-sm:w-4 text-primary" />
            {t("title")}
          </DialogTitle>
        </DialogHeader>

        <div
          className="px-6 max-sm:px-4 pb-6 max-sm:pb-4 overflow-y-auto flex-1"
          style={{
            height: "540px",
            maxHeight: "540px",
            minHeight: "540px",
          }}
        >
          {/* Loading State */}
          {isLoading && (
            <div className="flex flex-col items-center justify-center h-full">
              <Loader2 className="h-8 w-8 max-sm:h-6 max-sm:w-6 animate-spin text-green-500" />
              <p className="mt-2 text-sm max-sm:text-xs text-muted-foreground">
                Loading payment details...
              </p>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="flex flex-col items-center justify-center h-full text-red-500">
              <AlertTriangle className="h-8 w-8 max-sm:h-6 max-sm:w-6" />
              <p className="mt-2 text-sm max-sm:text-xs">
                Failed to load payment details. Using cached data.
              </p>
            </div>
          )}

          {/* Main Content when not loading */}
          {!isLoading && (
            <Tabs
              defaultValue="finance"
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid grid-cols-2 mb-4 max-sm:mb-3">
                <TabsTrigger
                  value="finance"
                  className={`flex items-center gap-1 max-sm:gap-0.5 max-sm:text-sm data-[state=active]:text-primary`}
                >
                  <Calculator className="h-4 w-4 max-sm:h-3 max-sm:w-3" />
                  <span className="max-sm:hidden">{t("tabs.finance")}</span>
                  <span className="sm:hidden text-xs">{t("tabs.finance")}</span>
                </TabsTrigger>
                <TabsTrigger
                  value="payments"
                  className={`flex items-center gap-1 max-sm:gap-0.5 max-sm:text-sm data-[state=active]:text-primary`}
                >
                  <CreditCard className="h-4 w-4 max-sm:h-3 max-sm:w-3" />
                  <span className="max-sm:hidden">{t("tabs.payments")}</span>
                  <span className="sm:hidden text-xs">
                    {t("tabs.payments")}
                  </span>
                </TabsTrigger>
              </TabsList>

              {/* Finance Tab Content */}
              <TabsContent value="finance" className="mt-0">
                <div className="border rounded-lg max-sm:rounded-md overflow-hidden mb-4 max-sm:mb-3 shadow-sm">
                  <div className="border-t">
                    {/* Desktop Header */}
                    <div className="max-sm:hidden grid grid-cols-[1fr_1fr_1fr_1fr] text-base p-3 font-semibold border-b bg-primary/5 dark:bg-secondary">
                      <div>{t("finance.header.items")}</div>
                      <div>{t("finance.header.amount")}</div>
                      <div>{t("finance.header.paid-amount")}</div>
                      <div>{t("finance.header.unpaid-amount")}</div>
                    </div>

                    {/* Mobile Header */}
                    <div className="sm:hidden grid grid-cols-2 text-sm p-2 font-semibold border-b bg-primary/5 dark:bg-secondary">
                      <div>{t("finance.header.items")}</div>
                      <div className="text-right">Balance</div>
                    </div>

                    {/* Desktop Vehicle Price Row */}
                    <div className="max-sm:hidden grid grid-cols-[1fr_1fr_1fr_1fr] text-sm p-3 border-b hover:bg-muted/5">
                      <div className="font-semibold flex items-center">
                        <Receipt className="h-4 w-4 mr-2 text-blue-500" />
                        {t("finance.items.vehicle-price")}
                      </div>
                      <div>{formatter.format(paymentData?.price || 0)}</div>
                      <div className="text-green-600 font-medium">
                        {formatter.format(auctionPayments)}
                      </div>
                      <div
                        className={`font-medium ${
                          auctionUnpaid > 0 ? "text-red-500" : "text-green-500"
                        }`}
                      >
                        {formatter.format(auctionUnpaid)}
                      </div>
                    </div>

                    {/* Mobile Vehicle Price Row */}
                    <div className="sm:hidden p-3 border-b">
                      <div className="flex items-center justify-between mb-2">
                        <div className="font-semibold flex items-center text-sm">
                          <Receipt className="h-4 w-4 mr-2 text-blue-500" />
                          {t("finance.items.vehicle-price")}
                        </div>
                        <div
                          className={`font-medium text-sm ${
                            auctionUnpaid > 0
                              ? "text-red-500"
                              : "text-green-500"
                          }`}
                        >
                          {formatter.format(auctionUnpaid)}
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                        <div>
                          Total: {formatter.format(paymentData?.price || 0)}
                        </div>
                        <div className="text-right">
                          Paid: {formatter.format(auctionPayments)}
                        </div>
                      </div>
                    </div>

                    {/* Transportation Fee Accordion - Only show if status is paid, open, or past_due */}
                    {shouldDisplayAccordion && (
                      <Accordion
                        defaultValue="item-0"
                        type="single"
                        collapsible
                        className="w-full"
                      >
                        <AccordionItem
                          value="item-0"
                          className="data-[state=open]:border-b-2 data-[state=open]:border-green-500 dark:data-[state=open]:border-green-400 data-[state=open]:shadow-sm"
                        >
                          {/* Desktop Transportation Fee Header */}
                          <AccordionTrigger
                            className="max-sm:hidden data-[state=open]:text-green-500 dark:data-[state=open]:text-green-400 text-sm font-semibold py-3 hover:bg-secondary/10 data-[state=open]:bg-primary/5 data-[state=open]:dark:bg-secondary focus:ring-2 focus:ring-green-500 hover:no-underline grid grid-cols-[1fr_1fr_1fr_1fr] items-center px-3"
                            aria-label="Toggle Transportation Fee details"
                            hideArrow
                          >
                            <div className="flex items-center">
                              <Ambulance className="h-4 w-4 mr-2 text-purple-500" />
                              {t("finance.items.transportation-fee")}
                            </div>
                            <div>{formatter.format(transportationFee)}</div>
                            <div className="text-green-600 font-medium">
                              {formatter.format(transportationPayments)}
                            </div>
                            <div
                              className={`font-medium ${
                                transportationUnpaid > 0
                                  ? "text-red-500"
                                  : "text-green-500"
                              }`}
                            >
                              {formatter.format(transportationUnpaid)}
                            </div>
                          </AccordionTrigger>

                          {/* Mobile Transportation Fee Header */}
                          <AccordionTrigger
                            className="sm:hidden data-[state=open]:text-green-500 dark:data-[state=open]:text-green-400 text-sm font-semibold py-3 hover:bg-secondary/10 data-[state=open]:bg-primary/5 data-[state=open]:dark:bg-secondary focus:ring-2 focus:ring-green-500 hover:no-underline px-3"
                            aria-label="Toggle Transportation Fee details"
                          >
                            <div className="flex items-center justify-between w-full mr-4">
                              <div className="flex items-center">
                                <Ambulance className="h-4 w-4 mr-2 text-purple-500" />
                                {t("finance.items.transportation-fee")}
                              </div>
                              <div className={`font-medium`}>
                                {formatter.format(transportationPayments)}
                              </div>
                              <div
                                className={`font-medium ${
                                  transportationUnpaid > 0
                                    ? "text-red-500"
                                    : "text-green-500"
                                }`}
                              >
                                {formatter.format(transportationUnpaid)}
                              </div>
                            </div>
                          </AccordionTrigger>

                          {!hasPreviousPaymentAllocation() && (
                            <AccordionContent className="px-3 max-sm:px-2 pb-3">
                              {/* Desktop Individual Charges Table */}
                              <div className="max-sm:hidden border rounded-lg overflow-hidden">
                                <div className="grid grid-cols-4 text-sm p-3 font-medium bg-muted/20 border-b">
                                  <div>Charge Name</div>
                                  <div>Amount</div>
                                  <div>Payment</div>
                                  <div>Balance</div>
                                </div>
                                {chargesData.map((charge, index) => {
                                  const paymentAmount = getPaymentForChargeType(
                                    charge.type
                                  );
                                  const balance =
                                    Number(charge.amount) -
                                    Number(paymentAmount);

                                  return (
                                    <div
                                      key={`charge-${index}`}
                                      className="grid grid-cols-4 items-center p-3 border-b last:border-0 hover:bg-muted/5"
                                    >
                                      <div className="flex items-center font-semibold capitalize">
                                        <DollarSign className="h-4 w-4 mr-2 text-green-500" />
                                        {charge.name}
                                      </div>
                                      <div className="font-medium">
                                        {formatter.format(charge.amount)}
                                      </div>
                                      <div className="text-green-600 font-medium">
                                        {formatter.format(paymentAmount)}
                                      </div>
                                      <div
                                        className={`font-medium ${
                                          balance > 0
                                            ? "text-red-500"
                                            : "text-green-500"
                                        }`}
                                      >
                                        {formatter.format(Math.max(0, balance))}
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>

                              {/* Mobile Individual Charges */}
                              <div className="sm:hidden space-y-2">
                                {chargesData.map((charge, index) => {
                                  const paymentAmount = getPaymentForChargeType(
                                    charge.type
                                  );
                                  const balance =
                                    Number(charge.amount) -
                                    Number(paymentAmount);

                                  return (
                                    <div
                                      key={`charge-${index}`}
                                      className="border rounded-md p-3 mt-3 bg-muted/5"
                                    >
                                      <div className="flex items-center justify-between mb-2">
                                        <div className="flex items-center font-semibold capitalize text-sm">
                                          <DollarSign className="h-3 w-3 mr-1 text-green-500" />
                                          {charge?.name}
                                        </div>

                                        <div>
                                          {formatter.format(charge.amount)}
                                        </div>
                                      </div>
                                      <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                                        <div>Paid Amount</div>
                                        <div className="text-right">
                                          {formatter.format(paymentAmount)}
                                        </div>
                                      </div>
                                      <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                                        <div>Due Balance</div>
                                        <div
                                          className={`text-right ${
                                            balance > 0
                                              ? "text-red-500"
                                              : "text-green-500"
                                          }`}
                                        >
                                          {formatter.format(
                                            Math.max(0, balance)
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            </AccordionContent>
                          )}
                        </AccordionItem>
                      </Accordion>
                    )}

                    {/* Desktop Grand Total */}
                    <div className="max-sm:hidden grid grid-cols-[1fr_1fr_1fr_1fr] text-sm p-4 dark:bg-secondary bg-primary/5 font-bold">
                      <div className="text-base flex items-center">
                        <Calculator className="h-4 w-4 mr-2 text-primary" />
                        {t("finance.items.grand-total")}
                      </div>
                      <div className="text-base text-primary">
                        {formatter.format(grandTotal)}
                      </div>
                      <div className="text-base text-primary">
                        {formatter.format(totalPaid)}
                      </div>
                      <div
                        className={`text-base ${
                          totalUnpaid > 0 ? "text-red-500" : "text-primary"
                        }`}
                      >
                        {formatter.format(totalUnpaid)}
                      </div>
                    </div>

                    {/* Mobile Grand Total */}
                    <div className="sm:hidden p-3 dark:bg-secondary bg-primary/5 font-bold">
                      <div className="flex items-center justify-between mb-2">
                        <div className="text-sm flex items-center">
                          <Calculator className="h-4 w-4 mr-2 text-primary" />
                          {t("finance.items.grand-total")}
                        </div>
                        <div
                          className={`text-sm ${
                            totalUnpaid > 0 ? "text-red-500" : "text-primary"
                          }`}
                        >
                          {formatter.format(totalUnpaid)}
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                        <div>Total: {formatter.format(grandTotal)}</div>
                        <div className="text-right">
                          Paid: {formatter.format(totalPaid)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Payment Status Card */}
                <div className="border rounded-lg max-sm:rounded-md p-4 max-sm:p-3 flex items-center gap-3 max-sm:gap-2 bg-muted/5 shadow-sm">
                  {totalUnpaid <= 0 ? (
                    <>
                      <div className="h-10 w-10 max-sm:h-8 max-sm:w-8 rounded-full bg-green-100 flex items-center justify-center">
                        <CheckCircle className="h-6 w-6 max-sm:h-5 max-sm:w-5 text-green-500" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-green-500 max-sm:text-sm">
                          {t("finance.cards.paid-in-full")}
                        </h4>
                        <p className="text-xs max-sm:text-xs text-muted-foreground">
                          {t("finance.cards.all-payments-have-been-received")}
                        </p>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="h-10 w-10 max-sm:h-8 max-sm:w-8 rounded-full bg-amber-100 flex items-center justify-center">
                        <AlertCircle className="h-6 w-6 max-sm:h-5 max-sm:w-5 text-amber-500" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-amber-500 max-sm:text-sm">
                          {t("finance.cards.payment-required")}
                        </h4>
                        <p className="text-xs max-sm:text-xs text-muted-foreground">
                          {formatter.format(totalUnpaid)}{" "}
                          {t("finance.cards.balance-remaining")}
                        </p>
                      </div>
                    </>
                  )}
                </div>
              </TabsContent>

              {/* Payments Tab Content */}
              <TabsContent value="payments" className="mt-0">
                <h3 className="font-semibold mb-3 max-sm:mb-2 max-sm:text-sm flex items-center gap-1">
                  <CreditCard className="h-4 w-4 max-sm:h-3 max-sm:w-3 text-primary" />
                  {t("payments-list.title")}
                </h3>

                {/* Auction Payments Card */}
                <div className="border rounded-lg max-sm:rounded-md overflow-hidden mb-4 max-sm:mb-3 shadow-sm">
                  <div className="p-4 max-sm:p-3 bg-primary/5 flex justify-between items-center">
                    <h4 className="font-semibold max-sm:text-sm flex items-center">
                      <Receipt className="h-4 w-4 max-sm:h-3 max-sm:w-3 mr-2 text-blue-500" />
                      {t("payments-list.header.auction-payment")}
                    </h4>
                    <span className="text-xs max-sm:text-xs px-2 py-1 rounded-full bg-primary/10">
                      {t("payments-list.header.auction-payment")}
                    </span>
                  </div>
                  <div className="border-t">
                    {/* Desktop Payments Header */}
                    <div className="max-sm:hidden grid grid-cols-3 text-sm p-3 font-medium bg-muted/20 border-b">
                      <div>{t("payments-list.auction-payment.amount")}</div>
                      <div>{t("payments-list.auction-payment.date")}</div>
                      <div>{t("payments-list.auction-payment.type")}</div>
                    </div>

                    {/* Mobile Payments Header */}
                    <div className="sm:hidden grid grid-cols-2 text-xs p-2 font-medium bg-muted/20 border-b">
                      <div>{t("payments-list.auction-payment.amount")}</div>
                      <div className="text-right">
                        {t("payments-list.auction-payment.date")}
                      </div>
                    </div>

                    {paymentData?.payments &&
                    paymentData.payments.length > 0 ? (
                      paymentData.payments
                        .filter((payment: any) => payment.type === "auction")
                        .map((payment: any) => (
                          <div key={payment.id}>
                            {/* Desktop Payment Row */}
                            <div className="max-sm:hidden grid grid-cols-3 text-sm p-3 border-b last:border-0 hover:bg-muted/5">
                              <div className="font-medium">
                                {formatter.format(payment.amount_applied)}
                              </div>
                              <div>{formatDate(payment.payment_date)}</div>
                              <div className="capitalize">{payment.type}</div>
                            </div>

                            {/* Mobile Payment Row */}
                            <div className="sm:hidden p-3 border-b last:border-0">
                              <div className="flex justify-between items-center mb-1">
                                <div className="font-medium text-sm">
                                  {formatter.format(payment.amount_applied)}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {formatDate(payment.payment_date)}
                                </div>
                              </div>
                              <div className="text-xs text-muted-foreground capitalize">
                                Type: {payment.type}
                              </div>
                            </div>
                          </div>
                        ))
                    ) : (
                      <div className="p-4 max-sm:p-3 text-sm max-sm:text-xs text-center text-muted-foreground">
                        No auction payments available
                      </div>
                    )}
                  </div>
                </div>

                {shouldDisplayAccordion && (
                  <div className="border rounded-lg max-sm:rounded-md overflow-hidden mb-4 max-sm:mb-3 shadow-sm">
                    <div className="p-4 max-sm:p-3 bg-primary/5 flex justify-between items-center">
                      <h4 className="font-semibold max-sm:text-sm flex items-center">
                        <ShoppingCart className="h-4 w-4 max-sm:h-3 max-sm:w-3 mr-2 text-purple-500" />
                        {t("payments-list.header.mix-payment")}
                      </h4>
                      <span className="text-xs max-sm:text-xs px-2 py-1 rounded-full bg-primary/10">
                        {t("payments-list.header.mix-payment")}
                      </span>
                    </div>
                    <div className="border-t">
                      {/* Desktop Mix Payments Header */}
                      <div className="max-sm:hidden grid grid-cols-3 text-sm p-3 font-medium bg-muted/20 border-b">
                        <div>{t("payments-list.mix-payment.amount")}</div>
                        <div>{t("payments-list.mix-payment.date")}</div>
                        <div>{t("payments-list.mix-payment.type")}</div>
                      </div>

                      {/* Mobile Mix Payments Header */}
                      <div className="sm:hidden grid grid-cols-2 text-xs p-2 font-medium bg-muted/20 border-b">
                        <div>{t("payments-list.mix-payment.amount")}</div>
                        <div className="text-right">
                          {t("payments-list.mix-payment.date")}
                        </div>
                      </div>

                      {mixPayments &&
                      mixPayments.length > 0 &&
                      mixShippingData?.mix_shipping_invoices.type
                        .trim()
                        .toLowerCase() === "mix" ? (
                        mixPayments.map((payment: any) => (
                          <div key={payment.id}>
                            {/* Desktop Mix Payment Row */}
                            <div className="max-sm:hidden grid grid-cols-3 text-sm p-3 border-b last:border-0 hover:bg-muted/5">
                              <div className="font-medium">
                                {formatter.format(payment.amount_applied)}
                              </div>
                              <div>{formatDate(payment.payment_date)}</div>
                              <div className="capitalize">{payment.type}</div>
                            </div>

                            {/* Mobile Mix Payment Row */}
                            <div className="sm:hidden p-3 border-b last:border-0">
                              <div className="flex justify-between items-center mb-1">
                                <div className="font-medium text-sm">
                                  {formatter.format(payment.amount_applied)}
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  {formatDate(payment.payment_date)}
                                </div>
                              </div>
                              <div className="text-xs text-muted-foreground capitalize">
                                Type: {payment.type}
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="p-4 max-sm:p-3 text-sm max-sm:text-xs text-center text-muted-foreground">
                          No mix payments available
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
