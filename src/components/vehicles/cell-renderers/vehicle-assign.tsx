import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { CustomCellRendererProps } from "ag-grid-react";
import { EllipsisVertical, Loader2, Pipette, PointerOff } from "lucide-react";
import { useEffect, useState, type FunctionComponent } from "react";

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { CustomCombobox } from "@/components/Common_UI/custom-combobox";
import { useGetCustomerAutoComplete } from "../services/use-autocomplete-customer";
import { z } from "zod";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useAssigVehicleToCustomer,
  useAssingVehicleToCustomerEdit,
  useUnAssingVehicleToCustomer,
} from "../services/use-assign-vehicle-to-customer";
import { toast } from "sonner";
import { LoadingSpinner } from "@/components/Common_UI/loading";
import { useTranslations } from "next-intl";
import { useDirection } from "@/hooks/useDirection";
import { useRouter } from "next/navigation";
import { useQueryClient } from "@tanstack/react-query";
const assignSchema = z.object({
  customer_id: z.string().min(1, "Required").nullable(),
  vehicle_id: z.string().min(1, "Required"),
});
type FormFields = z.infer<typeof assignSchema>;
export const AssignCell: FunctionComponent<CustomCellRendererProps> = ({
  data,
}) => {
  const t = useTranslations("datatable");
  const t2 = useTranslations("sidebar.nav.customer");
  const t3 = useTranslations("combox-options");
  const query = useQueryClient();
  const { isRTL, dir } = useDirection();
  const router = useRouter();
  const { data: customer, refetch } = useGetCustomerAutoComplete();
  const { assigVehicleToCustomer, isPending } = useAssigVehicleToCustomer();
  const { assigVehicleToCustomerEdit, isPending: isEditing } =
    useAssingVehicleToCustomerEdit();
  const { unAssigVehicleToCustomer, isPending: isUnAssigning } =
    useUnAssingVehicleToCustomer();
  const [isOpen, setIsOpen] = useState(false);
  const [isUnAssign, setIsUnAssign] = useState(false);
  const [customersArray, setCustomersArray] = useState<
    {
      name: string;
      id: string | number;
    }[]
  >([]);
  const form = useForm<FormFields>({
    resolver: zodResolver(assignSchema),
    defaultValues: {
      vehicle_id: `${data?.id}`,
      customer_id: data?.vehicles_customer_of_customer?.length
        ? data?.vehicles_customer_of_customer?.at(0).customer_of_customer?.id
        : null,
    },
  });
  const handleFetch = () => {
    refetch();
  };
  const isEdit = data?.vehicles_customer_of_customer?.length > 0 ? true : false;

  const [dropdownOpen, setDropdownOpen] = useState(false);

  const handleCellClick = () => {
    setDropdownOpen(true);
  };

  const onSubmit = (formData: FormFields) => {
    if (isEdit) {
      assigVehicleToCustomerEdit(
        { ...formData, id: data?.vehicles_customer_of_customer[0].id },
        {
          onSuccess() {
            router.refresh();
            toast.success("Vehicle Assigned Successfully");
            setIsOpen(false);
            query.invalidateQueries({ queryKey: ["customer-autocomplete"] });
          },
          onError() {
            toast.error("Failed to Assign Vehicle");
          },
        }
      );
    } else {
      assigVehicleToCustomer(formData, {
        onSuccess() {
          router.refresh();
          toast.success("Vehicle Assigned Successfully");
          setIsOpen(false);
          query.invalidateQueries({ queryKey: ["customer-autocomplete"] });
        },
        onError() {
          toast.error("Failed to Assign Vehicle");
        },
      });
    }
  };
  useEffect(() => {
    const customersArray =
      customer?.map((item: any) => ({
        id: item.id,
        name: `${item.fullname} (${item.loginable?.email})`,
      })) || [];
    setCustomersArray((prev) => [...prev, ...customersArray]);
  }, [customer]);

  return (
    <>
      <div
        onClick={handleCellClick}
        className="cursor-pointer w-full h-full flex items-center justify-center relative"
      >
        <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
          <DropdownMenuTrigger asChild className="w-full pt-7 mx-auto">
            <Button
              size={"icon"}
              variant={"link"}
              className="flex items-center justify-center"
            >
              <EllipsisVertical className="w-16 h-16 border-0" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-40">
            <DropdownMenuGroup>
              <DropdownMenuItem onClick={() => setIsOpen(true)}>
                {isRTL ? (
                  <span className="text-xs flex justify-around gap-2">
                    <DropdownMenuShortcut>
                      <Pipette className="w-4 h-4" />
                    </DropdownMenuShortcut>
                    {t("header.assigned")}
                  </span>
                ) : (
                  <span className="text-xs flex justify-around gap-2">
                    {t("header.assigned")}
                    <DropdownMenuShortcut>
                      <Pipette className="w-4 h-4" />
                    </DropdownMenuShortcut>
                  </span>
                )}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => setIsUnAssign(true)}
                className={`cursor-pointer  ${isEdit ? "" : "hidden"}`}
              >
                {isRTL ? (
                  <span className="text-xs flex justify-around gap-2">
                    <DropdownMenuShortcut>
                      <PointerOff className="w-4 h-4" />
                    </DropdownMenuShortcut>
                    {t("header.unassigned")}
                  </span>
                ) : (
                  <span className="text-xs flex justify-around gap-2">
                    {t("header.unassigned")}
                    <DropdownMenuShortcut>
                      <PointerOff className="w-4 h-4" />
                    </DropdownMenuShortcut>
                  </span>
                )}
              </DropdownMenuItem>
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen} key={"customer-form"}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t2("label")}</DialogTitle>
          </DialogHeader>
          <Card className="shadow-none border-none p-2 flex flex-col">
            <Controller
              name="customer_id"
              control={form.control}
              render={({ field }) => {
                return (
                  <CustomCombobox
                    options={customersArray}
                    btnClass="w-max min-w-full justify-between"
                    contentClass="rounded-lg border shadow-md md:min-w-[450px]"
                    label={t3("label")}
                    labelOption={t3("option-label")}
                    placeholder={t3("placehoder")}
                    value={field.value?.toString()}
                    onClick={handleFetch}
                    onChange={(value) =>
                      field.onChange(value ? `${value}` : null)
                    }
                    className={
                      form.formState.errors.customer_id ? "border-red-500" : ""
                    }
                  />
                );
              }}
            />
            <Button
              className="mt-4 "
              variant="default"
              onClick={form.handleSubmit(onSubmit)}
            >
              {isPending || isEditing ? (
                <LoadingSpinner className="h-5 w-5" />
              ) : (
                t("header.assigned")
              )}
            </Button>
          </Card>
        </DialogContent>
      </Dialog>
      <AlertDialog open={isUnAssign} onOpenChange={setIsUnAssign}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className={`${isRTL ? "text-right" : ""}`}>
              {t("vehicle-assigin.popup.title")}
            </AlertDialogTitle>
            <AlertDialogDescription className={`${isRTL ? "text-right" : ""}`}>
              {t("vehicle-assigin.popup.description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter dir={dir} className={`${isRTL ? "gap-2" : ""}`}>
            <AlertDialogCancel>
              {t("vehicle-assigin.popup.cancel")}
            </AlertDialogCancel>
            <Button
              onClick={() =>
                unAssigVehicleToCustomer(
                  data?.vehicles_customer_of_customer[0].id,
                  {
                    onSuccess: () => {
                      setIsUnAssign(false);
                      router.refresh();
                      toast.success("The vehilce successfully unassigned!");
                    },
                    onError: () => {
                      toast.error("Failed to Anassigned Vehicle");
                    },
                  }
                )
              }
              disabled={isUnAssigning}
            >
              {isPending ? (
                <Loader2 className="animate-spin" />
              ) : (
                t("vehicle-assigin.popup.continue")
              )}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
