import { colorSystem, ColorSystemKey } from "@/lib/constant";
import { removeUnderScore } from "@/utils/commons";
import type { CustomCellRendererProps } from "ag-grid-react";
import { type FunctionComponent } from "react";
// import { colorSystem, type ColorSystemKey } from "../services/config";

export const StatusRenderer: FunctionComponent<CustomCellRendererProps> = ({
  value,
}) => {
  const colors = colorSystem[value as ColorSystemKey] || {
    bg: "bg-green-500/10",
    txt: "text-green-500",
  };
  return (
    <div className="flex items-center h-full select-text">
      <div
        className={`rounded-md px-2 flex leading-[22px] text-xs font-semibold overflow-hidden ${colors.bg} ${colors.txt}`}
      >
        {removeUnderScore(value)}
      </div>
    </div>
  );
};
