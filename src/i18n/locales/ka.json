{"sidebar": {"title": "Peace Global Logistics", "description": "შენ ყიდულობ, ჩვენ ვაგზავნით", "nav": {"dashboard": "დეშბორდი", "search": "ძიება", "vechicles": {"label": "ტრანსპორტი", "all": "ყველა", "auction-unpaid": "აუქციონი გადაუხდელი", "auction-paid": "აუქციონი გადახდილი", "on-the-way": "მოგზაურობაში", "on-the-hand-no": "ხელში არა/სათაური", "on-the-hand-with": "ხელში/სათაური", "on-the-hand-with-load": "ხელში დატვირთვით/სათაური", "added_by_customer": "დაამატა_მყიდველმა", "inventory-poD": {"label": "ინვენტარი (POD)", "all": "ყველა", "without-pod": "POD-ის გარეშე", "jebel-ali-uea": "ჯებელ ალი UAE"}, "inventory-pol": {"label": "ინვენტარი (POL)", "all": "ყველა", "houson-tx": "ჰიუსტონი TX", "baltimore-md": "ბალტიმორი MD", "jacksonville-fl": "ჯექსონვილი FL"}, "shipped": "გაგზავნილი", "cost-analysis": "ხარჯების ანალიზი", "dateline": "დედლაინი"}, "shipments": {"label": "გზავნილები", "all": "ყველა", "at-loading": "დატვირთვაზე", "on-the-way": "მოგზაურობაში", "arrived": "მივიდა"}, "invoice": {"label": "ინვოისი", "all": "ყველა", "open": "ღია", "past-due": "გადაცილებული ვადით", "paid": "გადახდილი"}, "mix-shipping": {"label": "შერეული გადაზიდვა", "all": "ყველა", "open": "ღია", "past-due": "გადაცილებული ვადით", "paid": "გადახდილი"}, "towing-rates": {"label": "გადაზიდვის ტარიფები", "half-cut": "ნახევრად გაჭრილი", "complete": "სრული"}, "mix-shipping-rates": "შერეული გადაზიდვის ტარიფები", "shipping-rates": "მიწოდების ტარიფები", "payments": {"label": "გადახდები", "freight-payments": {"label": "გადაზიდვის გადახდები", "all": "ყველა", "approved": "დამტკიცებული", "pending": "მოლოდინში"}, "auction-payments": {"label": "აუქციონის გადახდები", "all": "ყველა", "approved": "დამტკიცებული", "pending": "მოლოდინში"}}, "announcements": "განცხადებები", "calculator": "გადაზიდვის გამოთვრილება", "customer": {"label": "მომხმარებლები", "all": "ყველა", "enable": "ჩართვა", "disable": "გამორთვა", "trash": "საფლავი"}, "buyer-number": "მომხმარებლების ნომერი", "customer-invoice": "კლიენტის ინვოისი", "customer-vehicles": "მომხმარებლების ვიპოვობები"}, "account": {"label": "დაყენება", "mode": {"label": "რეჟიმი", "system": "სისტემა", "light": "ნათელი", "dark": "ბნელი"}, "notifications": "შეტყობინებები", "logout": "გასვლა", "button": "ცვლილებების შენახვა", "popup": {"title": "დარწმუნებული ხართ?", "description": "ეს ქმედება ვერ გაუქმდება. ეს სამუდამოდ წაშლის თქვენს ანგარიშს და მონაცემებს ჩვენს სერვერებზე.", "cancel": "გაუქმება", "continue": "გაგრძელება"}, "forgot-password": "დაგავიწყდათ პაროლი?", "forgot-password-description": "პაროლის აღსადგენად, გთხოვთ, დაუკავშირდეთ ადმინისტრატორს დამატებითი დახმარებისთვის"}}, "lang": {"label": "ენები", "en": "English", "ru": "Русский", "ka": "ქართული", "ar": "العربية"}, "profile": {"account": {"label": "ანგარიში", "description": "ნახეთ თქვენი ანგარიშის ინფორმაცია და პროფილის დეტალები.", "name": "სახელი", "username": "მომხმარებლის სახელი", "button": "ცვლილებების შენახვა", "forgot-password-description": "პაროლის აღსადგენად, გთხოვთ, დაუკავშირდეთ ადმინისტრატორს დამატებითი დახმარებისთვის", "email": "ელფოსტა", "verified": "დადასტურებული", "security": {"title": "ანგარიშის უსაფრთხოება", "description": "თქვენი ანგარიშის ინფორმაცია დაცულია და მხოლოდ თქვენთვისაა ხილული. პროფილის დეტალების განახლებისთვის, გთხოვთ, დაუკავშირდეთ მხარდაჭერას."}}, "password": {"label": "პაროლი", "description": "შეცვალეთ პაროლი აქ.", "current_password": "მიმდინარე პაროლი", "new_password": "ახალი პაროლი", "confirm_password": "დაადასტურეთ პაროლი", "button": "ცვლილებების შენახვა", "updating": "მიმდინარეობს განახლება...", "show_password": "პაროლის ჩვენება", "hide_password": "პაროლის დამალვა", "requirements": "პაროლის მოთხოვნები", "min_length": "მინიმუმ 6 სიმბოლო", "include_number": "ჩართეთ მინიმუმ ერთი ციფრი და სპეციალური სიმბოლო (რეკომენდებულია)", "include_special": "შეიცავდეს სპეციალურ სიმბოლოებს (რეკომენდებულია)", "passwords_match": "პაროლები ემთხვევა", "passwords_dont_match": "პაროლები არ ემთხვევა", "update_success": "პაროლი წარმატებით განახლდა", "update_error": "პაროლის განახლება ვერ მოხერხდა"}, "image-preveiw": {"label": "სურათის მოსაჭრელი", "cancel": "გაუქმება", "crop": "მოაჭრეთ სურათს", "adjust_crop": "მოარგეთ სურათი ჩარჩოს, შემდეგ დააწკაპუნეთ 'მოჭრა' თქვენი პროფილის სურათის შესანახად."}, "devices": {"label": "მოწყობილობები", "description": "მართეთ მოწყობილობები, რომლებიც ამჟამად შესულია თქვენს ანგარიშზე", "current_device": "მიმდინარე მოწყობილობა", "last_active": "ბოლო აქტიურობა", "ip_address": "IP მისამართი", "location": "მდებარეობა", "browser": "ბრაუზერი", "os": "ოპერაციული სისტემა", "device_type": "მოწყობილობის ტიპი", "logout": "გასვლა", "remove": "წაშლა", "active": "აქტიური", "current": "მიმდინარე", "logout_title": "მოწყობილობიდან გასვლა", "logout_description": "ეს გამოიწვევს ამ მოწყობილობიდან გასვლას. თქვენ დაგჭირდებათ ამ მოწყობილობაზე ხელახლა შესვლა.", "remove_title": "მოწყობილობის წაშლა", "remove_description": "ეს სამუდამოდ წაშლის ამ მოწყობილობას თქვენი ანგარიშის ისტორიიდან. ეს მოქმედება ვერ გაუქმდება.", "cancel": "გაუქმება", "confirm_logout": "გასვლის დადასტურება", "confirm_remove": "მოწყობილობის წაშლა", "logout_success": "მოწყობილობა წარმატებით გავიდა სისტემიდან", "logout_error": "მოწყობილობიდან გასვლა ვერ მოხერხდა", "remove_success": "მოწყობილობა წარმატებით წაიშალა", "remove_error": "მოწყობილობის წაშლა ვერ მოხერხდა", "no_devices": "მოწყობილობები არ მოიძებნა", "error": "მოწყობილობების ჩატვირთვა ვერ მოხერხდა", "refresh": "განახლება"}, "profile_tab": {"label": "პროფილი", "title": "პროფილის განახლება", "description": "დააყენეთ ლოგო და სახელი თქვენი მომხმარებლისთვის.", "new_name": "სახელი", "logo": "გადმოათრიეთ ან დააწკაპუნეთ ატვირთვისთვის", "size": "ფაილის მაქსიმალური ზომა 5MB"}}, "login": {"label": "შესვლა", "title": "კეთილი იყოს თქვენი დაბრუნება", "title_description": "შესვლა თქვენს ანგარიშზე", "email": "ელფოსტა ან მომხმარებლის სახელი", "password": "პაროლი", "forget_password": "დაგავიწყდათ პაროლი?", "register": "რეგისტრაცია", "tracking": "მიმდინარეობა", "left-side": {"title": "თქვენ ყიდულობთ, ჩვენ ვაგზავნით", "description": "ჩვენ გთავაზობთ სრულ სერვისს — საყვარელ ავტომობილებზე აუქციონზე დადებიდან მათი სასურველ ადგილამდე მიტანამდე."}, "right-side": {"title": "გლობალური გადაზიდვა, ლოკალური მიწოდება", "option1": "8 წელზე მეტი გამოცდილება ლოგისტიკაში", "option2": "მორგებული გადაწყვეტილებები ავტომობილების დიდ და მცირე გამყიდველებისთვის", "option3": "სრული მხარდაჭერა ლოგისტიკაში, ტრანსპორტსა და მიწოდების ჯაჭვში"}}, "datatable": {"sidebar": {"columns": "სვეტები", "filters": "ფილტრები", "search": "ძებნა"}, "header": {"vehicle": "ტრანსპორტი", "container": "კონტეინერი", "dates": "თარიღები", "locations": "ლოკაციები", "auction": " აუქციონი", "assigned": "მომხმარებლის ანგარიშის დაყენება", "unassigned": "მომხმარებლის ანგარიშის გაუქმება", "customer_profit": "მომხმარებლების პროცენტული", "storage-charge": "შენახვის გადასახადი", "status": "სტატუსი", "title": "სათაური", "action": "მოქმედება", "vehicle-price": "ავტომობილის ფასი", "ship-cost": "მიწოდების ღირებულება", "total-amount": "სულ თანხა", "paid-amount": "გადახდილი თანხა", "due-balance": "გადასახდელი ბალანსი"}, "body": {"delivery": "მიწოდება", "cn": "CN", "eta": "ETA", "etd": "ETD", "from": "დან", "to": "მდე", "delete": "წაშლა", "delete-description": "დარწმუნებული ხართ, რომ გსურთ ამ ავტომობილის წაშლა?", "cancel": "გაუქმება", "number": "ნომერი", "status": "სტატუსი", "receive-date": "მიღების თარიღი", "title-delivery-location": "სახელის მიღების მდებარეობა", "add-receiver-name": "მიმღების სახელის დამატება", "receiver": "მიმღები", "add-destination": "დანიშნულების პუნქტის დამატება", "is_title": "ტიტული აქვს", "is_key": "გასაღები აქვს", "is-checked": "გადამოწმებულია"}, "vehicle-details": {"header": {"photo": "ფოტო", "vehicle": "ტრანსპორტი", "dates": "თარიღები", "state": "მდგომარეობა", "general": "გენერალური", "title": "სათაური"}, "body": {"lot-number": "ლოტის ნომერი", "checked": "ჩამოწმებულია", "is-key": "არის გასაღები", "is-printed": "ჩატარებულია შთაბეჭდილება", "status": "სტატუსი", "auction-invoice-link": "აუდიტის ანგარიშის ბმული", "document-link": "დოკუმენტის ბმული", "loading-date": "ჩატვირთვის თარიღი", "loaded": "ჩატვირთულია", "container_number": "CN", "purchase-date": "ყიდვის თარიღი", "account-number": "ანგარიშის ნომერი", "is-title": "არის ტიტული", "title-status": "ტიტულის სტატუსი", "title-recive-date": "ტიტულის მიღების თარიღი", "title-number": "ტიტულის ნომერი", "age-at-pgl": "ასაკი PGL-ზე", "comment": "კომენტარი", "ship-date": "გადაზიდვის თარიღი", "reciver-name": "მიმღების სახელი", "delivery-date": "მიწოდების თარიღი", "recieve-date": "მიღების თარიღი", "pick-up-date": "აღების თარიღი"}}, "payment_details": {"label": "გადახდის დეტალები", "transaction": "ტრანზაქცია", "payment_details": "გადახდის დეტალები", "amount": "თანხა", "amount_applied": "გადახდილი თანხა", "exchange_rate": "გაცვლითი კურსი", "payment_method": "გადახდის მეთოდი", "method": "მეთოდი", "payment_date": "გადახდის თარიღი", "remarks": "შენიშვნები", "view_attachment": "დანართის ნახვა", "no_data_available": "მონაცემები არ არის ხელმისაწვდომი", "state": {"completed": "დასრულებული", "pending": "მომლოდინე", "cancelled": "გაუქმებული"}, "payment-details": {"type": "ტიპი", "reference": "რეფერენსი", "applied": "გადახდილი თანხა", "status": "სტატუსი", "remark": "შენიშვნა"}}, "announcements": {"label": "უახლესი განცხადებები", "search": "ძებნა...", "no-data": "მონაცემები არ არის", "empty": "განცხადებები ვერ მოიძებნა"}, "vehicle-assigin": {"popup": {"title": "დარწმუნებული ხარ?", "description": "ეს ქმედება ვეღარ გაუქმდება. ეს ამოიღებს ავტომობილს კლიენტიდან.", "cancel": "გაუქმება", "continue": "გაგრძელება"}}, "customer-profit": {"label": "მომხმარებლის მოგება", "description": "შეიყვანეთ ამ ავტომობილის მოგების ოდენობა", "profit-amount": "მოგების ოდენობა", "add-profit": "მოგების დამატება", "save": "შენახვა", "cancel": "გაუქმება", "saving": "შენახვა მიმდინარეობს..."}, "vehicle-drawer": {"status": "სტატუსი", "auction-name": "აუქციონის სახელი", "year": "წელი", "make": "მწარმოებელი", "model": "მოდელი", "color": "ფერი", "container-number": "კონტეინერის ნომერი", "is-printed": "დაბეჭდილია", "is-key": "გასაღები არსებობს", "title-state": "ტაითლის შტატი", "title-status": "ტაითლის სტატუსი", "title-number": "ტაითლის ნომერი", "account-number": "ანგარიშის ნომერი", "age-at-pgl": "ასაკი დატვირთვის დროს", "point-of-loading": "დატვირთვის წერტილი", "point-of-destination": "მიმღების ადგილი", "receiver-name": "მიმღების სახელი", "comment": "კომენტარი", "check": "შემოწმება", "auction-invoice-link": "აუქციონის ინვოისის ბმული", "purchased-date": "შეძენის თარიღი", "title-received-date": "ტაითლის მიღების თარიღი", "ship-date": "გაგზავნის თარიღი", "loading-date": "დატვირთვის თარიღი", "pick-up-date": "ამოღების თარიღი", "delivery-date": "მიწოდების თარიღი", "lot-number": "არქივი", "vehicle-price": "ვაკეტის ფასი", "ship-cost": "გაგზავნის ფასი", "storage-charge": "საცავის ბათი", "total-amount": "სულ არჩევა", "paid-amount": "გადახდავთ არჩევა", "due-balance": "გადახდავთ უკან"}}, "shipment-datatable": {"header": {"shipment": "გადაზიდვა", "dates": "თარიღები", "locations": "ლოკაციები", "booking": "დაჯავშვნა", "units": "მხარეები", "track": "ტრეკი", "status": "სტატუსი", "clearnce": "გადამოწმება"}, "body": {"delivery": "მიწოდება", "eta": "ETA", "etd": "ETD", "from": "დან", "to": "მდე", "units": "მხარეები", "size": "ზომა", "track": "ტრეკი", "clearance-invoice-link": "გადამოწმების ანგარიშის ბმული", "container_number": "CN"}, "shipment_gallary": {"title": "გაგზავნის სურათები", "no_images_title": "კონტეინერს არ აქვს ფოტო.", "error_images_title": "სურათების ჩატვირთვისას მოხდა შეცდომა. სცადეთ ხელახლა"}}, "invoice-datatable": {"header": {"invoice": "ანგარიში", "dates": "თარიღები", "balance": "მიღებული / დაგვიანებული", "status": "სტატუსი", "invoice_amount": "ანგარიშის ოდენობა", "purpose": "მიზანი"}, "body": {"purpose": "მიზანი", "container_number": "CN", "status": "სტატუსი", "issue_date": "გამოშვების", "due_date": "გადახდის", "invoice_amount": "ანგარიშის ოდენობა", "payment_received": "დახურული გადახდა", "received_date": "მიღების თარიღი", "balance": "მიღებული / დაგვიანებული", "past_due_days": "გაგზავნის ბოლო დღეები"}}, "mix-shipping-datatable": {"header": {"invoice": "ანგარიში", "lot": "ლოტის ნომერი", "print": "ბეჭდვა", "balance": "ბალანსი / დაგვიანებული", "dates": "თარიღები", "vin": "VIN"}, "body": {"cn": "CN", "lot_number": "ლოტის ნომერი", "invoice_amount": "ანგარიშის ოდენობა", "payment_received": "დახურული გადახდა", "balance": "ბალანსი", "past_due_days": "გაგზავნის ბოლო დღეები", "issue_date": "გამოშვების ", "due_date": "გადახდის ", "discount": "გამოტოვება", "due": "გადასახდელი", "paid": "გადახდილი", "invoice": "ინვოისი", "total_footer": "მიმდინარე გვერდი: საერთო სტატისტიკა"}}, "towing-rate-datatable": {"header": {"state-name": "შტატის სახელი", "branch-name": "ფილიალის სახელი", "city-name": "ქალაქის სახელი", "ga&ca": "GA & CA", "tx&ng": "TX & (NJ)", "bal": "(BAL)"}, "body": {"ca": "CA", "ga": "GA", "tx": "TX", "nj": "(NJ)"}, "details": {"number": "#", "branch": "ფილიალი", "cityName": "ქალაქის სახელი", "ga": "ჯორჯია", "ca": "კალიფორნია", "tx": "ტეხასი", "nj": "(ნიუ ჯერსი)", "bal": "(ბალტიმორი)"}}, "mix-shipping-rates-datatable": {"header": {"state": "შტატი", "branch_city": "ფილიალი და ქალაქი", "locations": "ლოკაციები", "towing_shipping_costs": "ბუქსირების და გადაზიდვის ღირებულება", "clearance_tds_costs": "გადამოწმების და TDS ღირებულებები", "total": "მجموعი"}, "body": {"id": "ID", "state_name": "შტატის სახელი", "branch_name": "ფილიალის სახელი", "city_name": "ქალაქის სახელი", "location_name": "ლოკაციის სახელი", "destination": "მიმართულება", "towing_cost": "ბუქსირების ღირებულება", "clearance_cost": "გადამოწმების ღირებულება", "shipping_cost": "გადაზიდვის ღირებულება", "tds_charges": "TDS გადასახადები", "tax_duty": "გადასახადები და ტარიფები", "total": "მجموعი"}, "details": {"number": "#", "branch": "ფილიალი", "city": "ქალაქი", "towing": "ბუქსირება", "shipping": "ტრანსპორტირება", "clearance": "განბაჟება", "tdsCharges": "TDS გადასახადები", "taxDuty": "გადასახადი და მოსაკრებელი", "total": "ჯამი"}}, "payment-datatable": {"header": {"payment": "Төлем", "payment_method": "Төлем әдісі", "amount": "Сома", "transaction_fee": "Транзакция тарихы", "remain_applied_amount": "გამოუყენებელი თანხა", "status": "Күйі", "exchange_rate": "Айырбас бағамы"}, "body": {"currency": "ვალუტა", "payment_method": "გადახდის მეთოდი"}}, "auction-payment-datatable": {"header": {"payment": "გადახდა", "payment-method": "გადახდის მეთოდი", "amount": "რაოდენობა", "transaction-fee": "ტრანზაქციის საკომისიო", "unapplied-amount": "გამოუყენებელი თანხა", "exchange-rate": "გაცვლითი კურსი"}, "body": {"currency": "ვალუტა", "payment_method": "მეთოდი"}, "payment_details": {"label": "გადახდის დეტალები", "transaction": "ტრანზაქცია", "payment_details": "გადახდის დეტალები", "amount": "თანხა", "amount_applied": "გამოყენებული თანხა", "exchange_rate": "გაცვლითი კურსი", "payment_method": "გადახდის მეთოდი", "method": "მეთოდი", "payment_date": "გადახდის თარიღი", "remarks": "შენიშვნები", "view_attachment": "იხილეთ დანართი", "no_data_available": "მონაცემები არ არის ხელმისაწვდომი", "state": {"completed": "დასრულებული", "pending": "მომლოდინე", "cancelled": "გაუქმებული"}, "payment-details": {"type": "ტიპი", "reference": "რეფერენსი", "applied": "გამოყენებული თანხა", "status": "სტატუსი", "remark": "შენიშვნა"}}}, "shipping-rate-datatable": {"title": "გადაზიდვის ტარიფი", "destination": "დანიშნულების ადგილი", "Shiplines": "გემების ხაზები", "Equipment": "ჩატვირთვის წერტილი | აღჭურვილობა"}, "not_found": {"title": "გვერდი ვერ მოიძებნა", "description": "უი! ჩანს, რომ გზაში აეყარეთ. თქვენ მიერ მოთხოვნილი გვერდი არ არსებობს.", "button": "თავიდან დაბრუნება"}, "combox-options": {"option-label": "არჩევანი ვერ მოიძებნა.", "placehoder": "მოძებნე არჩევანი...", "label": "აირჩიეთ ვარიანტი..."}, "server_error": {"title": "შიდა შეცდომა", "header": "უი! რაღაც წაიშალა ჩვენს მხარეს.", "description": "ჩვენი გუნდი უკვე მუშაობს ამ პრობლემაზე. გთხოვთ, მოგვიანებით სცადეთ.", "button": "დახურვა"}, "unauthorized": {"title": "არასამსახურო", "description": "თქვენ არ გაქვთ დაშვება ამ გვერდზე წვდომისათვის. თუ მიიჩნევთ, რომ ეს შეცდომა არის, გთხოვთ, დაუკავშირდეთ ადმინისტრატორს.", "button": "შესვლა"}, "notification": {"title": "შეტყობინება", "announcements": "გავრცელებები", "arrival-notices": "მომგებიანობათა წინასწარი განცხადება", "payments": "გადახდები", "mark-all-read": "ყველას წაკითხული მონიშვნა"}, "Register": {"steps": {"basic_information": "ძირითადი ინფორმაცია", "additional_information": "დამატებითი ინფორმაცია", "consignee_information": "მიმღების ინფორმაცია", "notify_party": "შეტყობინების მხარე", "load_volume": "დატვირთვის მოცულობა", "registration": "რეგისტრაცია", "contract": "ხელშეკრულება"}, "BasicInformation": {"title": "ძირითადი ინფორმაცია", "description": "გთხოვთ, მიუთითოთ თქვენი კომპანიის ძირითადი ინფორმაცია", "fullName": "სრული სახელი", "companyName": "კომპანიის სახელი", "email": "ელ-ფოსტა", "phoneNumber": "ტელეფონის ნომერი", "destination": "დანიშნულების ადგილი", "selectDestination": "აირჩიეთ დანიშნულების ადგილი", "address": "მისამართი"}, "AdditionalInformation": {"title": "დამატებითი ინფორმაცია", "description": "გთხოვთ, მიუთითოთ დამატებითი დეტალები თქვენი ბიზნესის შესახებ", "secondaryEmail": "დამატებითი ელ-ფოსტა", "secondaryPhone": "დამატებითი ტელეფონი", "joinDate": "გაწევრიანების თარიღი", "usedCar": "მეორადი მანქანების ბიზნესი", "completeCars": "სრული მანქანები", "container": "კონტეინერი", "consolidation": "კონსოლიდაცია", "halfcutCars": "ნახევრად დაჭრილი მანქანები", "vehicleTypes": "ტრანსპორტის ტიპები", "suv": "ჯიპი", "sedan": "სედანი"}, "ConsigneeInformation": {"title": "მიმღების ინფორმაცია", "description": "გთხოვთ, მიუთითოთ მიმღების დეტალები", "fullName": "სრული სახელი", "companyName": "კომპანიის სახელი", "email": "ელ-ფოსტა", "phoneNumber": "ტელეფონის ნომერი", "address": "მისამართი"}, "NotifyParty": {"title": "შეტყობინების მხარე", "description": "გთხოვთ, მიუთითოთ შეტყობინების მხარის დეტალები", "fullName": "სრული სახელი", "companyName": "კომპანიის სახელი", "email": "ელ-ფოსტა", "phoneNumber": "ტელეფონის ნომერი", "address": "მისამართი"}, "LoadVolume": {"title": "დატვირთვის მოცულობა", "description": "გთხოვთ, მიუთითოთ მოსალოდნელი დატვირთვის მოცულობა", "numberOfVehicles": "ტრანსპორტის რაოდენობა", "numberOfContainers": "კონტეინერების რაოდენობა"}, "Registration": {"title": "რეგისტრაცია", "description": "გთხოვთ, გადახედოთ თქვენს ინფორმაციას", "submit": "რეგისტრაციის გაგზავნა"}, "Contract": {"title": "ხელშეკრულება", "description": "გთხოვთ, ჩამოტვირთოთ და ხელი მოაწეროთ ხელშეკრულებას", "downloadContract": "ხელშეკრულების ჩამოტვირთვა", "uploadContract": "ხელმოწერილი ხელშეკრულების ატვირთვა", "contractUploaded": "ხელშეკრულება წარმატებით აიტვირთა", "instructions": "გთხოვთ, ჩამოტვირთოთ ხელშეკრულება, ყურადღებით გადახედოთ, ხელი მოაწეროთ და ატვირთოთ ხელმოწერილი ვერსია რეგისტრაციის დასასრულებლად.", "downloadStep": "1. ხელშეკრულების ჩამოტვირთვა", "uploadStep": "2. ხელმოწერილი ხელშეკრულების არჩევა", "downloading": "მიმდინარეობს ჩამოტვირთვა...", "selectedFile": "არჩეულია", "downloadFirst": "გთხოვთ, ჩამოტვირთოთ და გადახედოთ ხელშეკრულებას ხელმოწერილი ვერსიის არჩევამდე", "registrationComplete": "რეგისტრაცია დასრულებულია!", "registrationCompleteDescription": "გმადლობთ რეგისტრაციის დასრულებისთვის. ჩვენ განვიხილავთ თქვენს განაცხადს და დაგიკავშირდებით უახლოეს დროში. რისი გაკეთება გსურთ შემდეგ?", "registerNew": "ახალი მომხმარებლის რეგისტრაცია", "goToLogin": "შესვლის გვერდზე გადასვლა", "registrationDataNotFound": "რეგისტრაციის მონაცემები ვერ მოიძებნა", "contractDownloaded": "ხელშეკრულება წარმატებით ჩამოიტვირთა", "downloadError": "ხელშეკრულების ჩამოტვირთვა ვერ მოხერხდა", "pdfOnly": "გთხოვთ, აირჩიოთ PDF ფაილი"}, "buttons": {"next": "შემდეგი", "previous": "წინა", "submit": "გაგზავნა", "upload": "ხელშეკრულების ატვირთვა", "submitting": "დამუშავება...", "signIn": "შესვლა"}}, "vehicle-inventory-pod-datatable": {"header": {"vehicle": "მანქანები", "lot-number": "ლოტის ნომერი", "auc-pics": "აუქციონის ფოტო", "locations": "ლოკაციები", "balance": "ბალანსი", "status": "სტატუსი"}, "body": {"delivery": "მიწოდება", "lot-number": "ლოტის ნომერი", "from": "დან", "to": "მდე"}, "vehicle-inventory-datatable-pod-details": {"header": {"title": "სათაური", "status": "სტატუსი", "general": "ზოგადი"}, "body": {"checked": "შემოწმებულია", "is-key": "გასაღები არის", "is-printed": "დაბეჭდილია", "title-status": "სათაურის სტატუსი", "title-number": "სათაურის ნომერი", "age-at-pgl": "ასაკი PGL-ში", "comment": "კომენტარი", "ship-as": "გაგზავნა როგორც", "reciver-name": "მიმღების სახელი"}}, "drawer": {"status": "სტატუსი", "year": "წელი", "make": "მწარმოებელი", "model": "მოდელი", "color": "ფერი", "is-printed": "დაბეჭდილია", "is-key": "არსებობს გასაღები", "title-state": "ტაითლის შტატი", "title-number": "ტაითლის ნომერი", "account-number": "ანგარიშის ნომერი", "age-at-pgl": "ასაკი დატვირთვის დროს", "point-of-loading": "დატვირთვის წერტილი", "point-of-destination": "დანიშნულების წერტილი", "receiver-name": "მიმღების სახელი", "comment": "კომენტარი", "check": "შემოწმება", "ship-date": "გაგზავნის თარიღი", "delivery-date": "მიწოდების თარიღი"}}, "vehicle-inventory-datatable": {"header": {"vehicle": "ტრანსპორტი", "lot-number": "ლოტის #", "auc-pics": "აუქციონური სურათები", "locations": "ლოკაციები", "balance": "ბალანსი", "status": "სტატუსი"}, "body": {"delivery": "მიწოდება", "lot-number": "ლოტის ნომერი", "from": "დან", "to": "მდე"}}, "vehicle-inventory-pol-datatable": {"header": {"vehicle": "მანქანები", "lot-number": "სათაურის ნომერი", "auc-pics": "აუქციონის ფოტო", "locations": "ლოკაციები", "balance": "ბალანსი", "status": "სტატუსი"}, "body": {"delivery": "მიწოდება", "lot-number": "ლოტის ნომერი", "from": "დან", "to": "მდე"}, "vehicle-inventory-datatable-pol-details": {"header": {"title": "სათაური", "status": "სტატუსი", "general": "ზოგადი"}, "body": {"checked": "შემოწმებულია", "is-key": "გასაღები არის", "is-printed": "დაბეჭდილია", "title-status": "სათაურის სტატუსი", "title-number": "სათაურის ნომერი", "age-at-pgl": "ასაკი PGL-ში", "comment": "კომენტარი", "ship-as": "გაგზავნა როგორც", "reciver-name": "მიმღების სახელი"}}, "drawer": {"status": "სტატუსი", "year": "წელი", "make": "მწარმოებელი", "model": "მოდელი", "color": "ფერი", "is-printed": "დაბეჭდილია", "is-key": "არსებობს გასაღები", "title-state": "ტაითლის შტატი", "title-number": "ტაითლის ნომერი", "age-at-pgl": "ასაკი დატვირთვის დროს", "point-of-loading": "დატვირთვის წერტილი", "point-of-destination": "დანიშნულების წერტილი", "receiver-name": "მიმღების სახელი", "comment": "კომენტარი", "check": "შემოწმება", "ship-date": "გაგზავნის თარიღი", "delivery-date": "მიწოდების თარიღი"}}, "vehicle-cost-analysis-datatable": {"header": {"vehicle": "ტრანსპორტი", "container": "კონტეინერის ნომერი", "vehicle-cost": "ტრანსპორტის ღირებულება", "ship-cost": "გადაზიდვის ღირებულება", "prof-lose": "პროფიტი/დაკარგული", "description": "აღწერა", "total": "სრული", "comment": "კომენტარი", "cost": "ღირებულება"}, "body": {"lot-number": "ლოტის ნომერი", "title-number": "ტიტულის ნომერი", "cn": "CN", "vehicle-cost": "ტრანსპორტის ღირებულება", "towing": "ბუქსირება", "dismental-cost": "გაშლილი ღირებულება", "ship-cost": "გადაზიდვის ღირებულება", "strg-pol": "Strg POL", "title-cost": "ტიტულის ღირებულება", "custom": "გამოიშვას", "other-cost": "მეორე ღირებულება", "prof-lose": "პროფიტი/დაკარგული", "is-printed": "ჩატარებულია შთაბეჭდილება", "comment": "კომენტარი", "pod": "POD"}, "drawer": {"status": "სტატუსი", "year": "წელი", "make": "მწარმოებელი", "model": "მოდელი", "color": "ფერი", "lot-number": "ლოტის ნომერი", "title-number": "ტაითლის ნომერი", "container-number": "კონტეინერის ნომერი", "is-printed": "დაბეჭდილია", "vehicle-cost": "ავტომობილის ღირებულება", "towing-cost": "ბუქსირების ღირებულება", "dismantale-cost": "დაშლის ღირებულება", "storage-cost": "შენახვის ღირებულება", "ship-cost": "გადაზიდვის ღირებულება", "custom-cost": "საბაჟო გადასახადი", "other-cost": "სხვა ხარჯები", "total": "ჯამი", "point-of-destination": "დანიშნულების პუნქტი", "comment": "კომენტარი"}}, "vehicle-datelines-datatable": {"header": {"vehicle": "ტრანსპორტი", "container": "კონტეინერის ნომერი", "purchase-date": "ყიდვის თარიღი", "payment-date": "გადახდის თარიღი", "pudf-purchase": "წინააღმდეგობების დღეები", "pick-up-date": "აღების თარიღი", "comment": "კომენტარი"}, "body": {"lot-number": "ლოტის ნომერი", "title-number": "ტიტულის ნომერი", "purchase-date": "ყიდვის თარიღი", "export-date": "ექსპორტის თარიღი", "report-date": "რეპორტის თარიღი", "payment-date": "გადახდის თარიღი", "tow-request-date": "ბუქსირების მოთხოვნის თარიღი", "pick-up-date": "აღების თარიღი", "delivery-date": "მიწოდების თარიღი", "pick-up-days-from-purchase": "დრო დღეების გადასაწყვეტელი", "pick-up-days-from-report": "განახლებისა", "is-printed": "შევსებული", "comment": "კომენტარი", "point-of-destination": "POD"}, "drawer": {"status": "სტატუსი", "year": "წელი", "make": "მწარმოებელი", "model": "მოდელი", "color": "ფერი", "lot-number": "ლოტის ნომერი", "title-number": "ტაითლის ნომერი", "purchase-date": "შეძენის თარიღი", "report-date": "რეპორტის თარიღი", "payment-date": "გადახდის თარიღი", "delivery-date": "მიწოდების თარიღი", "pick-up-days-from-purchase": "ამოღების დღეები შეძენიდან", "pick-up-days-from-report": "ამოღების დღეები რეპორტიდან", "towing-request-date": "ბუქსირის მოთხოვნის თარიღი", "pick-up-date": "ამოღების თარიღი", "point-of-destination": "დანიშნულების პუნქტი", "comment": "კომენტარი"}}, "customer-invoice": {"title": "კლიენტის ინვოისები", "datatable": {"id": "იდენტიფიკატორი", "receiver_name": "მიმღების სახელი", "customer_id": "მომხმარებლის ID", "total_cost": "სრული ღირებულება", "vehicles": "ავტომობილები", "created_at": "შექმნის თარიღი", "created_date": "შექმნის თარიღი", "purchase_date": "შესყიდვის თარიღი", "bank": "აირჩიეთ ბანკი", "search": "VIN-ის ძიება", "shipment": "გადაზიდვა", "both": "ორივე", "auction": "აუქციონი"}, "details": {"title": "ავტოინვოისის დეტალები", "client_info": "კლიენტის ინფორმაცია", "invoice_no": "ინვოისის ნომერი", "date": "თარიღი", "receiver_name": "სახელი", "customer_id": "მომხმარებლის ID", "sale_date": "შესყიდვის თარიღი", "description": "აღწერა", "type": "ტიპი", "price": "ფასი", "charges": "გადასახადები", "bank_transfer": "ბანკის გადარიცხვა", "amount": "თანხა", "total": "სულ", "payment": "წინასწარი გადახდა", "port_loading": "ჩატვირთვის პორტი – აშშ", "wire_info": "გადარიცხვის ინფორმაცია", "bank_name": "ბანკის სახელი:", "account_name": "ანგარიშის მფლობელი:", "account": "ანგარიშის ნომერი:", "routing_one": "გადარიცხვის კოდი:", "routing_two": "გადარიცხვის კოდი:", "swift_code": "SWIFT კოდი:", "bank_add": "ბანკის მისამართი:", "company_add": "კომპანიის მისამართი:", "line1": "1. საჭიროა გადარიცხვის დეტალებში მიუთითოთ თქვენი მყიდველის ნომერი. თუ იხდით", "line2": "კონკრეტული ავტომობილებისთვის, მიუთითეთ ლოტის/საწყობის ნომრები.", "buyer": "მყიდველის ნომერი:", "lot_stock": "ლოტი/საწყობი:", "copart_discharge": "გამოტვირთვის პორტი – ჯორჯია", "prepayment": "წინასწარი გადახდა ავტომობილზე", "copart_address": "Copart მისამართი: 14185 Dallas Pkwy #300, Dallas, TX 75254", "iaai_address": "IAAI მისამართი: 701 Harger Road, Suite 201, Oak Brook, IL 60523", "iaai_discharge": "გამოტვირთვის პორტი – ბაქო, აზერბაიჯანი"}, "body": {"edit": "რედაქტირება", "delete": "წაშლა", "cancel": "გაუქმება", "submit": "გაგზავნა", "details": "დეტალები", "delete-description": "დარწმუნებული ხართ, რომ გსურთ ამ კლიენტის ინვოისის წაშლა?"}}, "dashboard": {"title": "დაშბორდი", "btn-download": "ჩამოტვირთვა", "tabs": {"overview": "მიმოხილვა", "vehicles": "სატრანსპორტო საშუალებები", "shipments": "გადაზიდვები", "invoices": "ინვოისები", "mix-invoice": "შერეული ინვოისი", "calculator": "კომპიუტერები"}, "progress-card": {"completion": "დასრულება"}, "overview": {"shipping-calculator": {"title": "გადაზიდვის კალკულატორი", "description": "გამოთვალეთ თქვენი ავტომობილის გადაზიდვის ღირებულება"}, "business-overview": {"title": "ბიზნესის მიმოხილვა", "description": "თქვენი ბიზნესის ძირითადი მაჩვენებლები"}, "cards": {"total-vehicles": "სატრანსპორტო საშუალებების რაოდენობა", "total-shipments": "გადაზიდვების რაოდენობა", "invoices": "ინვოისები", "mix-shipping": "შერეული გადაზიდვა", "vehicles": "სატრანსპორტო საშუალებები", "shipments": "გადაზიდვები"}, "vehicle-summery": {"title": "სატრანსპორტო საშუალებების რეზიუმე", "all": "ყველა", "location": "მდებარეობა", "auction": "აუქციონი", "on-hand": "ხელმისაწვდომი", "shipping": "ტრანსპორტირება", "total": "ჯამი", "unpaid": "გადაუხდელი", "paid": "გადახდილი", "no-title": "ტიტულის გარეშე", "with-title": "ტიტულით", "with-load": "ტვირთით", "on-the-way": "გზაშია", "shipped": "გაგზავნილია", "loading": "ავტომობილების მონაცემების ჩატვირთვა...", "no-data": "ავტომობილების მონაცემები არ არის ხელმისაწვდომი", "no-results": "შედეგები არ არის."}, "shipment-summery": {"title": "გადაზიდვების რეზიუმე", "all": "ყველა", "at-loading": "დატვირთვისას", "arrived": "ჩამოსული", "on-the-way": "გზაშია", "total": "სულ", "location": "მდებარეობა"}, "vehicle-status-chart": {"title": "სატრანსპორტო საშუალებების სტატუსის განაწილება", "all": "ყველა", "on-the-way": "გზაშია", "auction-paid": "აუქციონი გადახდილი", "auction-unpaid": "აუქციონი გადაუხდელი", "on-hand-with-load": "ხელზე დატვირთვით", "shipped": "გაგზავნილი", "on-hand-no": "ხელზე ტიტულის გარეშე", "on-hand-with-title": "ხელზე ტიტულით"}, "mix-invoice-chart": {"title": "შერეული ღია ინვოისი", "all": "ყველა", "open": "ღია", "paid": "გადახდილი", "due": "ვადაგადაცილებული"}, "invoice-chart": {"title": "ყველა ღია ინვოისი", "all": "ყველა", "open": "ღია", "paid": "გადახდილი", "due": "ვადაგადაცილებული"}, "full-open-container-invoice-chart": {"title": "სრული ღია კონტეინერის ინვოისი", "all": "ყველა", "open": "ღია", "paid": "გადახდილი", "due": "ვადაგადაცილებული"}, "welcome": {"greeting": "მოგესალმებით", "message": "აი რა ხდება თქვენს ბიზნესში დღეს"}, "business-health": {"title": "ბიზნესის ჯანმრთელობა", "excellent": "შესანიშნავი", "good": "კარგი", "fair": "საშუალო", "needs-attention": "საჭიროებს ყურადღებას"}, "progress-cards": {"overall-payment": {"title": "საერთო გადახდის დასრულება", "description": "გადახდის სტატუსი ყველა ტიპის ინვოისისთვის"}, "regular-invoice": {"title": "რეგულარული ინვოისის გადახდები", "description": "სტანდარტული ინვოისის გადახდების სტატუსი"}, "mix-invoice": {"title": "შერეული ინვოისის გადახდები", "description": "შერეული გადაზიდვის ინვოისის გადახდების სტატუსი"}}, "summaries": {"vehicle": {"title": "სატრანსპორტო საშუალებების რეზიუმე", "description": "სისტემაში არსებული ყველა სატრანსპორტო საშუალების მიმდინარე სტატუსი"}, "shipment": {"title": "გადაზიდვების რეზიუმე", "description": "ყველა გადაზიდვის და მათი სტატუსის მიმოხილვა"}}, "invoice-status": {"title": "ინვოისის სტატუსი", "description": "რეგულარული ინვოისის გადახდების განაწილება", "paid": "გადახდილი", "due": "ვადაგადაცილებული"}, "mix-invoice-status": {"title": "შერეული ინვოისის სტატუსი", "description": "შერეული ინვოისის გადახდების განაწილება"}, "container-invoice-status": {"title": "კონტეინერის ინვოისის სტატუსი", "description": "კონტეინერის ინვოისის გადახდების განაწილება"}, "action-items": {"title": "სამოქმედო პუნქტები", "description": "საკითხები, რომლებიც საჭიროებენ თქვენს ყურადღებას", "full-invoices": "სრული ინვოისები", "mix-invoices": "შერეული ინვოისები", "open": "ღია", "paid": "გადახდილი", "regular-payment-rate": "რეგულარული გადახდის განაკვეთი", "mix-payment-rate": "შერეული გადახდის განაკვეთი", "good": "კარგი", "needs-attention": "საჭიროებს ყურადღებას", "view-all-invoices": "ყველა ინვოისის ნახვა", "view-due-invoices": "ვადაგადაცილებული ინვოისების ნახვა", "view-all-mix-invoices": "ყველა შერეული ინვოისის ნახვა", "view-due-mix-invoices": "ვადაგადაცილებული შერეული ინვოისების ნახვა"}}, "bar-chart-tooltip": {"first": "სულ", "second": "გადახდილი"}, "invoice-tab": {"full-invoice-section": {"title": "სრული ინვოისები", "description": "სრული ინვოისის გადახდების განაწილება"}, "mix-invoice-section": {"title": "შერეული ინვოისები", "description": "შერეული ინვოისის გადახდების განაწილება"}, "progress-card": {"title": "გადახდის დასრულება", "description": "გადახდილი ინვოისების პროცენტული მაჩვენებელი"}, "donut-chart": {"title": "ინვოისის სტატუსი", "description": "ინვოისის გადახდების განაწილება", "total-label": "ინვოისები"}, "bar-chart": {"title": "ბოლო 12 თვის სრული ინვოისების ტენდენციები", "description": "სრული და გადახდილი ინვოისები თვეების მიხედვით"}, "metrics": {"average-value": "ინვოისის საშუალო ღირებულება", "monthly-growth": "თვიური ზრდა", "open-invoices": "ღია ინვოისები", "payment-efficiency": "გადახდის ეფექტურობა"}}, "mix-invoice-tab": {"progress-card": {"title": "შერეული გადახდის დასრულება", "description": "გადახდილი შერეული ინვოისების პროცენტული მაჩვენებელი"}, "donut-chart": {"title": "შერეული ინვოისის სტატუსი", "description": "შერეული ინვოისის გადახდების განაწილება", "total-label": "შერეული ინვოისები"}, "bar-chart": {"title": "ბოლო 12 თვის შერეული ინვოისების ტენდენციები", "description": "სულ vs გადახდილი შერეული ინვოისები თვეების მიხედვით"}, "metrics": {"average-value": "ინვოისის საშუალო ღირებულება", "monthly-growth": "თვიური ზრდა", "open-invoices": "ღია ინვოისები", "paid-invoices": "გადახდილი ინვოისები"}}, "shipment-tab": {"total-shipment": "სულ გადაზიდვები", "pi-chart": {"title": "სრული ღია კონტეინერის ინვოისი", "description": "ინვოისების ჯამი", "total-label": "ინვოისები"}}, "vehicle-tab": {"total-vehicle": "სულ სატრანსპორტო საშუალებები"}}, "calculator": {"title": "გადაზიდვის ღირებულების გამოთვლა", "description": "გამოიყენეთ ჩვენი გადაზიდვის ღირებულების კალკულატორი თქვენი ავტომობილების გადაზიდვის საფასურის დაუყოვნებლივ შესაფასებლად.", "header": {"title": "გადაზიდვის კალკულატორი", "description": "გამოთვალეთ თქვენი ავტომობილის გადაზიდვის ღირებულება"}, "form": {"state": "შტატი", "branch": "ფილიალი", "city": "ქალაქი", "destination": "დანიშნულების ადგილი", "additional_services": "დამატებითი სერვისები", "full_size_suvs": "სრული ზომის SUV", "manheim_adesa": "Manheim Adesa", "major_accident": "მძიმე ავარია", "reset": "გადატვირთვა", "submit": "ღირებულების გამოთვლა", "total_cost": "სრული ღირებულება", "loading_calculate": "მიმდინარეობს გამოთვლა...", "loading_states": "შტატების ჩატვირთვა...", "loading_branches": "ფილიალების ჩატვირთვა...", "loading_cities": "ქალაქების ჩატვირთვა...", "loading_destinations": "დანიშნულების ადგილების ჩატვირთვა...", "state_placeholder": "აირჩიეთ შტატი", "branch_placeholder": "აირჩიეთ ფილიალი", "city_placeholder": "აირჩიეთ ქალაქი", "destination_placeholder": "აირჩიეთ დანიშნულების ადგილი", "disclaimer_title": "გაფრთხილება", "disclaimer_description": "შესაძლოა დაერიცხოს საბაჟო გადასახადი დაახლოებით ავტომობილის ღირებულების 10%-ის ოდენობით.", "no_state_text": "გთხოვთ პირველი აირჩიოთ შტატი", "no_branch_text": "გთხოვთ პირველი აირჩიოთ ფილიალი"}, "errors": {"select_required": "გთხოვთ აირჩიოთ ქალაქი და დანიშნულების ადგილი", "calculation_failed": "გადაზიდვის ღირებულების გამოთვლა ვერ მოხერხდა", "try_again": "გადაზიდვის ღირებულების გამოთვლა ვერ მოხერხდა. გთხოვთ, სცადოთ ხელახლა.", "no_response": "სერვერიდან პასუხი არ მიღებულა. გთხოვთ, შეამოწმოთ თქვენი კავშირი.", "unexpected_data": "სერვერიდან მიღებულია მოულოდნელი მონაცემები", "api_error": "შეცდომა {status}: {message}", "fetch_destinations_failed": "დანიშნულების ადგილების მიღება ვერ მოხერხდა", "fetch_states_failed": "შტატების მიღება ვერ მოხერხდა", "fetch_branches_failed": "ფილიალების მიღება ვერ მოხერხდა", "fetch_cities_failed": "ქალაქების მიღება ვერ მოხერხდა", "no_shipping_rate": "მითითებული კრიტერიუმებისთვის გადაზიდვის ტარიფი ვერ მოიძებნა", "empty_response": "სერვერმა დააბრუნა ცარიელი პასუხი", "missing_data": "სერვერმა დააბრუნა პასუხი მოსალოდნელი 'data' თვისების გარეშე", "calculation_success": "გამოთვლა წარმატებით დასრულდა"}}, "filter-modal": {"checked-option": "შემოწმებული პარამეტრები", "not-checked-option": "არშემოწმებული პარამეტრები", "filters": "ფილტრები", "vehicle-filter-modal": {"container": "კონტეინერი", "point-of-loading": "ჩატვირთვის წერტილი", "point-of-destination": "დანიშნულების წერტილი", "vehicle-status": "ტრანსპორტის სტატუსი", "data": {"label": "მონაცემები", "lot-number": "ლოტის ნომერი", "vin": "VIN", "make": "მწარმოებელი", "model": "მოდელი", "year": "წელი"}, "price": "ფასი", "date-renge": {"label": "თარიღის დიაპაზონი", "purchase-at": "შეძენის თარიღი", "payment-date": "გადახდის თარიღი", "delivery-date": "მიწოდების თარიღი"}, "checked": "შემოწმებული", "is-printed": "ბეჭდილი"}, "shippment-filter-modal": {"checked-option": "შემოწმებული პარამეტრები", "not-checked-option": "არშემოწმებული პარამეტრები", "container": "კონტეინერი", "booking": "წესრიგი", "status": "სტატუსი", "date-renge": {"label": "თარიღის დიაპაზონი", "loading-date": "ჩატვირთვის თარიღი", "eta": "ETA", "etd": "ETD"}}, "invoice-filter-modal": {"container": "კონტეინერი", "status": "ტრანსპორტის სტატუსი", "invoice-number": "ინვოისის ნომერი", "invoice-amount": "ინვოისის თანხა", "payment-received": "გადახდა მიღებულია", "date-range": {"label": "თარიღის დიაპაზონი", "issue-date": "გაცემის თარიღი"}}, "mix-shipping-rates-modal": {"state": "შტატი", "branch": "ფილიალები", "point-of-loading": "დატვირთვის წერტილი", "cities": "ქალაქები"}, "payment-filter-modal": {"date-range": {"label": "თარიღის დიაპაზონი", "created-at": "შექმნის თარიღი", "updated-at": "განახლების თარიღი"}}}, "customer-of-customer": {"header": {"id": "იდენტიფიკატორი", "fullname": "სრული სახელი", "email": "ელ. ფოსტა", "username": "მომხმარებლის სახელი", "phone": "ტელეფონი", "status": "სტატუსი", "created-at": "შექმნის თარიღი", "updated-at": "განახლების თარიღი", "actions": "ქმედებები", "password": "პაროლი"}, "body": {"customers": "მომხმარებლები", "customer": "მომხმარებელი", "add-customer": "მომხმარებლის დამატება", "edit-customer": "მომხმარებლის განახლება", "fullname": "სრული სახელი", "username": "მომხმარებლის სახელი", "password": "პაროლი", "email": "ელ. ფოსტა", "phone-number": "ტელეფონის ნომერი", "status": "სტატუსი", "status-placeholder": "აირჩიეთ სტატუსი", "edit": "რედაქტირება", "delete": "წაშლა", "cancel": "გაუქმება", "submit": "გაგზავნა", "enable": "ჩართვა", "disable": "გამორთვა", "active": "აქტიური", "deactive": "არააქტიური", "delete-description": "დარწმუნებული ხართ, რომ გსურთ ამ მომხმარებლის წაშლა?", "deactive-description": "დარწმუნებული ხართ, რომ გსურთ მომხმარებლის გამორთვა?", "active-description": "დარწმუნებული ხართ, რომ გსურთ მომხმარებლის ჩართვა?"}}, "pagination": {"previous": "წინა", "next": "შემდეგი", "page": "გვერდი", "of": "დან", "to": "მდე", "results": "შედეგი", "displaying": "ნაჩვენებია"}, "autocomplete": {"label": "ძიება...", "option-label": "ვარიანტები ვერ მოიძებნა.", "placehoder": "ძიების ვარიანტი...", "label-option": "აირჩიეთ ვარიანტი...", "selected": "არჩეული ვარიანტები არ არის", "unselected": "არჩეული ვარიანტები არ არის"}, "export-modal": {"title": "მონაცემების ექსპორტი Excel ფაილად", "sub-title": "მონაცემების ექსპორტი", "select-type": "მონაცემების ტიპის არჩევა", "current-data": "მიმდინარე მონაცემები", "all-data": "ყველა მონაცემი", "cancel": "გაუქმება", "export": "ექსპორტი"}, "add-vehicle-form": {"add-vehicle": "მანქანის დამატება", "edit-vehicle": "მანქანის რედაქტირება", "labels": {"vin": "მანქანის საიდენტიფიკაციო ნომერი", "auction_name": "აუქციონი", "year": "წელი", "make": "მწარმოებელი", "model": "მოდელი", "color": "ფერი", "price": "ფასი", "lot_number": "ლოტის ნომერი", "weight": "წონა", "point_of_destination": "დანიშნულების პორტი", "pol_locations": "ჩატვირთვის პორტი", "is_title_exist": "საკუთრების არსებობა", "is_key_present": "გასაღების არსებობა", "title_number": "საკუთრების ნომერი", "title_state": "საკუთრების მდგომარეობა", "receiver_name": "მიმღების სახელი"}, "stepper": {"vehicle-information": "მანქანის ინფორმაცია", "title-key-information": "საკუთრებისა და გასაღების ინფორმაცია", "shipping-information": "გადაზიდვის ინფორმაცია", "review-and-submit": "გადახედვა და გაგზავნა", "sub-items": {"vin": "მანქანის საიდენტიფიკაციო ნომერი", "auction_name": "აუქციონის სახელი", "year": "წელი", "make": "მწარმოებელი", "model": "მოდელი", "color": "ფერი", "price": "ფასი", "title-exist": "საკუთრების არსებობა", "title-number": "საკუთრების ნომერი", "title-state": "საკუთრების მდგომარეობა", "key-present": "გასაღების არსებობა", "point_of_loading": "ჩატვირთვის პორტი", "point_of_destination": "დანიშნულების პორტი", "weight": "წონა", "lot_number": "ლოტის ნომერი", "receiver_name": "მიმღების სახელი"}}, "buttons": {"submit": "გაგზავნა", "cancel": "გაუქმება", "update": "განახლება", "previous": "წინა", "next": "შემდეგი", "edit": "რედაქტირება"}}, "auction-payment-model": {"title": "გადახდის დეტალები", "tabs": {"finance": "ფინანსები", "payments": "გადახდები"}, "finance": {"header": {"items": "ელემენტები", "amount": "თანხა", "paid-amount": "გადახდილი თანხა", "unpaid-amount": "გადაუხდელი თანხა"}, "items": {"vehicle-price": "მანქანის ფასი", "transportation-fee": "ტრანსპორტირების საფასური", "grand-total": "სულ ჯამი"}, "cards": {"paid-in-full": "სრულიად გადახდილია", "all-payments-have-been-received": "ყველა გადახდა მიღებულია", "payment-required": "გადახდა საჭიროა", "balance-remaining": "დარჩენილი ბალანსი"}}, "payments-list": {"title": "გადახდების ისტორია", "header": {"auction-payment": "აუქციონის გადახდა", "mix-payment": "შერეული გადახდა"}, "auction-payment": {"amount": "თანხა", "date": "თარიღი", "type": "ტიპი", "auction": "აუქციონი"}, "mix-payment": {"amount": "თანხა", "date": "თარიღი", "type": "ტიპი", "mix": "შერეული"}}}, "buyer-number-datatable": {"header": {"buyer-number": "ბლოკის ნომერი"}}, "forgot-password": {"title": "დაგავიწყდა პაროლი", "description": "შეიყვანეთ თქვენი ელ.ფოსტის მისამართი ქვემოთ და ჩვენ გამოგიგზავნით ინსტრუქციებს პაროლის აღსადგენად", "linkText": "დაგავიწყდა პაროლი?", "cancelButton": "გაუქმება", "submitButton": "გადაყვანის ბმულის გაგზავნა", "emailLabel": "ელ.ფოსტის მისამართი", "emailPlaceholder": "<EMAIL>"}, "reset-password": {"title": "პაროლის აღდგენა", "description": "შეიყვანეთ თქვენი ახალი პაროლი ქვემოთ და ჩვენ გამოგიგზავნით ინსტრუქციებს პაროლის აღსადგენად", "linkText": "გსურთ პაროლის აღდგენა?", "cancelButton": "გაუქმება", "submitButton": "პაროლის აღდგენა", "passwordLabel": "ახალი პაროლი", "passwordPlaceholder": "ახალი პაროლი", "confirmPasswordLabel": "პაროლის დადასტურება", "confirmPasswordPlaceholder": "პაროლის დადასტურება", "passwordRequirements": "პაროლის მოთხოვნები", "minLength": "მინიმუმ 6 სიმბოლო", "includeNumbers": "შეიცავდეს მინიმუმ ერთ რიცხვს და სპეციალურ სიმბოლოებს (რეკომენდებულია)", "includeSpecial": "შეიცავდეს სპეციალურ სიმბოლოებს (რეკომენდებულია)", "passwordsMatch": "პაროლები ემთხვევა", "passwordsDontMatch": "პაროლები არ ემთხვევა", "updateSuccess": "პაროლი წარმატებით განახლდა", "updateError": "ვერ მოხერხდა პაროლის განახლება", "validating": "შემოწმება...", "rememberPassword": "გახსოვს პაროლი?", "signIn": "შესვლა", "link-expired": {"title": "ბმულის ვადა ამოიწურა", "message": "პაროლის აღდგენის ბმული არასწორია ან მისი ვადა ამოიწურა.", "returnButton": "დაბრუნება შესვლაზე"}}, "company-profiles": {"boosmotors": {"title": "ბოს მოტორს ჯგუფი", "description": "ბოს მოტორსი არის წამყვანი გლობალური კომპანია ავტონაწილებსა და სერვისებში"}}, "customer_of_customer_payments": {"title": "კლიენტის გადახდების მართვა", "loading": "გადახდების ჩატვირთვა...", "no-data": "ამ კლიენტისა და ავტომობილისთვის გადახდები არ მოიძებნა.", "tabs": {"add-payment": "გადახდის დამატება", "payment-history": "გადახდების ისტორია"}, "add-payment": {"title": "ახალი გადახდის დამატება", "customer": "კლიენტი", "payment-amount": "გადახდის თანხა", "payment-date": "გადახდის თარიღი", "add-payment": "გადახდის დამატება"}, "payment-history": {"title": "გადახდების ისტორია", "total-payment": "გადახდების ჯამი", "payment-date": "გადახდის თარიღი", "amount": "თანხა", "created-at": "შექმნის დრო", "total-paid": "სულ გადახდილია"}}, "customer-vehicles-datatable": {"label": "კლიენტის ავტომობილები", "header": {"vehicle": "ავტომობილი", "vehicle-price": "ავტომობილის ფასი", "ship-cost": "მიწოდების ღირებულება", "customer-profit": "მომხმარებლის მოგება", "storage-charge": "შენახვის გადასახადი", "total-amount": "სულ თანხა", "customer-name": "კლიენტის სახელი", "paid-amount": "გადახდილი თანხა", "balance": "ბალანსი"}, "customer-profit": {"label": "მომხმარებლის მოგება", "description": "შეიყვანეთ ამ ავტომობილის მოგების ოდენობა", "profit-amount": "მოგების ოდენობა", "add-profit": "მოგების დამატება", "save": "შენახვა", "cancel": "გაუქმება", "saving": "შენახვა მიმდინარეობს..."}, "storage-charge": {"label": "შენახვის გადასახადი", "description": "შეიყვანეთ ამ ავტომობილის შენახვის თანხა", "storage-amount": "შენახვის თანხა", "add-storage": "შენახვის დამატება", "save": "შენახვა", "cancel": "გაუქმება", "saving": "შენახვა მიმდინარეობს..."}}, "vehicle-tracking": {"sign-in": "შესვლა", "title": "ავტომობილის თვალყურის დევნება", "description": "მოიპოვეთ ინფორმაცია თქვენი გამოგზავნილი ავტომობილის შესახებ რეალურ დროში განახლებებით და დეტალური თვალყურის დევნების სისტემით.", "search": {"title": "ავტომობილის თვალყურის დევნება", "description": "შეიყვანეთ LOT ან VIN ნომერი თქვენი ავტომობილის მიმდინარე სტატუსის სანახავად", "placeholder": "შეიყვანეთ ავტომობილის LOT ან VIN ნომერი"}, "vehicle-progress": {"title": "მიწოდების პროგრესი", "description": "დააკვირდით თქვენი ავტომობილის მოძრაობას რეალურ დროში", "ordered": "შეკვეთილია", "auction-paid": "აუქციონი გადახდილია", "auction-unpaid": "აუქციონი გადაუხდელია", "pending-auction": "მოულოდნელი აუქციონი", "on-hand-with-title": "მარაგშია სათაურით", "on-hand-no-title": "მარაგშია სათაურის გარეშე", "pending-picked": "მოლოდინშია წამოღება", "at-port": "პორტშია", "pending": "მოლოდინშია", "cost-analysis": "ღირებულების ანალიზი", "datelines": "თარიღები", "on-hand-with-load": "მარაგშია ტვირთით", "added_by_customer": "დამატებულია კლიენტის მიერ", "order-placed": "შეკვეთა განხორციელდა", "processing": "მუშავდება", "being-prepared": "მზადდება გაგზავნისთვის", "shipped": "გაგზავნილია", "on-the-way": "მიმავალია", "delivered": "მიწოდებულია", "completed": "დასრულებულია"}, "vehicle-info": {"default": {"title": "მზად არის თვალყურის დევნებისთვის", "description": "შეიყვანეთ ზემოთ LOT ან VIN ნომერი, რომ ნახოთ თქვენი ავტომობილის მიმდინარე სტატუსი და თვალყურის დევნების ინფორმაცია."}, "found": {"location-and-time": "მდებარეობა და დროის ხაზი", "auction-name": "აუქციონი", "auction-city": "აუქციონის ქალაქი", "purchased": "შეძენილი", "pickup": "ამოღება", "delivery": "მიწოდება", "arrival-date": "ჩამოსვლის თარიღი", "departure-date": "გასვლის თარიღი", "loading-date": "დატვირთვის თარიღი"}, "not-found-image": {"title": "ავტომობილის ფოტო", "description": "სურათი აქ გამოჩნდება"}, "not-found-data": {"title": "ავტომობილი ვერ მოიძებნა", "description": "თქვენ მიერ მითითებული თვალყურის დევნის ნომრით ავტომობილი ვერ მოიძებნა. გთხოვთ, გადაამოწმეთ მონაცემები და სცადეთ ხელახლა.", "btn": "სცადე სხვა ძებნა"}}}}