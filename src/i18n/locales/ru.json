{"sidebar": {"title": "Peace Global Logistics", "description": "Вы покупаете — мы доставляем", "nav": {"dashboard": "Панель управления", "search": "Поиск", "vechicles": {"label": "Транспортные средства", "all": "Все", "auction-unpaid": "Аукцион: Неоплаченные", "auction-paid": "Аукцион: Оплаченные", "on-the-way": "В пути", "on-the-hand-no": "На руках без/Титул", "on-the-hand-with": "На руках с/Титул", "on-the-hand-with-load": "На руках с грузом/Титул", "added_by_customer": "добавлено_клиентом", "inventory-poD": {"label": "Инвентарь (POD)", "all": "Все", "without-pod": "Без POD", "jebel-ali-uea": "Джебель-Али ОАЭ"}, "inventory-pol": {"label": "Инвентарь (POL)", "all": "Все", "houson-tx": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Техас", "baltimore-md": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, М<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jacksonville-fl": "Джексон<PERSON>и<PERSON><PERSON>, Флорида"}, "shipped": "Отправлено", "cost-analysis": "Ана<PERSON><PERSON>з стоимости", "dateline": "Срок"}, "shipments": {"label": "Отгрузки", "all": "Все", "at-loading": "На загрузке", "on-the-way": "В пути", "arrived": "Прибыло"}, "invoice": {"label": "Счета", "all": "Все", "open": "Открытые", "past-due": "Просроченные", "paid": "Оплаченные"}, "mix-shipping": {"label": "Смешанные доставки", "all": "Все", "open": "Открытые", "past-due": "Просроченные", "paid": "Оплаченные"}, "towing-rates": {"label": "Тарифы на эвакуацию", "half-cut": "Половина кузова", "complete": "полный"}, "mix-shipping-rates": "Тарифы на смешанные доставки", "shipping-rates": "стоимость доставки", "payments": {"label": "Плате<PERSON>и", "freight-payments": {"label": "Оплата грузов", "all": "Все", "approved": "Одобренные", "pending": "В ожидании"}, "auction-payments": {"label": "Оплата аукционов", "all": "Все", "approved": "Одобренные", "pending": "В ожидании"}}, "announcements": "Объявления", "calculator": "Калькулятор", "customer": {"label": "Клиенты", "all": "Все", "enable": "Включить", "disable": "Отключить", "trash": "Корзина"}, "buyer-number": "Номер покупателя", "customer-invoice": "Счет клиента", "customer-vehicles": "Машины покупателя"}, "account": {"label": "Параметр", "mode": {"label": "Режим", "system": "Системный", "light": "Светлый", "dark": "Тёмный"}, "notifications": "Уведомления", "logout": "Выйти", "button": "Сохранить изменения", "popup": {"title": "Вы уверены?", "description": "Это действие нельзя будет отменить. Ваш аккаунт и данные будут удалены с наших серверов.", "cancel": "Отмена", "continue": "Продолжить"}, "forgot-password": "Забыли пароль?", "forgot-password-description": "Чтобы сбросить пароль, пожалуйста, свяжитесь с администратором для получения дальнейшей помощи"}}, "lang": {"label": "Языки", "en": "English", "ru": "Русский", "ka": "ქართული", "ar": "العربية"}, "profile": {"account": {"label": "Аккаунт", "description": "Просмотр информации вашего аккаунта и профиля.", "name": "Имя", "username": "Имя пользователя", "button": "Сохранить изменения", "forgot-password-description": "Чтобы сбросить пароль, пожалуйста, свяжитесь с администратором для получения дальнейшей помощи", "email": "Электронная почта", "verified": "Подтверждено", "security": {"title": "Безопасность аккаунта", "description": "Информация вашего аккаунта защищена и видна только вам. Для обновления данных профиля, пожалуйста, обратитесь в службу поддержки."}}, "password": {"label": "Пароль", "description": "Измените свой пароль здесь.", "current_password": "Текущий пароль", "new_password": "Новый пароль", "confirm_password": "Подтвердите пароль", "button": "Сохранить изменения", "updating": "Обновление...", "show_password": "Показать пароль", "hide_password": "Скрыть пароль", "requirements": "Требования к паролю", "min_length": "Минимум 6 символов", "include_number": "Включите хотя бы одну цифру и специальный символ (рекомендуется)", "include_special": "Включите специальные символы (рекомендуется)", "passwords_match": "Пароли совпадают", "passwords_dont_match": "Пароли не совпадают", "update_success": "Пароль успешно обновлен", "update_error": "Не удалось обновить пароль"}, "image-preveiw": {"label": "Инструмент обрезки изображений", "cancel": "Отмена", "crop": "Обрезать изображение", "adjust_crop": "Настройте изображение по размеру кадра, затем нажмите 'Обрезать', чтобы сохранить фото профиля."}, "devices": {"label": "Устройства", "description": "Управление устройствами, которые в настоящее время подключены к вашей учетной записи", "current_device": "Текущее устройство", "last_active": "Последняя активность", "ip_address": "IP-адрес", "location": "Местоположение", "browser": "Браузер", "os": "Операционная система", "device_type": "Тип устройства", "logout": "Выйти", "remove": "Удалить", "logout_title": "Выход с устройства", "logout_description": "Это приведет к выходу из системы на этом устройстве. Вам нужно будет снова войти в систему на этом устройстве.", "remove_title": "Удалить устройство", "remove_description": "Это навсегда удалит это устройство из истории вашей учетной записи. Это действие нельзя отменить.", "cancel": "Отмена", "confirm_logout": "Подтвердить выход", "confirm_remove": "Удалить устройство", "logout_success": "Устройство успешно вышло из системы", "logout_error": "Не удалось выйти из устройства", "remove_success": "Устройство успешно удалено", "remove_error": "Не удалось удалить устройство", "no_devices": "Устройства не найдены", "error": "Не удалось загрузить устройства", "refresh": "Обновить", "active": "Активно", "current": "Текущий"}, "profile_tab": {"label": "Профиль", "title": "Обновление профиля", "description": "Установите логотип и имя для вашего клиента.", "new_name": "Имя", "logo": "Перетащите или нажмите для загрузки", "size": "Макс. 5MB на файл"}}, "login": {"label": "Вход", "title": "С возвращением", "title_description": "Войдите в свой аккаунт", "email": "Электронная почта или имя пользователя", "password": "Пароль", "forget_password": "Забыли пароль?", "register": "Зарегистрироваться", "tracking": "Отслеживание", "left-side": {"title": "Вы покупаете — мы доставляем", "description": "Мы предоставляем полный комплекс услуг — от участия в торгах на аукционах США до доставки автомобилей в нужное вам место."}, "right-side": {"title": "Глобальная доставка, локальная логистика", "option1": "Более 8 лет надежного логистического опыта", "option2": "Индивидуальные решения для оптовых и розничных продавцов автомобилей", "option3": "Полная поддержка в логистике, транспорте и управлении цепочками поставок"}}, "datatable": {"sidebar": {"columns": "Столбцы", "filters": "Фильтры", "search": "Поиск"}, "header": {"vehicle": "Транспортные средства", "container": "Кон<PERSON>ейнер", "dates": "Даты", "locations": "Локации", "auction": "Аукцион", "assigned": "Назначить клиента", "unassigned": "Отменить назначение клиента", "customer_profit": "Прибыль клиента", "storage-charge": "შენახვის გადასახადი", "status": "Статус", "title": "заголовок", "action": "Действие", "vehicle-price": "Цена автомобиля", "ship-cost": "Стоимость доставки", "total-amount": "Общая сумма", "paid-amount": "Оплаченная сумма", "due-balance": "Оставшийся баланс"}, "body": {"delivery": "Доставка", "cn": "CN", "eta": "ETA", "etd": "ETD", "from": "Из", "to": "В", "delete": "Удалить", "delete-description": "Вы уверены, что хотите удалить этот автомобиль?", "cancel": "Отмена", "number": "Номер", "status": "Статус", "receive-date": "Дата получения", "title-delivery-location": "Место доставки заголовка", "add-receiver-name": "Добавить имя получателя", "receiver": "Получатель", "add-destination": "Добавить пункт назначения", "is_title": "Есть титул", "is_key": "<PERSON><PERSON><PERSON>ь ключ", "is-checked": "Проверено"}, "vehicle-details": {"header": {"photo": "Фото", "vehicle": "Транспорт", "dates": "Даты", "state": "Состояние", "general": "Общие", "title": "Титул"}, "body": {"lot-number": "Номер лота", "checked": "Проверено", "is-key": "<PERSON><PERSON><PERSON>ь ключ", "is-printed": "Напечатано", "status": "Статус", "auction-invoice-link": "Ссылка на аукционный счет", "loading-date": "Дата погрузки", "loaded": "Загружено", "container_number": "CN", "purchase-date": "Дата покупки", "account-number": "Номер счета", "is-title": "Есть титул", "title-status": "Статус титула", "title-recive-date": "Дата получения титула", "title-number": "Номер титула", "age-at-pgl": "Возраст на PGL", "comment": "Комментарий", "ship-date": "Дата отгрузки", "reciver-name": "Имя получателя", "delivery-date": "Дата доставки", "recieve-date": "Дата получения", "document-link": "Ссылка на документ", "pick-up-date": "Дата получения"}}, "payment_details": {"label": "Детали платежа", "transaction": "Транзакция", "payment_details": "Детали платежа", "amount": "Сумма", "amount_applied": "Примененная сумма", "exchange_rate": "Обменный курс", "payment_method": "Способ оплаты", "method": "Метод", "payment_date": "Дата платежа", "remarks": "Примечания", "view_attachment": "Просмотреть вложение", "no_data_available": "Нет доступных данных", "state": {"completed": "Завершено", "pending": "В ожидании", "cancelled": "Отменено"}, "payment-details": {"type": "Тип", "reference": "Справка", "applied": "Примененная сумма", "status": "Статус", "remark": "Примечание"}}, "announcements": {"label": "Последние объявления", "search": "Поиск...", "no-data": "Нет данных", "empty": "Объявления не найдены"}, "vehicle-assigin": {"popup": {"title": "Вы уверены?", "description": "Это действие невозможно отменить. Автомобиль будет удалён от клиента.", "cancel": "Отмена", "continue": "Продолжить"}}, "customer-profit": {"label": "Прибыль клиента", "description": "Введите сумму прибыли для этого транспортного средства", "profit-amount": "Сумма прибыли", "add-profit": "Добавить прибыль", "save": "Сохранить", "cancel": "Отмена", "saving": "Сохранение..."}, "vehicle-drawer": {"status": "Статус", "auction-name": "Название аукциона", "year": "Год", "make": "Производитель", "model": "Модель", "color": "Цвет", "container-number": "Номер контейнера", "is-printed": "Распечатано", "is-key": "<PERSON><PERSON><PERSON>ь ключ", "title-state": "Штат тайтла", "title-status": "Статус тайтла", "title-number": "Номер тайтла", "account-number": "Номер счёта", "age-at-pgl": "Возраст при погрузке", "point-of-loading": "Пункт погрузки", "point-of-destination": "Пункт назначения", "receiver-name": "Имя получателя", "comment": "Комментарий", "check": "Проверка", "auction-invoice-link": "Ссылка на счёт с аукциона", "purchased-date": "Дата покупки", "title-received-date": "Дата получения тайтла", "ship-date": "Дата отправки", "loading-date": "Дата погрузки", "pick-up-date": "Дата забора", "delivery-date": "Дата доставки", "lot-number": "Номер лота", "vehicle-price": "Цена автомобиля", "ship-cost": "Стоимость доставки", "storage-charge": "Списание на хранение", "total-amount": "Общая сумма", "paid-amount": "Оплаченная сумма", "due-balance": "Баланс до оплаты"}}, "shipment-datatable": {"header": {"shipment": "Отправления", "dates": "Даты", "locations": "Локации", "booking": "Бронирование", "units": "Единицы", "track": "Отслеживание", "status": "Статус", "clearnce": "Очистка"}, "body": {"delivery": "Доставка", "eta": "ETA", "etd": "ETD", "from": "Из", "to": "В", "units": "Единицы", "size": "Размер", "track": "Трекинг", "clearance-invoice-link": "Ссылка на счет", "container_number": "CN"}, "shipment_gallary": {"title": "Изображения отправления", "no_images_title": "Контейнер не содержит фотографий.", "error_images_title": "Произошла ошибка при загрузке изображений. Попробуйте снова"}}, "invoice-datatable": {"header": {"invoice": "Счет", "dates": "Даты", "balance": "Получено / Просрочено", "status": "Статус", "invoice_amount": "Сумма счета", "purpose": "Цель"}, "body": {"purpose": "Цель", "container_number": "CN", "status": "Статус", "issue_date": "выпуска", "due_date": "оплаты", "invoice_amount": "Сумма счета", "payment_received": "Полученная оплата", "received_date": "Дата получения", "balance": "Получено / Просрочено", "past_due_days": "Просроченные дни"}}, "mix-shipping-datatable": {"header": {"invoice": "Счет", "lot": "Номер лота", "print": "Печать", "balance": " Баланс / Просрочено", "dates": "Даты", "vin": "VIN"}, "body": {"cn": "CN", "lot_number": "лота", "invoice_amount": "Сумма счета", "payment_received": "Полученная оплата", "balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "past_due_days": "Просроченные дни", "issue_date": " выпуска", "due_date": " оплаты", "discount": "Скидка", "due": "К оплате", "paid": "Оплачено", "invoice": "Счет", "total_footer": "ТЕКУЩАЯ СТРАНИЦА: ОБЩАЯ СТАТИСТИКА"}}, "towing-rate-datatable": {"header": {"state-name": "Название штата", "branch-name": "Название филиала", "city-name": "Название города", "ga&ca": "GA & CA", "tx&ng": "TX & (NJ)", "bal": "(BAL)"}, "body": {"ca": "CA", "ga": "GA", "tx": "TX", "nj": "(NJ)"}, "details": {"number": "#", "branch": "Филиал", "cityName": "Название города", "ga": "Джорджия", "ca": "Калифорния", "tx": "<PERSON>е<PERSON><PERSON><PERSON>", "nj": "(Нью-Джерси)", "bal": "(Балтимор)"}}, "mix-shipping-rates-datatable": {"header": {"state": "<PERSON>т<PERSON><PERSON>", "branch_city": "Филиал и город", "locations": "Локации", "towing_shipping_costs": "Стоимость буксировки и доставки", "clearance_tds_costs": "Стоимость очистки и TDS", "total": "Итого"}, "body": {"id": "ID", "state_name": "Название штата", "branch_name": "Название филиала", "city_name": "Название города", "location_name": "Название локации", "destination": "Назначение", "towing_cost": "Стоимость буксировки", "clearance_cost": "Стоимость очистки", "shipping_cost": "Стоимость доставки", "tds_charges": "Плата за TDS", "tax_duty": "Налоги и пошлины", "total": "Итого"}, "details": {"number": "#", "branch": "Филиал", "city": "Город", "towing": "Эвакуация", "shipping": "Доставка", "clearance": "Таможенное оформление", "tdsCharges": "Сборы TDS", "taxDuty": "Налог и пошлина", "total": "Итого"}}, "auction-payment-datatable": {"header": {"payment": "Пла<PERSON><PERSON><PERSON>", "payment-method": "Способ оплаты", "amount": "Сумма", "transaction-fee": "Комиссия за транзакцию", "unapplied-amount": "Непримененная сумма", "exchange-rate": "Курс обмена"}, "body": {"currency": "Валюта", "payment_method": "Способ"}, "payment_details": {"label": "Детали платежа", "transaction": "Транзакция", "payment_details": "Детали платежа", "amount": "Сумма", "amount_applied": "Примененная сумма", "exchange_rate": "Курс обмена", "payment_method": "Способ оплаты", "method": "Способ", "payment_date": "Дата платежа", "remarks": "Примечания", "view_attachment": "Просмотреть вложение", "no_data_available": "Данные отсутствуют", "state": {"completed": "Завершено", "pending": "В ожидании", "cancelled": "Отменено"}, "payment-details": {"type": "Тип", "reference": "Референс", "applied": "Примененная сумма", "status": "Статус", "remark": "Примечание"}}}, "shipping-rate-datatable": {"title": "Тариф на доставку", "destination": "Пункт назначения", "Shiplines": "Судоходные линии", "Equipment": "Пункт загрузки | Оборудование"}, "combox-options": {"option-label": "Опции не найдены.", "placehoder": "Поиск опции...", "label": "Выберите опцию.."}, "not_found": {"title": "Страница не найдена", "description": "Упс! Похоже, вы заблудились. Страница, которую вы ищете, не существует", "button": "Вернуться на главную"}, "server_error": {"title": "Внутренняя ошибка сервера", "header": "Упс! Что-то пошло не так с нашей стороны.", "description": "Наша команда уже работает над устранением этой проблемы. Пожалуйста, попробуйте позже.", "button": "Попробовать снова"}, "unauthorized": {"title": "Неавторизовано", "description": "У вас нет прав для доступа к этой странице. Если вы считаете, что это ошибка, пожалуйста, свяжитесь с администратором.", "button": "Вернуться в систему"}, "notification": {"title": "Уведомление", "announcements": "Объявления", "arrival-notices": "Уведомления о прибытии", "payments": "Плате<PERSON>и", "mark-all-read": "Отметить все как прочитанные"}, "Register": {"steps": {"basic_information": "Основная информация", "additional_information": "Дополнительная информация", "consignee_information": "Информация о получателе", "notify_party": "Уведомляемая сторона", "load_volume": "Объем загрузки", "registration": "Регистрация", "contract": "Контракт"}, "BasicInformation": {"title": "Основная информация", "description": "Пожалуйста, предоставьте основную информацию о вашей компании", "fullName": "Полное имя", "companyName": "Название компании", "email": "Электронная почта", "phoneNumber": "Номер телефона", "destination": "Пункт назначения", "selectDestination": "Выберите пункт назначения", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "AdditionalInformation": {"title": "Дополнительная информация", "description": "Пожалуйста, предоставьте дополнительные сведения о вашем бизнесе", "secondaryEmail": "Дополнительная почта", "secondaryPhone": "Дополнительный телефон", "joinDate": "Дата присоединения", "usedCar": "Бизнес подержанных автомобилей", "completeCars": "Полные автомобили", "container": "Кон<PERSON>ейнер", "consolidation": "Консолидация", "halfcutCars": "Разрезанные автомобили", "vehicleTypes": "Типы транспортных средств", "suv": "Внедорожник", "sedan": "Седан"}, "ConsigneeInformation": {"title": "Информация о получателе", "description": "Пожалуйста, предоставьте информацию о получателе", "fullName": "Полное имя", "companyName": "Название компании", "email": "Электронная почта", "phoneNumber": "Номер телефона", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "NotifyParty": {"title": "Уведомляемая сторона", "description": "Пожалуйста, предоставьте информацию об уведомляемой стороне", "fullName": "Полное имя", "companyName": "Название компании", "email": "Электронная почта", "phoneNumber": "Номер телефона", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "LoadVolume": {"title": "Объем загрузки", "description": "Пожалуйста, укажите ожидаемый объем загрузки", "numberOfVehicles": "Количество транспортных средств", "numberOfContainers": "Количество контейнеров"}, "Registration": {"title": "Регистрация", "description": "Пожалуйста, проверьте вашу информацию", "submit": "Отправить регистрацию"}, "Contract": {"title": "Контракт", "description": "Пожалуйста, скачайте и подпишите контракт", "downloadContract": "Скачать контракт", "uploadContract": "Загрузить подписанный контракт", "contractUploaded": "Контракт успешно загружен", "instructions": "Пожалуйста, скачайте контракт, внимательно ознакомьтесь с ним, подпишите и загрузите подписанную версию для завершения регистрации.", "downloadStep": "1. Скача<PERSON>ь контракт", "uploadStep": "2. Выбрать подписанный контракт", "downloading": "Загрузка...", "selectedFile": "Выб<PERSON><PERSON>н", "downloadFirst": "Пожалуйста, скачайте и ознакомьтесь с контрактом перед выбором подписанной версии", "registrationComplete": "Регистрация завершена!", "registrationCompleteDescription": "Спасибо за завершение регистрации. Мы рассмотрим вашу заявку и свяжемся с вами в ближайшее время. Что бы вы хотели сделать дальше?", "registerNew": "Зарегистрировать нового клиента", "goToLogin": "Перейти на страницу входа", "registrationDataNotFound": "Данные регистрации не найдены", "contractDownloaded": "Контракт успешно скачан", "downloadError": "Не удалось скачать контракт", "pdfOnly": "Пожалуйста, выберите файл PDF"}, "buttons": {"next": "Далее", "previous": "Назад", "submit": "Отправить", "upload": "Загрузить контракт", "submitting": "Обработка...", "signIn": "Войти"}}, "vehicle-inventory-datatable": {"header": {"vehicle": "Транспортные средства", "lot-number": "Номер титула", "auc-pics": "Фото аукциона", "locations": "Локации", "balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "status": "Статус"}, "body": {"delivery": "Доставка", "lot-number": "Номер лота", "from": "Из", "to": "В"}, "drawer": {"status": "Статус", "year": "Год", "make": "Производитель", "model": "Модель", "color": "Цвет", "is-printed": "Распечатано", "is-key": "<PERSON><PERSON><PERSON>ь ключ", "title-state": "Штат тайтла", "title-number": "Номер тайтла", "account-number": "Номер счёта", "age-at-pgl": "Возраст при погрузке", "point-of-loading": "Пункт погрузки", "point-of-destination": "Пункт назначения", "receiver-name": "Имя получателя", "comment": "Комментарий", "check": "Проверка", "ship-date": "Дата отправки", "delivery-date": "Дата доставки"}}, "vehicle-inventory-datatable-pod-details": {"header": {"title": "Титул", "status": "Статус", "general": "Общие"}, "body": {"checked": "Проверено", "is-key": "<PERSON><PERSON><PERSON>ь ключ", "is-printed": "Напечатано", "title-status": "Статус титула", "title-number": "Номер титула", "age-at-pgl": "Возраст на PGL", "comment": "Комментарий", "ship-as": "Отгрузка", "reciver-name": "Имя получателя"}}, "vehicle-inventory-pol-datatable": {"header": {"vehicle": "Транспортные средства", "lot-number": "Номер титула", "auc-pics": "Фото аукциона", "locations": "Локации", "balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "status": "Статус"}, "body": {"delivery": "Доставка", "lot-number": "Номер лота", "from": "Из", "to": "В"}, "drawer": {"status": "სტატუსი", "year": "წელი", "make": "მწარმოებელი", "model": "მოდელი", "color": "ფერი", "is-printed": "დაბეჭდილია", "is-key": "არსებობს გასაღები", "title-state": "ტაითლის შტატი", "title-number": "ტაითლის ნომერი", "age-at-pgl": "ასაკი დატვირთვის დროს", "point-of-loading": "დატვირთვის წერტილი", "point-of-destination": "დანიშნულების წერტილი", "receiver-name": "მიმღების სახელი", "comment": "კომენტარი", "check": "შემოწმება", "ship-date": "გაგზავნის თარიღი", "delivery-date": "მიწოდების თარიღი"}}, "vehicle-inventory-pod-datatable": {"header": {"vehicle": "Автомобили", "lot-number": "Номер лота", "auc-pics": "Фото аукциона", "locations": "Локации", "balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "status": "Статус"}, "body": {"delivery": "Доставка", "lot-number": "Номер лота", "from": "Откуда", "to": "Куда"}, "vehicle-inventory-datatable-pod-details": {"header": {"title": "Название", "status": "Статус", "general": "Общее"}, "body": {"checked": "Проверено", "is-key": "<PERSON><PERSON><PERSON>ь ключ", "is-printed": "Распечатано", "title-status": "Статус названия", "title-number": "Номер названия", "age-at-pgl": "Возраст в PGL", "comment": "Комментарий", "ship-as": "Отправить как", "reciver-name": "Имя получателя"}}, "drawer": {"status": "Статус", "year": "Год", "make": "Производитель", "model": "Модель", "color": "Цвет", "is-printed": "Распечатано", "is-key": "<PERSON><PERSON><PERSON>ь ключ", "title-state": "Штат тайтла", "title-number": "Номер тайтла", "account-number": "Номер счёта", "age-at-pgl": "Возраст при погрузке", "point-of-loading": "Пункт погрузки", "point-of-destination": "Пункт назначения", "receiver-name": "Имя получателя", "comment": "Комментарий", "check": "Проверка", "ship-date": "Дата отправки", "delivery-date": "Дата доставки"}}, "vehicle-inventory-datatable-pol-details": {"header": {"title": "Титул", "status": "Статус", "general": "Общие"}, "body": {"checked": "Проверено", "is-key": "<PERSON><PERSON><PERSON>ь ключ", "is-printed": "Напечатано", "title-status": "Статус титула", "title-number": "Номер титула", "age-at-pgl": "Возраст на PGL", "comment": "Комментарий", "ship-as": "Отгрузка", "reciver-name": "Имя получателя"}}, "vehicle-cost-analysis-datatable": {"header": {"vehicle": "Транспортное средство", "container": "Номер контейнера", "vehicle-cost": "Стоимость транспортного средства", "ship-cost": "Стоимость доставки", "prof-lose": "Прибыль/Убыток", "description": "Описание", "total": "Итого", "comment": "Комментарий", "cost": "Стоимость"}, "body": {"lot-number": "Номер лота", "title-number": "Номер титула", "cn": "CN", "vehicle-cost": "Стоимость транспортного средства", "towing": "Буксировка", "dismental-cost": "Стоимость разборки", "ship-cost": "Стоимость доставки", "strg-pol": "Strg POL", "title-cost": "Стоимость титула", "custom": "Таможня", "other-cost": "Прочие расходы", "prof-lose": "Прибыль/Убыток", "is-printed": "Напечатано", "comment": "Комментарий", "pod": "POD"}, "drawer": {"status": "Статус", "year": "Год", "make": "Производитель", "model": "Модель", "color": "Цвет", "lot-number": "Номер лота", "title-number": "Номер тайтла", "container-number": "Номер контейнера", "is-printed": "Распечатано", "vehicle-cost": "Стоимость автомобиля", "towing-cost": "Стоимость эвакуации", "dismantale-cost": "Стоимость разборки", "storage-cost": "Стоимость хранения", "ship-cost": "Стоимость доставки", "custom-cost": "Таможенные расходы", "other-cost": "Прочие расходы", "total": "Итого", "point-of-destination": "Пункт назначения", "comment": "Комментарий"}}, "vehicle-datelines-datatable": {"header": {"vehicle": "Транспорт", "container": "Номер контейнера", "purchase-date": "Дата покупки", "payment-date": "Дата оплаты", "pudf-purchase": "Дни до возврата", "pick-up-date": "Дата получения", "comment": "Комментарий"}, "body": {"lot-number": "Номер лота", "title-number": "Номер титула", "purchase-date": "Дата покупки", "export-date": "Дата экспорта", "report-date": "Дата отчета", "payment-date": "Дата оплаты", "tow-request-date": "Дата запроса буксировки", "pick-up-date": "Дата получения", "delivery-date": "Дата доставки", "pick-up-days-from-purchase": "Дни с момента покупки", "pick-up-days-from-report": "Дни с момента отчета", "is-printed": "Напечатано", "comment": "Комментарий", "point-of-destination": "POD"}, "drawer": {"status": "Статус", "year": "Год", "make": "Производитель", "model": "Модель", "color": "Цвет", "lot-number": "Номер лота", "title-number": "Номер тайтла", "purchase-date": "Дата покупки", "report-date": "Дата отчёта", "payment-date": "Дата оплаты", "delivery-date": "Дата доставки", "pick-up-days-from-purchase": "Дней до забора от даты покупки", "pick-up-days-from-report": "Дней до забора от даты отчёта", "towing-request-date": "Дата запроса на эвакуацию", "pick-up-date": "Дата забора", "point-of-destination": "Пункт назначения", "comment": "Комментарий"}}, "dashboard": {"title": "Панель управления", "btn-download": "Скачать", "tabs": {"overview": "Обзор", "vehicles": "Транспортные средства", "shipments": "Отправления", "invoices": "Счета", "mix-invoice": "Комбинированные счета", "calculator": "Калькулятор"}, "progress-card": {"completion": "Завершение"}, "overview": {"shipping-calculator": {"title": "Калькулятор доставки", "description": "Рассчитайте стоимость доставки вашего автомобиля"}, "business-overview": {"title": "Обзор бизнеса", "description": "Ключевые показатели вашего бизнеса"}, "cards": {"total-vehicles": "Всего транспортных средств", "total-shipments": "Всего отправлений", "invoices": "Счета", "mix-shipping": "Комбинированная доставка", "vehicles": "Транспортные средства", "shipments": "Отправления"}, "vehicle-summery": {"title": "Сводка по транспортным средствам", "all": "Все", "location": "Местоположение", "auction": "Аукцион", "on-hand": "В наличии", "shipping": "Доставка", "total": "Всего", "unpaid": "Неоплаченные", "paid": "Оплаченные", "no-title": "Без титула", "with-title": "С титулом", "with-load": "С грузом", "on-the-way": "В пути", "shipped": "Отправленные", "loading": "Загрузка данных о транспортных средствах...", "no-data": "Нет данных о транспортных средствах", "no-results": "Нет результатов."}, "shipment-summery": {"title": "Сводка по отправкам", "all": "Все", "location": "Местоположение", "at-loading": "На погрузке", "arrived": "Прибыли", "on-the-way": "В пути", "total": "Всего", "loading": "Загрузка данных об отправках...", "no-data": "Нет данных об отправках", "no-results": "Нет результатов."}, "vehicle-status-chart": {"title": "Распределение статусов транспортных средств", "all": "Все", "on-the-way": "В пути", "auction-paid": "Аукцион оплаченный", "auction-unpaid": "Аукцион неоплаченный", "on-hand-with-load": "На руках с грузом", "shipped": "Отправлено", "on-hand-no": "На руках без титула", "on-hand-with-title": "На руках с титулом"}, "mix-invoice-chart": {"title": "Открытые комбинированные счета", "all": "Все", "open": "Открытые", "paid": "Оплаченные", "due": "Просроченные"}, "invoice-chart": {"title": "Все открытые счета", "all": "Все", "open": "Открытые", "paid": "Оплаченные", "due": "Просроченные"}, "full-open-container-invoice-chart": {"title": "Открытые счета по полным контейнерам", "all": "Все", "open": "Открытые", "paid": "Оплаченные", "due": "Просроченные"}, "welcome": {"greeting": "Добро пожаловать", "message": "Вот что происходит с вашим бизнесом сегодня"}, "business-health": {"title": "Состояние бизнеса", "excellent": "Отлично", "good": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fair": "Удовлетворительно", "needs-attention": "Требует внимания"}, "progress-cards": {"overall-payment": {"title": "Общее завершение платежей", "description": "Сводный статус платежей по всем типам счетов"}, "regular-invoice": {"title": "Платежи по обычным счетам", "description": "Статус платежей по стандартным счетам"}, "mix-invoice": {"title": "Платежи по комбинированным счетам", "description": "Статус платежей по комбинированным счетам"}}, "summaries": {"vehicle": {"title": "Сводка по транспортным средствам", "description": "Текущий статус всех транспортных средств в системе"}, "shipment": {"title": "Сводка по отправлениям", "description": "Обзор всех отправлений и их статусов"}}, "invoice-status": {"title": "Статус счетов", "description": "Распределение платежей по обычным счетам", "paid": "Оплачено", "due": "Просрочено"}, "mix-invoice-status": {"title": "Статус комбинированных счетов", "description": "Распределение платежей по комбинированным счетам"}, "container-invoice-status": {"title": "Статус счетов по контейнерам", "description": "Распределение платежей по счетам за контейнеры"}, "action-items": {"title": "Зада<PERSON>и", "description": "Пункты, требующие вашего внимания", "full-invoices": "Полные счета", "mix-invoices": "Комбинированные счета", "open": "открытые", "paid": "оплаченные", "regular-payment-rate": "Скорость оплаты обычных счетов", "mix-payment-rate": "Скорость оплаты комбинированных счетов", "good": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "needs-attention": "Требует внимания", "view-all-invoices": "Просмотреть все счета", "view-due-invoices": "Просмотреть просроченные счета", "view-all-mix-invoices": "Просмотреть все комбинированные счета", "view-due-mix-invoices": "Просмотреть просроченные комбинированные счета"}}, "bar-chart-tooltip": {"first": "Итого", "second": "Оплачено"}, "invoice-tab": {"full-invoice-section": {"title": "Полные счета", "description": "Распределение платежей по полным счетам"}, "mix-invoice-section": {"title": "Комбинированные счета", "description": "Распределение платежей по комбинированным счетам"}, "progress-card": {"title": "Завершение платежей", "description": "Процент оплаченных счетов"}, "donut-chart": {"title": "Статус счетов", "description": "Распределение платежей по счетам", "total-label": "Счета"}, "bar-chart": {"title": "Тенденции по счетам за последние 12 месяцев", "description": "Всего счетов vs Оплаченные счета по месяцам"}, "metrics": {"average-value": "Средняя стоимость счета", "monthly-growth": "Месячный рост", "open-invoices": "Открытые счета", "payment-efficiency": "Эффективность платежей"}}, "mix-invoice-tab": {"progress-card": {"title": "Завершение платежей по комбинированным счетам", "description": "Процент оплаченных комбинированных счетов"}, "donut-chart": {"title": "Статус комбинированных счетов", "description": "Распределение платежей по комбинированным счетам", "total-label": "Комбинированные счета"}, "bar-chart": {"title": "Смешанные тенденции по счетам за 12 месяцев", "description": "Всего vs Оплаченные смешанные счета по месяцам"}, "metrics": {"average-value": "Средняя стоимость счета", "monthly-growth": "Месячный рост", "open-invoices": "Открытые счета", "paid-invoices": "Оплаченные счета"}}, "shipment-tab": {"total-shipment": "Всего отправлений", "pi-chart": {"title": "Открытые счета по полным контейнерам", "description": "Итоги по счетам", "total-label": "Счета"}}, "vehicle-tab": {"total-vehicle": "Всего транспортных средств"}}, "customer-invoice": {"title": "Счета клиентов", "datatable": {"id": "ID", "receiver_name": "Имя получателя", "customer_id": "ID клиента", "total_cost": "Общая стоимость", "vehicles": "Транспортные средства", "created_at": "Дата создания", "created_date": "Дата создания", "purchase_date": "Дата покупки", "bank": "Выберите банк", "search": "Поиск по VIN", "shipment": "Доставка", "both": "Оба", "auction": "Аукцион"}, "details": {"title": "Детали счета за автомобиль", "client_info": "Информация о клиенте", "invoice_no": "Номер счета", "date": "Дата", "receiver_name": "Имя", "customer_id": "ID клиента", "sale_date": "Дата покупки", "description": "Описание", "type": "Тип", "price": "Цена", "charges": "Сборы", "bank_transfer": "Банковский перевод", "amount": "Сумма", "total": "Итого", "payment": "Предоплата", "port_loading": "Порт загрузки – США", "wire_info": "Информация для перевода", "bank_name": "Название банка:", "account_name": "Имя получателя:", "account": "№ счета:", "routing_one": "Маршрут №:", "routing_two": "Маршрут №:", "swift_code": "SWIFT код:", "bank_add": "Адрес банка:", "company_add": "Адрес компании:", "line1": "1. Обязательно укажите ваш номер участника/покупателя в информации о переводе. Если вы оплачиваете", "line2": "конкретные автомобили, укажите номера лота/склада.", "buyer": "Номер покупателя:", "lot_stock": "Лот/Склад:", "copart_discharge": "Порт выгрузки – Джорджия", "prepayment": "Предоплата за автомобиль", "copart_address": "Адрес Copart: 14185 Dallas Pkwy #300, Dallas, TX 75254", "iaai_address": "Адрес IAAI: 701 Harger Road, Suite 201, Oak Brook, IL 60523", "iaai_discharge": "Порт выгрузки – Баку, Азербайджан"}, "body": {"edit": "Редактировать", "delete": "Удалить", "cancel": "Отмена", "submit": "Отправить", "details": "Детали", "delete-description": "Вы уверены, что хотите удалить этот счет клиента?"}}, "payment-datatable": {"header": {"payment": "Пла<PERSON><PERSON><PERSON>", "payment_method": "Способ оплаты", "amount": "Сумма", "transaction_fee": "Комиссия за транзакцию", "remain_applied_amount": "Непримененная сумма", "status": "Статус", "exchange_rate": "Обменный курс"}, "body": {"currency": "Валюта", "payment_method": "Метод"}}, "calculator": {"title": "Расчет стоимости доставки", "description": "Используйте наш калькулятор стоимости доставки, чтобы мгновенно рассчитать стоимость доставки ваших автомобилей.", "header": {"title": "Калькулятор доставки", "description": "Рассчитайте стоимость доставки вашего автомобиля"}, "form": {"state": "<PERSON>т<PERSON><PERSON>", "branch": "Филиал", "city": "Город", "destination": "Пункт назначения", "additional_services": "Дополнительные услуги", "full_size_suvs": "Полноразмерные внедорожники", "manheim_adesa": "Manheim Adesa", "major_accident": "Серьезная авария", "reset": "Сбросить", "submit": "Рассчитать стоимость", "total_cost": "Общая стоимость", "loading_calculate": "Идет расчет...", "loading_states": "Загрузка штатов...", "loading_branches": "Загрузка филиалов...", "loading_cities": "Загрузка городов...", "loading_destinations": "Загрузка пунктов назначения...", "state_placeholder": "Выберите штат", "branch_placeholder": "Выберите филиал", "city_placeholder": "Выберите город", "destination_placeholder": "Выберите пункт назначения", "disclaimer_title": "Отказ от ответственности", "disclaimer_description": "Может применяться ориентировочная таможенная пошлина в размере около 10% от стоимости автомобиля.", "no_state_text": "Пожалуйста, выберите штат первым", "no_branch_text": "Пожалуйста, выберите филиал первым"}, "errors": {"select_required": "Пожалуйста, выберите город и пункт назначения", "calculation_failed": "Не удалось рассчитать стоимость доставки", "try_again": "Не удалось рассчитать стоимость доставки. Пожалуйста, попробуйте снова.", "no_response": "Нет ответа от сервера. Пожалуйста, проверьте ваше соединение.", "unexpected_data": "Получены неожиданные данные от сервера", "api_error": "Ошибка {status}: {message}", "fetch_destinations_failed": "Не удалось получить пункты назначения", "fetch_states_failed": "Не удалось получить штаты", "fetch_branches_failed": "Не удалось получить филиалы", "fetch_cities_failed": "Не удалось получить города", "no_shipping_rate": "Не найден тариф доставки для указанных критериев", "empty_response": "Сервер вернул пустой ответ", "missing_data": "Сервер вернул ответ без ожидаемого свойства 'data'", "calculation_success": "Расчет успешно завершен"}}, "filter-modal": {"checked-option": "Проверенные параметры", "not-checked-option": "Непроверенные параметры", "filters": "Фильтры", "vehicle-filter-modal": {"container": "Кон<PERSON>ейнер", "point-of-loading": "Пункт загрузки", "point-of-destination": "Пункт назначения", "vehicle-status": "Статус транспорта", "data": {"label": "Данные", "lot-number": "Номер лота", "vin": "VIN", "make": "Марка", "model": "Модель", "year": "Год"}, "price": "Цена", "date-renge": {"label": "Диа<PERSON>азон дат", "purchase-at": "Дата покупки", "payment-date": "Дата платежа", "delivery-date": "Дата доставки"}, "checked": "Проверено", "is-printed": "Напечатано"}, "shippment-filter-modal": {"checked-option": "Проверенные параметры", "not-checked-option": "Непроверенные параметры", "container": "Кон<PERSON>ейнер", "booking": "Бронирование", "status": "Статус", "date-renge": {"label": "Диа<PERSON>азон дат", "loading-date": "Дата загрузки", "eta": "ETA", "etd": "ETD"}}, "invoice-filter-modal": {"container": "Кон<PERSON>ейнер", "status": "Статус транспорта", "invoice-number": "Номер счета", "invoice-amount": "Сумма счета", "payment-received": "Платеж получен", "date-range": {"label": "Диа<PERSON>азон дат", "issue-date": "Дата выпуска"}}, "mix-shipping-rates-modal": {"state": "<PERSON>т<PERSON><PERSON>", "branch": "Филиалы", "point-of-loading": "Точка загрузки", "cities": "Города"}, "payment-filter-modal": {"date-range": {"label": "Диа<PERSON>азон дат", "created-at": "Дата создания", "updated-at": "Дата обновления"}}}, "customer-of-customer": {"header": {"id": "Идентификатор", "fullname": "Полное имя", "email": "Электронная почта", "username": "Имя пользователя", "phone": "Телефон", "status": "Статус", "created-at": "Дата создания", "updated-at": "Дата обновления", "actions": "Действия", "password": "Пароль"}, "body": {"customers": "Клиенты", "customer": "Кли<PERSON><PERSON>т", "add-customer": "Добавить клиента", "edit-customer": "Обновить клиента", "fullname": "Полное имя", "username": "Имя пользователя", "password": "Пароль", "email": "Электронная почта", "phone-number": "Номер телефона", "status": "Статус", "status-placeholder": "Выберите статус", "edit": "Редактировать", "delete": "Удалить", "cancel": "Отмена", "submit": "Отправить", "enable": "Включить", "disable": "Отключить", "active": "Акти<PERSON><PERSON>н", "deactive": "Неактивен", "delete-description": "Вы уверены, что хотите удалить этого клиента?", "deactive-description": "Вы уверены, что хотите отключить клиента?", "active-description": "Вы уверены, что хотите включить клиента?"}}, "pagination": {"previous": "Предыдущая", "next": "Следующая", "page": "Страница", "of": "из", "to": "по", "results": "результа<PERSON>ов", "displaying": "Отображение"}, "autocomplete": {"label": "Поиск...", "option-label": "Варианты не найдены.", "placehoder": "Поиск варианта...", "label-option": "Выберите вариант...", "selected": "Нет выбранных вариантов", "unselected": "Нет невыбранных вариантов"}, "export-modal": {"title": "Экспорт данных в Excel", "sub-title": "Экспорт данных", "select-type": "Выберите тип данных", "current-data": "Текущие данные", "all-data": "Все данные", "cancel": "Отмена", "export": "Экспорт"}, "add-vehicle-form": {"add-vehicle": "Добавить транспортное средство", "edit-vehicle": "Редактировать транспортное средство", "labels": {"vin": "Номер VIN", "auction_name": "Аукцион", "year": "Год", "make": "Производитель", "model": "Модель", "color": "Цвет", "price": "Цена", "lot_number": "Номер лота", "weight": "<PERSON>е<PERSON>", "point_of_destination": "Порт назначения", "pol_locations": "Порт погрузки", "is_title_exist": "Наличие титула", "is_key_present": "Наличие ключа", "title_number": "Номер титула", "title_state": "Состояние титула", "receiver_name": "Имя получателя"}, "stepper": {"vehicle-information": "Информация о транспортном средстве", "title-key-information": "Информация о титуле и ключе", "shipping-information": "Информация о доставке", "review-and-submit": "Проверка и отправка", "sub-items": {"vin": "Номер VIN", "auction_name": "Название аукциона", "year": "Год", "make": "Производитель", "model": "Модель", "color": "Цвет", "price": "Цена", "title-exist": "Наличие титула", "title-number": "Номер титула", "title-state": "Состояние титула", "key-present": "Наличие ключа", "point_of_loading": "Порт погрузки", "point_of_destination": "Порт назначения", "weight": "<PERSON>е<PERSON>", "lot_number": "Номер лота", "receiver_name": "Имя получателя"}}, "buttons": {"submit": "Отправить", "cancel": "Отмена", "update": "Обновить", "previous": "Предыдущий", "next": "Следующий", "edit": "Редактировать"}}, "auction-payment-model": {"title": "Детали оплаты", "tabs": {"finance": "Финан<PERSON>ы", "payments": "Плате<PERSON>и"}, "finance": {"header": {"items": "Элементы", "amount": "Сумма", "paid-amount": "Оплачено", "unpaid-amount": "Неоплачено"}, "items": {"vehicle-price": "Цена автомобиля", "transportation-fee": "Транспортные расходы", "grand-total": "Общая сумма"}, "cards": {"paid-in-full": "Оплачено полностью", "all-payments-have-been-received": "Все платежи получены", "payment-required": "Требуется оплата", "balance-remaining": "Оставшийся баланс"}}, "payments-list": {"title": "История платежей", "header": {"auction-payment": "Платеж на аукционе", "mix-payment": "Смешанный платеж"}, "auction-payment": {"amount": "Сумма", "date": "Дата", "type": "Тип", "auction": "Аукцион"}, "mix-payment": {"amount": "Сумма", "date": "Дата", "type": "Тип", "mix": "Смешанный"}}}, "buyer-number-datatable": {"header": {"buyer-number": "Номер покупателя"}}, "forgot-password": {"title": "Забыли пароль", "description": "Введите свой адрес электронной почты ниже, и мы отправим вам инструкции по сбросу пароля", "linkText": "Забыли пароль?", "cancelButton": "Отмена", "submitButton": "Отправить ссылку для сброса", "emailLabel": "Адрес электронной почты", "emailPlaceholder": "<EMAIL>"}, "reset-password": {"title": "Сброс пароля", "description": "Введите новый пароль ниже, и мы отправим вам инструкции по его сбросу", "linkText": "Сбросить пароль?", "cancelButton": "Отмена", "submitButton": "Сбросить пароль", "passwordLabel": "Новый пароль", "passwordPlaceholder": "Новый пароль", "confirmPasswordLabel": "Подтвердите пароль", "confirmPasswordPlaceholder": "Подтвердите пароль", "passwordRequirements": "Требования к паролю", "minLength": "Минимум 6 символов", "includeNumbers": "Включите хотя бы одну цифру и специальные символы (рекомендуется)", "includeSpecial": "Включите специальные символы (рекомендуется)", "passwordsMatch": "Пароли совпадают", "passwordsDontMatch": "Пароли не совпадают", "updateSuccess": "Пароль успешно обновлён", "updateError": "Не удалось обновить пароль", "validating": "Проверка...", "rememberPassword": "Помните свой пароль?", "signIn": "Войти", "link-expired": {"title": "Срок действия ссылки истёк", "message": "Ссылка для сброса пароля недействительна или её срок истёк.", "returnButton": "Вернуться ко входу"}}, "company-profiles": {"boosmotors": {"title": "Группа компаний Босс Моторс", "description": "Boss Motors — ведущая мировая компания по автозапчастям и обслуживанию автомобилей"}}, "customer_of_customer_payments": {"title": "Управление платежами клиентов", "loading": "Загрузка платежей...", "no-data": "Платежи для этого клиента и автомобиля не найдены.", "tabs": {"add-payment": "Добавить платеж", "payment-history": "История платежей"}, "add-payment": {"title": "Добавить новый платеж", "customer": "Кли<PERSON><PERSON>т", "payment-amount": "Сумма платежа", "payment-date": "Дата платежа", "add-payment": "Добавить платеж"}, "payment-history": {"title": "История платежей", "total-payment": "Общая сумма платежей", "payment-date": "Дата платежа", "amount": "Сумма", "created-at": "Дата создания", "total-paid": "Всего оплачено"}}, "customer-vehicles-datatable": {"label": "Автомобили клиента", "header": {"vehicle": "Автомобиль", "vehicle-price": "Цена автомобиля", "ship-cost": "Стоимость доставки", "customer-profit": "Прибыль клиента", "storage-charge": "Плата за хранение", "total-amount": "Общая сумма", "customer-name": "Имя клиента", "paid-amount": "Оплаченная сумма", "balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "customer-profit": {"label": "Прибыль клиента", "description": "Введите сумму прибыли для этого автомобиля", "profit-amount": "Сумма прибыли", "add-profit": "Добавить прибыль", "save": "Сохранить", "cancel": "Отмена", "saving": "Сохранение..."}, "storage-charge": {"label": "Плата за хранение", "description": "Введите сумму хранения для этого автомобиля", "storage-amount": "Сумма хранения", "add-storage": "Добавить хранение", "save": "Сохранить", "cancel": "Отмена", "saving": "Сохранение..."}}, "vehicle-tracking": {"sign-in": "Войти", "title": "Отслеживание Транспортного Средства", "description": "Следите за перемещением вашего груза с помощью обновлений в реальном времени и подробной информации об отслеживании.", "search": {"title": "Отслеживание ТС", "description": "Введите номер LOT или VIN, чтобы узнать текущий статус вашего транспортного средства", "placeholder": "Введите номер LOT или VIN"}, "vehicle-progress": {"title": "<PERSON><PERSON><PERSON>вки", "description": "Следите за маршрутом вашего транспортного средства с обновлениями в реальном времени", "ordered": "Заказано", "auction-paid": "Аукцион Оплачен", "auction-unpaid": "Аукцион Неоплачен", "pending-auction": "Ожидание Аукциона", "on-hand-with-title": "В наличии с титулом", "on-hand-no-title": "В наличии без титула", "pending-picked": "О<PERSON><PERSON><PERSON><PERSON><PERSON>т Забора", "at-port": "В Порту", "pending": "В ожидании", "cost-analysis": "Ана<PERSON><PERSON><PERSON> Стоимости", "datelines": "Сроки", "on-hand-with-load": "В наличии с грузом", "added_by_customer": "Добавлено клиентом", "order-placed": "Заказ размещён", "processing": "В обработке", "being-prepared": "Готовится к отправке", "shipped": "Отправлено", "on-the-way": "В пути", "delivered": "Доставлено", "completed": "Завершено"}, "vehicle-info": {"default": {"title": "Готово к Отслеживанию", "description": "Введите номер LOT или VIN выше, чтобы увидеть текущий статус и информацию об отслеживании вашего транспортного средства."}, "found": {"location-and-time": "Местоположение и Временная Шкала", "auction-name": "АУКЦИОН", "auction-city": "ГОРОД АУКЦИОНА", "purchased": "ПРИОБРЕТЕНО", "pickup": "ЗАБОР", "delivery": "ДОСТАВКА", "arrival-date": "ДАТА ПРИБЫТИЯ", "departure-date": "ДАТА ОТПРАВЛЕНИЯ", "loading-date": "ДАТА ПОГРУЗКИ"}, "not-found-image": {"title": "Фото Транспортного Средства", "description": "Изображение появится здесь"}, "not-found-data": {"title": "Транспортное Средство Не Найдено", "description": "Мы не смогли найти транспортное средство с указанным номером отслеживания. Пожалуйста, проверьте введённые данные и попробуйте снова.", "btn": "Попробовать другой поиск"}}}}