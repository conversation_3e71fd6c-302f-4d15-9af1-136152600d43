{"sidebar": {"title": "Peace Global Logistics", "description": "You Buy We Ship", "nav": {"dashboard": "Dashboard", "search": "Search", "vechicles": {"label": "Vehicles", "all": "All", "auction-unpaid": "Auction Unpaid", "auction-paid": "Auction Paid", "on-the-way": "On the way", "on-the-hand-no": "On hand No/Title", "on-the-hand-with": "On hand with/Title", "on-the-hand-with-load": "On hand with Load", "added_by_customer": "Addeded By Customer", "inventory-poD": {"label": "Inventory (POD)", "all": "All", "without-pod": "Without POD", "jebel-ali-uea": "JEBEL Ali UEA"}, "inventory-pol": {"label": "Inventory (POL)", "all": "All", "houson-tx": "Houston TX", "baltimore-md": "Baltimore , MD", "jacksonville-fl": "Jacksonville , FL"}, "shipped": "Shipped", "cost-analysis": "Cost Analysis", "dateline": "Date Line"}, "shipments": {"label": "Shipments", "all": "All", "at-loading": "At Loading", "on-the-way": "On the way", "arrived": "Arrived"}, "invoice": {"label": "Invoice", "all": "All", "open": "Open", "past-due": "Past Due", "paid": "Paid"}, "mix-shipping": {"label": "Mix Shipping", "all": "All", "open": "Open", "past-due": "Past Due", "paid": "Paid"}, "towing-rates": {"label": "Towing Rates", "half-cut": "Half Cut", "complete": "Complete"}, "mix-shipping-rates": "Mix Shipping Rates", "shipping-rates": "Shipping Rates", "payments": {"label": "Payments", "freight-payments": {"label": "Freight Payments", "all": "All", "approved": "Approved", "pending": "Pending"}, "auction-payments": {"label": "Auction Payments", "all": "All", "approved": "Approved", "pending": "Pending"}}, "announcements": "Announcements", "calculator": "Calculator", "customer": {"label": "Customers", "all": "All", "enable": "Enable", "disable": "Disable", "trash": "Trash"}, "buyer-number": "Buyer Number", "customer-invoice": "Customer Invoice", "customer-vehicles": "Customer Vehicles"}, "account": {"label": "Setting", "mode": {"label": "Mode", "system": "System", "light": "Light", "dark": "Dark"}, "notifications": "Notifications", "logout": "Log out", "button": "Save changes", "popup": {"title": "Are you absolutely sure?", "description": "This action cannot be undone. This will log you out of your account.", "cancel": "Cancel", "continue": "Continue"}, "forgot-password": "Forgot Password?", "forgot-password-description": "To reset your password, please contact the administrator for further assistance"}}, "lang": {"label": "Languages", "en": "English", "ru": "Русский", "ka": "ქართული", "ar": "العربية"}, "profile": {"account": {"label": "Account", "description": "View your account information and profile details.", "name": "Name", "username": "Username", "button": "Save changes", "forgot-password-description": "To reset your password, please contact the administrator for further assistance", "email": "Email", "verified": "Verified", "security": {"title": "Account Security", "description": "Your account information is secure and only visible to you. To update your profile details, please contact support."}}, "devices": {"label": "Devices", "description": "Manage devices that are currently signed in to your account", "current_device": "Current Device", "last_active": "Last active", "ip_address": "IP Address", "location": "Location", "browser": "Browser", "os": "Operating System", "device_type": "Device Type", "logout": "Logout", "remove": "Remove", "logout_title": "Logout Device", "logout_description": "This will sign out this device. You'll need to sign in again on that device.", "remove_title": "Remove <PERSON>ce", "remove_description": "This will permanently remove this device from your account history. This action cannot be undone.", "cancel": "Cancel", "confirm_logout": "Confirm <PERSON>ut", "confirm_remove": "Remove <PERSON>ce", "logout_success": "<PERSON><PERSON> successfully logged out", "logout_error": "Failed to log out device", "remove_success": "<PERSON><PERSON> successfully removed", "remove_error": "Failed to remove device", "no_devices": "No devices found", "error": "Failed to load devices", "refresh": "Refresh", "active": "Active", "current": "Current"}, "password": {"label": "Password", "description": "Change your password here.", "current_password": "Current password", "new_password": "New password", "confirm_password": "Confirm password", "button": "Save changes", "updating": "Updating...", "show_password": "Show password", "hide_password": "Hide password", "requirements": "Password Requirements", "min_length": "Minimum 6 characters", "include_number": "Include at least one number and special characters (recommended)", "include_special": "Include special characters (recommended)", "passwords_match": "Passwords match", "passwords_dont_match": "Passwords don't match", "update_success": "Password updated successfully", "update_error": "Failed to update password"}, "image-preveiw": {"label": "Image Cropper", "cancel": "Cancel", "crop": "Crop image", "adjust_crop": "Adjust the image to fit the frame, then click crop to save your profile picture."}, "profile_tab": {"label": "Profile", "title": "Update Profile", "description": "Set Logo and Name for your own customer.", "new_name": "Name", "logo": "Drag & drop or click to upload", "size": "Max 5MB per file"}}, "login": {"label": "<PERSON><PERSON>", "title": "Welcome back", "title_description": "Login to your account", "email": "Email or Username", "password": "Password", "forget_password": "Forgot your password?", "register": "Register", "loading": "Loading...", "login_success": "Logged in successfully", "login_error": "<PERSON><PERSON> failed. Please try again", "tracking": "Tracking", "left-side": {"title": "You Buy We Ship", "description": " We provide you with full package services, from bidding on your favorite cars in the US auctions and delivering to your desiredlocations."}, "right-side": {"title": "Global Shipping, Local Delivery", "option1": "Over 8 Years of Trusted Logistics Expertise", "option2": "Tailored solutions for car wholesalers and retailers", "option3": "End-to-end support in logistics, transport & supply chain"}}, "datatable": {"sidebar": {"columns": "Columns", "filters": "Filters", "search": "Search"}, "header": {"vehicle": "Vehicles", "container": "Container", "dates": "Dates", "locations": "Locations", "auction": "Auction", "assigned": "Assign Customer", "unassigned": "Unassign Customer", "customer_profit": "Customer Profit", "status": "Status", "title": "Title", "action": "Action", "vehicle-price": "Vehicle Price", "ship-cost": "Shipping Cost", "storage-charge": "Storage Charge", "total-amount": "Total Amount", "paid-amount": "<PERSON><PERSON>", "due-balance": "Due Balance"}, "body": {"delivery": "Delivery", "cn": "CN", "eta": "ETA", "etd": "ETD", "from": "From", "to": "To", "delete": "Delete", "delete-description": "Are you sure you want to delete this vehicle?", "cancel": "Cancel", "number": "Number", "status": "Status", "receive-date": "Receive Date", "title-delivery-location": "Title Delivery Location", "add-receiver-name": "Add Receiver Name", "receiver": "Receiver", "add-destination": "Add Point Of Destination", "is_title": "Is Title", "is_key": "Is Key", "is-checked": "Is Checked"}, "vehicle-details": {"header": {"photo": "Photo", "vehicle": "Vehicle", "dates": "Dates", "state": "States", "general": "General"}, "body": {"lot-number": "Lot Number", "checked": "Checked", "is-key": "Is Key", "is-printed": "Is Printed", "status": "Status", "auction-invoice-link": "Auction Invoice Link", "loading-date": "Loading Date", "loaded": "Loaded", "container_number": "CN", "purchase-date": "Purchase Date", "account-number": "Account Number", "is-title": "Is Title", "title-status": "Title Status", "title-recive-date": "Title Recive Date", "title-number": "Title Number", "age-at-pgl": "Age At Pgl", "comment": "Comment", "ship-date": "Ship Date", "delivery-date": "Delivery Date", "recieve-date": "Recieve Date", "auction-city": "Auction City", "document-link": "Document Link", "picked-up-date": "PickUp Date"}}, "payment_details": {"label": "Payment Details", "transaction": "Transaction", "payment_details": "Payment Details", "amount": "Amount", "amount_applied": "Amount Applied", "exchange_rate": "Exchange Rate", "payment_method": "Payment Method", "method": "Method", "payment_date": "Payment Date", "remarks": "Remarks", "view_attachment": "View Attachment", "no_data_available": "No Data Available", "state": {"completed": "Completed", "pending": "Pending", "cancelled": "Cancelled"}, "payment-details": {"type": "Type", "reference": "Reference", "applied": "Amount Applied", "status": "Status", "remark": "Remark"}}, "announcements": {"label": "Latest Announcements", "search": "Search...", "no-data": "No data available", "empty": "No announcements found"}, "vehicle-assigin": {"popup": {"title": "Are you absolutely sure?", "description": "This action cannot be undone. This will remove vehicle out of customer.", "cancel": "Cancel", "continue": "Continue"}}, "vehicle-drawer": {"status": "Status", "auction-name": "Auction Name", "year": "Year", "make": "Make", "model": "Model", "color": "Color", "container-number": "Container Number", "is-printed": "Is Printed", "is-key": "Is Key", "title-state": "Title State", "title-status": "Title Status", "title-number": "Title Number", "account-number": "Account Number", "age-at-pgl": "Age At Pgl", "point-of-loading": "Point Of Loading", "point-of-destination": "Point Of Destination", "receiver-name": "Receiver Name", "comment": "Comment", "check": "Check", "auction-invoice-link": "Auction Invoice Link", "purchased-date": "Purchased Date", "title-received-date": "Title Received Date", "ship-date": "Ship Date", "loading-date": "Loading Date", "pick-up-date": "Pick Up Date", "delivery-date": "Delivery Date", "lot-number": "Lot Number", "vehicle-price": "Vehicle Price", "ship-cost": "Shipping Cost", "storage-charge": "Storage Charge", "total-amount": "Total Amount", "paid-amount": "<PERSON><PERSON>", "due-balance": "Due Balance"}}, "shipment-datatable": {"header": {"shipment": "Shipments", "dates": "Dates", "locations": "Locations", "booking": "Bookings", "units": "Units", "track": "Track", "status": "Status", "clearnce": "Clearnce"}, "body": {"delivery": "Delivery", "eta": "ETA", "etd": "ETD", "from": "From", "to": "To", "units": "Units", "size": "Size", "track": "Tracking", "clearance-invoice-link": "Inovice Link", "container_number": "CN"}, "shipment_gallary": {"title": "Shipment Images", "no_images_title": "container does not have a photo.", "error_images_title": "Something Went wrong loading images. Try Again"}}, "invoice-datatable": {"header": {"invoice": "Invoice", "dates": "Dates", "balance": "Received / Overdue", "status": "Status", "invoice_amount": "Invoice Amount", "purpose": "Purpose"}, "body": {"purpose": "Purpose", "container_number": "CN", "status": "Status", "issue_date": "Issue", "due_date": "Due", "invoice_amount": "Invoice Amount", "payment_received": "Payment Recived", "received_date": "Received Date", "balance": "Received / Overdue", "past_due_days": "Past Due Days"}}, "mix-shipping-datatable": {"header": {"invoice": "Invoice", "lot": "Lot Number", "print": "Print", "balance": "Balance / Overdue", "dates": "Dates", "vin": "VIN"}, "body": {"cn": "CN", "lot_number": "Lot #", "invoice_amount": "Invoice Amount", "payment_received": "Payment Recived", "balance": "Balance", "past_due_days": "Past Due Days", "issue_date": "Issue ", "due_date": "Due ", "discount": "Discount", "due": "Due", "paid": "Paid", "invoice": "Invoice", "total_footer": "CURRENT PAGE : TOTAL STISTICS"}}, "towing-rate-datatable": {"header": {"state-name": "State Name", "branch-name": "Branch Name", "city-name": "City Name", "ga&ca": "GA & CA", "tx&ng": "TX & (NJ)", "bal": "(BAL)"}, "body": {"ca": "CA", "ga": "GA", "tx": "TX", "nj": "(NJ)"}, "details": {"number": "#", "branch": "Branch", "cityName": "City Name", "ga": "GA", "ca": "CA", "tx": "TX", "nj": "(NJ)", "bal": "(BAL)"}}, "mix-shipping-rates-datatable": {"header": {"state": "State", "branch_city": "Branch & City", "locations": "Locations", "towing_shipping_costs": "Towing & Shpping Cost", "clearance_tds_costs": "Clearance & TDS Cost", "total": "Total"}, "body": {"id": "ID", "state_name": "State Name", "branch_name": "Branch Name", "city_name": "City Name", "location_name": "Location Name", "destination": "Destination", "towing_cost": "Towing Cost", "clearance_cost": "Clearance Cost", "shipping_cost": "Shipping Cost", "tds_charges": "TDS Charges", "tax_duty": "Tax & Duties", "total": "Total"}, "details": {"number": "#", "branch": "Branch", "city": "City", "towing": "Towing", "shipping": "Shipping", "clearance": "Clearance", "tdsCharges": "TDS Charges", "taxDuty": "TAX & Duty", "total": "Total"}}, "payment-datatable": {"header": {"payment": "Payment", "payment_method": "Payment Method", "amount": "Amount", "transaction_fee": "Transaction Fee", "remain_applied_amount": "Unapplied Amount", "status": "Status", "exchange_rate": "Exchange Rate"}, "body": {"currency": "<PERSON><PERSON><PERSON><PERSON>", "payment_method": "Method"}}, "auction-payment-datatable": {"header": {"payment": "Payment", "payment-method": "Payment Method", "amount": "Amount", "transaction-fee": "Transaction Fee", "unapplied-amount": "Unapplied Amount", "exchange-rate": "Exchange Rate"}, "body": {"currency": "<PERSON><PERSON><PERSON><PERSON>", "payment_method": "Method"}, "payment_details": {"label": "Payment Details", "transaction": "Transaction", "payment_details": "Payment Details", "amount": "Amount", "amount_applied": "Amount Applied", "exchange_rate": "Exchange Rate", "payment_method": "Payment Method", "method": "Method", "payment_date": "Payment Date", "remarks": "Remarks", "view_attachment": "View Attachment", "no_data_available": "No Data Available", "state": {"completed": "Completed", "pending": "Pending", "cancelled": "Cancelled"}, "payment-details": {"type": "Type", "reference": "Reference", "applied": "Amount Applied", "status": "Status", "remark": "Remark"}}}, "shipping-rate-datatable": {"title": "shiping Rate", "destination": "Destination", "Shiplines": "Shiplines", "Equipment": "POL | Equipment"}, "combox-options": {"option-label": "No options found.", "placehoder": "Search option...", "label": "Select an option..."}, "not_found": {"title": "Page Not Found", "description": "Oops! It seems you've ventured into uncharted territory. The page you're looking for doesn't exist", "button": "Return to Home"}, "server_error": {"title": "Internal server Error", "header": "Oops! Something went wrong on our end.", "description": "Our team is already working on fixing this issue. Please try again later.", "button": "Try again"}, "unauthorized": {"title": "Unauthorized", "description": "You don't have permission to access this page. If you believe this is an error, please contact the administrator.", "button": "Return to Login"}, "notification": {"title": "Notification", "announcements": "Announcements", "arrival-notices": "Arrival Notices", "payments": "Payments", "mark-all-read": "<PERSON>"}, "Register": {"steps": {"basic_information": "Basic Information", "additional_information": "Additional Information", "consignee_information": "Consignee Information", "notify_party": "Notify Party", "load_volume": "Load Volume", "registration": "Registration", "contract": "Contract"}, "BasicInformation": {"title": "Basic Information", "description": "Please provide your company's basic information", "fullName": "Full Name", "companyName": "Company Name", "email": "Email Address", "phoneNumber": "Phone Number", "destination": "Destination", "selectDestination": "Select destination", "address": "Address"}, "AdditionalInformation": {"title": "Additional Information", "description": "Please provide additional details about your business", "secondaryEmail": "Secondary Email", "secondaryPhone": "Secondary Phone", "joinDate": "Join Date", "usedCar": "Used Car Business", "completeCars": "Complete Cars", "container": "Container", "consolidation": "Consolidation", "halfcutCars": "Halfcut Cars", "vehicleTypes": "Vehicle Types", "suv": "SUV", "sedan": "Sedan"}, "ConsigneeInformation": {"title": "Consignee Information", "description": "Please provide consignee details", "fullName": "Full Name", "companyName": "Company Name", "email": "Email Address", "phoneNumber": "Phone Number", "address": "Address"}, "NotifyParty": {"title": "Notify Party", "description": "Please provide notify party details", "fullName": "Full Name", "companyName": "Company Name", "email": "Email Address", "phoneNumber": "Phone Number", "address": "Address"}, "LoadVolume": {"title": "Load Volume", "description": "Please specify your expected load volume", "numberOfVehicles": "Number of Vehicles", "numberOfContainers": "Number of Containers"}, "Registration": {"title": "Registration", "description": "Please review your information", "submit": "Submit Registration"}, "Contract": {"title": "Contract", "description": "Please download and sign the contract", "downloadContract": "Download Contract", "uploadContract": "Upload Signed Contract", "contractUploaded": "Contract uploaded successfully", "instructions": "Please download your contract, review it carefully, sign it, and upload the signed version to fully complete your registration.", "downloadStep": "1. Download Contract", "uploadStep": "2. Select Signed Contract", "downloading": "Downloading...", "selectedFile": "Selected", "downloadFirst": "Please download and review the contract before selecting the signed version", "registrationComplete": "Registration Completed!", "registrationCompleteDescription": "Thank you for completing your registration. We will review your application and get back to you as soon as possible. What would you like to do next?", "registerNew": "Register New Customer", "goToLogin": "Go to Login page", "registrationDataNotFound": "Registration data not found", "contractDownloaded": "Contract downloaded successfully", "downloadError": "Failed to download contract", "pdfOnly": "Please select a PDF file"}, "buttons": {"next": "Next", "previous": "Previous", "submit": "Submit", "upload": "Upload Contract", "submitting": "Processing...", "signIn": "Sign In"}}, "vehicle-inventory-pod-datatable": {"header": {"vehicle": "Vehicles", "lot-number": "Lot Number", "auc-pics": "Auction Picture", "locations": "Locations", "balance": "Balance", "status": "Status"}, "body": {"delivery": "Delivery", "lot-number": "Lot Number", "from": "From", "to": "To"}, "vehicle-inventory-datatable-pod-details": {"header": {"title": "Title", "status": "Status", "general": "General"}, "body": {"checked": "Checked", "is-key": "Is Key", "is-printed": "Is Printed", "title-status": "Title Status", "title-number": "Title Number", "age-at-pgl": "Age At Pgl", "comment": "Comment", "ship-as": "Ship", "reciver-name": "Reciver Name"}}, "drawer": {"status": "Status", "year": "Year", "make": "Make", "model": "Model", "color": "Color", "is-printed": "Is Printed", "is-key": "Is Key", "title-state": "Title State", "title-number": "Title Number", "account-number": "Account Number", "age-at-pgl": "Age At Pgl", "point-of-loading": "Point Of Loading", "point-of-destination": "Point Of Destination", "receiver-name": "Receiver Name", "comment": "Comment", "check": "Check", "ship-date": "Ship Date", "delivery-date": "Delivery Date"}}, "vehicle-inventory-pol-datatable": {"header": {"vehicle": "Vehicles", "lot-number": "Title Number", "auc-pics": "Auction Picture", "locations": "Locations", "balance": "Balance", "status": "Status"}, "body": {"delivery": "Delivery", "lot-number": "Lot Number", "from": "From", "to": "To"}, "vehicle-inventory-datatable-pol-details": {"header": {"title": "Title", "status": "Status", "general": "General"}, "body": {"checked": "Checked", "is-key": "Is Key", "is-printed": "Is Printed", "title-status": "Title Status", "title-number": "Title Number", "age-at-pgl": "Age At Pgl", "comment": "Comment", "ship-as": "Ship", "reciver-name": "Reciver Name"}}, "drawer": {"status": "Status", "year": "Year", "make": "Make", "model": "Model", "color": "Color", "is-printed": "Is Printed", "is-key": "Is Key", "title-state": "Title State", "title-number": "Title Number", "age-at-pgl": "Age At Pgl", "point-of-loading": "Point Of Loading", "point-of-destination": "Point Of Destination", "receiver-name": "Receiver Name", "comment": "Comment", "check": "Check", "ship-date": "Ship Date", "delivery-date": "Delivery Date"}}, "vehicle-cost-analysis-datatable": {"header": {"vehicle": "Vehicle", "container": "Container Number", "vehicle-cost": "Vehicle Cost", "ship-cost": "Ship Cost", "prof-lose": "<PERSON>/<PERSON>e", "description": "Description", "total": "Total", "comment": "Comment", "cost": "Cost"}, "body": {"lot-number": "Lot Number", "title-number": "Title Number", "cn": "CN", "vehicle-cost": "Vehicle Cost", "towing": "Towing", "dismental-cost": "Dismental Cost", "ship-cost": "Ship Cost", "strg-pol": "Strg POL", "title-cost": "Title Cost", "custom": "Custom", "other-cost": "Other Cost", "prof-lose": "<PERSON>/<PERSON>e", "is-printed": "Is Printed", "comment": "Comment", "pod": "POD"}, "drawer": {"status": "Status", "year": "Year", "make": "Make", "model": "Model", "color": "Color", "lot-number": "Lot Number", "title-number": "Title Number", "container-number": "Container Number", "is-printed": "Is Printed", "vehicle-cost": "Vehicle Cost", "towing-cost": "Towing Cost", "dismantale-cost": "Dismantling Cost", "storage-cost": "Storage Cost", "ship-cost": "Shipping Cost", "custom-cost": "Customs Fees", "other-cost": "Other Expenses", "total": "Total", "point-of-destination": "Point of Destination", "comment": "Comment"}}, "vehicle-datelines-datatable": {"header": {"vehicle": "Vehicle", "container": "Container Number", "purchase-date": "Purchase Date", "payment-date": "Payment Date", "pudf-purchase": "Pick Up Days From", "pick-up-date": "Pick Up Date", "comment": "Comment"}, "body": {"lot-number": "Lot Number", "title-number": "Title Number", "purchase-date": "Purchase Date", "export-date": "Export Date", "report-date": "Report Date", "payment-date": "Payment Date", "tow-request-date": "Tow Request Date", "pick-up-date": "Pick Up Date", "delivery-date": "Deliver Date", "pick-up-days-from-purchase": "Purchase", "pick-up-days-from-report": "Report", "is-printed": "Is Printed", "comment": "Comment", "point-of-destination": "POD"}, "drawer": {"status": "Status", "year": "Year", "make": "Manufacturer", "model": "Model", "color": "Color", "lot-number": "Lot Number", "title-number": "Title Number", "purchase-date": "Purchase Date", "report-date": "Report Date", "payment-date": "Payment Date", "delivery-date": "Delivery Date", "pick-up-days-from-purchase": "Pick Up Days From Purchase", "pick-up-days-from-report": "Pick Up Days From Report", "towing-request-date": "Towing Request Date", "pick-up-date": "Pick Up Date", "point-of-destination": "Point of Destination", "comment": "Comment"}}, "customer-invoice": {"title": "Customer Invoices", "datatable": {"id": "ID", "receiver_name": "Receiver Name", "customer_id": "Customer ID", "total_cost": "Total Cost", "vehicles": "Vehicles", "created_at": "Created At", "created_date": "Created Date", "purchase_date": "Purchase Date", "bank": "Select Bank", "search": "Search VIN", "shipment": "Shipment", "both": "Both", "auction": "Auction"}, "details": {"title": "Vehicle Invoice Details", "client_info": "Client Information", "invoice_no": "Invoice No", "date": "Date", "receiver_name": "Name", "customer_id": "Customer ID", "sale_date": "Purchase Date", "description": "Description", "type": "Type", "price": "Price", "charges": "Charges", "bank_transfer": "Bank Wire Transfer", "amount": "Amount", "total": "Total", "payment": "PREPAYMENT", "port_loading": "Port of loading – U.S.A", "wire_info": "Wire Information", "bank_name": "Bank Name: ", "account_name": "Account Name:", "account": "Account #:", "routing_one": "Routing#:", "routing_two": "Routing#:", "swift_code": "SWIFT Code: ", "bank_add": "Bank add: ", "company_add": "Company add:", "line1": "1. You MUST include your Member/ buyer number in the wire information. If you are paying for", "line2": "Specific vehicles, please include the lot/ Stock numbers.", "buyer": "Buyer Number:", "lot_stock": "Lot/ Stock:", "copart_discharge": "Port of discharge – Georgia", "prepayment": "Prepayment for the Car", "copart_address": "Copart Address: 14185 Dallas Pkwy #300, Dallas, TX 75254", "iaai_address": "IAAI Address: 701 Harger Road, Suite 201, Oak Brook, IL 60523", "iaai_discharge": "Port of discharge – Cip baku Azerbaijan"}, "body": {"edit": "Edit", "delete": "Delete", "cancel": "Cancel", "submit": "Submit", "details": "Details", "delete-description": "Are you sure you want to delete this customer Invoice?"}}, "dashboard": {"title": "Dashboard", "btn-download": "Download", "tabs": {"overview": "Overview", "vehicles": "Vehicles", "shipments": "Shipments", "invoices": "Invoices", "mix-invoice": "Mix Invoice", "calculator": "Calculator"}, "progress-card": {"completion": "Completion"}, "overview": {"shipping-calculator": {"title": "Shipping Calculator", "description": "Calculate shipping costs for your vehicle"}, "business-overview": {"title": "Business Overview", "description": "Key metrics for your business"}, "cards": {"total-vehicles": "Total Vehicles", "total-shipments": "Total Shipments", "invoices": "Invoices", "mix-shipping": "Mix Shipping", "vehicles": "Vehicles", "shipments": "Shipments"}, "vehicle-summery": {"title": "Vehicle Summary", "all": "All", "location": "Location", "auction": "Auction", "on-hand": "On Hand", "shipping": "Shipping", "total": "Total", "unpaid": "Unpaid", "paid": "Paid", "no-title": "No Title", "with-title": "With Title", "with-load": "With Load", "on-the-way": "On The Way", "shipped": "Shipped", "loading": "Loading vehicle data...", "no-data": "No vehicle data available", "no-results": "No results."}, "shipment-summery": {"title": "Shipment Summary", "all": "All", "location": "Location", "at-loading": "At Loading", "arrived": "Arrived", "on-the-way": "On The Way", "total": "Total", "loading": "Loading shipment data...", "no-data": "No shipment data available", "no-results": "No results."}, "vehicle-status-chart": {"title": "Vehicle Status Distribution", "all": "All", "on-the-way": "On The Way", "auction-paid": "Auction Paid", "auction-unpaid": "Auction Unpaid", "on-hand-with-load": "On Hand With Load", "shipped": "Shipped", "on-hand-no": "On Hand No", "on-hand-with-title": "On Hand With Title"}, "mix-invoice-chart": {"title": "Mix Open Invoice", "all": "All", "open": "Open", "paid": "Paid", "due": "Due"}, "invoice-chart": {"title": "All Open Invoice", "all": "All", "open": "Open", "paid": "Paid", "due": "Due"}, "full-open-container-invoice-chart": {"title": "Full Open Container Invoice", "all": "All", "open": "Open", "paid": "Paid", "due": "Due"}, "welcome": {"greeting": "Welcome", "message": "Here is what is happening with your business today"}, "business-health": {"title": "Business Health", "excellent": "Excellent", "good": "Good", "fair": "Fair", "needs-attention": "Needs Attention"}, "progress-cards": {"overall-payment": {"title": "Overall Payment Completion", "description": "Combined payment status across all invoice types"}, "regular-invoice": {"title": "Regular Invoice Payments", "description": "Status of standard invoice payments"}, "mix-invoice": {"title": "Mix Invoice Payments", "description": "Status of mix shipping invoice payments"}}, "summaries": {"vehicle": {"title": "Vehicle Summary", "description": "Current status of all vehicles in the system"}, "shipment": {"title": "Shipment Summary", "description": "Overview of all shipments and their status"}}, "invoice-status": {"title": "Full Invoice Status", "description": "Regular invoice payment distribution", "paid": "Paid", "due": "Due"}, "mix-invoice-status": {"title": "Mix Invoice Status ", "description": "Mix invoice payment distribution"}, "container-invoice-status": {"title": "Container Invoice Status", "description": "Container invoice payment distribution"}, "action-items": {"title": "Action Items", "description": "Items that need your attention", "full-invoices": "Full Invoices", "mix-invoices": "Mix Invoices", "open": "open", "paid": "paid", "regular-payment-rate": "Regular Payment Rate", "mix-payment-rate": "Mix Payment Rate", "good": "Good", "needs-attention": "Needs attention", "view-all-invoices": "View all invoices", "view-due-invoices": "View due invoices", "view-all-mix-invoices": "View all mix invoices", "view-due-mix-invoices": "View due mix invoices"}}, "bar-chart-tooltip": {"first": "Total", "second": "Paid"}, "invoice-tab": {"full-invoice-section": {"title": "Full Invoices", "description": "Full invoice payment distribution"}, "mix-invoice-section": {"title": "Mix Invoices", "description": "Mix invoice payment distribution"}, "progress-card": {"title": "Payment Completion", "description": "Percentage of full invoices paid"}, "donut-chart": {"title": "Full Invoice Status", "description": "Distribution of full invoice payments", "total-label": "Full Invoices"}, "bar-chart": {"title": "12 Months Full Invoice Trends", "description": "Total vs Paid invoices by month"}, "metrics": {"average-value": "Average Invoice Value", "monthly-growth": "Month-over-Month Growth", "open-invoices": "Open Invoices", "payment-efficiency": "Payment Efficiency"}}, "mix-invoice-tab": {"progress-card": {"title": "Mix Payment Completion", "description": "Percentage of mix invoices paid"}, "donut-chart": {"title": "Mix Invoice Status", "description": "Distribution of mix invoice payments", "total-label": "Mix Invoices"}, "bar-chart": {"title": "12 Months Mix Invoice Trends", "description": "Total vs Paid mix invoices by month"}, "metrics": {"average-value": "Average Invoice Value", "monthly-growth": "Month-over-Month Growth", "open-invoices": "Open Invoices", "paid-invoices": "Paid Invoices"}}, "shipment-tab": {"total-shipment": "Total Shipment", "pi-chart": {"title": "Full Open Container Invoice", "description": "Invoice Totals", "total-label": "Invoices"}}, "vehicle-tab": {"total-vehicle": "Total Vehicle"}}, "calculator": {"title": "Calculate Shipping", "description": "Use our Shipping Cost Calculator to instantly estimate shipping fees for your vehicles.", "header": {"title": "Shipping Calculator", "description": "Calculate shipping costs for your vehicle"}, "form": {"state": "State", "branch": "Branch", "city": "City", "destination": "Destination", "additional_services": "Additional Services", "full_size_suvs": "Full Size SUVs", "manheim_adesa": "Manheim Adesa", "major_accident": "Major Accident", "reset": "Reset", "submit": "Calculate Cost", "total_cost": "Total Cost", "loading_calculate": "Calculating...", "loading_states": "Loading States...", "loading_branches": "Loading Branches...", "loading_cities": "Loading Cities...", "loading_destinations": "Loading Destinations...", "state_placeholder": "Select State", "branch_placeholder": "Select Branch", "city_placeholder": "Select City", "destination_placeholder": "Select Destination", "disclaimer_title": "Disclaimer", "disclaimer_description": "An estimated custom duty charge of approximately 10% of the vehicle price and additional services charges may apply.", "no_state_text": "Please select a state first", "no_branch_text": "Please select a branch first"}, "errors": {"select_required": "Please select both city and destination", "calculation_failed": "Failed to calculate shipping cost", "try_again": "Failed to calculate shipping cost. Please try again.", "no_response": "No response received from server. Please check your connection.", "unexpected_data": "Received unexpected data from the server", "api_error": "Error {status}: {message}", "fetch_destinations_failed": "Failed to fetch shipping destinations", "fetch_states_failed": "Failed to fetch shipping states", "fetch_branches_failed": "Failed to fetch shipping branches", "fetch_cities_failed": "Failed to fetch shipping cities", "no_shipping_rate": "No shipping rate found for the provided criteria", "empty_response": "The server returned an empty response", "missing_data": "The server returned a response without the expected 'data' property", "calculation_success": "Calculation completed successfully"}}, "customer-of-customer": {"header": {"id": "ID", "fullname": "Full Name", "password": "Password", "email": "Email", "username": "Username", "phone": "Phone", "status": "Status", "created-at": "Created At", "updated-at": "Updated At", "actions": "Actions"}, "body": {"customers": "Customers", "customer": "Customer", "add-customer": "Add Customer", "edit-customer": "Update Customer", "fullname": "Full Name", "username": "Username", "password": "Password", "email": "Email", "phone-number": "Phone Number", "status": "Status", "status-placeholder": "Select a status", "edit": "Edit", "delete": "Delete", "cancel": "Cancel", "submit": "Submit", "enable": "Enable", "disable": "Disable", "active": "Active", "deactive": "Deactive", "delete-description": "Are you sure you want to delete this customer?", "deactive-description": "Are you sure you want to disable  customer?", "active-description": "Are you sure you want to enable customer?"}}, "filter-modal": {"checked-option": "Checked Options", "not-checked-option": "Not Checked Options", "filters": "Filters", "vehicle-filter-modal": {"container": "Container", "point-of-loading": "Point Of Loading", "point-of-destination": "Point Of Destination", "vehicle-status": "Vehicle Status", "data": {"label": "Data", "lot-number": "Lot Number", "vin": "Vin", "make": "Make", "model": "Model", "year": "Year"}, "price": "Price", "date-renge": {"label": "Date Range", "purchase-at": "Purchase At", "payment-date": "Payment Date", "delivery-date": "Delivery Date"}, "checked": "Checked", "is-printed": "Is Printed"}, "shippment-filter-modal": {"container": "Container", "booking": "Booking", "status": "Status", "date-renge": {"label": "Date Range", "loading-date": "Loading Date", "eta": "ETA", "etd": "ETD"}}, "invoice-filter-modal": {"container": "Container", "status": "vehicle status", "invoice-number": "Invoice Number", "invoice-amount": "Invoice Amount", "payment-received": "Payment Received", "date-range": {"label": "Date Range", "issue-date": "Issue Date"}}, "mix-shipping-rates-modal": {"state": "State", "branch": "Branches", "point-of-loading": "Point Of Loading", "cities": "Cities"}, "payment-filter-modal": {"date-range": {"label": "Date Range", "created-at": "Created At", "updated-at": "Updated At"}}}, "pagination": {"previous": "Previous", "next": "Next", "page": "Page", "of": "of", "to": "to", "results": "results", "displaying": "Displaying"}, "autocomplete": {"label": "Search...", "option-label": "No options found.", "placehoder": "Search option...", "label-option": "Select an option...", "selected": "No Selected options", "unselected": "No Unselected options"}, "export-modal": {"title": "Exporting Data as Excel Sheet", "sub-title": "Exporting Data", "select-type": "Select Type of Data", "current-data": "Current Data", "all-data": "All Data", "cancel": "Cancel", "export": "Export"}, "add-vehicle-form": {"add-vehicle": "Add Vehicle", "edit-vehicle": "Edit Vehicle", "labels": {"vin": "VIN Number", "auction_name": "Auction", "year": "Year", "make": "Make", "model": "Model", "color": "Color", "price": "Price", "lot_number": "Lot Number", "weight": "Weight", "point_of_destination": "Port of Destination", "pol_locations": "Port of Loading", "is_title_exist": "Title Exist", "is_key_present": "Key Present", "title_number": "Title Number", "title_state": "Title State", "receiver_name": "Receiver Name"}, "stepper": {"vehicle-information": "Vehicle Information", "title-key-information": "Title & Key Information", "shipping-information": "Shipping Information", "review-and-submit": "Review & Submit", "sub-items": {"vin": "Vin", "auction_name": "Auction Name", "year": "Year", "make": "Make", "model": "Model", "color": "Color", "price": "Price", "title-exist": "Title Exist", "title-number": "Title Number", "title-state": "Title State", "key-present": "Key Present", "point_of_loading": "Point of Loading", "point_of_destination": "Port of Destination", "weight": "Weight", "lot_number": "Lot Number", "receiver_name": "Receiver Name"}}, "buttons": {"submit": "Submit", "cancel": "Cancel", "update": "Update", "previous": "Previous", "next": "Next", "edit": "Edit"}}, "auction-payment-model": {"title": "Payment Details", "tabs": {"finance": "Finance", "payments": "Payments"}, "finance": {"header": {"items": "Items", "amount": "Amount", "paid-amount": "Paid", "unpaid-amount": "Balance"}, "items": {"vehicle-price": "Vehicle Price", "transportation-fee": "Transportation Fee", "grand-total": "Grand Total"}, "cards": {"paid-in-full": "Paid in Full", "all-payments-have-been-received": "All payments have been received", "payment-required": "Payment Required", "balance-remaining": "Balance Remaining"}}, "payments-list": {"title": "Payment History", "header": {"auction-payment": "Auction Payment", "mix-payment": "Mix Payment"}, "auction-payment": {"amount": "Amount", "date": "Date", "type": "Type", "auction": "Auction"}, "mix-payment": {"amount": "Amount", "date": "Date", "type": "Type", "mix": "Mix"}}}, "buyer-number-datatable": {"header": {"buyer-number": "Buyer Number"}}, "forgot-password": {"title": "Forgot Password", "description": "Enter your email address below and we'll send you instructions to reset your password", "linkText": "Forgot Password?", "cancelButton": "Cancel", "submitButton": "Send Reset Link", "emailLabel": "Email Address", "emailPlaceholder": "<EMAIL>"}, "reset-password": {"title": "Reset Password", "description": "Enter your new password below and we'll send you instructions to reset your   password", "linkText": "Reset Password?", "cancelButton": "Cancel", "submitButton": "Reset Password", "passwordLabel": "New Password", "passwordPlaceholder": "New Password", "confirmPasswordLabel": "Confirm Password", "confirmPasswordPlaceholder": "Confirm Password", "passwordRequirements": "Password Requirements", "minLength": "Minimum 6 characters", "includeNumbers": "Include at least one number and special characters (recommended)", "includeSpecial": "Include special characters (recommended)", "passwordsMatch": "Passwords match", "passwordsDontMatch": "Passwords don't match", "updateSuccess": "Password updated successfully", "updateError": "Failed to update password", "validating": "Validating...", "rememberPassword": "Remember your password?", "signIn": "Sign in", "link-expired": {"title": "Link Expired", "message": "This password reset link is invalid or has expired.", "returnButton": "Return to Login"}}, "company-profiles": {"boosmotors": {"title": "Boss Motors Group", "description": "Boss Motors is a leading global automotive parts and services company"}}, "customer_of_customer_payments": {"title": "Customer Payment Management", "loading": "Loading payments...", "no-data": "No payments found for this customer and vehicle.", "tabs": {"add-payment": "Add Payment", "payment-history": "Payment History"}, "add-payment": {"title": "Add New Payment", "customer": "Customer", "payment-amount": "Payment Amount", "payment-date": "Payment Date", "add-payment": "Add Payment"}, "payment-history": {"title": "Payment History", "total-payment": "Total Payment", "payment-date": "Payment Date", "amount": "Amount", "created-at": "Created At", "total-paid": "Total Paid"}}, "customer-vehicles-datatable": {"label": "Customer Vehicles", "header": {"vehicle": "Vehicle", "vehicle-price": "Vehicle Price", "ship-cost": "Shipping Cost", "customer-profit": "Customer Profit", "storage-charge": "Storage Charge", "total-amount": "Total Amount", "customer-name": "Customer Name", "paid-amount": "<PERSON><PERSON>", "balance": "Balance"}, "customer-profit": {"label": "Customer Profit", "description": "Enter the profit amount for this vehicle", "profit-amount": "Profit <PERSON>", "add-profit": "Add Profit", "save": "Save", "cancel": "Cancel", "saving": "Saving..."}, "storage-charge": {"label": "Storage Charge", "description": "Enter the Storage amount for this vehicle", "storage-amount": "Storage Amount", "add-storage": "Add storage", "save": "Save", "cancel": "Cancel", "saving": "Saving..."}}, "vehicle-tracking": {"sign-in": "Sign In", "title": "Track Your Vehicle", "description": "Monitor your shipment's progress with real-time updates and detailed tracking information.", "search": {"title": "Vehicle Tracking", "description": "Enter your LOT or VIN number to track your vehicle's current status", "placeholder": "Enter vehicle LOT or VIN number"}, "vehicle-progress": {"title": "Shipment Progress", "description": "Track your vehicle's journey with real-time updates", "ordered": "Ordered", "auction-paid": "Auction Paid", "auction-unpaid": "Auction Unpaid", "pending-auction": "Pending Auction", "on-hand-with-title": "On Hand with Title", "on-hand-no-title": "On Hand No Title", "pending-picked": "Pending Pickup", "at-port": "At Port", "pending": "Pending", "cost-analysis": "Cost Analysis", "datelines": "Date Lines", "on-hand-with-load": "On Hand with Load", "added_by_customer": "Added by Customer", "order-placed": "Order Placed", "processing": "Processing", "being-prepared": "Being Prepared", "shipped": "Shipped", "on-the-way": "On the way", "delivered": "Delivered", "completed": "Completed"}, "vehicle-info": {"default": {"title": "Ready to Track", "description": "Enter your LOT or VIN number above to see your vehicle's current status and tracking information."}, "found": {"location-and-time": "Location & Timeline", "auction-name": "AUCTION", "auction-city": "AUCTION CITY", "purchased": "PURCHASED", "pickup": "PICKUP", "delivery": "DELIVERY", "arrival-date": "ARRIVAL DATE", "departure-date": "DEPARTURE DATE", "loading-date": "LOADING DATE"}, "not-found-image": {"title": "Vehicle Photo", "description": "Image Will Appear Here"}, "not-found-data": {"title": "No Vehicle Found", "description": "We couldn't find a vehicle with the provided tracking number. Please check your input and try again.", "btn": "Try Another Search"}}}}