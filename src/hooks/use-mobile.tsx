import { useEffect, useState } from "react";

const MOBILE_BREAKPOINT = 500;
const TABLET_BREAKPOINT = 768;

export function useResponsive() {
  const [screen, setScreen] = useState({
    isMobile: false,
    isTablet: false,
    isAppSidebar: false,
  });

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;

      setScreen({
        isMobile: width < MOBILE_BREAKPOINT,
        isTablet: width < TABLET_BREAKPOINT && width >= MOBILE_BREAKPOINT,
        isAppSidebar: width < TABLET_BREAKPOINT,
      });
    };

    handleResize();

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return {
    isMobile: !!screen.isMobile,
    isTablet: !!screen.isTablet,
    isAppSidebar: !!screen.isAppSidebar,
  };
}
