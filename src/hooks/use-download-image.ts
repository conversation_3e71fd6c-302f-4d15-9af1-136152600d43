"use client"; // Only needed if using Next.js App Router
import { useFetchClient } from "@/utils/axios";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";

async function fetchImage(url: string) {
  const response = await axios.get(url, { responseType: "blob" });
  if (response.status !== 200) throw new Error("Failed to fetch image");
  return response.data;
}

export default function useImageDownloader(url: string) {
  const {
    data: imageBlob,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ["image", url],
    queryFn: () => fetchImage(url),
    enabled: false, // Disable automatic fetching
  });

  const handleDownload = () => {
    refetch().then(({ data }) => {
      if (data) {
        const blobUrl = URL.createObjectURL(data);
        const filename = url.split("/").pop() || "downloaded-image.jpg";

        const anchor = document.createElement("a");
        anchor.href = blobUrl;
        anchor.download = filename;
        document.body.appendChild(anchor);
        anchor.click();
        document.body.removeChild(anchor);

        URL.revokeObjectURL(blobUrl); // Cleanup
      }
    });
  };
  return { isLoading, isError, imageBlob, handleDownload };
}

export async function downloadGoogleDriveImages(
  folderId: string,
  url: string,
  fileName: string,
  fetchClient: ReturnType<typeof useFetchClient>
) {
  const response = await fetchClient(url, {
    params: { folderId },
    responseType: "arraybuffer",
    timeout: 50000,
  });

  // Convert response data into a Blob
  const blob = new Blob([response.data], { type: "application/zip" });
  const objectUrl = window.URL.createObjectURL(blob);

  // Create a link and trigger download
  const link = document.createElement("a");
  link.href = objectUrl;
  link.setAttribute("download", `${fileName}.zip`);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(objectUrl);
}
