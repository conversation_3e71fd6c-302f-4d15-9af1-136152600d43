import { AuctionRenderer } from "@/components/vehicles/cell-renderers/AuctionRenderer";
import { CenterCell } from "@/components/vehicles/cell-renderers/CenterCell";
import { ContainerRenderer } from "@/components/vehicles/cell-renderers/ContainerRenderer";
import { LocationRenderer } from "@/components/vehicles/cell-renderers/LocationRenderer";
import { StatusRenderer } from "@/components/vehicles/cell-renderers/StatusRenderer";
import VehicleRenderer from "@/components/vehicles/cell-renderers/VehicleRenderer";
import { useTranslations } from "next-intl";
import { FunctionComponent } from "react";

// Define the type for table column fields
export interface TableColumn {
  field?: string;
  headerName?: string;
  minWidth?: number;
  cellDataType?: string;
  cellRenderer?: FunctionComponent<any>;
}

export const TAB_NAMES = [
  "all",
  "on_the_way",
  "on_hand_no_title",
  "on_hand_with_title",
  "shipped",
  "on_hand_with_load",
] as const;

export type TabName = (typeof TAB_NAMES)[number];

const useTableColumns = (tab: TabName): TableColumn[] => {
  const t = useTranslations("sidebar");

  const commonColumns: TableColumn[] = [
    {
      field: "vehicle",
      headerName: t("header.vehicle"),
      cellDataType: "text",
      cellRenderer: VehicleRenderer,
      minWidth: 300,
    },
    {
      field: "status",
      headerName: t("header.status"),
      minWidth: 70,
      cellRenderer: StatusRenderer,
    },
    {
      field: "container_number",
      headerName: t("header.container"),
      cellRenderer: ContainerRenderer,
      minWidth: 96,
    },
    {
      field: "dates",
      headerName: t("header.dates"),
      minWidth: 96,
      cellRenderer: CenterCell,
    },
    {
      field: "locations",
      headerName: t("header.locations"),
      minWidth: 160,
      cellRenderer: LocationRenderer,
    },

    {
      field: "auction_balance",
      headerName: t("header.auction"),
      cellRenderer: AuctionRenderer,
      minWidth: 170,
    },
  ];

  const columns: Record<TabName, TableColumn[]> = {
    all: [
      ...commonColumns,
      {
        field: "auction_balance",
        headerName: t("header.auction"),
        minWidth: 170,
        cellRenderer: AuctionRenderer,
      },
    ],
    on_the_way: [
      ...commonColumns,
      {
        field: "shipping_status",
        headerName: t("header.shipping_status"),
        minWidth: 150,
        cellRenderer: StatusRenderer,
      },
    ],
    on_hand_no_title: [
      ...commonColumns,
      {
        field: "documents_needed",
        headerName: t("header.documents"),
        minWidth: 180,
        cellRenderer: CenterCell,
      },
    ],
    on_hand_with_title: [
      ...commonColumns,
      {
        field: "title_received_date",
        headerName: t("header.title_received"),
        minWidth: 140,
        cellRenderer: CenterCell,
      },
    ],
    shipped: [
      ...commonColumns,
      {
        field: "shipment_date",
        headerName: t("header.shipment_date"),
        minWidth: 130,
        cellRenderer: CenterCell,
      },
      {
        field: "carrier_name",
        headerName: t("header.carrier"),
        minWidth: 150,
        cellRenderer: StatusRenderer,
      },
    ],
    on_hand_with_load: [
      ...commonColumns,
      {
        field: "load_capacity",
        headerName: t("header.load_capacity"),
        minWidth: 120,
        cellRenderer: CenterCell,
      },
    ],
  };

  return columns[tab] || [];
};

export default useTableColumns;
