"use client";
import { useFetchClient } from "@/utils/axios";
import { useQuery } from "@tanstack/react-query";
export function useGetPGLProfile() {
  const fetchClient = useFetchClient();

  const { data, isLoading } = useQuery({
    queryKey: ["PGL-profile"],
    queryFn: () => getPGLProfile(fetchClient),
  });

  return { data, isLoading };
}

async function getPGLProfile(fetchClient: ReturnType<typeof useFetchClient>) {
  try {
    const responese = await fetchClient("/v2/contact-us");
    return responese.data;
  } catch (error) {
    throw error;
  }
}
