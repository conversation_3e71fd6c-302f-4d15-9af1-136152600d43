import { useCallback, useMemo, useRef } from "react";
import type { IServerSideDatasource, GridReadyEvent } from "ag-grid-community";

interface DataLoadParams {
  page: number;
  per_page: number;
  search?: string;
  filterData?: string;
  status?: string;
}

interface UseServerSideDatasourceOptions {
  onLoadMoreData: (params: DataLoadParams) => Promise<any>;
  initialParams?: {
    search?: string;
    filterData?: string;
    status?: string;
  };
  pageSize?: number;
  initialRecords?: {
    page: number;
    per_page: number;
    total: number;
    data: any[];
  } | null;
}

export const useServerSideDatasource = ({
  onLoadMoreData,
  initialParams,
  pageSize = 20,
  initialRecords,
}: UseServerSideDatasourceOptions) => {
  const usedInitialRecordsForSearch = useRef<string | null>(null);

  const createDatasource = useCallback((): IServerSideDatasource => {
    return {
      getRows: (params) => {
        const page = Math.floor((params.request.startRow || 0) / pageSize) + 1;
        const currentSearch = initialParams?.search || "";

        if (
          page === 1 &&
          initialRecords &&
          initialRecords.data?.length > 0 &&
          currentSearch &&
          usedInitialRecordsForSearch.current !== currentSearch
        ) {
          usedInitialRecordsForSearch.current = currentSearch;

          params.success({
            rowData: initialRecords.data,
            rowCount: initialRecords.total,
          });
          return;
        }

        const requestParams: DataLoadParams = {
          page,
          per_page: pageSize,
          search: currentSearch,
          filterData: initialParams?.filterData || "",
          status: initialParams?.status || "",
        };

        onLoadMoreData(requestParams)
          .then((response) => {
            if (response && response.data) {
              params.success({
                rowData: response.data,
                rowCount: response.total,
              });
            } else {
              params.success({
                rowData: [],
                rowCount: 0,
              });
            }
          })
          .catch((error) => {
            console.error("[Datasource] - error:", error);
            params.fail();
          });
      },
    };
  }, [onLoadMoreData, initialParams, pageSize, initialRecords]);

  // Memoize the datasource to prevent unnecessary recreations
  const datasource = useMemo(() => {
    usedInitialRecordsForSearch.current = null;
    return createDatasource();
  }, [
    createDatasource,
    initialParams?.search,
    initialParams?.filterData,
    initialParams?.status,
  ]);

  // Grid ready handler that sets up the datasource
  const onGridReady = useCallback(
    (params: GridReadyEvent) => {
      usedInitialRecordsForSearch.current = null;
      params.api.setGridOption("serverSideDatasource", datasource);
    },
    [datasource]
  );

  // Function to refresh the datasource (useful for filters/search updates)
  const refreshDatasource = useCallback(
    (gridApi: any) => {
      usedInitialRecordsForSearch.current = null;
      const newDatasource = createDatasource();
      gridApi.setGridOption("serverSideDatasource", newDatasource);
    },
    [createDatasource]
  );

  return {
    datasource,
    onGridReady,
    refreshDatasource,
  };
};
