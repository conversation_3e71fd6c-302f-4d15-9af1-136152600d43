"use client";

import { useState } from "react";
import <PERSON><PERSON><PERSON><PERSON> from "jszip";
import { saveAs } from "file-saver";
import { toast } from "sonner";
import axios from "axios";
import { useFetchClient } from "@/utils/axios";

export function useDownloadMultipleImages() {
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [isZipping, setIsZipping] = useState(false); // State for zipping process
  const [isZippingAll, setIsZippingAll] = useState(false); // State for zipping process
  const fetchClient = useFetchClient();

  const handleSelect = (url: string) => {
    setSelectedImages((prev: any) =>
      prev.includes(url)
        ? prev.filter((img: string) => img !== url)
        : [...prev, url]
    );
  };
  const handleDownload = async () => {
    if (selectedImages.length === 0) {
      toast.info("No images selected");
      return;
    }

    setIsZipping(true); // Show zipping state

    const zip = new JSZip();
    // Fetch images

    for (const imageUrl of selectedImages) {
      // Fetch the image using Axios
      const response = await fetchClient(`${imageUrl}`, {
        responseType: "blob",
      });

      // Get the image data as a Blob
      const blob = response.data;

      // Extract the file name from the URL
      const fileName = imageUrl.split("/").pop();

      // Add the file to the ZIP
      zip.file(fileName || "image", blob);
    }
    // Generate ZIP and download
    zip.generateAsync({ type: "blob" }).then((zipBlob) => {
      saveAs(zipBlob, "images.zip");
      setIsZipping(false); // Hide zipping state
    });
  };

  const handleDownloadAll = async (images: string[]) => {
    if (!images?.length || images.length === 0) {
      toast.info("No images selected or No Image exist. ");
      return;
    }

    setIsZippingAll(true); // Show zipping state

    const zip = new JSZip();
    // Fetch images

    for (const imageUrl of images) {
      // Fetch the image using Axios
      const response = await axios.get(`${imageUrl}`, { responseType: "blob" });

      // Get the image data as a Blob
      const blob = response.data;

      // Extract the file name from the URL
      const fileName = imageUrl.split("/").pop();

      // Add the file to the ZIP
      zip.file(fileName || "image", blob);
    }
    // Generate ZIP and download
    zip.generateAsync({ type: "blob" }).then((zipBlob) => {
      saveAs(zipBlob, "images.zip");
      setIsZippingAll(false); // Hide zipping state
    });
  };

  return {
    handleDownload,
    handleSelect,
    handleDownloadAll,
    isZipping,
    isZippingAll,
    selectedImages,
    setSelectedImages,
  };
}
