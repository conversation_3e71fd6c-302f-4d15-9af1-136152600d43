"use client";
import { useEffect, useRef, useState } from "react";
import { onMessage, Unsubscribe } from "firebase/messaging";
import { fetchToken, messaging } from "@/lib/firebase";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { useNotification } from "@/context/notification-context";

interface FirebaseError extends Error {
  code?: string;
}

const isDevelopment = process.env.NODE_ENV === "development";
// const isDevelopment = false;

// Helper function to check if we're in a browser environment
const isBrowser = () => typeof window !== "undefined";

async function getNotificationPermissionAndToken() {
  try {
    // Step 1: Check if we're in a browser environment
    if (!isBrowser()) {
      return null;
    }

    // Step 2: Check if Notifications are supported in the browser.
    if (!("Notification" in window)) {
      throw new Error("Notifications are not supported in this browser");
    }

    // Step 3: Check if we're in development mode and on localhost
    if (isDevelopment && window.location.hostname === "localhost") {
      console.info(
        "%cPush Notifications are disabled in development mode on localhost",
        "color: orange; background: #c7c7c7; padding: 8px; font-size: 20px"
      );
      return null;
    }

    // Step 4: Check if permission is already granted.
    if (Notification.permission === "granted") {
      try {
        return await fetchToken();
      } catch (error) {
        const firebaseError = error as FirebaseError;
        if (
          firebaseError?.code === "messaging/failed-service-worker-registration"
        ) {
          console.warn(
            "%cPush Notifications disabled - Service worker registration failed. This is normal in development mode.",
            "color: orange; background: #c7c7c7; padding: 8px; font-size: 20px"
          );
          return null;
        }
        throw error;
      }
    }

    // Step 5: If permission is not denied, request permission from the user.
    if (Notification.permission !== "denied") {
      const permission = await Notification.requestPermission();
      if (permission === "granted") {
        try {
          return await fetchToken();
        } catch (error) {
          const firebaseError = error as FirebaseError;
          if (
            firebaseError?.code ===
            "messaging/failed-service-worker-registration"
          ) {
            console.warn(
              "%cPush Notifications disabled - Service worker registration failed. This is normal in development mode.",
              "color: orange; background: #c7c7c7; padding: 8px; font-size: 20px"
            );
            return null;
          }
          throw error;
        }
      }
      if (permission === "denied") {
        throw new Error("Notification permission was denied");
      }
      return null; // Return null for "default" permission state
    }

    return null; // Return null for denied permission without throwing error
  } catch (error) {
    console.error("Error getting notification permission and token:", error);
    throw error;
  }
}

const useFcmToken = () => {
  const queryClient = useQueryClient();
  const { tabValue, totalNotification, setTotalNotification } =
    useNotification();
  const router = useRouter(); // Initialize the router for navigation.
  const [notificationPermissionStatus, setNotificationPermissionStatus] =
    useState<NotificationPermission | null>(null); // State to store the notification permission status.
  const [token, setToken] = useState<string | null>(null); // State to store the FCM token.
  const [error, setError] = useState<string | null>(null);
  const [isDevelopmentMode, setIsDevelopmentMode] = useState(false);
  const retryLoadToken = useRef(0); // Ref to keep track of retry attempts.
  const isLoading = useRef(false); // Ref to keep track if a token fetch is currently in progress.
  const retryTimeout = useRef<NodeJS.Timeout | null>(null);

  const loadToken = async () => {
    if (isLoading.current || !isBrowser()) return;

    try {
      isLoading.current = true;
      setError(null);
      const token = await getNotificationPermissionAndToken();

      // Update permission status regardless of token
      if (isBrowser()) {
        setNotificationPermissionStatus(Notification.permission);
      }

      if (Notification.permission === "denied") {
        console.info(
          "%cPush Notifications disabled - permission denied",
          "color: orange; background: #c7c7c7; padding: 8px; font-size: 20px"
        );
        return;
      }

      if (!token) {
        if (retryLoadToken.current >= 3) {
          console.info(
            "%cPush Notifications disabled - unable to load token after 3 retries",
            "color: orange; background: #c7c7c7; padding: 8px; font-size: 20px"
          );
          return;
        }

        retryLoadToken.current += 1;
        // Exponential backoff: 1s, 2s, 4s
        const delay = Math.pow(2, retryLoadToken.current - 1) * 1000;
        retryTimeout.current = setTimeout(() => {
          loadToken();
        }, delay);
        return;
      }

      setToken(token);
      retryLoadToken.current = 0; // Reset retry counter on success
    } catch (error) {
      const firebaseError = error as FirebaseError;
      if (error instanceof Error) {
        // Only set error for unexpected errors, not for permission denied or service worker issues
        if (
          !error.message.includes("permission was denied") &&
          firebaseError?.code !== "messaging/failed-service-worker-registration"
        ) {
          setError(error.message);
        }
      } else {
        setError("An unknown error occurred");
      }
      console.error("Error loading FCM token:", error);
    } finally {
      isLoading.current = false;
    }
  };

  // Initialize development mode state
  useEffect(() => {
    if (isBrowser()) {
      setIsDevelopmentMode(
        isDevelopment && window.location.hostname === "localhost"
      );
    }
  }, []);

  // Load token and set up listeners
  useEffect(() => {
    if (isBrowser()) {
      loadToken();
    }

    return () => {
      if (retryTimeout.current) {
        clearTimeout(retryTimeout.current);
      }
    };
  }, []);

  useEffect(() => {
    const setupListener = async () => {
      if (!token) return; // Exit if no token is available.

      const m = await messaging();
      if (!m) return;

      // Step 9: Register a listener for incoming FCM messages.
      const unsubscribe = onMessage(m, (payload) => {
        if (Notification.permission !== "granted") return;
        setTotalNotification({ ...totalNotification, isSave: false });
        queryClient.invalidateQueries({ queryKey: ["notification", tabValue] });

        const link = payload.fcmOptions?.link || payload.data?.link;

        if (link) {
          toast.info(
            `${payload.notification?.title}: ${payload.notification?.body}`,
            {
              action: {
                label: "Visit",
                onClick: () => {
                  const link = payload.fcmOptions?.link || payload.data?.link;
                  if (link) {
                    router.push(link);
                  }
                },
              },
            }
          );
        } else {
          toast.info(`${payload.notification?.title}`, {
            description: payload.notification?.body,
            action: {
              label: "Close",
              onClick: () => {},
            },
          });
        }
        // --------------------------------------------
        // Disable this if you only want toast notifications.
        // const n = new Notification(
        //   payload.notification?.title || "New message",
        //   {
        //     body: payload.notification?.body || "This is a new message",
        //     data: link ? { url: link } : undefined,
        //   }
        // );

        // // Step 10: Handle notification click event to navigate to a link if present.
        // n.onclick = (event) => {
        //   event.preventDefault();
        //   const link = (event.target as any)?.data?.url;
        //   if (link) {
        //     router.push(link);
        //   } else {
        //     console.log("No link found in the notification payload");
        //   }
        // };
        // --------------------------------------------
      });

      return unsubscribe;
    };

    let unsubscribe: Unsubscribe | null = null;

    setupListener().then((unsub) => {
      if (unsub) {
        unsubscribe = unsub;
      }
    });

    // Step 11: Cleanup the listener when the component unmounts.
    return () => unsubscribe?.();
  }, [
    token,
    router,
    queryClient,
    setTotalNotification,
    tabValue,
    totalNotification,
  ]);

  return {
    token,
    notificationPermissionStatus,
    error,
    isLoading: isLoading.current,
    isPermissionDenied: notificationPermissionStatus === "denied",
    isServiceWorkerError: error?.includes("service worker"),
    isDevelopmentMode,
  };
};

export default useFcmToken;
