// types/next-auth.d.ts
import "next-auth";
declare module "next-auth" {
  interface Session {
    backendTokens: {
      accessToken: string;
      refreshToken: string;
      expiresIn: number;
    };
    user_type: string;
    sessionId: number;
    profile: {
      photo?: string;
      companies?: {
        logo?: string;
        profile_name?: string;
        show_shipping_rate?: boolean;
        has_customer?: boolean;
        mix?: boolean;
      };
    };
  }

  interface User {
    backendTokens: {
      accessToken: string;
      refreshToken: string;
      expiresIn: number;
    };
    user_type: string;
    sessionId: number;
    profile?: {
      photo?: string;
      companies?: {
        logo?: string;
        profile_name?: string;
        show_shipping_rate?: boolean;
        has_customer?: boolean;
        mix?: boolean;
      };
    };
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    backendTokens: {
      accessToken: string;
      refreshToken: string;
      expiresIn: number;
    };
    user_type: string;
    sessionId: number;
    profile?: {
      photo?: string;
      companies?: {
        logo?: string;
        profile_name?: string;
        show_shipping_rate?: boolean;
        has_customer?: boolean;
        mix?: boolean;
      };
    };
  }
}