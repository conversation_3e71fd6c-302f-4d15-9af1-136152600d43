export interface BasicInformationData {
  fullName: string;
  companyName: string;
  email: string;
  phone: string;
  address: string;
  destination: string;
}

export interface ConsigneeInformationData {
  consigneeFullName: string;
  consigneeCompanyName: string;
  consigneeEmail: string;
  consigneePhone: string;
  consigneeAddress: string;
}

export interface NotifyPartyData {
  notifyFullName: string;
  notifyCompanyName: string;
  notifyEmail: string;
  notifyPhone: string;
  notifyAddress: string;
}

export interface LoadVolumeData {
  numberOfVehicles: string;
  numberOfContainers: string;
}

export interface CarTypes {
  container: boolean;
  consolidation: boolean;
}

export interface VehicleTypes {
  suv: boolean;
  sedan: boolean;
}

export interface AdditionalInformationData {
  secondaryEmail: string;
  secondaryPhone: string;
  joinDate: string;
  usedCar: boolean;
  completeCars: CarTypes;
  halfcutCars: CarTypes;
  vehicleTypes: VehicleTypes;
}

export interface RegistrationResponseData {
  id: string;
  customers: {
    id: number;
    lang: string;
    loginable: {
      id: number;
      email: string;
    }
  }[];
}

export interface ContractData {
  uploadedFile: File | null;
  hasDownloadedContract: boolean;
  registrationData?: RegistrationResponseData;
}

export interface FormData {
  basicInformation: BasicInformationData;
  additionalInformation: AdditionalInformationData;
  consigneeInformation: ConsigneeInformationData;
  notifyParty: NotifyPartyData;
  loadVolume: LoadVolumeData;
  contract: ContractData;
} 