import { differenceInDays, format, isValid, parseISO } from "date-fns";

export const getInvoicePayments = (item: any, type = 'Full') => {
  let totalAmountApplied = 0;

  if (type != 'Full') {
    item?.mix_shipping_vehicles?.forEach((veh: any) => {
      veh.payments?.forEach((payment: any) => {
        if (item?.type == 'mix') {
          totalAmountApplied +=
            parseFloat(payment.amount_applied) * item.exchange_rate;
        } else {
          totalAmountApplied += parseFloat(payment.amount_applied);
        }
      });
    });
  } else {
    item?.payments?.forEach((payment: any) => {
      totalAmountApplied += parseFloat(payment.amount_applied);
    });
  }

  return totalAmountApplied;
};

export const countTotal = (data: any) => {
  let total = 0;
  if (data) {
    data?.map((item: any) => {
      total +=
        +item?.clearance +
        +item?.freight +
        +item?.vat_and_custom +
        +item?.tow_amount +
        item?.mix_shipping_vehicle_charges?.reduce(
          (accumulator: number, p: any) => {
            return accumulator + +p?.value;
          },
          0,
        );
    });

    return total;
  }
  return total;
};

export const countPaidTotal = (data: any) => {
  let paidTotal = 0;
  if (data) {
    data?.map((item: any) => {
      paidTotal += +item?.payment_amount;
    });
    return paidTotal;
  }
  return paidTotal;
};

export const formatter = (currency: string) => new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: currency,
  maximumFractionDigits: 0,
});

export const getUniquePaymentDate = (item: any) => {
  if (item) {
    const uniquePaymentDates = Array.from(
      new Set(item.map((payment: any) => payment.payment_date)),
    );
    const uniqueDates = uniquePaymentDates.map((date) => date);
    return uniqueDates.map((date: any) => isValid(date) ? format(date, "YYYY-MM-DD") : "");
  } else {
    return [];
  }
};

export const formatDateFromNow = (date: any) => {
  const dateFromNow = differenceInDays(new Date(Date.now()), new Date(date))
  return dateFromNow;
};

export const formatOnlyDate = (date: string | Date) => {
  let formattedDates: string[] = [];

  if (typeof date === "string") {
    // Split the input by "|", trim spaces, and format each date
    formattedDates = date.split("|").map((d) => {
      const parsedDate = parseISO(d.trim());
      return isValid(parsedDate) ? format(parsedDate, "MMM d, yyyy") : "Invalid date";
    });
  } else {
    formattedDates.push(format(new Date(date), "MMM d, yyyy"));
  }

  return formattedDates.join(" | ");
};

export function removeMatchingValue<T extends Record<string, any>>(
  obj: T,
  key: keyof T,
  valuesToRemove: any[]
): void {
  if (valuesToRemove.includes(obj[key])) {
    obj[key] = "" as T[typeof key]; // or undefined/null if needed
  }
}

export const formatNumberByLocale = (number: number, locale: "ar" | "ru" | "ka" | "en") => {
  const localeMap = {
    ar: 'ar-EG',
    ru: 'ru-RU',
    ka: 'ka-GE',
    en: 'en-US',
  };
  const intlLocale = localeMap[locale] || 'en-US';
  const formatter = new Intl.NumberFormat(intlLocale);
  return formatter.format(number);
};