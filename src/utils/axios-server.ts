import Axios, { AxiosInstance, InternalAxiosRequestConfig } from "axios";
import { API_CONFIG } from "../config/api";
import { auth } from "@/lib/auth";

// Create main API instance
const axios: AxiosInstance = Axios.create({
  baseURL: API_CONFIG.URL,
  timeout: 60000,
  withCredentials: true,
});

// Request interceptor
axios.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    const session = await auth();
    if (session) {
      config.headers.Authorization = `Bearer ${
        session?.backendTokens?.accessToken?.split("@aaaa")[0]
      }`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default axios;
