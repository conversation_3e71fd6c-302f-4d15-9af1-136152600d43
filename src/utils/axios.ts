"use client";

import axios, { AxiosRequestConfig } from "axios";
import { API_CONFIG } from "@/config/api";
import { getSession, useSession } from "next-auth/react";
import { useCallback } from "react";

export const useFetchClient = () => {
  const { data: session } = useSession();
  const fetchClient = useCallback(
    async (url: string, options?: AxiosRequestConfig) => {
      let token = session?.backendTokens?.accessToken?.split("@aaaa")[0];
      let expiresIn = session?.backendTokens?.expiresIn;


      // Proactively refresh if token is near expiration
      if (token && expiresIn && Date.now() >= expiresIn - 60 * 1000) {
        console.log("Expiring the refresh token");

        const updatedSession = await getSession(); // fetch updated session
        token = updatedSession?.backendTokens?.accessToken?.split("@aaaa")[0];
        expiresIn = updatedSession?.backendTokens?.expiresIn;

      }

      const headers = {
        ...options?.headers,
        ...(token && { Authorization: `Bearer ${token}` }),
      };

      return axios({
        url: `${API_CONFIG.URL}${url}`,
        method: options?.method || "GET",
        headers,
        data: options?.data,
        params: options?.params,
        responseType: options?.responseType,
      });
    },
    [session]
  );

  return fetchClient;
};