"use client";

import { useFetchClient } from "../axios";

export async function getAutoComplete(
  {
    column,
    model,
  }: {
    column: string;
    model: string;
  },
  fetchClient: ReturnType<typeof useFetchClient>
) {
  try {
    const response = await fetchClient(
      `/v2/autocomplete?column_name=${column}&modal=${model}`
    );
    return response.data;
  } catch (error) {
    throw new Error(error as any);
  }
}
