"use server";
import axios from "../axios-server";

export async function getAutoComplete({
  column,
  model,
  name,
  ids,
  id,
}: {
  column: string;
  model: string;
  name?: string;
  ids?: string | string[];
  id?: string;
}) {
  try {
    let url = `/v2/autocomplete?column_name=${column}&modal=${model}`;

    if (name) {
      url += `&${column}=${encodeURIComponent(name)}`;
    }

    if (ids) {
      url += `&ids=${encodeURIComponent(Number(ids))}`;
    }
    if (id) {
      url += `&id=${encodeURIComponent(id)}`;
    }

    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    throw error;
  }
}
