export function generateImageUrl(image: string) {
  // Convert the base64 string to a Blob
  const byteString = atob(image.split(',')[1]); // Remove the data URL prefix if present
  const mimeString = image.split(',')[0].split(':')[1].split(';')[0]; // Extract MIME type
  const ab = new ArrayBuffer(byteString.length);
  const ia = new Uint8Array(ab);
  for (let i = 0; i < byteString.length; i++) {
    ia[i] = byteString.charCodeAt(i);
  }
  const blob = new Blob([ab], { type: mimeString });

  // Create a File object from the Blob
  const file = new File([blob], "uploaded_file.jpg", { type: mimeString });
  return file;
}