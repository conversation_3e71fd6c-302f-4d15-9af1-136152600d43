const _imageSizes = [1024, 900, 250, 100];
type imageURL = {
  url?: string;
  size?: number;
};
export const getImageSizeUrl = ({ size = 250, url }: imageURL): string => {
  if (!url) return "";
  let processedUrl = "";
  if (_imageSizes.includes(size)) {
    processedUrl =
      process.env.NEXT_PUBLIC_MINIO_ENDPOINT +
      `/${url.replace(/\b(900|250|100|1024)\b/, `${size}`)}`;
  } else {
    processedUrl = process.env.NEXT_PUBLIC_MINIO_ENDPOINT + `/${url}`;
  }
  return processedUrl.replace(/([^:]\/)\/+/g, "$1");
};
export const getGoogleDriveImageSizeUrl = ({ size = 250, url }: imageURL): string => {
  if (!url) return "";
  let processedUrl = "";
  if (_imageSizes.includes(size)) {
    processedUrl = `${url.replace(/\b(900|250|100|1024)\b/, `${size}`)}`;
  } else {
    processedUrl = `${url}`;
  }
  return processedUrl.replace(/([^:]\/)\/+/g, "$1");
};
