"use client";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { getAutoComplete } from "./autoComplete/autoComplete-server";

type Prop = {
  column: string;
  model: string;
  key?: string;
};

export function useGetAutoComplete({ column, model }: Prop) {
  const [searchTerm, setSearchTerm] = useState<string>("");

  const queryKey = [model + "loc", searchTerm];

  const { data, isLoading, refetch } = useQuery({
    queryKey: queryKey,
    queryFn: () =>
      getAutoComplete({
        column: column,
        model: model,
        name: searchTerm,
      }),
    enabled: false,
  });
  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  return {
    data,
    isLoading,
    refetch,
    handleSearch,
  };
}
