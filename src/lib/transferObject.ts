export function transformObject(
  input: Record<string, any>,
  allowedKeys: string[],
  mapping: Record<string, string>,
  alwaysStringFields: string[] = []
) {
  const output: Record<string, any> = {};

  allowedKeys.forEach((key) => {
    if (input[key] !== undefined && mapping[key]) {
      const mappedKey = mapping[key];
      const parts = mappedKey.split(".");
      const shouldBeString = alwaysStringFields.includes(key);
      let value: any;

      if (parts.length === 1) {
        if (Array.isArray(input[key])) {
          value = input[key].map((val) =>
            shouldBeString ? String(val) : isNaN(val) ? val : Number(val)
          );
        } else {
          value = shouldBeString
            ? String(input[key])
            : isNaN(input[key])
            ? input[key]
            : Number(input[key]);
        }
        output[mappedKey] = value;
      } else {
        const rawValue = Array.isArray(input[key]) ? input[key][0] : input[key];
        value = shouldBeString
          ? String(rawValue)
          : isNaN(rawValue)
          ? rawValue
          : Number(rawValue);
        const parentKey = parts.slice(0, -1).join(".");
        const childKey = parts[parts.length - 1];
        if (!output[parentKey]) {
          output[parentKey] = {};
        }
        output[parentKey][childKey] = value;
      }
    }
  });

  return output;
}
