import * as z from "zod"
import { BasicInformationData, AdditionalInformationData, ConsigneeInformationData, NotifyPartyData, LoadVolumeData } from "@/types/form"

// Zod Schemas
export const basicInformationSchema = z.object({
  fullName: z.string().min(2, "Name must be at least 2 characters"),
  companyName: z.string().min(2, "Company name must be at least 2 characters").optional().or(z.literal("")),
  email: z.string().email("Invalid email address"),
  phone: z.string()
    .min(10, "Phone number must be at least 10 digits")
    .regex(/^[0-9]\d{9,}$/, "Phone number must be number and be at least 10 digits"),
  address: z.string().min(5, "Address must be at least 5 characters").optional().or(z.literal("")),
  destination: z.number().min(1, "Please select a destination"),
})

export const additionalInformationSchema = z.object({
  secondaryEmail: z.string().email("Invalid secondary email address").optional().or(z.literal("")),
  secondaryPhone: z.string()
    .regex(/^[0-9]\d{9,}$/, "Phone number must be number and be at least 10 digits")
    .optional()
    .or(z.literal("")),
  joinDate: z.string().optional().or(z.literal("")),
  usedCar: z.boolean().optional().default(false),
  completeCars: z.object({
    container: z.boolean().optional().default(false),
    consolidation: z.boolean().optional().default(false),
  }).optional().default({}),
  halfcutCars: z.object({
    container: z.boolean().optional().default(false),
    consolidation: z.boolean().optional().default(false),
  }).optional().default({}),
  vehicleTypes: z.object({
    suv: z.boolean().optional().default(false),
    sedan: z.boolean().optional().default(false),
  }).optional().default({}),
})

export const consigneeInformationSchema = z.object({
  consigneeFullName: z.string().optional(),
  consigneeCompanyName: z.string().optional(),
  consigneeEmail: z.string().email("Invalid email address").optional().or(z.literal("")),
  consigneePhone: z.string()
  .regex(/^[0-9]\d{9,}$/, "Phone number must be number and be at least 10 digits")
  .optional()
  .or(z.literal("")),
  consigneeAddress: z.string().optional(),
})

export const notifyPartySchema = z.object({
  notifyFullName: z.string().optional(),
  notifyCompanyName: z.string().optional(),
  notifyEmail: z.string().email("Invalid email address").optional().or(z.literal("")),
  notifyPhone: z.string()
  .regex(/^[0-9]\d{9,}$/, "Phone number must be number and be at least 10 digits")
  .optional()
  .or(z.literal("")),
  notifyAddress: z.string().optional(),
})

export const loadVolumeSchema = z.object({
  numberOfVehicles: z.string().min(1, "Number of vehicles is required").refine(
    (val) => !isNaN(Number(val)) && Number(val) > 0,
    "Number of vehicles must be a positive number"
  ).optional().or(z.literal("")),
  numberOfContainers: z.string().min(1, "Number of containers is required").refine(
    (val) => !isNaN(Number(val)) && Number(val) > 0,
    "Number of containers must be a positive number"
  ).optional().or(z.literal("")),
})

// Helper function to handle Zod validation errors
const handleZodError = (error: unknown) => {
  if (error instanceof z.ZodError) {
    return error.errors.reduce((acc, curr) => {
      const path = curr.path[0] as string
      acc[path] = curr.message
      return acc
    }, {} as Record<string, string>)
  }
  return {}
}

// Validation functions using Zod schemas
export const validateBasicInformation = (data: BasicInformationData) => {
  try {
    basicInformationSchema.parse(data)
    return {}
  } catch (error) {
    return handleZodError(error)
  }
}

export const validateAdditionalInformation = (data: AdditionalInformationData) => {
  try {
    additionalInformationSchema.parse(data)
    return {}
  } catch (error) {
    return handleZodError(error)
  }
}

export const validateConsigneeInformation = (data: ConsigneeInformationData) => {
  try {
    consigneeInformationSchema.parse(data)
    return {}
  } catch (error) {
    return handleZodError(error)
  }
}

export const validateNotifyParty = (data: NotifyPartyData) => {
  try {
    notifyPartySchema.parse(data)
    return {}
  } catch (error) {
    return handleZodError(error)
  }
}

export const validateLoadVolume = (data: LoadVolumeData) => {
  try {
    loadVolumeSchema.parse(data)
    return {}
  } catch (error) {
    return handleZodError(error)
  }
} 