export const cleanAndTransformParams = (urlParams: any) => {
  const transformedParams: any = {};

  Object.keys(urlParams).forEach((key) => {
    if (urlParams[key]) {
      try {
        // Decode the parameter first
        let decodedValue = decodeURIComponent(urlParams[key]);

        // Manually replace encoded symbols with their normal form (optional)
        decodedValue = decodedValue
          .replace(/%20/g, " ") // Replace %20 with space
          .replace(/%2C/g, ",") // Replace %2C with comma
          .replace(/%3A/g, ":") // Replace %3A with colon
          .replace(/%2F/g, "/"); // Replace %2F with slash

        transformedParams[key] = decodedValue;
      } catch (erro: any) {
        erro.value=urlParams[key];
        console.warn(`Invalid URI component for key "${key}":`, urlParams[key]);
        transformedParams[key] = urlParams[key]; // Keep the original value if decoding fails
      }
    }
  });

  return transformedParams;
};
