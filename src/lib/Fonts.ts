import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON>o, <PERSON>o_Sans_Arabic, Inter } from "next/font/google";

export const notoSansArabic = Noto_Sans_Arabic({
  weight: ["400", "700"], // Add the weight property here
  subsets: ["arabic"],
  variable: "--font-noto-sans-arabic-sans",
});

export const inter = Inter({
  subsets: ["latin"],
  display: "swap",
});

export const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

export const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});
