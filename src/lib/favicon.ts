// lib/favicon.ts
export async function getFaviconForSubdomain(
  subdomain: string
): Promise<string> {
  // You could fetch this from your database based on subdomain
  // For now, using a simple mapping
  const faviconMap: Record<string, string> = {
    // Map your subdomains to favicon paths
    customer1: "/favicons/customer1.ico",
    customer2: "/favicons/customer2.ico",
    // Add more mappings as needed
  };

  return faviconMap[subdomain] || "/favicons/default.ico";
}
