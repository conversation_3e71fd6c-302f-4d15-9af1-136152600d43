import moment from "moment";

export const filterAllowedKeys = (
  filterObject: any,
  allowedKeys: string[]
): any => {
  if (Array.isArray(filterObject)) {
    // Recursively process arrays and remove empty objects/arrays
    const filteredArray = filterObject
      .map((item) => filterAllowedKeys(item, allowedKeys))
      .filter((item) => item && Object.keys(item).length > 0);
    return filteredArray.length > 0 ? filteredArray : null;
  }

  if (typeof filterObject === "object" && filterObject !== null) {
    // Recursively handle `AND` and `OR` keys and filter empty objects
    const filteredObject = Object.fromEntries(
      Object.entries(filterObject)
        .filter(
          ([key]) => key === "AND" || key === "OR" || allowedKeys.includes(key)
        )
        .flatMap(([key, value]) => {
          if (key === "AND" || key === "OR") {
            // Recursively process nested conditions
            const nested = filterAllowedKeys(value, allowedKeys);
            return nested ? [[key, nested]] : [];
          }
          // Keep allowed key-value pairs
          return [[key, value]];
        })
    );
    return Object.keys(filteredObject).length > 0 ? filteredObject : null;
  }

  // If the input is neither an object nor an array, return it as is
  return filterObject;
};
export const $format = (val: any) => (val > 0 ? `${"$" + val}` : "");
export const formatDateFromNow = (date: any) => {
  const today = moment().utc();
  const deliverDate = moment(date).utc();
  return !Number.isNaN(today.diff(deliverDate, "day"))
    ? today.diff(deliverDate, "day") < 0
      ? ""
      : today.diff(deliverDate, "day") + " days"
    : "";
};
export const culcHelper = (item: any, type: string) => {
  const total =
    parseInt(item?.price ?? 0) +
    parseInt(item?.vehicle_costs?.towing_cost ?? 0) +
    parseInt(item?.vehicle_costs?.dubai_custom_cost ?? 0) +
    parseInt(item?.vehicle_costs?.dismantal_cost ?? 0) +
    parseInt(item?.vehicle_costs?.ship_cost ?? 0) +
    parseInt(item?.vehicle_costs?.pgl_storage_costs ?? 0) +
    parseInt(item?.vehicle_costs?.title_charge ?? 0) +
    parseInt(item?.vehicle_costs?.dubai_custom_cost ?? 0) +
    parseInt(item?.vehicle_costs?.other_cost ?? 0);
  if (type == "total_cost") return total;
  const profit = parseInt(item?.vehicle_costs?.sales_cost ?? 0) - total;
  if (type == "profit") return $format(profit ?? 0);
  if (type == "percent_profit") {
    if ((profit * 100) / total) {
      return ((profit * 100) / total).toFixed(2) + " %";
    }
    return 0 + " %";
  }
};

const currencyFormat: Record<string, { locale: string; currency: string }> = {
  USD: { locale: "en-US", currency: "USD" },
  AED: { locale: "ar-AE", currency: "AED" },
  OMR: { locale: "ar-OM", currency: "OMR" },
  GE: { locale: "ka-GE", currency: "GE" }
};
export const CustomFormatCurrency = (value: number,
  currency: string
) => {
  if (currency === "AED" || currency === "OMR") {
    const formatedValue = new Intl.NumberFormat(currencyFormat[currency].locale, {
      maximumFractionDigits: 2,
      minimumFractionDigits: 0,
      numberingSystem: "latn"
    }).format(value);
    return `${formatedValue} ${currency}`
  } else {
    return new Intl.NumberFormat(currencyFormat[currency].locale, {
      style: "currency",
      currency,
      maximumFractionDigits: 2,
      minimumFractionDigits: 0,
    }).format(value);
  }
};