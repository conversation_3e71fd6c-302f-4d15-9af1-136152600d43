import credential from "next-auth/providers/credentials";
import { authConfig } from "./auth.config";
import axios from "axios";
import NextAuth, { Session } from "next-auth";

type credentialType = {
  username: string;
  password: string;
};

const getProfile = async (token: string) => {
  try {
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/customer/v2/auth/profile`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data.data || undefined;
  } catch (error) {
    return { result: false, error };
  }
};
const handleCustomerLogin = async (credential: credentialType) => {

  try {
    const user = await axios.post(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/customer/v2/auth/admin-login-as-customer`,
      { customer_id: Number(credential.username) }, {
      headers: {
        'Authorization': `Bearer ${credential.password?.split('token-')[1]}`
      }
    });
    const accessToken = user.data?.access_token;
    const refreshToken = user.data?.refresh_token;
    const expiresIn = user.data?.expireIn;
    const sessionId = user.data?.session?.id;

    let profile = undefined;
    if (accessToken) {
      profile = await getProfile(accessToken);
    }
    return {
      backendTokens: {
        accessToken,
        refreshToken,
        expiresIn,
      },
      user_type: user.data?.user_type,
      profile: profile,
      expires: expiresIn,
      sessionId: sessionId,
    };
  } catch (error: any) {
    console.log("Customer login", error?.response?.data);

    // throw new Error("Something went wrong", error?.response?.data?.message);
  }
};
export const login = async (credential: credentialType): Promise<Session> => {
  try {
    const user = await axios.post(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/customer/v2/auth/login`,
      {
        email_username: credential.username,
        password: credential.password,
      }
    );
    // accessing the accessToken returned by server
    const accessToken = user.data?.access_token;
    const refreshToken = user.data?.refresh_token;
    const expiresIn = user.data?.expireIn;
    const sessionId = user.data?.session?.id;

    let profile = undefined;
    if (accessToken) {
      profile = await getProfile(accessToken);
    }
    return {
      backendTokens: {
        accessToken,
        refreshToken,
        expiresIn,
      },
      user_type: user.data?.user_type,
      profile: profile,
      expires: expiresIn,
      sessionId: sessionId,
    };
  } catch (error: any) {
    throw new Error("Something went wrong", error?.response?.data?.message);
  }
};

const credentialConfig = credential({
  name: "credential",
  async authorize(credential: any): Promise<any> {
    try {
      let loginUser
      if (credential?.adminPassword?.includes('token-')) {
        credential.password = credential.adminPassword;
        delete credential.adminPassword
        loginUser = await handleCustomerLogin(credential as credentialType)
      } else {
        loginUser = await login(credential as credentialType);
      }
      return loginUser
    } catch (error: any) {
      console.error("Authorize Error:", error);
      return null;
    }
  },
});

// // Configure the direct-login provider

export const { handlers, auth, signIn, signOut } = NextAuth({
  ...authConfig.session,
  providers: [credentialConfig],
  callbacks: {
    ...authConfig.callbacks,
  },
});
