// export const BASE_URL = "http://localhost:3000";
export const CHARGES = {
  attestation_fee: "Attestation Fee",
  inspection_charges: "Inspection Charges",
  title_charges: "Title Charges",
  auction_storage: "Auction Storage",
  sharjah_yard_storage: "Sharjah Yard Storage",
  fed_ex_or_mailing_fee: "FedEx/ Mailing Fee",
  recovery_fee: "Recovery Fee",
  custom_hold: "Custom Hold",
  relist_fee: "Relist Fee",
  detention_charges: "Detention Charges",
  shortage: "Shortage",
  suv_charges: "SUV Charges",
  other_charges: "Other Charges",
  tds_charges: "tds_charges",
} as const;

export type ChargeKeys = keyof typeof CHARGES; // This will be a union type of all the keys

export const colorSystem = {
  // Vehicle Status Colors
  on_hand_no_title: {
    bg: "bg-amber-500/10 dark:bg-amber-500/20",
    txt: "text-amber-600 dark:text-amber-400",
  },
  on_hand_with_title: {
    bg: "bg-emerald-500/10 dark:bg-emerald-500/20",
    txt: "text-emerald-600 dark:text-emerald-400",
  },
  on_hand_with_load: {
    bg: "bg-teal-500/10 dark:bg-teal-500/20",
    txt: "text-teal-600 dark:text-teal-400",
  },
  shipped: {
    bg: "bg-blue-500/10 dark:bg-blue-500/20",
    txt: "text-blue-600 dark:text-blue-400",
  },
  on_the_way: {
    bg: "bg-indigo-500/10 dark:bg-indigo-500/20",
    txt: "text-indigo-600 dark:text-indigo-400",
  },

  // Auction Status Colors
  auction_paid: {
    bg: "bg-green-500/10 dark:bg-green-500/20",
    txt: "text-green-600 dark:text-green-400",
  },
  auction_unpaid: {
    bg: "bg-red-500/10 dark:bg-red-500/20",
    txt: "text-red-600 dark:text-red-400",
  },

  // Process Status Colors
  pending: {
    bg: "bg-yellow-100 dark:bg-yellow-900",
    txt: "text-yellow-800 dark:text-yellow-100",
  },
  approved: {
    bg: "bg-blue-100 dark:bg-blue-900",
    txt: "text-blue-800 dark:text-blue-100",
  },
  up_coming: {
    bg: "bg-cyan-500/10 dark:bg-cyan-500/20",
    txt: "text-cyan-600 dark:text-cyan-400",
  },
  in_process: {
    bg: "bg-purple-500/10 dark:bg-purple-500/20",
    txt: "text-purple-600 dark:text-purple-400",
  },
  rolled_over: {
    bg: "bg-slate-500/10 dark:bg-slate-500/20",
    txt: "text-slate-600 dark:text-slate-400",
  },
  cancelled: {
    bg: "bg-red-600/10 dark:bg-red-600/20",
    txt: "text-red-600 dark:text-red-400",
  },
  not_submited: {
    bg: "bg-orange-500/10 dark:bg-orange-500/20",
    txt: "text-orange-600 dark:text-orange-400",
  },

  // Critical Status Colors
  missing: {
    bg: "bg-red-700/10 dark:bg-red-700/20",
    txt: "text-red-700 dark:text-red-400",
  },

  // Progress Status Colors
  done: {
    bg: "bg-green-600/10 dark:bg-green-600/20",
    txt: "text-green-600 dark:text-green-400",
  },
  at_loading: {
    bg: "bg-purple-100 dark:bg-purple-900",
    txt: "text-purple-800 dark:text-purple-100",
  },
  arrived: {
    bg: "bg-teal-500/10 dark:bg-teal-500/20",
    txt: "text-teal-600 dark:text-teal-400",
  },
  reserved: {
    bg: "bg-purple-600/10 dark:bg-purple-600/20",
    txt: "text-purple-600 dark:text-purple-400",
  },

  // Inspection Status Colors
  checked: {
    bg: "bg-emerald-500/10 dark:bg-emerald-500/20",
    txt: "text-emerald-600 dark:text-emerald-400",
  },
  final_checked: {
    bg: "bg-green-600/10 dark:bg-green-600/20",
    txt: "text-green-700 dark:text-green-400",
  },

  // Location Status Colors
  at_the_dock: {
    bg: "bg-cyan-600/10 dark:bg-cyan-600/20",
    txt: "text-cyan-600 dark:text-cyan-400",
  },
  clearance: {
    bg: "bg-amber-600/10 dark:bg-amber-600/20",
    txt: "text-amber-600 dark:text-amber-400",
  },
  cbp_inspection: {
    bg: "bg-orange-600/10 dark:bg-orange-600/20",
    txt: "text-orange-600 dark:text-orange-400",
  },

  // Generic Status Colors
  status: {
    bg: "bg-slate-500/10 dark:bg-slate-500/20",
    txt: "text-slate-600 dark:text-slate-400",
  },
  past_due: {
    bg: "bg-red-600/10 dark:bg-red-600/20",
    txt: "text-red-600 dark:text-red-400",
  },
  paid: {
    bg: "bg-green-600/10 dark:bg-green-600/20",
    txt: "text-green-600 dark:text-green-400",
  },
  open: {
    bg: "bg-blue-500/10 dark:bg-blue-500/20",
    txt: "text-blue-600 dark:text-blue-400",
  },
} as const;

export type ColorSystemKey = keyof typeof colorSystem;

export const chooseColor = (value: ColorSystemKey | string): string =>
  colorSystem[value as ColorSystemKey]?.bg || "bg-green-500";
