// lib/auth.config.ts
import type { NextAuthConfig, Session } from "next-auth";
import { JWT } from "next-auth/jwt";
import { jwtDecode } from "jwt-decode";
import { NextRequest, NextResponse } from "next/server";
import { routing } from "@/i18n/routing";
import axios from "axios";
import createMiddleware from "next-intl/middleware";

const nextIntlMiddleware = createMiddleware(routing);

type jwtType = {
  token: JWT;
  user: any;
  trigger?: "signIn" | "signUp" | "update";
  session?: any;
};
const publicRoutes = [
  "/auth/signin",
  "/auth/register",
  "/auth/forgot-password",
  "/auth/reset-password",
  "/server-error",
  "/auth/direct-login",
  "/auth/tracking-vehicle",
];

const commonRoutes = ["/", "/profile"];

async function refreshAccessToken(token: JWT): Promise<JWT | null> {
  const response = await axios.post(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/customer/v2/auth/refresh`,
    {},
    {
      headers: {
        Authorization: `refresh_token ${token.backendTokens.refreshToken}`,
      },
    }
  );
  return {
    ...token,
    backendTokens: {
      accessToken: response.data.access_token,
      refreshToken:
        response.data.refresh_token ?? token.backendTokens.refreshToken,
      expiresIn: response.data.expireIn,
    },
  };
}

export const authConfig: NextAuthConfig = {
  trustHost: true,
  session: {
    strategy: "jwt",
  },
  providers: [],
  callbacks: {
    async jwt({ token, user, trigger, session }: jwtType): Promise<JWT | null> {
      try {
        // If there's an update with new profile data, merge it
        if (trigger === "update") {
          if (session.profile.photo)
            token.profile.photo = session.profile.photo;

          if (session.profile.companies.logo)
            token.profile.companies.logo = session.profile.companies.logo;
          token.profile.companies.profile_name =
            session.profile.companies.profile_name;
          if (session.profile && session.profileupdating) {
            token.profile = session.profile
          }
        }

        // If this is initial sign-in, use the user object
        if (user) {
          // Parse sessionId as a number
          let sessionId: number = 0;
          if (user.sessionId) {
            sessionId =
              typeof user.sessionId === "number"
                ? user.sessionId
                : parseInt(user.sessionId as string, 10);

            if (isNaN(sessionId)) {
              sessionId = 0;
            }
          }

          token.profile = user.profile;
          token.backendTokens = user.backendTokens;
          token.user_type = user.user_type;
          token.backendTokens.expiresIn = user.backendTokens.expiresIn;
          token.sessionId = sessionId;
          return token;
        }

        if (token.backendTokens.accessToken) {
          const decodedToken: any = jwtDecode(token.backendTokens.accessToken);
          if (decodedToken.exp) {
            token.backendTokens.expiresIn = decodedToken.exp * 1000;
            token.exp = decodedToken.exp;
          }
        }

        if (Date.now() < token.backendTokens.expiresIn - 60 * 1000) {
          return token;
        }

        return await refreshAccessToken(token);
      } catch (error: any) {
        console.log("Error:", error?.response?.data?.message);

        return null;
      }
    },

    async session({ token, session }) {
      session.profile = token.profile;
      session.backendTokens = {
        accessToken:
          token.backendTokens.accessToken +
          "@aaaa" +
          token.backendTokens.accessToken +
          "qwzxc",
        expiresIn: token.backendTokens.expiresIn,
      };
      session.user_type = token.user_type;
      session.sessionId = token.sessionId;
      return session;
    },

    async authorized({
      request,
      auth,
    }: {
      request: NextRequest;
      auth: Session | null;
    }) {
      try {
        const response = nextIntlMiddleware(request);
        const pathname = request.nextUrl.pathname;
        const locale = pathname.split("/")[1] as "en" | "ru" | "ka" | "ar";
        const userType = auth?.user_type;
        const currentLocale = routing.locales.includes(locale) ? locale : "en";
        const allowedPathsToCustomerOfCustomer = [
          `/${currentLocale}/c_vehicles`,
          `/${currentLocale}/profile`,
        ];
        const isAllowedPath = allowedPathsToCustomerOfCustomer.some(
          (path) => pathname === path
        );

        const isPublicRoute = publicRoutes.some((route) =>
          pathname.startsWith(`/${locale}${route}`)
        );
        const isCommonRoute = commonRoutes.some((route) => {
          if (route === "/") {
            return pathname.endsWith(`/${locale}${route}`);
          }
          return pathname.startsWith(`/${locale}${route}`);
        });

        if (!auth) {
          if (isPublicRoute) return response;
          return NextResponse.redirect(
            new URL(`/${currentLocale}/auth/signin`, request.url)
          );
        }

        if (
          (userType === "customer_of_customer" && isAllowedPath) ||
          pathname === `/${currentLocale}`
        ) {
          return response;
        }

        if (
          (userType === "customer" && !isAllowedPath) ||
          pathname === `/${currentLocale}`
        ) {
          if (
            !auth.profile?.companies?.show_shipping_rate &&
            pathname.endsWith(`/${currentLocale}/mix-shipping-rates`)
          ) {
            return NextResponse.redirect(
              new URL(`/${currentLocale}`, request.url)
            );
          }

          if (
            !auth.profile?.companies?.has_customer &&
            pathname.includes("/customer/")
          ) {
            return NextResponse.redirect(
              new URL(`/${currentLocale}`, request.url)
            );
          }
          if (
            !auth.profile?.companies?.has_customer_invoice &&
            pathname.includes("/customer_invoice")
          ) {
            return NextResponse.redirect(
              new URL(`/${currentLocale}`, request.url)
            );
          }
          if (
            !auth.profile?.companies?.mix &&
            !auth.profile.mix_shipping_status &&
            pathname.startsWith(`/${currentLocale}/mix-shipping`)
          ) {
            return NextResponse.redirect(
              new URL(`/${currentLocale}`, request.url)
            );
          }

          return response;
        }

        if (isPublicRoute || isCommonRoute) {
          return response;
        }

        return NextResponse.redirect(
          new URL(`/${currentLocale}/`, request.url)
        );
      } catch (error) {
        console.error("Authorization error:", error);
        return NextResponse.redirect(new URL(`/server-error`, request.url));
      }
    },
  },
};
