import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

import type { Metadata } from "next";
import { getTranslations } from "next-intl/server";

export const metadata: Metadata = {
  title: "404 - Page Not Found",
  description: "...",
};
const NotFound = async () => {
  const t = await getTranslations("not_found");
  return (
    <div className="flex min-h-screen w-full flex-col items-center justify-center bg-background text-foreground">
      <div className="relative text-center">
        <h1 className="absolute left-1/2 top-1/4 z-10 mb-8 -translate-x-1/2 -translate-y-1/2 select-none text-[clamp(16px,80vw,500px)] font-extrabold text-primary opacity-10">
          404
        </h1>
        <div className="relative z-20 mt-20 max-w-[500px]">
          <h2 className="mb-6 text-4xl font-extrabold">{t("title")}</h2>
          <p className="mx-auto mb-8 text-lg text-muted-foreground">
            {t("description")}
          </p>
          <Link href="/">
            <Button size="lg" className="font-semibold">
              {t("button")}
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
