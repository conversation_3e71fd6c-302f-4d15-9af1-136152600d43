import AppHeader from "@/components/header/app-header";
import { localesTypes } from "@/i18n/routing";
import { getTranslations } from "next-intl/server";
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: localesTypes }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "sidebar" });
  return {
    title: t("nav.invoice.label"),
  };
}
const InvoiceLayout = async ({
  children,
  params,
}: {
  children: React.ReactNode;
  params:Promise<{ locale: localesTypes }>;
}) => {
  const t = await getTranslations("sidebar");
  const { locale } = await params;
  const isRTL = locale === "ar";
  return (
    <div dir={isRTL ? "rtl" : "ltr"}>
      <AppHeader
        breadcrumbs={[
          {
            name: t("nav.dashboard"),
            href: "/",
          },
          {
            name: t("nav.invoice.label"),
            href: "/invoices/all",
          },
          {
            name: "All",
            href: "/invoices/all",
          },
        ]}
        dropdownItems={[
          { name: t("nav.invoice.all"), href: "/invoices/all" },
          {
            name: t("nav.invoice.open"),
            href: "/invoices/open",
          },
          {
            name: t("nav.invoice.past-due"),
            href: "/invoices/past_due",
          },
          {
            name: t("nav.invoice.paid"),
            href: "/invoices/paid",
          },
        ]}
      />

      <div className="p-4">{children}</div>
    </div>
  );
};

export default InvoiceLayout;
