import InputSearch from "@/components/Common_UI/InputSearch";
import ExportMixShippingData from "@/components/mix-shipping/export-mix-shipping-data";
import MixShippingClient from "@/components/mix-shipping/mix-shipping-client";
import { loadMixShippingData } from "@/components/mix-shipping/services/mix-shipping-action";
import { getTranslations } from "next-intl/server";
import React from "react";

type PageProps = {
  params: Promise<any>;
  searchParams: any;
};
const page = async ({ params, searchParams }: PageProps) => {
  const params2 = await params;
  const searchPara = await searchParams;
  const baseState = params2?.tab !== "all" ? params2?.tab : "";
  const records = await loadMixShippingData(baseState, searchPara, {
    page: searchPara?.page || 1,
    per_page: searchPara?.per_page || 20,
    search: searchPara?.search || "",
  });

  const t = await getTranslations("sidebar");
  const t2 = await getTranslations("datatable");
  return (
    <div className="">
      <div className="flex flex-col md:flex-row items-center justify-between mb-4">
        <h2 className="hidden md:text-2xl lg:text-3xl font-bold tracking-tight md:block">
          {t("nav.mix-shipping.label")}
        </h2>
        <div className="flex items-center space-x-2">
          <ExportMixShippingData records={records} baseState={baseState} />

          <InputSearch
            type="text"
            fieldName="search"
            placeholder={t2("sidebar.search")}
          />
        </div>
      </div>
      <div className="">
        <div className="h-[calc(100vh-200px)] w-[calc(100vw-32px)] sm:w-[calc(100vw-32px)] md:w-[calc(100vw-32px)] lg:w-full xl:w-full relative">
          <MixShippingClient
            initialRecords={records}
            baseState={baseState}
            searchParams={searchPara}
          />
        </div>
      </div>
    </div>
  );
};

export default page;
