import AppHeader from "@/components/header/app-header";
import { localesTypes } from "@/i18n/routing";
import { getTranslations } from "next-intl/server";
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: localesTypes }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "sidebar" });
  return {
    title: t("nav.mix-shipping.label"),
  };
}
const InvoiceLayout = async ({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: localesTypes }>;
}) => {
  const t = await getTranslations("sidebar");
  const { locale } = await params;
  const isRTL = locale === "ar";
  return (
    <div dir={isRTL ? "rtl" : "ltr"}>
      <AppHeader
        breadcrumbs={[
          {
            name: t("nav.dashboard"),
            href: "/",
          },
          {
            name: t("nav.mix-shipping.label"),
            href: "/mix-shipping/all",
          },
          {
            name: "All",
            href: "/mix-shipping/all",
          },
        ]}
        dropdownItems={[
          { name: t("nav.invoice.all"), href: "/mix-shipping/all" },
          {
            name: t("nav.mix-shipping.open"),
            href: "/mix-shipping/open",
          },
          {
            name: t("nav.mix-shipping.past-due"),
            href: "/mix-shipping/past-due",
          },
          {
            name: t("nav.mix-shipping.paid"),
            href: "/mix-shipping/paid",
          },
        ]}
      />

      <div className="p-4">{children}</div>
    </div>
  );
};

export default InvoiceLayout;
