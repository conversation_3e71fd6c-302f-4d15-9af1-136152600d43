import AppHeader from '@/components/header/app-header'
import ProfileTabs from '@/components/profile/profile-tabs'

import { Card } from '@/components/ui/card'
import { useLocale, useTranslations } from 'next-intl';
import React from 'react'

export default function ProfilePage() {
  const t = useTranslations("sidebar");
  const t1 = useTranslations("profile");
  const locale = useLocale();
  return (
    <Card className='gird grid-rows-[auto_1fr] '>
      <AppHeader
        breadcrumbs={[
          {
            name: t("nav.dashboard"),
            href: "/",
          },
          {
            name: t1("account.label"),
            href: `/${locale}/profile`,
          },
        ]}
      />

      <ProfileTabs />
    </Card>
  )
}
