import AppHeader from "@/components/header/app-header";
import { localesTypes } from "@/i18n/routing";
import { getTranslations } from "next-intl/server";
export async function generateMetadata({
  params,
}: {
  params:Promise<{ locale: localesTypes }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "sidebar" });
  return {
    title: t("nav.shipments.label"),
  };
}
const ShipmentLayout = async ({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: localesTypes }>;
}) => {
  const t = await getTranslations("sidebar");
  const { locale } = await params;
  const isRTL = locale === "ar";
  return (
    <div dir={isRTL ? "rtl" : "ltr"}>
      <AppHeader
        breadcrumbs={[
          {
            name: t("nav.dashboard"),
            href: "/",
          },
          {
            name: t("nav.shipments.label"),
            href: "/shipments/all",
          },
          {
            name: "All",
            href: "/shipments/all",
          },
        ]}
        dropdownItems={[
          { name: t("nav.shipments.all"), href: "/shipments/all" },
          {
            name: t("nav.shipments.at-loading"),
            href: "/shipments/at_loading",
          },
          {
            name: t("nav.shipments.on-the-way"),
            href: "/shipments/on_the_way",
          },
          {
            name: t("nav.shipments.arrived"),
            href: "/shipments/arrived",
          },
        ]}
      />

      <div className="p-4">{children}</div>
    </div>
  );
};

export default ShipmentLayout;
