"use client";
import React from "react";
import { AppSidebar } from "@/components/sidebar/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useDirection } from "@/hooks/useDirection";
import NotificationProvider from "@/context/notification-context";
import { SidebarCountProvider } from "@/context/sidebar-count-context";
import LocationsProvider from "@/context/get-POLoading-context";
import { useSession } from "next-auth/react";
import { LoadingSpinner } from "@/components/Common_UI/loading";

const WithSidebar = ({ children }: { children: React.ReactNode }) => {
  const { isRTL } = useDirection();
  const { status } = useSession();
  return status === "authenticated" ? (
    <div className="max-w-[1400px] mx-auto relative overflow-hidden">
      <SidebarCountProvider>
        <NotificationProvider>
          <LocationsProvider>
            <SidebarProvider defaultOpen={true}>
              {isRTL ? (
                <>
                  <SidebarInset>{children}</SidebarInset>
                  <AppSidebar />
                </>
              ) : (
                <>
                  <AppSidebar />
                  <SidebarInset>{children}</SidebarInset>
                </>
              )}
            </SidebarProvider>
          </LocationsProvider>
        </NotificationProvider>
      </SidebarCountProvider>
    </div>
  ) : (
    <div className="w-screen h-screen flex items-center justify-center">
      <LoadingSpinner />
    </div>
  );
};

export default WithSidebar;
