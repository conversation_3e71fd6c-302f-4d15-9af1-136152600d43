import InputSearch from "@/components/Common_UI/InputSearch";
import { loadCustomerVehicleData } from "@/components/customer_vehicle/services/customer-vehicle-action";
import CustomerVehicleClient from "@/components/customer_vehicle/vehicle-client";
import AppHeader from "@/components/header/app-header";
import { localesTypes } from "@/i18n/routing";
import { getTranslations } from "next-intl/server";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: localesTypes }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "sidebar" });
  return {
    title: t("nav.vechicles.label"),
  };
}

type PageProps = {
  params: Promise<{ locale: localesTypes }>;
  searchParams: any;
};

const CustomerVehiclePage = async ({ params, searchParams }: PageProps) => {
  const t = await getTranslations("sidebar");
  const t2 = await getTranslations("datatable");
  const { locale } = await params;
  const searchPara = await searchParams;
  const isRTL = locale === "ar";

  const records = await loadCustomerVehicleData(searchPara, {
    page: searchPara?.page || 1,
    per_page: searchPara?.per_page || 20,
    search: searchPara?.search || "",
  });

  return (
    <div dir={isRTL ? "rtl" : "ltr"}>
      <AppHeader
        breadcrumbs={[
          {
            name: t("nav.dashboard"),
            href: "/",
          },
          { name: t2("header.vehicle"), href: "/c_vehicles" },
        ]}
      />
      <div className="h-[calc(100vh-188px)] px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col md:flex-row items-center justify-between my-3">
          <h2 className="hidden md:text-2xl lg:text-3xl font-bold tracking-tight md:block">
            {t2("header.vehicle")}
          </h2>
          <div className="flex items-center gap-2">
            <InputSearch
              fieldName="search"
              type="text"
              placeholder={t2("sidebar.search")}
            />
          </div>
        </div>
        <div className="flex justify-center w-full">
          <div className="w-full max-w-full">
            <div className="h-[calc(100vh-200px)] w-full">
              <CustomerVehicleClient
                initialRecords={records}
                searchParams={searchPara}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerVehiclePage;
