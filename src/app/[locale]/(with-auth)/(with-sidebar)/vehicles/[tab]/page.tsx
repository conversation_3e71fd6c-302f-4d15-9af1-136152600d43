import { PageProps } from "@/components/vehicles/services/config";
import { loadVehicleData } from "@/components/vehicles/services/veihicle-actions";
import VehiclePageClient from "@/components/vehicles/vehicle-client";
import React from "react";

const Page = async ({ params, searchParams }: PageProps) => {
  const params2 = await params;
  const searchPara = await searchParams;

  const baseState = params2?.tab !== "all" ? params2?.tab : "";

  // Load initial data
  const records = await loadVehicleData(baseState, searchPara, {
    page: searchPara?.page || 1,
    per_page: searchPara?.per_page || 20,
    search: searchPara?.search || "",
  });

  return (
    <div className="">
      <div className="h-[calc(100vh-200px)] w-[calc(100vw-32px)] sm:w-[calc(100vw-32px)] md:w-[calc(100vw-32px)] lg:w-full xl:w-full relative">
        <VehiclePageClient
          initialRecords={records}
          baseState={baseState}
          searchParams={searchPara}
        />
      </div>
    </div>
  );
};

export default Page;
