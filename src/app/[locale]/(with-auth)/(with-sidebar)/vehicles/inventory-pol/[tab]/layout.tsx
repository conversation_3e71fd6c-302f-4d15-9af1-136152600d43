import AppHeader from "@/components/header/app-header";
import { localesTypes } from "@/i18n/routing";
import { getTranslations } from "next-intl/server";
import { getAllLocations } from "@/components/vehicles/services/vehicle-service";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: localesTypes }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "sidebar" });
  return {
    title: t("nav.vechicles.inventory-pol.label"),
  };
}

const VehiclesPOLLayout = async ({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: localesTypes }>;
}) => {
  const t = await getTranslations("sidebar");
  const { locale } = await params;
  const isRTL = locale === "ar";

  const destinations = await getAllLocations();

  const dropdownItems = destinations?.map((destination: any) => ({
    name: destination.name,
    href: `/vehicles/inventory-pol/${destination.name}`,
  }));

  return (
    <div dir={isRTL ? "rtl" : "ltr"}>
      <AppHeader
        breadcrumbs={[
          {
            name: t("nav.dashboard"),
            href: "/",
          },
          {
            name: t("nav.vechicles.label"),
            href: "/vehicles/all",
          },
          {
            name: "All",
            href: "/vehicles/all",
          },
        ]}
        dropdownItems={[
          {
            name: "All",
            href: "/vehicles/inventory-pol/all",
          },
          ...dropdownItems,
        ]}
      />
      <div className="p-4">{children}</div>
    </div>
  );
};

export default VehiclesPOLLayout;
