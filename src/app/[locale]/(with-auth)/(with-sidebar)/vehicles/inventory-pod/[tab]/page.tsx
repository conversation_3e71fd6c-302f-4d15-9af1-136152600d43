import InputSearch from "@/components/Common_UI/InputSearch";
import { PageProps } from "@/components/vehicles/services/config";
import { getDestinations } from "@/components/vehicles/services/vehicle-service";
import { transformObject } from "@/lib/transferObject";
import { getTranslations } from "next-intl/server";
import React from "react";
import { cleanAndTransformParams } from "@/lib/decode-url-params";
import ExportInventoryPODVehicleData from "@/components/vehicles/inventory-pod/export-inventory-pod-vehicle-data";
import InventoryPODClient from "@/components/vehicles/inventory-pod/inventory-pod-client";
import { loadPointOfDestinationData } from "@/components/vehicles/services/veihicle-actions";

const allowedKeys = [
  "con",
  "loc",
  "des",
  "pri_from",
  "pri_to",
  "payment_from",
  "payment_to",
  "carstate",
  "vin",
  "lot_number",
  "make",
  "model",
  "year",
  "from_purchased_at",
  "to_purchased_at",
  "from_payment_date",
  "to_payment_date",
  "from_deliver_date",
  "to_deliver_date",
  "checked",
  "is_printed",
];

// Define dynamic mapping (Customize as needed)
const mapping = {
  con: "container_id",
  des: "destination_id",
  loc: "point_of_loading",
  inv: "invoice_number",
  pri_from: "price.min",
  pri_to: "price.max",
  payment_from: "payment_received.min",
  payment_to: "payment_received.max",
  carstate: "carstate",
  lot_number: "lot_number",
  vin: "vin",
  make: "make",
  model: "model",
  year: "year",
  from_purchased_at: "purchased_at.from",
  to_purchased_at: "purchased_at.to",
  from_payment_date: "payment_date.from",
  to_payment_date: "payment_date.to",
  from_deliver_date: "deliver_date.from",
  to_deliver_date: "deliver_date.to",
  checked: "bool@@customer_checked",
  is_printed: "bool@@is_printed",
};
const Page = async ({ params, searchParams }: PageProps) => {
  const params2 = await params;
  const searchPara = await searchParams;
  const t = await getTranslations("sidebar");
  const t2 = await getTranslations("datatable");
  const cleanedParams = cleanAndTransformParams(params2);

  const destinations = await getDestinations();
  const baseState = params2?.tab !== "all" ? params2?.tab : "";

  const mappedDestinations = destinations.map((item: any) => ({
    name: item.name,
    id: item.id,
  }));

  const destinationId =
    cleanedParams?.tab && cleanedParams?.tab !== "all"
      ? cleanedParams?.tab === "none"
        ? 9999
        : mappedDestinations.find(
            (item: any) => item.name === cleanedParams?.tab
          )?.id
      : undefined;

  const transformedFilters = transformObject(searchPara, allowedKeys, mapping);

  const filterData =
    Object.keys(transformedFilters).length !== 0
      ? JSON.stringify(transformedFilters)
      : "";

  const records = await loadPointOfDestinationData(baseState, searchPara, {
    destinationId: destinationId,
    page: searchPara?.page || 1,
    per_page: searchPara?.per_page || 20,
    search: searchPara?.search || "",
    filterData: filterData,
  });

  return (
    <div>
      <div className="flex flex-col md:flex-row items-center justify-between mb-4">
        <h2 className="hidden md:text-2xl lg:text-3xl font-bold tracking-tight md:block">
          {t("nav.vechicles.inventory-poD.label")}
        </h2>
        <div className="flex items-center gap-2">
          <ExportInventoryPODVehicleData
            records={records}
            destinationid={destinationId}
          />
          <InputSearch
            type="text"
            fieldName="search"
            placeholder={t2("sidebar.search")}
            isClose={true}
          />
        </div>
      </div>

      <div className="">
        <div className="h-[calc(100vh-200px)] w-[calc(100vw-32px)] sm:w-[calc(100vw-32px)] md:w-[calc(100vw-32px)] lg:w-full xl:w-full relative">
          <InventoryPODClient
            destinationId={destinationId}
            initialRecords={records}
            baseState={baseState}
            searchParams={searchPara}
          />
        </div>
      </div>
    </div>
  );
};

export default Page;
