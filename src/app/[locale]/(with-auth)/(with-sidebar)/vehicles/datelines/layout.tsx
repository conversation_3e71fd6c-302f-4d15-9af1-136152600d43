import AppHeader from "@/components/header/app-header";
import { localesTypes } from "@/i18n/routing";
import { getTranslations } from "next-intl/server";
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: localesTypes }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "sidebar" });
  return {
    title: t("nav.vechicles.dateline"),
  };
}
const CostAnalysisLayout = async ({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: localesTypes }>;
}) => {
  const t = await getTranslations("sidebar");
  const { locale } = await params;
  const isRTL = locale === "ar";
  return (
    <div dir={isRTL ? "rtl" : "ltr"}>
      <AppHeader
        breadcrumbs={[
          {
            name: t("nav.dashboard"),
            href: "/",
          },
          {
            name: t("nav.vechicles.label"),
            href: "/vehicles/all",
          },
          {
            name: t("nav.vechicles.cost-analysis"),
            href: "/vehicles/cost-analysis",
          },
        ]}
        dropdownItems={[
          { name: t("nav.vechicles.all"), href: "/vehicles/all" },
          {
            name: t("nav.vechicles.auction-unpaid"),
            href: "/vehicles/auction_unpaid",
          },
          {
            name: t("nav.vechicles.auction-paid"),
            href: "/vehicles/auction_paid",
          },
          { name: t("nav.vechicles.on-the-way"), href: "/vehicles/on_the_way" },
          {
            name: t("nav.vechicles.on-the-hand-no"),
            href: "/vehicles/on_hand_no_title",
          },
          {
            name: t("nav.vechicles.on-the-hand-with"),
            href: "/vehicles/on_hand_with_title",
          },
          {
            name: t("nav.vechicles.on-the-hand-with-load"),
            href: "/vehicles/on_hand_with_load",
          },
          {
            name: t("nav.vechicles.shipped"),
            href: "/vehicles/shipped",
          },
          {
            name: t("nav.vechicles.cost-analysis"),
            href: "/vehicles/cost-analysis",
          },
          {
            name: t("nav.vechicles.dateline"),
            href: "/vehicles/datelines",
          },
        ]}
      />

      <div className="p-4">{children}</div>
    </div>
  );
};

export default CostAnalysisLayout;
