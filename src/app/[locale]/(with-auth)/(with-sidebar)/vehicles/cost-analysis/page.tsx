import InputSearch from "@/components/Common_UI/InputSearch";
import CostAnalysisClient from "@/components/vehicles/cost-analysis/cell-renderers/const-analysis-client";
import ExportCostAnalysis from "@/components/vehicles/cost-analysis/export-const-analysis-data";
import { PageProps } from "@/components/vehicles/services/config";
import { loadCostAnalysisData } from "@/components/vehicles/services/veihicle-actions";
import { getTranslations } from "next-intl/server";
import React from "react";

const Page = async ({ params, searchParams }: PageProps) => {
  const params2 = await params;
  const searchPara = await searchParams;
  const baseState = params2?.tab !== "all" ? params2?.tab : "";
  const records = await loadCostAnalysisData(baseState, searchPara, {
    page: searchPara?.page || 1,
    per_page: searchPara?.per_page || 20,
    search: searchPara?.search || "",
  });

  const t = await getTranslations("sidebar");
  const t2 = await getTranslations("datatable");
  return (
    <div className="">
      <div className="flex flex-col md:flex-row items-center justify-between mb-4">
        <h2 className="hidden text-3xl font-bold tracking-tight md:block">
          {t("nav.vechicles.cost-analysis")}
        </h2>
        <div className="flex items-center space-x-2">
          <ExportCostAnalysis records={records} />

          <InputSearch
            type="text"
            fieldName="search"
            placeholder={t2("sidebar.search")}
            isClose={true}
          />
        </div>
      </div>

      <div className="">
        <div className="h-[calc(100vh-200px)] w-[calc(100vw-32px)] sm:w-[calc(100vw-32px)] md:w-[calc(100vw-32px)] lg:w-full xl:w-full relative">
          <CostAnalysisClient
            records={records}
            baseState={baseState}
            searchParams={searchPara}
          />
        </div>
      </div>
    </div>
  );
};

export default Page;
