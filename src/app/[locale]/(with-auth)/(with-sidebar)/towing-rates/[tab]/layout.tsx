import AppHeader from "@/components/header/app-header";
import { localesTypes } from "@/i18n/routing";
import { getTranslations } from "next-intl/server";
import axios from "@/utils/axios-server";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: localesTypes }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "sidebar" });
  return {
    title: t("nav.towing-rates.label"),
  };
}

const getProfile = async () => {
  try {
    const response = await axios.get(`/v2/auth/profile`);
    return { result: true, data: response.data.data };
  } catch (error) {
    return { result: false, error };
  }
};

const TowingRatesLayout = async ({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: localesTypes }>;
}) => {
  const { locale } = await params;
  const isRTL = locale === "ar";

  const t = await getTranslations({ locale, namespace: "sidebar" });
  const profileResponse = await getProfile();
  const profile = profileResponse.result ? profileResponse.data : null;

  const towingItems = [];
  if (profile?.companies?.complete || profile?.companies?.complete_halfcut) {
    if (profile?.companies?.complete) {
      towingItems.push({
        name: t("nav.towing-rates.complete"),
        href: "/towing-rates/complete",
      });
    }
    if (profile?.companies?.complete_halfcut) {
      towingItems.push({
        name: t("nav.towing-rates.half-cut"),
        href: "/towing-rates/halfcut",
      });
    }
  }

  const dropdownItems = towingItems.length > 1 ? towingItems : [];

  return (
    <div dir={isRTL ? "rtl" : "ltr"}>
      <AppHeader
        breadcrumbs={[
          {
            name: t("nav.dashboard"),
            href: "/",
          },
          {
            name: t("nav.towing-rates.label"),
            href: "/towing-rates",
          },
          {
            name: t("nav.towing-rates.half-cut"),
            href: "/towing-rates/halfcut",
          },
        ]}
        dropdownItems={dropdownItems}
      />

      <div className="p-4">{children}</div>
    </div>
  );
};

export default TowingRatesLayout;
