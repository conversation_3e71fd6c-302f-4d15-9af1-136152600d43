import InputSearch from "@/components/Common_UI/InputSearch";
import { MixShippingRatesDataTable } from "@/components/mix-shipping-rates/cell-renderers/mix-shipping-rate-data-table";
import DownloadData from "@/components/mix-shipping-rates/download-data";
import { getMixShippingRate } from "@/components/mix-shipping-rates/services/mix-shipping-rates-service";
import { transformObject } from "@/lib/transferObject";
import { getTranslations } from "next-intl/server";
import React from "react";

type PageProps = {
  params: Promise<any>;
  searchParams: any;
};

// Define allowed keys (Only these fields will be processed)
const allowedKeys = ["states", "loc", "branches", "city"];

// Define dynamic mapping (Customize as needed)
const mapping = {
  states: "stateId",
  branches: "branchId",
  loc: "location_id",
  city: "loading_city_id",
};
const Page = async ({ params, searchParams }: PageProps) => {
  const params2 = await params;
  const searchPara = await searchParams;
  const records = await getMixShippingRate({
    params: {
      status: params2?.tab !== "all" ? params2?.tab : "",
      page: searchPara?.page || 1,
      per_page: searchPara?.per_page || 20,
      search: searchPara?.search || "",
      exactMatch: false,
      filterData:
        Object.keys(transformObject(searchPara, allowedKeys, mapping))
          .length !== 0
          ? JSON.stringify(transformObject(searchPara, allowedKeys, mapping))
          : "{}",
    },
  });
  const t = await getTranslations("sidebar");
  const t2 = await getTranslations("datatable");
  return (
    <div className="">
      <div className="flex flex-col md:flex-row items-center justify-between mb-4">
        <h2 className="hidden md:text-2xl lg:text-3xl font-bold tracking-tight md:block">
          {t("nav.mix-shipping-rates")}
        </h2>
        <div className="flex items-center space-x-2">
          <DownloadData />
          <InputSearch
            fieldName="search"
            type="text"
            placeholder={t2("sidebar.search")}
          />
        </div>
      </div>
      <div className="h-[calc(100vh-148px)]">
        <MixShippingRatesDataTable records={records} />
      </div>
    </div>
  );
};

export default Page;
