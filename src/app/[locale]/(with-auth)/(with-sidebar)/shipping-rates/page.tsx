import AppHeader from '@/components/header/app-header';
import { getShippingRate } from '@/components/shipping-rates/services/shipping-rate-service';
import { PageProps } from "@/components/payments/services/config";
import { getTranslations } from 'next-intl/server';
import React from 'react'
import { transformObject } from '@/lib/transferObject';
import { ShippingRateTable } from '@/components/shipping-rates/cellRenderer/shipping-rate-table';
const allowedKeys = [
  "from_created_at",
  "to_created_at",
  "from_updated_at",
  "to_updated_at",
];

// Define dynamic mapping (Customize as needed)
const mapping = {
  from_created_at: `created_at.from`,
  to_created_at: "created_at.to",
  from_updated_at: "updated_at.from",
  to_updated_at: "updated_at.to",
};
export default async function Page({
  params,
  searchParams
}: PageProps) {
  const t = await getTranslations("sidebar");
  const { locale } = await params;
  const params2 = await params;
  const searchPara = await searchParams;

  const isRTL = locale === "ar";
  const records = await getShippingRate({
    params: {
      state: params2?.tab !== "all" ? params2?.tab : "",
      page: searchPara?.page || 1,
      per_page: searchPara?.per_page || 20,
      search: searchPara?.search || "",
      exactMatch: false,
      filterData:
        Object.keys(transformObject(searchPara, allowedKeys, mapping))
          .length !== 0
          ? JSON.stringify(transformObject(searchPara, allowedKeys, mapping))
          : "",
    },
  });

  return (
    <div dir={isRTL ? "rtl" : "ltr"}>
      <AppHeader
        breadcrumbs={[
          {
            name: t("nav.dashboard"),
            href: "/",
          },
          { name: t('nav.shipping-rates'), href: "/shipping-rates" },

        ]}
      />

      <div className='h-[calc(100svh-188px)] p-4'>
        <ShippingRateTable data={records?.data} />
      </div>
    </div>
  )
}
