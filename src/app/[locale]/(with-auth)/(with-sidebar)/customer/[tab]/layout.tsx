import AppHeader from "@/components/header/app-header";
import { localesTypes } from "@/i18n/routing";
import { getTranslations } from "next-intl/server";
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: localesTypes }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "sidebar" });
  return {
    title: t('nav.customer.label'),
  };
}
const CustomerLayout = async ({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: localesTypes }>;
}) => {
  const t = await getTranslations("sidebar");
  const { locale } = await params;
  const isRTL = locale === "ar";
  return (
    <div dir={isRTL ? "rtl" : "ltr"}>
      <AppHeader
        breadcrumbs={[
          {
            name: t("nav.dashboard"),
            href: "/",
          },
          { name: t('nav.customer.label'), href: "/customer/all" },
          {
            name: "All",
            href: "/customer/all",
          },
        ]}
        dropdownItems={[
          { name: t('nav.customer.all'), href: "/customer/all" },
          {
            name: t('nav.customer.enable'),
            href: "/customer/enable",
          },
          {
            name: t('nav.customer.disable'),
            href: "/customer/disable",
          },
          { name: t('nav.customer.trash'), href: "/customer/trash" },
        ]}
      />

      <div className="p-4">{children}</div>
    </div>
  );
};

export default CustomerLayout;
