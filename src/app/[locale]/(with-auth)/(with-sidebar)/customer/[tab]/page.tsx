import CustomerPageClient from "@/components/customers/customer-client";
import { loadCustomerData } from "@/components/customers/services/customer-action";

import React from "react";
type PageProps = {
  params: Promise<any>;
  searchParams: any;
};

const Page = async ({ params, searchParams }: PageProps) => {
  const searchPara = await searchParams;
  const paramsData = await params;
  const baseState = paramsData?.tab !== "all" ? paramsData?.tab : "";
  const records = await loadCustomerData(baseState, {
    page: searchPara?.page || 1,
    per_page: searchPara?.per_page || 20,
    search: searchPara?.search || "",
  });
  return (
    <div className="h-[calc(100svh-188px)]">
      <CustomerPageClient
        initialRecords={records}
        baseState={baseState}
        searchParams={searchPara}
      />
    </div>
  );
};

export default Page;
