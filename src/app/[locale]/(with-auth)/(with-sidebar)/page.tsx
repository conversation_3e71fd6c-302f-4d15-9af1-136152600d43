"use server";
import AppHeader from "@/components/header/app-header";
import { getTranslations } from "next-intl/server";
import { localesTypes } from "@/i18n/routing";
import DashboardClient from "@/components/dashboard/DashboardClient";
import { auth } from "@/lib/auth";
import VehicleSummery from "@/components/dashboard/summeries/vehicle-summery";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { BusFront } from "lucide-react";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: localesTypes }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "sidebar" });
  return {
    title: t("nav.dashboard"),
  };
}

export default async function DashboardPage({
  params,
}: {
  params: Promise<any>;
}) {
  const { locale } = await params; //dashboard
  const t = await getTranslations({ locale, namespace: "sidebar" });
  const t2 = await getTranslations({ locale, namespace: "dashboard" });
  const session = await auth();
  return (
    <>
      <AppHeader
        breadcrumbs={[
          {
            name: t("nav.dashboard"),
            href: "/",
          },
        ]}
      />
      {session?.user_type ? (
        session?.user_type === "customer" ? (
          <DashboardClient />
        ) : (
          <div className="grid p-1">
            <Card className="relative shadow-[0_10px_20px_-5px_rgba(0,0,0,0.07)] hover:shadow-[0_20px_25px_-5px_rgba(0,0,0,0.1),0_10px_10px_-5px_rgba(0,0,0,0.04)] group transition-all duration-500 border-t-3 border-blue-500/80 dark:border-green-700/20 overflow-hidden dark:bg-gradient-to-br dark:from-green-100/5 dark:to-green-100/0 dark:border hover:dark:border-green-500/20">
              {/* Softened gradient overlay on hover - light mode only */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-blue-50/10 to-transparent opacity-0 group-hover:opacity-50 dark:group-hover:opacity-0 transition-opacity duration-500"></div>

              {/* Dark mode subtle gradient - always visible */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-950/3 via-transparent to-transparent opacity-0 dark:opacity-10 transition-opacity duration-500"></div>

              {/* Softened card glow effect on hover */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-300/0 via-blue-300/10 to-blue-300/0 dark:from-green-300/0 dark:via-green-300/5 dark:to-green-300/0 rounded-lg blur opacity-0 group-hover:opacity-50 dark:group-hover:opacity-30 transition duration-500 group-hover:duration-200"></div>

              {/* Background pattern */}
              <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] dark:bg-[radial-gradient(#15803d_1px,transparent_1px)] [background-size:16px_16px] opacity-[0.15] dark:opacity-[0.05]"></div>

              <div className="relative">
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-center justify-between">
                    <span className="text-blue-700 group-hover:text-blue-700 dark:text-white dark:group-hover:text-white transition-colors duration-300 font-bold">
                      {t2("overview.vehicle-summery.title")}
                    </span>
                    <div className="p-2 rounded-full bg-blue-100 dark:bg-green-800/40 group-hover:bg-blue-200 dark:group-hover:bg-green-800/95 transition-colors duration-300 shadow-sm">
                      <BusFront className="h-5 w-5 text-blue-600 dark:text-white" />
                    </div>
                  </CardTitle>
                  <CardDescription className="text-slate-600 group-hover:text-slate-800 dark:text-white/85 dark:group-hover:text-white transition-colors duration-300">
                    {t2("overview.summaries.vehicle.description")}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <VehicleSummery />
                </CardContent>
              </div>
            </Card>
          </div>
        )
      ) : (
        <Card></Card>
      )}
    </>
  );
}
