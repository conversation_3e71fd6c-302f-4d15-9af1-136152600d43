import PaginationComponent from '@/components/Common_UI/use-paginition';
import { CustomerInvoiceTable } from '@/components/customer-invoice/cell-renderers/customer-invoice-table';
import { getCusotmerInvoice } from '@/components/customer-invoice/services/get-customer-invoice';
import AppHeader from '@/components/header/app-header';
import { localesTypes } from '@/i18n/routing';
import { getTranslations } from 'next-intl/server';
import React from 'react'

type PageProps = {
  params: Promise<any>;
  searchParams: any;
};
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: localesTypes }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "customer-invoice" });
  return {
    title: t("title"),
  };
}
export default async function CustomerVehicleInvoice({ params, searchParams }: PageProps) {
  const searchPara = await searchParams;
  const paramsData = await params;
  const t = await getTranslations("sidebar");
  const records = await getCusotmerInvoice({
    params: {
      status: paramsData?.tab ? paramsData?.tab : "",
      page: searchPara?.page || 1,
      per_page: searchPara?.per_page || 20,
      search: searchPara?.search || "",
    },
  });
  return (
    <>
      <AppHeader
        breadcrumbs={[
          {
            name: t("nav.dashboard"),
            href: "/",
          },
          {
            name: t("nav.customer-invoice"),
            href: "/customer_invoice",
          },
        ]}
      />
      <div className="h-[calc(100svh-160px)] p-4">
        <CustomerInvoiceTable records={records} />
        <PaginationComponent
          count={records?.total || 0}
          pageSize={records?.per_page || 0}
        />
      </div>
    </>
  )
}
