import { getTranslations } from "next-intl/server";
import { localesTypes } from "@/i18n/routing";
import { ShippingCalculator } from "@/components/calculator/shipping-calculator";

export async function generateMetadata({
  params,
}: {
  params:Promise<{ locale: localesTypes }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "calculator" });
  return {
    title: t("title"),
  };
}

export default async function CalculatorPage() {
  
  const t = await getTranslations("calculator");
  return (
    <div className="container mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold">{t("title")}</h1>
        <p className="text-sm  mt-1">
          {t("description")}
        </p>
      </div>
      <ShippingCalculator />
    </div>
  );
} 