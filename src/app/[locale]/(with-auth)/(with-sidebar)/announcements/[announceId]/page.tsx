import AnnouncementSingleCard from '@/components/announcements/announcement-single-card';
import { getOneCustomerAnnouncement } from '@/components/announcements/Service/announcements';
import AppHeader from '@/components/header/app-header'
import { Card } from '@/components/ui/card'
import { localesTypes } from '@/i18n/routing';
import { getTranslations } from 'next-intl/server';
import React from 'react'

export default async function Page({ params }: Readonly<{ params: Promise<{ locale: localesTypes, announceId: string }> }>) {
  const { locale, announceId } = await params;
  const isRTL = locale === 'ar';
  const data = await getOneCustomerAnnouncement({ id: Number(announceId) });
  const t = await getTranslations("sidebar");
  return (
    <Card className='overflow-y-auto max-h-screen' dir={isRTL ? 'rtl' : 'ltr'} >
      <AppHeader
        breadcrumbs={[
          {
            name: t("nav.dashboard"),
            href: "/",
          },
          {
            name: t("nav.announcements"),
            href: "/announcements",
          },
        ]}
      />
      <AnnouncementSingleCard data={data?.data} />
    </Card>
  )
}
