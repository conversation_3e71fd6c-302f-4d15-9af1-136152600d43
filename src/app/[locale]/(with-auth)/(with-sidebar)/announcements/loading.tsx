import { AnnouncementSkeleton } from "@/components/announcements/announcements-skeleton"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"


export default function LoadingSkeleton() {
  return (<Card className='min-h-[calc(100svh-66px)]  gird grid-rows-[auto_1fr]  w-full  rounded-sm'>
    <div className="flex justify-between items-center px-6 py-4">
      <Skeleton className="h-8 mt-3 w-64 animate-pulse" />
      <Skeleton className="h-8  w-64 animate-pulse" />
    </div>

    <CardContent className='grid gap-4 md:grid-cols-3 gird-cols-1 sm:grid-cols-2 place-items-center'>

      <AnnouncementSkeleton />
      <AnnouncementSkeleton />
      <AnnouncementSkeleton />
      <AnnouncementSkeleton />
      <AnnouncementSkeleton />
      <AnnouncementSkeleton />

    </CardContent>
  </Card>
  )
}