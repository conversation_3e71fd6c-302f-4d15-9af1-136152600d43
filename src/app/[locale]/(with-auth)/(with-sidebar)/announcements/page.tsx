import Announcement from "@/components/announcements/announcement";
import AppHeader from "@/components/header/app-header";
import { Card } from "@/components/ui/card";
import { localesTypes } from "@/i18n/routing";
import { getTranslations } from "next-intl/server";
import React from "react";

export default async function Page({
  params,
}: Readonly<{ params: Promise<{ locale: localesTypes }> }>) {
  const { locale } = await params;
  const isRTL = locale === "ar";
  const t = await getTranslations("sidebar");
  return (
    <Card
      className="gird grid-rows-[auto_1fr] overflow-y-auto max-h-screen"
      dir={isRTL ? "rtl" : "ltr"}
    >
      <AppHeader
        breadcrumbs={[
          {
            name: t("nav.dashboard"),
            href: "/",
          },
          {
            name: t("nav.announcements"),
            href: "/announcements",
          },
        ]}
      />
      <Announcement />
    </Card>
  );
}
