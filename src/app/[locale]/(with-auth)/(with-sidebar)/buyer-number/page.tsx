import { BuyerNumberDataTable } from "@/components/buyer-number/cell-renderers/buyer-number-data-table";
import { getBuyerNumber } from "@/components/buyer-number/services/buyer-number-service";
import BuyerNumberExport from "@/components/buyer-number/services/export-buyer-number-data";
import InputSearch from "@/components/Common_UI/InputSearch";
import PaginationComponent from "@/components/Common_UI/use-paginition";
import { getTranslations } from "next-intl/server";
import React from "react";

type PageProps = {
  params: any;
  searchParams: any;
};
const BuyerNumberPage = async ({ searchParams }: PageProps) => {
  const searchPara = await searchParams;
  const records = await getBuyerNumber({
    params: {
      page: searchPara?.page || 1,
      per_page: searchPara?.per_page || 20,
      search: searchPara?.search || "",
      exactMatch: false,
      filterData: "",
    },
  });
  const t = await getTranslations("sidebar");
  const t2 = await getTranslations("datatable");
  return (
    <div className="">
      <div className="flex flex-col md:flex-row items-center justify-between mb-4">
        <h2 className="text-3xl font-bold tracking-tight">
          {t("nav.buyer-number")}
        </h2>
        <div className="flex items-center gap-2">
          <BuyerNumberExport records={records} />
          <InputSearch
            fieldName="search"
            type="text"
            placeholder={t2("sidebar.search")}
          />
        </div>
      </div>
      <div className="h-[calc(100svh-188px)]">
        <BuyerNumberDataTable records={records} />
        <PaginationComponent
          count={records?.total || 0}
          pageSize={records?.per_page || 0}
        />
      </div>
    </div>
  );
};

export default BuyerNumberPage;
