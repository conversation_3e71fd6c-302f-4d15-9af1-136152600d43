import InputSearch from "@/components/Common_UI/InputSearch";
import PaymentClient from "@/components/payments/payment-client";
import { PageProps } from "@/components/payments/services/config";
import ExportPaymentData from "@/components/payments/services/export-payment-data";
import { loadPaymentData } from "@/components/payments/services/payment-action";
import { getTranslations } from "next-intl/server";
import React from "react";

const Page = async ({ params, searchParams }: PageProps) => {
  const params2 = await params;
  const searchPara = await searchParams;
  const baseState = params2?.tab !== "all" ? params2?.tab : "";
  const records = await loadPaymentData(baseState, searchPara, {
    page: searchPara?.page || 1,
    per_page: searchPara?.per_page || 20,
    search: searchPara?.search || "",
  });

  const t = await getTranslations("sidebar");
  const t2 = await getTranslations("datatable");
  return (
    <div className="">
      <div className="flex flex-col md:flex-row items-center justify-between mb-4">
        <h2 className="hidden md:text-2xl lg:text-3xl font-bold tracking-tight md:block">
          {t("nav.payments.label")}
        </h2>
        <div className="flex items-center gap-2">
          <ExportPaymentData records={records} />
          <InputSearch
            type="text"
            fieldName="search"
            placeholder={t2("sidebar.search")}
          />
        </div>
      </div>
      <div className="">
        <div className="h-[calc(100vh-200px)] w-[calc(100vw-32px)] sm:w-[calc(100vw-32px)] md:w-[calc(100vw-32px)] lg:w-full xl:w-full relative">
          <PaymentClient
            initialRecords={records}
            baseState={baseState}
            searchParams={searchPara}
          />
        </div>
      </div>
    </div>
  );
};

export default Page;
