import AppHeader from "@/components/header/app-header";
import { localesTypes } from "@/i18n/routing";
import { getTranslations } from "next-intl/server";
export async function generateMetadata({
  params,
}: {
  params:Promise<{ locale: localesTypes }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "sidebar" });
  return {
    title: t("nav.payments.label"),
  };
}
const PaymentsLayout = async ({
  children,
  params,
}: {
  children: React.ReactNode;
  params:Promise<{ locale: localesTypes }>;
}) => {
  const t = await getTranslations("sidebar");
  const { locale } = await params;
  const isRTL = locale === "ar";
  return (
    <div dir={isRTL ? "rtl" : "ltr"}>
      <AppHeader
        breadcrumbs={[
          {
            name: t("nav.dashboard"),
            href: "/",
          },
          {
            name: t("nav.payments.label"),
            href: "/payments/all",
          },
          {
            name: t("nav.payments.freight-payments.label"),
            href: "/payments/all",
          },
        ]}
        dropdownItems={[
          {
            name: t("nav.payments.freight-payments.all"),
            href: "/payments/all",
          },
          {
            name: t("nav.payments.freight-payments.approved"),
            href: "/payments/approved",
          },
          {
            name: t("nav.payments.freight-payments.pending"),
            href: "/payments/pending",
          },
        ]}
      />

      <div className="p-4">{children}</div>
    </div>
  );
};

export default PaymentsLayout;
