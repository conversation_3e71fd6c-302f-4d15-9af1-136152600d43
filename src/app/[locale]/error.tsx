"use client";

import { Button } from "@/components/ui/button";
import { useProgress } from "@bprogress/next";
import { useTranslations } from "next-intl";
import { useParams, useRouter } from "next/navigation";

const ServerError = () => {
  const t = useTranslations("server_error");
  const params = useParams();
  const { start } = useProgress();
  const router = useRouter();

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background text-foreground">
      <div className="flex min-h-screen w-full flex-col items-center justify-center bg-background text-foreground">
        <div className="relative text-center">
          <h1 className="absolute left-1/2 top-1/4 z-10 mb-8 -translate-x-1/2 -translate-y-1/2 select-none text-[clamp(16px,80vw,500px)] font-extrabold text-primary opacity-10">
            500
          </h1>
          <div className="relative z-20 mt-32 max-w-[500px]">
            <h2 className="mb-6 text-4xl font-extrabold">{t("title")}</h2>
            <p className="mx-auto mb-2 max-w-md text-lg text-muted-foreground">
              {t("header")}
            </p>
            <p className="mx-auto mb-8 max-w-md text-lg text-muted-foreground">
              {t("description")}
            </p>
            <Button
              size="lg"
              className="font-semibold"
              onClick={() => {
                // if (callback) {
                start();
                router.push(`/${params.locale}`);
                // reset();
                // } else {
                //   window.location.reload();
                // }
              }}
            >
              {t("button")}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServerError;
