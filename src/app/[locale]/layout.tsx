import { NextIntlClientProvider } from "next-intl";
import { redirect } from "next/navigation";
import { localesTypes, routing } from "@/i18n/routing";
import { getMessages } from "next-intl/server";
import AppDirectionProvider from "@/components/direction-provider/direction-provider";
import { notoSansArabic, inter } from "@/lib/Fonts";
import { getTranslations } from "next-intl/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { Metadata } from "next";

// List of allowed custom domains
const allowedCustomDomains = ["bossmotorsgroup.com", "alargansystem.com"];

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: localesTypes }>;
}): Promise<Metadata> {
  const reqHeaders = await headers();
  const session = await auth();
  const host = (reqHeaders.get("host") || "").split(":")[0];
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: "sidebar" });

  const name = session?.profile.company
    ? session.profile.company.profile_name
    : t("title");

  // Determine customer based on full domain match
  let customer = "default";

  // Check if the entire host is in the allowedCustomDomains list
  if (allowedCustomDomains.includes(host)) {
    customer = host;
  }

  const faviconUrl = `/favicons/${customer}.ico`;

  if (customer === "default") {
    return {
      title: { template: `%s / ${name}`, default: name },
    };
  }

  return {
    icons: {
      icon: faviconUrl,
      apple: faviconUrl,
      shortcut: faviconUrl,
    },
    title: customer,
  };
}

export default async function LocaleLayout({
  children,
  params,
}: Readonly<{
  children: React.ReactNode;
  params: Promise<{ locale: localesTypes }>;
}>) {
  const { locale } = await params;

  // Ensure that the incoming `locale` is valid
  if (!routing.locales.includes(locale as localesTypes)) {
    redirect(routing.defaultLocale);
  }

  const isRTL = locale === "ar";
  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      <AppDirectionProvider>
        <div
          className={`${inter.className} antialiased ${
            isRTL ? notoSansArabic.className : ""
          }`}
        >
          {children}
        </div>
      </AppDirectionProvider>
    </NextIntlClientProvider>
  );
}
