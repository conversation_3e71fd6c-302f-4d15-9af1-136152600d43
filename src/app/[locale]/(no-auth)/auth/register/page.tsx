import { getTranslations, getMessages } from 'next-intl/server'
import { NextIntlClientProvider } from 'next-intl'
import RegisterStepperForm from '@/components/register/RegisterStepperForm'

export default async function Page() {
  const messages = await getMessages()
  const t = await getTranslations('Register')

const steps = [
    { title: t('steps.basic_information'), icon: "User" },
    { title: t('steps.additional_information'), icon: "FileText" },
    { title: t('steps.consignee_information'), icon: "Users" },
    { title: t('steps.notify_party'), icon: "UserPlus" },
    { title: t('steps.load_volume'), icon: "Package" },
    { title: t('steps.registration'), icon: "FileCheck" },
    { title: t('steps.contract'), icon: "FileSignature" },
  ]

  return (
    <NextIntlClientProvider messages={messages}>
      <RegisterStepperForm initialSteps={steps} />
    </NextIntlClientProvider>
  )
}

