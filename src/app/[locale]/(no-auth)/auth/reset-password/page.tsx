"use client";
import { useEffect, useState } from "react";
import { useSearch<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { useDirection } from "@/hooks/useDirection";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";

import {
  Loader2,
  Eye,
  EyeOff,
  LockKeyhole,
  AlertTriangle,
  CheckCircle,
} from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useFetchClient } from "@/utils/axios";

// Define form validation schema
const resetPasswordSchema = z
  .object({
    newPassword: z.string().min(8, "Password must be at least 8 characters"),
    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

export default function ResetPassword() {
  const t = useTranslations("reset-password");
  const { dir } = useDirection();
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get("token");
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [tokenValid, setTokenValid] = useState(true);
  const [isValidating, setIsValidating] = useState(true);
  const [passwordStrength, setPasswordStrength] = useState({
    strength: 0,
    text: "",
    color: "",
  });
  const fetchClient = useFetchClient();

  const form = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      newPassword: "",
      confirmPassword: "",
    },
  });

  const watchPassword = form.watch("newPassword");
  const watchConfirmPassword = form.watch("confirmPassword");
  const passwordsMatch =
    watchPassword &&
    watchConfirmPassword &&
    watchPassword === watchConfirmPassword;

  useEffect(() => {
    if (token) {
      validateToken();
    } else {
      setTokenValid(false);
      setIsValidating(false);
    }
  }, [token]);

  useEffect(() => {
    if (watchPassword) {
      setPasswordStrength(getPasswordStrength(watchPassword));
    }
  }, [watchPassword]);

  const validateToken = async () => {
    if (!token) return;

    try {
      setIsValidating(true);
      const response = await fetchClient(
        `/v2/auth/validate-reset-token/${token}`
      );

      setTokenValid(response.data.valid);
      if (!response.data.valid) {
        toast.error("This password reset link has expired.");
      }
    } catch (error) {
      setTokenValid(false);

      toast.error(
        "This password reset link is invalid or has expired." + error
      );
    } finally {
      setIsValidating(false);
    }
  };

  const getPasswordStrength = (password: string) => {
    if (!password) return { strength: 0, text: "", color: "" };

    const hasLowerCase = /[a-z]/.test(password);
    const hasUpperCase = /[A-Z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChars = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const length = password.length;

    let strength = 0;
    if (length >= 8) strength += 1;
    if (hasLowerCase && hasUpperCase) strength += 1;
    if (hasNumbers) strength += 1;
    if (hasSpecialChars) strength += 1;

    const strengthMap = [
      { text: "Weak", color: "#f44336" },
      { text: "Fair", color: "#ff9800" },
      { text: "Good", color: "#2196f3" },
      { text: "Strong", color: "#4caf50" },
      { text: "Very Strong", color: "var(--primary)" },
    ];

    return {
      strength,
      text: strengthMap[strength].text,
      color: strengthMap[strength].color,
    };
  };

  async function onSubmit(data: ResetPasswordFormValues) {
    if (!token) {
      toast.error("Invalid reset link.");
      return;
    }

    setIsLoading(true);
    try {
      await fetchClient(`/v2/auth/reset-password`, {
        method: "POST",
        data: {
          token,
          newPassword: data.newPassword,
        },
      });

      toast.success("Password has been reset successfully!");

      // Get the locale from URL or use default
      const locale = "en"; // Replace with your locale detection logic if needed
      router.push(`/${locale}/auth/login`);
    } catch (error: any) {
      toast.error(
        error?.response?.data?.message ||
          "An error occurred while resetting your password."
      );
    } finally {
      setIsLoading(false);
    }
  }

  // If the token is invalid or expired
  if (!isValidating && !tokenValid) {
    return (
      <div
        className="flex items-center justify-center min-h-screen p-4"
        dir={dir}
      >
        <Card className="w-full max-w-md overflow-hidden rounded-2xl shadow-xl border-0 dark:bg-gray-800/95 dark:backdrop-blur-sm">
          <CardContent className="p-6 text-center">
            <div className="mx-auto bg-red-100 p-4 rounded-full w-16 h-16 flex items-center justify-center mb-4">
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
            <h2 className="text-xl font-semibold text-red-500 mb-2">
              {t("link-expired.title")}
            </h2>
            <p className="text-muted-foreground mb-6">
              {t("link-expired.message")}
            </p>
            <Button
              className="w-full bg-primary hover:bg-primary/90"
              onClick={() => router.push("/auth/login")}
            >
              {t("link-expired.returnButton")}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show loading state while validating token
  if (isValidating) {
    return (
      <div
        className="flex items-center justify-center min-h-screen p-4"
        dir={dir}
      >
        <Card className="w-full max-w-md overflow-hidden rounded-2xl shadow-xl border-0 dark:bg-gray-800/95 dark:backdrop-blur-sm">
          <CardContent className="p-6 text-center">
            <div className="flex flex-col items-center justify-center py-8 gap-4">
              <Loader2 className="h-8 w-8 text-primary animate-spin" />
              <p className="text-muted-foreground">
                {t("validating") || "Validating your reset link..."}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // The main reset password form
  return (
    <div
      className="flex items-center justify-center min-h-screen p-4"
      dir={dir}
    >
      <Card
        className="w-full max-w-md overflow-hidden rounded-2xl shadow-xl border-0 dark:bg-gray-800/95 dark:backdrop-blur-sm"
        style={{ borderTop: "4px solid var(--primary)" }}
      >
        <CardContent className="p-6">
          <div className="flex flex-col items-center mb-6">
            <div className="mx-auto bg-primary/10 p-4 rounded-full mb-4">
              <LockKeyhole className="h-8 w-8 text-primary" />
            </div>
            <h2 className="text-xl font-semibold text-center">{t("title")}</h2>
            <p className="text-sm text-muted-foreground text-center mt-1">
              {t("description")}
            </p>
          </div>

          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="newPassword" className="text-sm font-medium">
                {t("passwordLabel")}
              </Label>
              <div className="relative">
                <Input
                  id="newPassword"
                  type={showNewPassword ? "text" : "password"}
                  className="pl-4 h-12 rounded-lg pr-10 focus-visible:ring-2 focus-visible:ring-primary/50 focus-visible:ring-offset-0"
                  {...form.register("newPassword")}
                  disabled={isLoading}
                  placeholder={t("passwordPlaceholder")}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                >
                  {showNewPassword ? (
                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  )}
                </Button>
              </div>
              {form.formState.errors.newPassword && (
                <p className="text-xs text-red-500 mt-1">
                  {form.formState.errors.newPassword.message}
                </p>
              )}
            </div>

            {watchPassword && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Progress
                    value={(passwordStrength.strength + 1) * 20}
                    className="h-1"
                    style={
                      {
                        backgroundColor: "#e0e0e0",
                        "--progress-background": passwordStrength.color,
                      } as any
                    }
                  />
                  <span
                    className="text-xs"
                    style={{ color: passwordStrength.color }}
                  >
                    {passwordStrength.text}
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">
                  {t("passwordRequirements")}
                </p>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-sm font-medium">
                {t("confirmPasswordLabel")}
              </Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  className="pl-4 h-12 rounded-lg pr-10 focus-visible:ring-2 focus-visible:ring-primary/50 focus-visible:ring-offset-0"
                  {...form.register("confirmPassword")}
                  disabled={isLoading}
                  placeholder={t("confirmPasswordPlaceholder")}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 p-0"
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  )}
                </Button>
              </div>
              {form.formState.errors.confirmPassword && (
                <p className="text-xs text-red-500 mt-1">
                  {form.formState.errors.confirmPassword.message}
                </p>
              )}
            </div>

            {passwordsMatch && (
              <Alert className="bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-300 border-green-100 dark:border-green-900/30">
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>{t("passwordsMatch")}</AlertDescription>
              </Alert>
            )}

            <Button
              type="submit"
              className="w-full h-12 bg-primary hover:bg-primary/90 text-white rounded-lg font-medium text-base transition-colors mt-4"
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              ) : (
                t("submitButton") || "Reset Password"
              )}
            </Button>

            <div className="text-center mt-6">
              <p className="text-sm text-muted-foreground">
                {t("rememberPassword")}
                <Button
                  variant="link"
                  className="p-0 h-auto text-primary hover:underline font-medium"
                  onClick={() => router.push("/auth/login")}
                >
                  {t("signIn") || "Sign in"}
                </Button>
              </p>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
