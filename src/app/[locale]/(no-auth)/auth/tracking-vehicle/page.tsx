import { Suspense } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import VehicleTrackingPage from "@/components/tracking-page/client-page";
import getTrackingData from "@/components/tracking-page/service";
import { Link } from "@/i18n/routing";
import { ArrowLeft } from "lucide-react";
import type { VehicleData } from "@/components/tracking-page/types";
import { getTranslations } from "next-intl/server";
import DarkModeToggle from "@/components/Common_UI/DarkModeToggle";
import Language from "@/components/localization/langauge";
import { headers } from "next/headers";

interface SearchParams {
  search?: string;
}

interface PageProps {
  params: Promise<{
    locale: string;
  }>;
  searchParams: Promise<SearchParams>;
}

export default async function TrackingPage({
  params,
  searchParams,
}: PageProps) {
  const { locale } = await params;
  const { search } = await searchParams;

  const searchValue = search || "";
  let initialData: VehicleData | null = null;
  let error = "";

  const header = await headers();
  const domainData: any = {};

  const host = (header.get("host") || "").split(":")[0];
  if (host === "bossmotorsgroup.com") {
    domainData.logo = "/favicons/bossmotorsgroup.com.ico";
    domainData.customer_id = 860;
  } else if (host === "alargansystem.com") {
    domainData.logo = "/alargan.jpg";
    domainData.customer_id = 868;
  }

  if (searchValue) {
    try {
      const response = await getTrackingData(
        searchValue,
        domainData?.customer_id
      );
      if (response?.data?.length) {
        initialData = response.data[0];
      } else {
        error = "Vehicle not found. Please check your tracking number.";
      }
    } catch (err: any) {
      error =
        err.message || "An error occurred while searching for the vehicle.";
    }
  }

  const isRTL = locale === "ar";
  const t = await getTranslations("vehicle-tracking");

  return (
    <div className="min-h-screen relative">
      <Suspense
        fallback={
          <div className="container mx-auto px-4 py-8">
            <Card className="max-w-2xl mx-auto">
              <CardContent className="p-4 sm:p-6 lg:p-8">
                <Skeleton className="h-6 sm:h-8 w-3/4 mb-3 sm:mb-4" />
                <Skeleton className="h-4 sm:h-6 w-1/2 mb-6 sm:mb-8" />
                <div className="space-y-3 sm:space-y-4">
                  <Skeleton className="h-10 sm:h-12 w-full" />
                  <Skeleton className="h-3 sm:h-4 w-1/3" />
                </div>
              </CardContent>
            </Card>
          </div>
        }
      >
        {/* Header Controls */}
        <div
          className={`absolute top-4 z-10 ${
            isRTL ? "left-4 right-auto" : "right-4 left-auto"
          } flex  sm:flex-row items-start sm:items-center gap-2 sm:gap-4`}
        >
          <Link
            href="/auth/signin"
            className="text-primary flex items-center gap-2 border border-primary/50 px-3 sm:px-4 py-2 sm:py-1 rounded-lg hover:border-primary hover:shadow-md transition-all duration-200 text-sm sm:text-base min-w-0 whitespace-nowrap"
          >
            <ArrowLeft
              size={14}
              className={`${isRTL ? "ml-1 sm:ml-2" : "mr-1 sm:mr-2"}`}
            />
            <span className="hidden sm:inline">{t("sign-in")}</span>
            <span className="sm:hidden">Sign In</span>
          </Link>

          {/* Controls Container */}
          <div className="flex items-center gap-2 sm:gap-3">
            <DarkModeToggle />
            <Language className="md:w-[140px] w-10" />
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-16 sm:py-20 md:py-24">
          <VehicleTrackingPage
            initialData={initialData}
            initialSearch={searchValue}
            initialError={error}
            domainData={domainData}
          />
        </div>
      </Suspense>
    </div>
  );
}
