// app/[locale]/auth/direct-login/page.tsx
'use client';

import { useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { signIn } from 'next-auth/react';
import { useProgress } from '@bprogress/next';
import { toast } from 'sonner';

export default function DirectLoginPage(props: any & { callbackUrl?: string }) {
  const router = useRouter();
  const { start } = useProgress();
  const { locale } = useParams();
  const searchParams = useSearchParams();

  useEffect(() => {
    const handleDirectLogin = async () => {
      try {
        const customer_id = searchParams.get('id');
        const token = searchParams.get('token');
        const result = await signIn('credentials', {
          username: `${customer_id}`,
          adminPassword: `token-${token}`,
          redirect: false,
        });
        if (result && (!result.ok || result.error)) {
          const loginError =
            result.error === "CredentialsSignin"
              ? "Wrong password or username"
              : result.error;
          toast.error(loginError as string);
          return;
        }
        start();
        router.push(props.callbackUrl ? props.callbackUrl : `/${locale}`);
      } catch (error) {
        toast.error(String(error));
      }
    };
    handleDirectLogin();
  }, [searchParams, router,]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Logging you in...</h1>
        <p>Please wait while we authenticate your session.</p>
      </div>
    </div>
  );
}