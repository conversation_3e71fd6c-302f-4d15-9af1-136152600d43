import DarkModeToggle from "@/components/Common_UI/DarkModeToggle";
import Language from "@/components/localization/langauge";
import {
  CustomLoginForm,
  LoginFormConfig,
} from "@/components/login/custom-login-form";
import { LoginForm } from "@/components/login/login-form";
import { headers } from "next/headers";
import { JSX } from "react";

type PageProps = {
  params: Promise<any>;
};

const domainConfigs: Record<string, LoginFormConfig> = {
  "bossmotorsgroup.com": {
    logo: {
      src: "/favicons/bossmotorsgroup.com.ico",
      alt: "Boss Motors Group Logo",
      width: 48,
      height: 48,
    },
    company: {
      title: "Boss Motors Group",
      description: "Your trusted automotive partner",
    },
    backgroundImage: {
      src: "/bossmotorsgroup.jpg",
      alt: "Boss Motors Group showcase",
      priority: true,
      silder: true,
      images: [
        "/login-images/boss-login-1.png",
        "/login-images/boss-login-2.png",
        "/login-images/boss-login-3.png",
      ],
    },
    auth: {
      showTrackingLink: true,
      showRegisterLink: false,
    },
    customer_id: 860,
  },

  "alargansystem.com": {
    logo: {
      src: "/alargan.jpg",
      alt: "Alargan System Logo",
      width: 48,
      height: 48,
    },
    company: {
      title: "Alargan Auto System",
    },
    backgroundImage: {
      src: "/alarganautoshipping.png",
      alt: "Alargan System office",
      priority: true,
      silder: false,
      images: [],
    },
    auth: {
      showTrackingLink: true,
      showRegisterLink: false,
    },
    customer_id: 868,
  },
};

const domainComponents: Record<
  string,
  {
    component: "custom" | "default";
    config?: LoginFormConfig;
  }
> = {
  "bossmotorsgroup.com": {
    component: "custom",
    config: domainConfigs["bossmotorsgroup.com"],
  },
  "alargansystem.com": {
    component: "custom",
    config: domainConfigs["alargansystem.com"],
  },
};

const Signin = async ({ params }: PageProps) => {
  const { locale } = await params;
  const isRTL = locale === "ar";

  const reqHeaders = await headers();
  const host = (reqHeaders.get("host") || "").split(":")[0];

  // Default to standard login form
  let loginComponent: JSX.Element = <LoginForm />;

  // Check if the host has a custom configuration
  const domainConfig = domainComponents[host];
  if (domainConfig) {
    if (domainConfig.component === "custom") {
      loginComponent = <CustomLoginForm config={domainConfig.config} />;
    } else {
      loginComponent = <LoginForm />;
    }
  }

  return (
    <div className="flex min-h-svh flex-col items-center justify-center bg-muted relative">
      <div
        className={`flex justify-center items-center gap-4 absolute md:top-4 md:right-10 top-4 ${
          isRTL ? "right-auto left-4" : "left-auto right-4"
        }`}
      >
        <DarkModeToggle />
        <Language className="md:w-[140px] w-10" />
      </div>
      <div className="w-full max-w-xl xl:max-w-[1268px]">{loginComponent}</div>
    </div>
  );
};

export default Signin;
