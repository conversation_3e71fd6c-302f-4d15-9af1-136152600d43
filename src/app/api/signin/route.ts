import axios from "@/utils/axios-server";
import { cookies } from "next/headers";
import { NextRequest } from "next/server";

export async function POST(request: NextRequest) {
  const { email_username, password } = await request.json();
  const cookiesObj = await cookies();
  try {
    const res = await axios.post("/v2/auth/login", {
      email_username,
      password,
    });
    const cookies = res.headers["set-cookie"];

    cookies?.forEach((cookie) => {
      cookiesObj.set(cookie.split("=")[0], cookie.split("=")[1].split(";")[0]);
    });

    return Response.json(res.data);
  } catch (error: any) {
    if (error.response) {
      // Axios error with response
      return Response.json(error.message, {
        status: error.response.status,
        statusText: error.response.statusText,
      });
    } else if (error.request) {
      // Network error
      return Response.json("Network error occurred", {
        status: 500,
        statusText: "Network Error",
      });
    } else {
      // Other errors
      return Response.json(error.message || "An error occurred", {
        status: 500,
        statusText: "Internal Server Error",
      });
    }
  }
}
