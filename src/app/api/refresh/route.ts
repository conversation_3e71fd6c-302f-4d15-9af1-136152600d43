import axios from "@/utils/axios-server";
import { cookies } from "next/headers";

export async function POST() {
  const cookiesObj = await cookies();
  try {
    const refreshToken = cookiesObj.get("refresh_token")?.value;
    const res = await axios.post(
      `/v2/auth/refresh`,
      {},
      {
        headers: {
          Cookie: `refresh_token=${refreshToken}`,
        },
      }
    );

    const cookies = res.headers["set-cookie"];

    cookies?.forEach((cookie) => {
      cookiesObj.set(cookie.split("=")[0], cookie.split("=")[1].split(";")[0]);
    });

    return Response.json({
      result: true,
      access_token: res.data.access_token,
      refresh_token: res.data.refresh_token,
      access_token_expiry: res.data.access_token_expiry,
    });
  } catch (error) {
    return Response.json({ result: false, error });
  }
}
