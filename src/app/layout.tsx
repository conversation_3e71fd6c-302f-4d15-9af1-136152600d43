import "./globals.css";
import { ThemeProvider } from "next-themes";
import ReactQueryContext from "@/context/react-query-context";
import { Toaster } from "@/components/ui/sonner";
import AppProgressProviders from "@/components/header/app-progress-bar";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html suppressHydrationWarning>
      <body>
        <ReactQueryContext>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <AppProgressProviders>{children}</AppProgressProviders>
            <Toaster />
          </ThemeProvider>
        </ReactQueryContext>
      </body>
    </html>
  );
}
