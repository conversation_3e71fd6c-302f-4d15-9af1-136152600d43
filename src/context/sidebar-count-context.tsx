"use client";
import { useFetchClient } from "@/utils/axios";
import { useSession } from "next-auth/react";
import {
  useState,
  ReactNode,
  createContext,
  useContext,
  useEffect,
} from "react";

type SidebarCountContext = {
  sidebarCounts: any;
  getSidebarCounts: (param: { key: string }) => void;
  revalidateSidebarCounts: () => void;
  revalidateLoading: boolean;
  fetchableCount: string[],
  markReadAnnouncements: () => void
};

export const SidebarCountContext = createContext({} as SidebarCountContext);

export const useSidebarCountContext = () => useContext(SidebarCountContext);

type Props = {
  children: ReactNode;
};

export function SidebarCountProvider({ children }: Props) {
  const fetchableCount = [
    "vehicles",
    "containers",
    "inventory_pod",
    "inventory_pol",
    "invoices",
    "mix_shipping",
    "all",
  ];
  const [sidebarCounts, setSidebarCounts] = useState({});
  const [revalidateLoading, setRevalidateLoading] = useState(false);
  const [loadedSidebars, setLoadedsidebars] = useState<string[]>([]);
  const fetchClient = useFetchClient();

  const session = useSession();

  const type = session.data?.profile?.loginable?.loginable_type;

  const getSidebarCounts = async (
    data: { key: string },
    ignoreLoaded = false
  ) => {
    try {
      if (
        (!loadedSidebars.includes(data.key) || ignoreLoaded) &&
        fetchableCount.includes(data.key)
      ) {
        if (type) {
          const id = localStorage.getItem("userKey");
          setLoadedsidebars((prev) =>
            !prev.includes(data.key) ? [...prev, data.key] : prev
          );
          const response = await fetchClient(`/v2/statistics/sidebarCounts`, {
            params: { userKey: id, ...data },
          });
          setSidebarCounts((prev) => {
            return { ...prev, ...response.data };
          });
        }
      }
    } catch (error: any) {
      throw error?.message;
    }
  };

  const revalidateSidebarCounts = async () => {
    if (!revalidateLoading) {
      try {
        setRevalidateLoading(true);
        const [response, res] = await Promise.all([
          fetchClient(
            "/v2/statistics/revalidateSidebarCounts",
            {
              method: "POST",
            }
          ),
          fetchClient(`/v2/auth/profile`)
        ])
        if (response.data.result) {
          setSidebarCounts({});
          await Promise.all(
            loadedSidebars.map(async (item) => {
              return await getSidebarCounts({ key: item }, true);
            })
          );
        }
        setRevalidateLoading(false);
        if (res.data.result) {
          if (
            JSON.stringify(session.data?.profile) !==
            JSON.stringify(res.data.data)
          ) {
            await session.update?.({
              profile: res.data.data,
              profileupdating: true,
            });
          }
        }
      } catch (error: any) {
        console.error(error?.message);
        setRevalidateLoading(false);
        throw error?.message;
      }
    }
  };
  const markReadAnnouncements = async () => {
    const res = await fetchClient(`/v2/announcements/`, { method: "PATCH" });
    if (res.data === true) {
      setSidebarCounts(prev => {
        return { ...prev, announcements: 0 }
      })
    }
  }
  useEffect(() => {
    if (type) {
      getSidebarCounts({ key: "all" });
    }
  }, [type]);

  return (
    <SidebarCountContext.Provider
      value={{
        sidebarCounts,
        getSidebarCounts,
        revalidateSidebarCounts,
        revalidateLoading,
        fetchableCount,
        markReadAnnouncements
      }}
    >
      {children}
    </SidebarCountContext.Provider>
  );
}
