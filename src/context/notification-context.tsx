"use client"
import useFcmToken from "@/hooks/useFcmToken";
import React, { createContext, use, useState } from "react";
type tabsType = "announcement" | "shipping_rate" | "mix_shipping_rate" | "arrival_notice" | "transaction";
export interface NotificationContextType {
  announcement: number,
  arrival_notice: number,
  transaction: number,
  isSave: boolean
};
type NotificationContextProps = {
  notifications: boolean;
  setNotifications: (value: boolean) => void;
  tabValue: tabsType;
  setTabValue: (value: tabsType) => void;
  totalNotification: NotificationContextType;
  setTotalNotification: (value: NotificationContextType) => NotificationContextType | void;
  notificationPermissionStatus: string | null;
  token: string | null;
};
export const NotificationContext = createContext<NotificationContextProps>({
  notifications: false,
  setNotifications: () => { },
  tabValue: 'announcement',
  setTabValue: () => { },
  totalNotification: {
    announcement: 0,
    arrival_notice: 0,
    transaction: 0,
    isSave: false
  },
  setTotalNotification: () => { },
  notificationPermissionStatus: "",
  token: ''
});

const NotificationProvider = ({ children }: { children: React.ReactNode }) => {
  const [notifications, setNotifications] = useState<boolean>(false);
  const [tabValue, setTabValue] = useState<tabsType>("announcement");
  const [totalNotification, setTotalNotification] = useState<NotificationContextType>({
    announcement: 0,
    arrival_notice: 0,
    transaction: 0,
    isSave: false
  });
  const { notificationPermissionStatus, token } = useFcmToken()

  return (
    <NotificationContext.Provider value={{ notifications, setNotifications, setTabValue, tabValue, totalNotification, setTotalNotification,notificationPermissionStatus,token }}>
      {children}
    </NotificationContext.Provider>
  );
};

const useNotification = () => {
  const context = use(NotificationContext);
  if (!context) {
    throw new Error("useNotification must be used within a NotificationProvider");
  }

  return context;
};
export { useNotification };

export default NotificationProvider;