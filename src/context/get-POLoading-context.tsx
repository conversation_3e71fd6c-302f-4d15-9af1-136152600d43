"use client";
import { useFetchClient } from "@/utils/axios";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useContext, createContext, ReactNode, JSX } from "react";

type LocationsProviderProps = {
  children: ReactNode;
};

type LocationsContextValue = {
  map(
    arg0: (item: any) => {
      name: any;
      id: number;
      key: any;
      icon: JSX.Element;
      to: string;
    }
  ): ConcatArray<{
    name: string;
    id: number;
    key: string;
    to: string;
    icon: JSX.Element;
  }>;
  refreshLocations: () => void;
  isLoadingLocations: boolean;
};

export const contextProvider = createContext<LocationsContextValue>({
  map: () => [],
  refreshLocations: () => {},
  isLoadingLocations: true,
});

const LocationsProvider = ({ children }: LocationsProviderProps) => {
  const fetchClient = useFetchClient();

  const { data, isLoading } = useQuery({
    queryKey: ["point-of-loading"],
    queryFn: async () => {
      const response = await fetchClient("/v2/vehicles/getPointOfLoading");
      return response.data;
    },
  });
  const queryClient = useQueryClient();

  const refreshLocations = () => {
    queryClient.invalidateQueries({
      refetchType: "active",
      queryKey: ["point-of-loading"],
    });
  };

  return (
    <contextProvider.Provider
      value={{
        refreshLocations: refreshLocations,
        map: (callback) => data?.data?.map(callback),
        isLoadingLocations: isLoading,
      }}
    >
      {children}
    </contextProvider.Provider>
  );
};

export default LocationsProvider;
export const useLocationContext = () => useContext(contextProvider);
