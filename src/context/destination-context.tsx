"use client";
import { useFetchClient } from "@/utils/axios";
import {
  useContext,
  createContext,
  ReactNode,
  useState,
  useEffect,
  ReactElement,
} from "react";

type DestinationsProviderProps = {
  children: ReactNode;
};

type DestinationItem = {
  name: string;
  id: number;
  key: string;
  to: string;
  icon: ReactElement;
};

type DestinationsContextValue = {
  map_destination: (
    callback: (item: any) => DestinationItem
  ) => DestinationItem[];
  refreshDestinations: () => void;
  isLoadingDestinations: boolean;
};

export const DestinationsContext = createContext<DestinationsContextValue>({
  map_destination: () => [],
  refreshDestinations: () => {},
  isLoadingDestinations: true,
});

const DestinationsProvider = ({ children }: DestinationsProviderProps) => {
  const [dataTem, setDataTem] = useState<any[]>([]);
  const [refreshCounter, setRefreshCounter] = useState(0);
  const [isLoadingDestinations, setIsLoadingDestinations] = useState(true);
  const fetchClient = useFetchClient();

  const getDestinations = async () => {
    setIsLoadingDestinations(true);
    const token = localStorage.getItem("token");

    if (token) {
      try {
        const res = await fetchClient("customer/locations/getDestination");
        if (res.status === 200) {
          setDataTem(res.data?.data || []);
        }
      } catch (error: any) {
        throw error;
      } finally {
        setIsLoadingDestinations(false);
      }
    }
  };

  const refreshDestinations = () => {
    setRefreshCounter((prevCounter) => prevCounter + 1);
  };

  useEffect(() => {
    getDestinations();
  }, [refreshCounter]);

  return (
    <DestinationsContext.Provider
      value={{
        refreshDestinations,
        map_destination: (callback) => dataTem.map(callback),
        isLoadingDestinations,
      }}
    >
      {children}
    </DestinationsContext.Provider>
  );
};

export default DestinationsProvider;
export const useDestinationContext = () => useContext(DestinationsContext);
