import NextAuth from "next-auth";

type companyType = {
  name: string;
  show_shipping_rate: boolean;
  id: number;
  mix: boolean;
  complete: boolean;
  mix_halfcut: boolean;
  complete_halfcut: boolean;
  destination_id: number;
  destinations: object;
  is_special_rate: boolean;
  is_mix_sis_mix_special_rate: boolean;
  has_customer: boolean;
  has_customer_invoice: boolean;
  logo: string;
  profile_name: string;
  code: true;
  _count: {
    buyer_numbers: number;
    invoices: number;
  };
};
export interface UserObject {
  id: number;
  fullname: string;
  address: string | null;
  phone: string | null;
  secondary_email: string | null;
  secondary_phone: string | null;
  gender: string;
  photo: string;
  mix_shipping_status: boolean;
  has_invoices: boolean;
  loginable: {
    id: number;
    email: string;
    username: string;
    loginable_type: string;
  };
  companies: companyType;
  company: companyType;
  sessionId: number;
}
declare module "next-auth" {
  interface Session {
    profile: UserObject;
    backendTokens: {
      accessToken: string;
      refreshToken?: string;
      expiresIn?: number;
    };
    user_type: string;
    sessionId: number;
  }
}

import { JWT } from "next-auth/jwt";

declare module "next-auth/jwt" {
  interface JWT {
    profile: UserObject;
    backendTokens: {
      accessToken: string;
      refreshToken: string;
      expiresIn: number;
    };
    user_type: string;
    sessionId: number;
  }
}
