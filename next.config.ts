import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig: any = {
  output: "standalone",

  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "storage-server.pglsystem.com",
      },
      {
        protocol: "https",
        hostname: "s3-server.pglsystem.com",
      },
      {
        protocol: "https",
        hostname: "storage-server.tglsupplies.com",
      },
    ],
  },
};

export default withNextIntl(nextConfig);
